import React, { useEffect, useState } from 'react';
import { Box, Alert, Snackbar, Typography } from '@mui/material';
import { useAuth } from '../../context/AuthContext';
import { useLocationTracking } from '../../hooks/useLocationTracking';

/**
 * Location Integration Component
 * Automatically handles location tracking for couriers and provides status updates
 */
const LocationIntegration = ({ 
  autoStart = true, 
  showNotifications = true,
  onLocationUpdate,
  onError,
  children 
}) => {
  const { user } = useAuth();
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  const [hasStartedTracking, setHasStartedTracking] = useState(false);

  const {
    isTracking,
    currentLocation,
    error,
    stats,
    permissionStatus,
    startTracking,
    stopTracking,
    requestPermission,
    isSupported
  } = useLocationTracking();

  // Auto-start tracking for couriers
  useEffect(() => {
    if (!user || !isSupported || hasStartedTracking) return;

    const initializeTracking = async () => {
      // Only auto-start for couriers
      if (user.role === 'courier' && autoStart) {
        try {
          // Check if auto-start is enabled in settings
          const autoStartEnabled = localStorage.getItem('autoStartTracking') === 'true';
          
          if (autoStartEnabled || permissionStatus === 'granted') {
            const success = await startTracking({
              enableHighAccuracy: true,
              timeout: 15000,
              maximumAge: 30000,
              distanceThreshold: 5,
              accuracyThreshold: 50
            });

            if (success) {
              setHasStartedTracking(true);
              if (showNotifications) {
                showNotification('Location tracking started successfully', 'success');
              }
            } else {
              if (showNotifications) {
                showNotification('Failed to start location tracking', 'error');
              }
            }
          } else if (permissionStatus === 'prompt') {
            // Request permission first
            try {
              await requestPermission();
            } catch (err) {
              console.log('User denied location permission');
            }
          }
        } catch (err) {
          console.error('Error initializing location tracking:', err);
          if (showNotifications) {
            showNotification('Error starting location tracking', 'error');
          }
        }
      }
    };

    // Delay initialization to ensure component is mounted
    const timer = setTimeout(initializeTracking, 1000);
    return () => clearTimeout(timer);
  }, [user, isSupported, autoStart, permissionStatus, hasStartedTracking, startTracking, requestPermission, showNotifications]);

  // Handle location updates
  useEffect(() => {
    if (currentLocation && onLocationUpdate) {
      onLocationUpdate(currentLocation);
    }
  }, [currentLocation, onLocationUpdate]);

  // Handle errors
  useEffect(() => {
    if (error) {
      if (onError) {
        onError(error);
      }
      
      if (showNotifications) {
        let errorMessage = 'Location tracking error';
        
        switch (error.code) {
          case 'PERMISSION_DENIED':
            errorMessage = 'Location permission denied. Please enable location access in your browser settings.';
            break;
          case 'POSITION_UNAVAILABLE':
            errorMessage = 'Location information is unavailable. Please check your GPS settings.';
            break;
          case 'TIMEOUT':
            errorMessage = 'Location request timed out. Trying again...';
            break;
          default:
            errorMessage = error.message || 'Unknown location error occurred';
        }
        
        showNotification(errorMessage, 'error');
      }
    }
  }, [error, onError, showNotifications]);

  // Show notification helper
  const showNotification = (message, severity = 'info') => {
    setNotification({
      open: true,
      message,
      severity
    });
  };

  // Handle notification close
  const handleNotificationClose = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  // Don't render anything if geolocation is not supported
  if (!isSupported) {
    return (
      <Box>
        {showNotifications && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            Geolocation is not supported by this browser. Location tracking features will not be available.
          </Alert>
        )}
        {children}
      </Box>
    );
  }

  // Don't render location status for non-courier users unless explicitly requested
  if (user?.role !== 'courier' && !children) {
    return null;
  }

  return (
    <Box>
      {/* Location Status for Couriers */}
      {user?.role === 'courier' && showNotifications && (
        <Box sx={{ mb: 2 }}>
          {permissionStatus === 'denied' && (
            <Alert severity="error" sx={{ mb: 1 }}>
              Location access is denied. Please enable location permissions in your browser settings to use tracking features.
            </Alert>
          )}
          
          {permissionStatus === 'prompt' && !isTracking && (
            <Alert severity="warning" sx={{ mb: 1 }}>
              Location permission is required for tracking. Please allow location access when prompted.
            </Alert>
          )}
          
          {isTracking && currentLocation && (
            <Alert severity="success" sx={{ mb: 1 }}>
              <Typography variant="body2">
                Location tracking active - Accuracy: ±{Math.round(currentLocation.accuracy || 0)}m
                {stats?.successRate && ` | Success Rate: ${stats.successRate}`}
              </Typography>
            </Alert>
          )}
          
          {isTracking && !currentLocation && (
            <Alert severity="info" sx={{ mb: 1 }}>
              <Typography variant="body2">
                Location tracking is active, waiting for GPS signal...
              </Typography>
            </Alert>
          )}
        </Box>
      )}

      {/* Render children */}
      {children}

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleNotificationClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Alert 
          onClose={handleNotificationClose} 
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default LocationIntegration;
