import {
  addDays,
  addHours,
  addMinutes,
  addMonths,
  addSeconds,
  addWeeks,
  addYears,
  differenceInDays,
  differenceInHours,
  differenceInMilliseconds,
  differenceInMinutes,
  differenceInMonths,
  differenceInQuarters,
  differenceInSeconds,
  differenceInWeeks,
  differenceInYears,
  eachDayOfInterval,
  en_US_default,
  endOfDay,
  endOfMonth,
  endOfWeek,
  endOfYear,
  format,
  formatISO,
  getDate,
  getDay,
  getDaysInMonth,
  getHours,
  getMinutes,
  getMonth,
  getSeconds,
  getYear,
  isAfter,
  isBefore,
  isEqual,
  isSameDay,
  isSameHour,
  isSameMonth,
  isSameYear,
  isValid,
  isWithinInterval,
  parse,
  parseISO,
  setDate,
  setHours,
  setMinutes,
  setMonth,
  setSeconds,
  setYear,
  startOfDay,
  startOfMonth,
  startOfWeek,
  startOfYear
} from "./chunk-4AFKSIAV.js";
import {
  __commonJS,
  __toESM
} from "./chunk-4B2QHNJT.js";

// node_modules/date-fns/_lib/format/longFormatters/index.js
var require_longFormatters = __commonJS({
  "node_modules/date-fns/_lib/format/longFormatters/index.js"(exports, module) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var dateLongFormatter = function dateLongFormatter2(pattern, formatLong) {
      switch (pattern) {
        case "P":
          return formatLong.date({
            width: "short"
          });
        case "PP":
          return formatLong.date({
            width: "medium"
          });
        case "PPP":
          return formatLong.date({
            width: "long"
          });
        case "PPPP":
        default:
          return formatLong.date({
            width: "full"
          });
      }
    };
    var timeLongFormatter = function timeLongFormatter2(pattern, formatLong) {
      switch (pattern) {
        case "p":
          return formatLong.time({
            width: "short"
          });
        case "pp":
          return formatLong.time({
            width: "medium"
          });
        case "ppp":
          return formatLong.time({
            width: "long"
          });
        case "pppp":
        default:
          return formatLong.time({
            width: "full"
          });
      }
    };
    var dateTimeLongFormatter = function dateTimeLongFormatter2(pattern, formatLong) {
      var matchResult = pattern.match(/(P+)(p+)?/) || [];
      var datePattern = matchResult[1];
      var timePattern = matchResult[2];
      if (!timePattern) {
        return dateLongFormatter(pattern, formatLong);
      }
      var dateTimeFormat;
      switch (datePattern) {
        case "P":
          dateTimeFormat = formatLong.dateTime({
            width: "short"
          });
          break;
        case "PP":
          dateTimeFormat = formatLong.dateTime({
            width: "medium"
          });
          break;
        case "PPP":
          dateTimeFormat = formatLong.dateTime({
            width: "long"
          });
          break;
        case "PPPP":
        default:
          dateTimeFormat = formatLong.dateTime({
            width: "full"
          });
          break;
      }
      return dateTimeFormat.replace("{{date}}", dateLongFormatter(datePattern, formatLong)).replace("{{time}}", timeLongFormatter(timePattern, formatLong));
    };
    var longFormatters3 = {
      p: timeLongFormatter,
      P: dateTimeLongFormatter
    };
    var _default = longFormatters3;
    exports.default = _default;
    module.exports = exports.default;
  }
});

// node_modules/@date-io/date-fns/build/index.esm.js
var import_longFormatters = __toESM(require_longFormatters());
var defaultFormats = {
  dayOfMonth: "d",
  fullDate: "PP",
  fullDateWithWeekday: "PPPP",
  fullDateTime: "PP p",
  fullDateTime12h: "PP hh:mm aaa",
  fullDateTime24h: "PP HH:mm",
  fullTime: "p",
  fullTime12h: "hh:mm aaa",
  fullTime24h: "HH:mm",
  hours12h: "hh",
  hours24h: "HH",
  keyboardDate: "P",
  keyboardDateTime: "P p",
  keyboardDateTime12h: "P hh:mm aaa",
  keyboardDateTime24h: "P HH:mm",
  minutes: "mm",
  month: "LLLL",
  monthAndDate: "MMMM d",
  monthAndYear: "LLLL yyyy",
  monthShort: "MMM",
  weekday: "EEEE",
  weekdayShort: "EEE",
  normalDate: "d MMMM",
  normalDateWithWeekday: "EEE, MMM d",
  seconds: "ss",
  shortDate: "MMM d",
  year: "yyyy"
};
var DateFnsUtils = class {
  constructor({ locale, formats } = {}) {
    this.lib = "date-fns";
    this.is12HourCycleInCurrentLocale = () => {
      var _a;
      if (this.locale) {
        return /a/.test((_a = this.locale.formatLong) === null || _a === void 0 ? void 0 : _a.time());
      }
      return true;
    };
    this.getFormatHelperText = (format2) => {
      var _a, _b;
      const longFormatRegexp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;
      const locale2 = this.locale || en_US_default;
      return (_b = (_a = format2.match(longFormatRegexp)) === null || _a === void 0 ? void 0 : _a.map((token) => {
        const firstCharacter = token[0];
        if (firstCharacter === "p" || firstCharacter === "P") {
          const longFormatter = import_longFormatters.default[firstCharacter];
          return longFormatter(token, locale2.formatLong, {});
        }
        return token;
      }).join("").replace(/(aaa|aa|a)/g, "(a|p)m").toLocaleLowerCase()) !== null && _b !== void 0 ? _b : format2;
    };
    this.parseISO = (isoString) => {
      return parseISO(isoString);
    };
    this.toISO = (value) => {
      return formatISO(value, { format: "extended" });
    };
    this.getCurrentLocaleCode = () => {
      var _a;
      return ((_a = this.locale) === null || _a === void 0 ? void 0 : _a.code) || "en-US";
    };
    this.addSeconds = (value, count) => {
      return addSeconds(value, count);
    };
    this.addMinutes = (value, count) => {
      return addMinutes(value, count);
    };
    this.addHours = (value, count) => {
      return addHours(value, count);
    };
    this.addDays = (value, count) => {
      return addDays(value, count);
    };
    this.addWeeks = (value, count) => {
      return addWeeks(value, count);
    };
    this.addMonths = (value, count) => {
      return addMonths(value, count);
    };
    this.addYears = (value, count) => {
      return addYears(value, count);
    };
    this.isValid = (value) => {
      return isValid(this.date(value));
    };
    this.getDiff = (value, comparing, unit) => {
      var _a;
      const dateToCompare = (_a = this.date(comparing)) !== null && _a !== void 0 ? _a : value;
      if (!this.isValid(dateToCompare)) {
        return 0;
      }
      switch (unit) {
        case "years":
          return differenceInYears(value, dateToCompare);
        case "quarters":
          return differenceInQuarters(value, dateToCompare);
        case "months":
          return differenceInMonths(value, dateToCompare);
        case "weeks":
          return differenceInWeeks(value, dateToCompare);
        case "days":
          return differenceInDays(value, dateToCompare);
        case "hours":
          return differenceInHours(value, dateToCompare);
        case "minutes":
          return differenceInMinutes(value, dateToCompare);
        case "seconds":
          return differenceInSeconds(value, dateToCompare);
        default: {
          return differenceInMilliseconds(value, dateToCompare);
        }
      }
    };
    this.isAfter = (value, comparing) => {
      return isAfter(value, comparing);
    };
    this.isBefore = (value, comparing) => {
      return isBefore(value, comparing);
    };
    this.startOfDay = (value) => {
      return startOfDay(value);
    };
    this.endOfDay = (value) => {
      return endOfDay(value);
    };
    this.getHours = (value) => {
      return getHours(value);
    };
    this.setHours = (value, count) => {
      return setHours(value, count);
    };
    this.setMinutes = (value, count) => {
      return setMinutes(value, count);
    };
    this.getSeconds = (value) => {
      return getSeconds(value);
    };
    this.setSeconds = (value, count) => {
      return setSeconds(value, count);
    };
    this.isSameDay = (value, comparing) => {
      return isSameDay(value, comparing);
    };
    this.isSameMonth = (value, comparing) => {
      return isSameMonth(value, comparing);
    };
    this.isSameYear = (value, comparing) => {
      return isSameYear(value, comparing);
    };
    this.isSameHour = (value, comparing) => {
      return isSameHour(value, comparing);
    };
    this.startOfYear = (value) => {
      return startOfYear(value);
    };
    this.endOfYear = (value) => {
      return endOfYear(value);
    };
    this.startOfMonth = (value) => {
      return startOfMonth(value);
    };
    this.endOfMonth = (value) => {
      return endOfMonth(value);
    };
    this.startOfWeek = (value) => {
      return startOfWeek(value, { locale: this.locale });
    };
    this.endOfWeek = (value) => {
      return endOfWeek(value, { locale: this.locale });
    };
    this.getYear = (value) => {
      return getYear(value);
    };
    this.setYear = (value, count) => {
      return setYear(value, count);
    };
    this.date = (value) => {
      if (typeof value === "undefined") {
        return /* @__PURE__ */ new Date();
      }
      if (value === null) {
        return null;
      }
      return new Date(value);
    };
    this.toJsDate = (value) => {
      return value;
    };
    this.parse = (value, formatString) => {
      if (value === "") {
        return null;
      }
      return parse(value, formatString, /* @__PURE__ */ new Date(), { locale: this.locale });
    };
    this.format = (date, formatKey) => {
      return this.formatByString(date, this.formats[formatKey]);
    };
    this.formatByString = (date, formatString) => {
      return format(date, formatString, { locale: this.locale });
    };
    this.isEqual = (date, comparing) => {
      if (date === null && comparing === null) {
        return true;
      }
      return isEqual(date, comparing);
    };
    this.isNull = (date) => {
      return date === null;
    };
    this.isAfterDay = (date, value) => {
      return isAfter(date, endOfDay(value));
    };
    this.isBeforeDay = (date, value) => {
      return isBefore(date, startOfDay(value));
    };
    this.isBeforeYear = (date, value) => {
      return isBefore(date, startOfYear(value));
    };
    this.isAfterYear = (date, value) => {
      return isAfter(date, endOfYear(value));
    };
    this.isWithinRange = (date, [start, end]) => {
      return isWithinInterval(date, { start, end });
    };
    this.formatNumber = (numberToFormat) => {
      return numberToFormat;
    };
    this.getMinutes = (date) => {
      return getMinutes(date);
    };
    this.getDate = (date) => {
      return getDate(date);
    };
    this.setDate = (date, count) => {
      return setDate(date, count);
    };
    this.getMonth = (date) => {
      return getMonth(date);
    };
    this.getDaysInMonth = (date) => {
      return getDaysInMonth(date);
    };
    this.setMonth = (date, count) => {
      return setMonth(date, count);
    };
    this.getMeridiemText = (ampm) => {
      return ampm === "am" ? "AM" : "PM";
    };
    this.getNextMonth = (date) => {
      return addMonths(date, 1);
    };
    this.getPreviousMonth = (date) => {
      return addMonths(date, -1);
    };
    this.getMonthArray = (date) => {
      const firstMonth = startOfYear(date);
      const monthArray = [firstMonth];
      while (monthArray.length < 12) {
        const prevMonth = monthArray[monthArray.length - 1];
        monthArray.push(this.getNextMonth(prevMonth));
      }
      return monthArray;
    };
    this.mergeDateAndTime = (date, time) => {
      return this.setSeconds(this.setMinutes(this.setHours(date, this.getHours(time)), this.getMinutes(time)), this.getSeconds(time));
    };
    this.getWeekdays = () => {
      const now = /* @__PURE__ */ new Date();
      return eachDayOfInterval({
        start: startOfWeek(now, { locale: this.locale }),
        end: endOfWeek(now, { locale: this.locale })
      }).map((day) => this.formatByString(day, "EEEEEE"));
    };
    this.getWeekArray = (date) => {
      const start = startOfWeek(startOfMonth(date), { locale: this.locale });
      const end = endOfWeek(endOfMonth(date), { locale: this.locale });
      let count = 0;
      let current = start;
      const nestedWeeks = [];
      let lastDay = null;
      while (isBefore(current, end)) {
        const weekNumber = Math.floor(count / 7);
        nestedWeeks[weekNumber] = nestedWeeks[weekNumber] || [];
        const day = getDay(current);
        if (lastDay !== day) {
          lastDay = day;
          nestedWeeks[weekNumber].push(current);
          count += 1;
        }
        current = addDays(current, 1);
      }
      return nestedWeeks;
    };
    this.getYearRange = (start, end) => {
      const startDate = startOfYear(start);
      const endDate = endOfYear(end);
      const years = [];
      let current = startDate;
      while (isBefore(current, endDate)) {
        years.push(current);
        current = addYears(current, 1);
      }
      return years;
    };
    this.locale = locale;
    this.formats = Object.assign({}, defaultFormats, formats);
  }
  isBeforeMonth(value, comparing) {
    return isBefore(value, startOfMonth(comparing));
  }
  isAfterMonth(value, comparing) {
    return isAfter(value, startOfMonth(comparing));
  }
};

// node_modules/@mui/x-date-pickers/AdapterDateFns/index.js
var import_longFormatters2 = __toESM(require_longFormatters());
var formatTokenMap = {
  y: "year",
  yy: "year",
  yyy: "year",
  yyyy: "year",
  MMMM: "month",
  MM: "month",
  DD: "day",
  d: "day",
  dd: "day",
  H: "hour",
  HH: "hour",
  h: "hour",
  hh: "hour",
  mm: "minute",
  ss: "second",
  a: "am-pm",
  aa: "am-pm",
  aaa: "am-pm"
};
var AdapterDateFns = class extends DateFnsUtils {
  constructor(...args) {
    super(...args);
    this.formatTokenMap = formatTokenMap;
    this.expandFormat = (format2) => {
      const longFormatRegexp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;
      return format2.match(longFormatRegexp).map((token) => {
        const firstCharacter = token[0];
        if (firstCharacter === "p" || firstCharacter === "P") {
          const longFormatter = import_longFormatters2.default[firstCharacter];
          const locale = this.locale || en_US_default;
          return longFormatter(token, locale.formatLong, {});
        }
        return token;
      }).join("");
    };
    this.getFormatHelperText = (format2) => {
      return this.expandFormat(format2).replace(/(aaa|aa|a)/g, "(a|p)m").toLocaleLowerCase();
    };
  }
};
export {
  AdapterDateFns
};
//# sourceMappingURL=@mui_x-date-pickers_AdapterDateFns.js.map
