// Hierarchical Location Selector for Indian States and Cities
import React, { useState, useEffect } from 'react';
import {
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Box,
  Typography,
  Chip,
  Autocomplete,
  Paper
} from '@mui/material';
import { LocationOn, Business, Home } from '@mui/icons-material';

// Indian States and Cities Data
const INDIAN_LOCATIONS = {
  "Karnataka": {
    cities: {
      "Bangalore": {
        areas: ["Koramangala", "Indiranagar", "Whitefield", "Electronic City", "Hebbal", "Jayanagar", "BTM Layout", "HSR Layout", "Marathahalli", "Sarjapur"],
        landmarks: ["UB City Mall", "Forum Mall", "Phoenix MarketCity", "Orion Mall", "Mantri Square"]
      },
      "Mysore": {
        areas: ["Saraswathipuram", "Kuvempunagar", "Vijayanagar", "Hebbal", "Bogadi"],
        landmarks: ["Mysore Palace", "Chamundi Hills", "KRS Dam"]
      },
      "Mangalore": {
        areas: ["Hampankatta", "Kadri", "Bejai", "<PERSON>al<PERSON>", "Kankanady"],
        landmarks: ["City Centre Mall", "Forum Fiza Mall", "Mangalore Central"]
      },
      "Hubli": {
        areas: ["Vidyanagar", "Keshwapur", "Gokul Road", "Deshpande Nagar"],
        landmarks: ["Urban Oasis Mall", "Hubli Railway Station"]
      }
    }
  },
  "Maharashtra": {
    cities: {
      "Mumbai": {
        areas: ["Andheri", "Bandra", "Powai", "Malad", "Borivali", "Thane", "Navi Mumbai", "Worli", "Lower Parel", "Goregaon"],
        landmarks: ["Gateway of India", "Marine Drive", "Juhu Beach", "Phoenix Mills", "Palladium Mall", "R City Mall"]
      },
      "Pune": {
        areas: ["Koregaon Park", "Hinjewadi", "Wakad", "Baner", "Aundh", "Kothrud", "Hadapsar", "Magarpatta"],
        landmarks: ["Phoenix MarketCity", "Westend Mall", "Aga Khan Palace", "Shaniwar Wada"]
      },
      "Nagpur": {
        areas: ["Sitabuldi", "Dharampeth", "Sadar", "Hingna", "Wardha Road"],
        landmarks: ["Empress City Mall", "Eternity Mall", "Deekshabhoomi"]
      }
    }
  },
  "Tamil Nadu": {
    cities: {
      "Chennai": {
        areas: ["T. Nagar", "Anna Nagar", "Adyar", "Velachery", "OMR", "Porur", "Tambaram", "Chrompet"],
        landmarks: ["Express Avenue", "Phoenix MarketCity", "Forum Vijaya Mall", "Marina Beach"]
      },
      "Coimbatore": {
        areas: ["RS Puram", "Peelamedu", "Saibaba Colony", "Gandhipuram", "Race Course"],
        landmarks: ["Brookefields Mall", "Fun Republic Mall", "Marudamalai Temple"]
      },
      "Madurai": {
        areas: ["Anna Nagar", "KK Nagar", "Sellur", "Vilangudi", "Pasumalai"],
        landmarks: ["Meenakshi Temple", "Thirumalai Nayakkar Palace"]
      }
    }
  },
  "Delhi": {
    cities: {
      "New Delhi": {
        areas: ["Connaught Place", "Karol Bagh", "Lajpat Nagar", "Saket", "Vasant Kunj", "Dwarka", "Rohini", "Janakpuri"],
        landmarks: ["India Gate", "Red Fort", "Select City Walk", "DLF Mall", "Ambience Mall"]
      }
    }
  },
  "Gujarat": {
    cities: {
      "Ahmedabad": {
        areas: ["Satellite", "Vastrapur", "Bopal", "Prahlad Nagar", "Navrangpura", "CG Road"],
        landmarks: ["Ahmedabad One Mall", "Alpha One Mall", "Sabarmati Ashram"]
      },
      "Surat": {
        areas: ["Adajan", "Vesu", "Pal", "Rander", "Katargam"],
        landmarks: ["VR Surat", "Rahul Raj Mall", "Surat Castle"]
      }
    }
  },
  "West Bengal": {
    cities: {
      "Kolkata": {
        areas: ["Salt Lake", "New Town", "Ballygunge", "Park Street", "Howrah", "Dum Dum"],
        landmarks: ["Victoria Memorial", "Howrah Bridge", "South City Mall", "Quest Mall"]
      }
    }
  },
  "Rajasthan": {
    cities: {
      "Jaipur": {
        areas: ["Malviya Nagar", "Vaishali Nagar", "Mansarovar", "Jagatpura", "Tonk Road"],
        landmarks: ["Hawa Mahal", "City Palace", "World Trade Park", "Pink Square Mall"]
      },
      "Udaipur": {
        areas: ["Fateh Sagar", "Sukhadia Circle", "Hiran Magri", "Sector 14"],
        landmarks: ["City Palace", "Lake Pichola", "Celebration Mall"]
      }
    }
  },
  "Kerala": {
    cities: {
      "Kochi": {
        areas: ["Ernakulam", "Fort Kochi", "Kakkanad", "Edapally", "Kaloor"],
        landmarks: ["Lulu Mall", "Oberon Mall", "Marine Drive", "Chinese Fishing Nets"]
      },
      "Thiruvananthapuram": {
        areas: ["Pattom", "Kowdiar", "Vellayambalam", "Sasthamangalam", "Technopark"],
        landmarks: ["Padmanabhaswamy Temple", "Mall of Travancore"]
      }
    }
  },
  "Telangana": {
    cities: {
      "Hyderabad": {
        areas: ["Banjara Hills", "Jubilee Hills", "Gachibowli", "Hitech City", "Kondapur", "Madhapur", "Secunderabad"],
        landmarks: ["Charminar", "Golconda Fort", "Inorbit Mall", "Forum Sujana Mall", "GVK One"]
      }
    }
  },
  "Andhra Pradesh": {
    cities: {
      "Visakhapatnam": {
        areas: ["MVP Colony", "Dwaraka Nagar", "Gajuwaka", "Madhurawada"],
        landmarks: ["CMR Central", "Vizag Beach", "Kailasagiri"]
      },
      "Vijayawada": {
        areas: ["Benz Circle", "Labbipet", "Governorpet", "Auto Nagar"],
        landmarks: ["Kanaka Durga Temple", "PVP Square Mall"]
      }
    }
  }
};

const HierarchicalLocationSelector = ({
  onLocationChange,
  initialState = "",
  initialCity = "",
  initialArea = "",
  initialStreet = "",
  disabled = false,
  showLandmarks = true,
  required = false
}) => {
  const [selectedState, setSelectedState] = useState(initialState);
  const [selectedCity, setSelectedCity] = useState(initialCity);
  const [selectedArea, setSelectedArea] = useState(initialArea);
  const [selectedStreet, setSelectedStreet] = useState(initialStreet);
  const [selectedLandmark, setSelectedLandmark] = useState("");

  const states = Object.keys(INDIAN_LOCATIONS);
  const cities = selectedState ? Object.keys(INDIAN_LOCATIONS[selectedState]?.cities || {}) : [];
  const areas = selectedState && selectedCity ? INDIAN_LOCATIONS[selectedState]?.cities[selectedCity]?.areas || [] : [];
  const landmarks = selectedState && selectedCity ? INDIAN_LOCATIONS[selectedState]?.cities[selectedCity]?.landmarks || [] : [];

  useEffect(() => {
    if (onLocationChange) {
      const locationData = {
        state: selectedState,
        city: selectedCity,
        area: selectedArea,
        street: selectedStreet,
        landmark: selectedLandmark,
        fullAddress: buildFullAddress()
      };
      onLocationChange(locationData);
    }
  }, [selectedState, selectedCity, selectedArea, selectedStreet, selectedLandmark]);

  const buildFullAddress = () => {
    const parts = [];
    if (selectedStreet) parts.push(selectedStreet);
    if (selectedArea) parts.push(selectedArea);
    if (selectedCity) parts.push(selectedCity);
    if (selectedState) parts.push(selectedState);
    return parts.join(', ');
  };

  const handleStateChange = (event) => {
    const newState = event.target.value;
    setSelectedState(newState);
    setSelectedCity("");
    setSelectedArea("");
    setSelectedStreet("");
    setSelectedLandmark("");
  };

  const handleCityChange = (event) => {
    const newCity = event.target.value;
    setSelectedCity(newCity);
    setSelectedArea("");
    setSelectedStreet("");
    setSelectedLandmark("");
  };

  const handleAreaChange = (event, newValue) => {
    setSelectedArea(newValue || "");
  };

  const handleLandmarkChange = (event, newValue) => {
    setSelectedLandmark(newValue || "");
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <LocationOn color="primary" />
        Location Details
      </Typography>
      
      <Grid container spacing={2}>
        {/* State Selection */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth required={required}>
            <InputLabel>State</InputLabel>
            <Select
              value={selectedState}
              onChange={handleStateChange}
              label="State"
              disabled={disabled}
            >
              <MenuItem value="">
                <em>Select State</em>
              </MenuItem>
              {states.map((state) => (
                <MenuItem key={state} value={state}>
                  {state}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* City Selection */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth required={required} disabled={!selectedState}>
            <InputLabel>City</InputLabel>
            <Select
              value={selectedCity}
              onChange={handleCityChange}
              label="City"
              disabled={disabled || !selectedState}
            >
              <MenuItem value="">
                <em>Select City</em>
              </MenuItem>
              {cities.map((city) => (
                <MenuItem key={city} value={city}>
                  {city}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Area/Locality Selection */}
        <Grid item xs={12} sm={6}>
          <Autocomplete
            options={areas}
            value={selectedArea}
            onChange={handleAreaChange}
            disabled={disabled || !selectedCity}
            freeSolo
            renderInput={(params) => (
              <TextField
                {...params}
                label="Area/Locality"
                placeholder="Select or type area"
                required={required}
              />
            )}
            renderOption={(props, option) => (
              <Box component="li" {...props}>
                <Home sx={{ mr: 1, color: 'text.secondary' }} />
                {option}
              </Box>
            )}
          />
        </Grid>

        {/* Landmark Selection */}
        {showLandmarks && (
          <Grid item xs={12} sm={6}>
            <Autocomplete
              options={landmarks}
              value={selectedLandmark}
              onChange={handleLandmarkChange}
              disabled={disabled || !selectedCity}
              freeSolo
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Nearby Landmark"
                  placeholder="Select or type landmark"
                />
              )}
              renderOption={(props, option) => (
                <Box component="li" {...props}>
                  <Business sx={{ mr: 1, color: 'text.secondary' }} />
                  {option}
                </Box>
              )}
            />
          </Grid>
        )}

        {/* Street/Building Details */}
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Street Address / Building Details"
            value={selectedStreet}
            onChange={(e) => setSelectedStreet(e.target.value)}
            placeholder="Enter street address, building name, floor, etc."
            disabled={disabled}
            multiline
            rows={2}
          />
        </Grid>

        {/* Full Address Preview */}
        {buildFullAddress() && (
          <Grid item xs={12}>
            <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
              <Typography variant="caption" color="text.secondary">
                Full Address:
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5 }}>
                {buildFullAddress()}
              </Typography>
              {selectedLandmark && (
                <Chip
                  icon={<Business />}
                  label={`Near ${selectedLandmark}`}
                  size="small"
                  sx={{ mt: 1 }}
                />
              )}
            </Paper>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default HierarchicalLocationSelector;
