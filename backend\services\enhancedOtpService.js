const admin = require("firebase-admin");
const crypto = require("crypto");
const axios = require("axios");

/**
 * Enhanced OTP Service with multiple providers, security features, and production-ready capabilities
 */
class EnhancedOtpService {
    constructor() {
        this.otpStore = new Map(); // In-memory store for OTP metadata
        this.rateLimitStore = new Map(); // Rate limiting store
        this.blacklistedNumbers = new Set(); // Blacklisted phone numbers
        
        // Configuration
        this.config = {
            otpLength: 6,
            otpExpiry: 5 * 60 * 1000, // 5 minutes
            maxAttempts: 3,
            rateLimitWindow: 60 * 1000, // 1 minute
            maxOtpPerWindow: 3,
            resendCooldown: 30 * 1000, // 30 seconds
            blockDuration: 24 * 60 * 60 * 1000, // 24 hours
        };

        // SMS Providers configuration
        this.smsProviders = {
            twofactor: {
                apiKey: process.env.TWOFACTOR_API_KEY || "c824e20b-2b69-11f0-8b17-0200cd936042",
                baseUrl: "https://2factor.in/API/V1",
                priority: 1
            },
            fast2sms: {
                apiKey: process.env.FAST2SMS_API_KEY,
                baseUrl: "https://www.fast2sms.com/dev/bulkV2",
                priority: 2
            },
            textlocal: {
                apiKey: process.env.TEXTLOCAL_API_KEY,
                baseUrl: "https://api.textlocal.in/send",
                priority: 3
            },
            twilio: {
                accountSid: process.env.TWILIO_ACCOUNT_SID,
                authToken: process.env.TWILIO_AUTH_TOKEN,
                fromNumber: process.env.TWILIO_FROM_NUMBER,
                priority: 4
            }
        };
    }

    /**
     * Generate cryptographically secure OTP
     * @param {number} length - OTP length
     * @returns {string} Generated OTP
     */
    generateSecureOtp(length = this.config.otpLength) {
        const digits = '**********';
        let otp = '';
        
        for (let i = 0; i < length; i++) {
            const randomIndex = crypto.randomInt(0, digits.length);
            otp += digits[randomIndex];
        }
        
        return otp;
    }

    /**
     * Generate OTP hash for secure storage
     * @param {string} otp - OTP to hash
     * @param {string} salt - Salt for hashing
     * @returns {string} Hashed OTP
     */
    hashOtp(otp, salt) {
        return crypto.pbkdf2Sync(otp, salt, 10000, 64, 'sha512').toString('hex');
    }

    /**
     * Validate phone number format
     * @param {string} phoneNumber - Phone number to validate
     * @returns {Object} Validation result
     */
    validatePhoneNumber(phoneNumber) {
        // Remove all non-digit characters
        const cleaned = phoneNumber.replace(/\D/g, '');
        
        // Indian phone number validation
        const indianPattern = /^(\+91|91|0)?[6-9]\d{9}$/;
        
        if (!indianPattern.test(cleaned)) {
            return {
                isValid: false,
                error: "Invalid phone number format. Please enter a valid Indian mobile number."
            };
        }

        // Format to standard format
        const formatted = cleaned.length === 10 ? cleaned : cleaned.slice(-10);
        
        return {
            isValid: true,
            formatted: formatted,
            international: `+91${formatted}`
        };
    }

    /**
     * Check rate limiting for phone number
     * @param {string} phoneNumber - Phone number to check
     * @returns {Object} Rate limit status
     */
    checkRateLimit(phoneNumber) {
        const now = Date.now();
        const key = `rate_${phoneNumber}`;
        
        if (!this.rateLimitStore.has(key)) {
            this.rateLimitStore.set(key, { count: 0, windowStart: now });
        }

        const rateData = this.rateLimitStore.get(key);
        
        // Reset window if expired
        if (now - rateData.windowStart > this.config.rateLimitWindow) {
            rateData.count = 0;
            rateData.windowStart = now;
        }

        if (rateData.count >= this.config.maxOtpPerWindow) {
            const timeRemaining = this.config.rateLimitWindow - (now - rateData.windowStart);
            return {
                allowed: false,
                timeRemaining: Math.ceil(timeRemaining / 1000),
                message: `Rate limit exceeded. Please wait ${Math.ceil(timeRemaining / 1000)} seconds.`
            };
        }

        return { allowed: true };
    }

    /**
     * Check if phone number is blacklisted
     * @param {string} phoneNumber - Phone number to check
     * @returns {boolean} Is blacklisted
     */
    isBlacklisted(phoneNumber) {
        return this.blacklistedNumbers.has(phoneNumber);
    }

    /**
     * Send OTP via 2Factor.in
     * @param {string} phoneNumber - Phone number
     * @param {string} otp - OTP to send
     * @param {string} method - 'sms' or 'voice'
     * @returns {Promise<Object>} Result
     */
    async sendVia2Factor(phoneNumber, otp, method = 'sms') {
        try {
            const { apiKey, baseUrl } = this.smsProviders.twofactor;
            
            let url;
            if (method === 'voice') {
                url = `${baseUrl}/${apiKey}/VOICE/${phoneNumber}/${otp}`;
            } else {
                url = `${baseUrl}/${apiKey}/SMS/${phoneNumber}/${otp}/OTPSEND`;
            }

            const response = await axios.get(url, { timeout: 10000 });
            
            if (response.data.Status === 'Success') {
                return {
                    success: true,
                    sessionId: response.data.Details,
                    provider: '2factor',
                    method: method
                };
            } else {
                throw new Error(response.data.Details || 'Failed to send OTP');
            }
        } catch (error) {
            return {
                success: false,
                error: error.message,
                provider: '2factor'
            };
        }
    }

    /**
     * Send OTP via Fast2SMS
     * @param {string} phoneNumber - Phone number
     * @param {string} otp - OTP to send
     * @returns {Promise<Object>} Result
     */
    async sendViaFast2SMS(phoneNumber, otp) {
        try {
            const { apiKey, baseUrl } = this.smsProviders.fast2sms;
            
            if (!apiKey || apiKey === 'your_fast2sms_api_key_here') {
                throw new Error('Fast2SMS API key not configured');
            }

            const message = `Your OTP for delivery verification is: ${otp}. Valid for 5 minutes. Do not share with anyone.`;
            
            const response = await axios.post(baseUrl, {
                variables_values: otp,
                route: 'otp',
                numbers: phoneNumber,
                message: message
            }, {
                headers: {
                    'authorization': apiKey,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });

            if (response.data.return) {
                return {
                    success: true,
                    messageId: response.data.request_id,
                    provider: 'fast2sms'
                };
            } else {
                throw new Error(response.data.message || 'Failed to send OTP');
            }
        } catch (error) {
            return {
                success: false,
                error: error.message,
                provider: 'fast2sms'
            };
        }
    }

    /**
     * Send OTP via Firebase Cloud Messaging
     * @param {string} userId - User ID
     * @param {string} otp - OTP to send
     * @returns {Promise<Object>} Result
     */
    async sendViaFirebase(userId, otp) {
        try {
            const userDoc = await admin.firestore().collection("users").doc(userId).get();
            
            if (!userDoc.exists) {
                throw new Error('User not found');
            }

            const userData = userDoc.data();
            if (!userData.deviceToken) {
                throw new Error('Device token not available');
            }

            const message = {
                notification: {
                    title: "🔐 Delivery Verification OTP",
                    body: `Your OTP: ${otp} (Valid for 5 minutes)`
                },
                data: {
                    type: 'otp_verification',
                    otp: otp,
                    timestamp: Date.now().toString()
                },
                token: userData.deviceToken,
                android: {
                    priority: 'high',
                    notification: {
                        sound: 'default',
                        channelId: 'otp_channel'
                    }
                },
                apns: {
                    payload: {
                        aps: {
                            sound: 'default',
                            badge: 1
                        }
                    }
                }
            };

            const response = await admin.messaging().send(message);
            
            return {
                success: true,
                messageId: response,
                provider: 'firebase'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                provider: 'firebase'
            };
        }
    }

    /**
     * Send OTP with fallback providers
     * @param {Object} params - Parameters
     * @returns {Promise<Object>} Result
     */
    async sendOtp(params) {
        const {
            phoneNumber,
            orderId,
            userId,
            customerName = 'Customer',
            method = 'sms',
            preferredProvider = 'auto'
        } = params;

        try {
            // Validate phone number
            const phoneValidation = this.validatePhoneNumber(phoneNumber);
            if (!phoneValidation.isValid) {
                return {
                    success: false,
                    error: phoneValidation.error
                };
            }

            const formattedPhone = phoneValidation.formatted;

            // Check if blacklisted
            if (this.isBlacklisted(formattedPhone)) {
                return {
                    success: false,
                    error: 'Phone number is temporarily blocked'
                };
            }

            // Check rate limiting
            const rateLimit = this.checkRateLimit(formattedPhone);
            if (!rateLimit.allowed) {
                return {
                    success: false,
                    error: rateLimit.message,
                    timeRemaining: rateLimit.timeRemaining
                };
            }

            // Generate secure OTP
            const otp = this.generateSecureOtp();
            const salt = crypto.randomBytes(32).toString('hex');
            const hashedOtp = this.hashOtp(otp, salt);

            // Store OTP metadata
            const otpKey = `${orderId}_${formattedPhone}`;
            const otpData = {
                hashedOtp,
                salt,
                phoneNumber: formattedPhone,
                orderId,
                userId,
                attempts: 0,
                createdAt: Date.now(),
                expiresAt: Date.now() + this.config.otpExpiry,
                method,
                customerName
            };

            this.otpStore.set(otpKey, otpData);

            // Update rate limiting
            const rateKey = `rate_${formattedPhone}`;
            const rateData = this.rateLimitStore.get(rateKey);
            rateData.count++;

            // Try sending via providers with fallback
            const providers = this.getProviderOrder(preferredProvider);
            let lastError = null;
            let result = null;

            for (const provider of providers) {
                try {
                    switch (provider) {
                        case '2factor':
                            result = await this.sendVia2Factor(formattedPhone, otp, method);
                            break;
                        case 'fast2sms':
                            if (method === 'sms') {
                                result = await this.sendViaFast2SMS(formattedPhone, otp);
                            }
                            break;
                        case 'firebase':
                            if (userId) {
                                result = await this.sendViaFirebase(userId, otp);
                            }
                            break;
                    }

                    if (result && result.success) {
                        // Update OTP data with provider info
                        otpData.provider = result.provider;
                        otpData.sessionId = result.sessionId;
                        otpData.messageId = result.messageId;
                        
                        // Update Firestore
                        await this.updateOrderOtpData(orderId, {
                            otpSentToCustomer: true,
                            otpGeneratedAt: admin.firestore.FieldValue.serverTimestamp(),
                            otpSentMethod: result.provider,
                            otpDeliveryMethod: method,
                            otpSessionId: result.sessionId,
                            otpMessageId: result.messageId,
                            updatedAt: admin.firestore.FieldValue.serverTimestamp()
                        });

                        return {
                            success: true,
                            provider: result.provider,
                            method: method,
                            formattedPhone: phoneValidation.international,
                            expiresIn: this.config.otpExpiry / 1000
                        };
                    }

                    lastError = result ? result.error : 'Unknown error';
                } catch (error) {
                    lastError = error.message;
                    console.error(`Error with provider ${provider}:`, error);
                }
            }

            // All providers failed
            this.otpStore.delete(otpKey);
            return {
                success: false,
                error: `Failed to send OTP: ${lastError}`
            };

        } catch (error) {
            console.error('Error in sendOtp:', error);
            return {
                success: false,
                error: 'Internal server error'
            };
        }
    }

    /**
     * Verify OTP
     * @param {Object} params - Parameters
     * @returns {Promise<Object>} Result
     */
    async verifyOtp(params) {
        const { orderId, phoneNumber, otp, sessionId } = params;

        try {
            const phoneValidation = this.validatePhoneNumber(phoneNumber);
            if (!phoneValidation.isValid) {
                return {
                    success: false,
                    error: 'Invalid phone number'
                };
            }

            const formattedPhone = phoneValidation.formatted;
            const otpKey = `${orderId}_${formattedPhone}`;
            const otpData = this.otpStore.get(otpKey);

            if (!otpData) {
                return {
                    success: false,
                    error: 'OTP not found or expired'
                };
            }

            // Check expiry
            if (Date.now() > otpData.expiresAt) {
                this.otpStore.delete(otpKey);
                return {
                    success: false,
                    error: 'OTP has expired'
                };
            }

            // Check max attempts
            if (otpData.attempts >= this.config.maxAttempts) {
                this.otpStore.delete(otpKey);
                // Temporarily blacklist the number
                this.blacklistedNumbers.add(formattedPhone);
                setTimeout(() => {
                    this.blacklistedNumbers.delete(formattedPhone);
                }, this.config.blockDuration);

                return {
                    success: false,
                    error: 'Maximum verification attempts exceeded. Number temporarily blocked.'
                };
            }

            // Increment attempts
            otpData.attempts++;

            // Verify OTP
            let isValid = false;

            // First try provider-specific verification
            if (otpData.provider === '2factor' && sessionId) {
                const verifyResult = await this.verify2FactorOtp(sessionId, otp);
                isValid = verifyResult.success;
            } else {
                // Fallback to hash comparison
                const hashedInput = this.hashOtp(otp, otpData.salt);
                isValid = hashedInput === otpData.hashedOtp;
            }

            if (isValid) {
                // OTP verified successfully
                this.otpStore.delete(otpKey);
                
                // Update Firestore
                await this.updateOrderOtpData(orderId, {
                    otpVerified: true,
                    otpVerifiedAt: admin.firestore.FieldValue.serverTimestamp(),
                    updatedAt: admin.firestore.FieldValue.serverTimestamp()
                });

                return {
                    success: true,
                    message: 'OTP verified successfully'
                };
            } else {
                const remainingAttempts = this.config.maxAttempts - otpData.attempts;
                return {
                    success: false,
                    error: `Invalid OTP. ${remainingAttempts} attempts remaining.`,
                    remainingAttempts
                };
            }

        } catch (error) {
            console.error('Error in verifyOtp:', error);
            return {
                success: false,
                error: 'Internal server error'
            };
        }
    }

    /**
     * Verify OTP using 2Factor API
     * @param {string} sessionId - Session ID
     * @param {string} otp - OTP to verify
     * @returns {Promise<Object>} Result
     */
    async verify2FactorOtp(sessionId, otp) {
        try {
            const { apiKey, baseUrl } = this.smsProviders.twofactor;
            const url = `${baseUrl}/${apiKey}/SMS/VERIFY/${sessionId}/${otp}`;

            const response = await axios.get(url, { timeout: 10000 });

            return {
                success: response.data.Status === 'Success',
                error: response.data.Status !== 'Success' ? response.data.Details : null
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get provider order based on preference
     * @param {string} preferredProvider - Preferred provider
     * @returns {Array} Ordered list of providers
     */
    getProviderOrder(preferredProvider) {
        const allProviders = ['2factor', 'fast2sms', 'firebase'];
        
        if (preferredProvider && preferredProvider !== 'auto') {
            return [preferredProvider, ...allProviders.filter(p => p !== preferredProvider)];
        }

        // Default order based on priority
        return allProviders;
    }

    /**
     * Update order OTP data in Firestore
     * @param {string} orderId - Order ID
     * @param {Object} data - Data to update
     */
    async updateOrderOtpData(orderId, data) {
        try {
            await admin.firestore().collection('orders').doc(orderId).update(data);
        } catch (error) {
            console.error('Error updating order OTP data:', error);
        }
    }

    /**
     * Resend OTP
     * @param {Object} params - Parameters
     * @returns {Promise<Object>} Result
     */
    async resendOtp(params) {
        const { orderId, phoneNumber } = params;

        try {
            const phoneValidation = this.validatePhoneNumber(phoneNumber);
            if (!phoneValidation.isValid) {
                return {
                    success: false,
                    error: 'Invalid phone number'
                };
            }

            const formattedPhone = phoneValidation.formatted;
            const otpKey = `${orderId}_${formattedPhone}`;
            const otpData = this.otpStore.get(otpKey);

            if (!otpData) {
                return {
                    success: false,
                    error: 'No active OTP session found'
                };
            }

            // Check resend cooldown
            const timeSinceCreated = Date.now() - otpData.createdAt;
            if (timeSinceCreated < this.config.resendCooldown) {
                const waitTime = Math.ceil((this.config.resendCooldown - timeSinceCreated) / 1000);
                return {
                    success: false,
                    error: `Please wait ${waitTime} seconds before requesting a new OTP`
                };
            }

            // Delete old OTP and send new one
            this.otpStore.delete(otpKey);
            
            return await this.sendOtp(params);

        } catch (error) {
            console.error('Error in resendOtp:', error);
            return {
                success: false,
                error: 'Internal server error'
            };
        }
    }

    /**
     * Get OTP status
     * @param {string} orderId - Order ID
     * @param {string} phoneNumber - Phone number
     * @returns {Object} OTP status
     */
    getOtpStatus(orderId, phoneNumber) {
        const phoneValidation = this.validatePhoneNumber(phoneNumber);
        if (!phoneValidation.isValid) {
            return { exists: false };
        }

        const otpKey = `${orderId}_${phoneValidation.formatted}`;
        const otpData = this.otpStore.get(otpKey);

        if (!otpData) {
            return { exists: false };
        }

        const now = Date.now();
        const timeRemaining = Math.max(0, otpData.expiresAt - now);
        const canResend = (now - otpData.createdAt) >= this.config.resendCooldown;

        return {
            exists: true,
            expired: timeRemaining === 0,
            timeRemaining: Math.ceil(timeRemaining / 1000),
            attempts: otpData.attempts,
            maxAttempts: this.config.maxAttempts,
            canResend,
            provider: otpData.provider,
            method: otpData.method
        };
    }

    /**
     * Clean up expired OTPs
     */
    cleanupExpiredOtps() {
        const now = Date.now();
        for (const [key, data] of this.otpStore.entries()) {
            if (now > data.expiresAt) {
                this.otpStore.delete(key);
            }
        }
    }

    /**
     * Get service statistics
     * @returns {Object} Statistics
     */
    getStatistics() {
        return {
            activeOtps: this.otpStore.size,
            rateLimitedNumbers: this.rateLimitStore.size,
            blacklistedNumbers: this.blacklistedNumbers.size,
            config: this.config
        };
    }
}

// Create singleton instance
const enhancedOtpService = new EnhancedOtpService();

// Cleanup expired OTPs every minute
setInterval(() => {
    enhancedOtpService.cleanupExpiredOtps();
}, 60000);

module.exports = enhancedOtpService;
