"use client";
import {
  require_createSvgIcon
} from "./chunk-EXDT6RSN.js";
import "./chunk-2ARNDKYY.js";
import "./chunk-RZZYHUX5.js";
import "./chunk-FWU3CBNV.js";
import "./chunk-3L5MCMBQ.js";
import {
  require_interopRequireDefault
} from "./chunk-HYFG2SCB.js";
import "./chunk-KR3C65D2.js";
import "./chunk-R4ZBIWV7.js";
import "./chunk-PSO6HBME.js";
import {
  require_jsx_runtime
} from "./chunk-CRNJR6QK.js";
import "./chunk-ZMLY2J2T.js";
import {
  __commonJS
} from "./chunk-4B2QHNJT.js";

// node_modules/@mui/icons-material/Warning.js
var require_Warning = __commonJS({
  "node_modules/@mui/icons-material/Warning.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M1 21h22L12 2zm12-3h-2v-2h2zm0-4h-2v-4h2z"
    }), "Warning");
  }
});
export default require_Warning();
//# sourceMappingURL=@mui_icons-material_Warning.js.map
