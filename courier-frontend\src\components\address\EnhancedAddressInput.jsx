// Enhanced Address Input Component combining Autocomplete and Hierarchical Selection
import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Tabs,
  Tab,
  Typography,
  Grid,
  TextField,
  Button,
  Chip,
  Alert,
  Collapse,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Search,
  List as ListIcon,
  Map as MapIcon,
  ExpandMore,
  ExpandLess,
  Verified,
  Warning,
  Info
} from '@mui/icons-material';
import AddressAutocomplete from './AddressAutocomplete';
import HierarchicalLocationSelector from './HierarchicalLocationSelector';
import MapAddressSelector from './MapAddressSelector';

const EnhancedAddressInput = ({
  label = "Address",
  value = "",
  onChange,
  onLocationSelect,
  onCoordinatesChange,
  error = false,
  helperText = "",
  required = false,
  addressType = "delivery",
  showMapSelector = true,
  showHierarchicalSelector = true,
  showAddressValidation = true,
  disabled = false,
  ...props
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [addressData, setAddressData] = useState({
    formatted_address: value,
    coordinates: null,
    components: {},
    validation: null,
    source: 'manual'
  });
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [validationStatus, setValidationStatus] = useState(null);

  useEffect(() => {
    if (value !== addressData.formatted_address) {
      setAddressData(prev => ({
        ...prev,
        formatted_address: value
      }));
    }
  }, [value]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleAddressChange = (newAddress) => {
    setAddressData(prev => ({
      ...prev,
      formatted_address: newAddress,
      source: 'autocomplete'
    }));
    
    if (onChange) {
      onChange(newAddress);
    }
  };

  const handleLocationSelect = (locationData) => {
    setAddressData(prev => ({
      ...prev,
      ...locationData,
      source: activeTab === 0 ? 'autocomplete' : activeTab === 1 ? 'hierarchical' : 'map'
    }));

    if (onLocationSelect) {
      onLocationSelect(locationData);
    }

    if (locationData.coordinates && onCoordinatesChange) {
      onCoordinatesChange(locationData.coordinates);
    }

    // Validate address if enabled
    if (showAddressValidation) {
      validateAddress(locationData);
    }
  };

  const handleHierarchicalLocationChange = (locationData) => {
    const formattedAddress = locationData.fullAddress;
    setAddressData(prev => ({
      ...prev,
      formatted_address: formattedAddress,
      components: locationData,
      source: 'hierarchical'
    }));

    if (onChange) {
      onChange(formattedAddress);
    }

    if (onLocationSelect) {
      onLocationSelect({
        formatted_address: formattedAddress,
        components: locationData
      });
    }
  };

  const validateAddress = async (locationData) => {
    try {
      setValidationStatus('validating');
      
      // Simulate address validation (replace with actual validation service)
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const hasCoordinates = locationData.coordinates && 
        locationData.coordinates.lat && 
        locationData.coordinates.lng;
      
      const hasDetailedComponents = locationData.address_components && 
        locationData.address_components.length > 0;
      
      if (hasCoordinates && hasDetailedComponents) {
        setValidationStatus('valid');
      } else if (hasCoordinates) {
        setValidationStatus('partial');
      } else {
        setValidationStatus('invalid');
      }
    } catch (error) {
      console.error('Address validation error:', error);
      setValidationStatus('error');
    }
  };

  const getValidationIcon = () => {
    switch (validationStatus) {
      case 'valid':
        return <Verified color="success" />;
      case 'partial':
        return <Warning color="warning" />;
      case 'invalid':
        return <Warning color="error" />;
      case 'error':
        return <Warning color="error" />;
      default:
        return <Info color="info" />;
    }
  };

  const getValidationMessage = () => {
    switch (validationStatus) {
      case 'valid':
        return 'Address verified and deliverable';
      case 'partial':
        return 'Address found but may need additional details';
      case 'invalid':
        return 'Address could not be verified';
      case 'error':
        return 'Validation service unavailable';
      case 'validating':
        return 'Validating address...';
      default:
        return 'Enter address to validate';
    }
  };

  const getValidationSeverity = () => {
    switch (validationStatus) {
      case 'valid':
        return 'success';
      case 'partial':
        return 'warning';
      case 'invalid':
      case 'error':
        return 'error';
      default:
        return 'info';
    }
  };

  return (
    <Paper sx={{ p: 2, border: error ? '1px solid red' : 'none' }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          {label}
          {required && <span style={{ color: 'red' }}> *</span>}
        </Typography>
        
        {helperText && (
          <Typography variant="caption" color="text.secondary">
            {helperText}
          </Typography>
        )}
      </Box>

      <Tabs 
        value={activeTab} 
        onChange={handleTabChange} 
        sx={{ mb: 2 }}
        variant="fullWidth"
      >
        <Tab 
          icon={<Search />} 
          label="Search" 
          iconPosition="start"
          disabled={disabled}
        />
        {showHierarchicalSelector && (
          <Tab 
            icon={<ListIcon />} 
            label="Select" 
            iconPosition="start"
            disabled={disabled}
          />
        )}
        {showMapSelector && (
          <Tab 
            icon={<MapIcon />} 
            label="Map" 
            iconPosition="start"
            disabled={disabled}
          />
        )}
      </Tabs>

      {/* Search Tab */}
      {activeTab === 0 && (
        <AddressAutocomplete
          label=""
          value={addressData.formatted_address}
          onChange={handleAddressChange}
          onLocationSelect={handleLocationSelect}
          onCoordinatesChange={onCoordinatesChange}
          addressType={addressType}
          disabled={disabled}
          placeholder="Start typing to search for an address..."
          showMapButton={false}
          {...props}
        />
      )}

      {/* Hierarchical Selection Tab */}
      {activeTab === 1 && showHierarchicalSelector && (
        <HierarchicalLocationSelector
          onLocationChange={handleHierarchicalLocationChange}
          disabled={disabled}
          required={required}
        />
      )}

      {/* Map Selection Tab */}
      {activeTab === 2 && showMapSelector && (
        <MapAddressSelector
          onLocationSelect={handleLocationSelect}
          initialCoordinates={addressData.coordinates}
          disabled={disabled}
          height={400}
        />
      )}

      {/* Address Validation */}
      {showAddressValidation && addressData.formatted_address && (
        <Box sx={{ mt: 2 }}>
          <Alert 
            severity={getValidationSeverity()}
            icon={getValidationIcon()}
            sx={{ mb: 1 }}
          >
            {getValidationMessage()}
          </Alert>
        </Box>
      )}

      {/* Address Details */}
      {addressData.formatted_address && (
        <Box sx={{ mt: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="subtitle2">
              Selected Address
            </Typography>
            <Tooltip title={showAdvanced ? "Hide details" : "Show details"}>
              <IconButton
                size="small"
                onClick={() => setShowAdvanced(!showAdvanced)}
              >
                {showAdvanced ? <ExpandLess /> : <ExpandMore />}
              </IconButton>
            </Tooltip>
          </Box>
          
          <Typography variant="body2" sx={{ mt: 1, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
            {addressData.formatted_address}
          </Typography>

          <Box sx={{ mt: 1, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip 
              label={`Source: ${addressData.source}`} 
              size="small" 
              variant="outlined" 
            />
            {addressData.coordinates && (
              <Chip 
                label="GPS Coordinates" 
                size="small" 
                color="primary" 
                variant="outlined" 
              />
            )}
            {validationStatus === 'valid' && (
              <Chip 
                label="Verified" 
                size="small" 
                color="success" 
                variant="outlined" 
              />
            )}
          </Box>

          <Collapse in={showAdvanced}>
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                {addressData.coordinates && (
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Latitude"
                      value={addressData.coordinates.lat?.toFixed(6) || ''}
                      size="small"
                      InputProps={{ readOnly: true }}
                    />
                  </Grid>
                )}
                {addressData.coordinates && (
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Longitude"
                      value={addressData.coordinates.lng?.toFixed(6) || ''}
                      size="small"
                      InputProps={{ readOnly: true }}
                    />
                  </Grid>
                )}
                {addressData.place_id && (
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Place ID"
                      value={addressData.place_id}
                      size="small"
                      InputProps={{ readOnly: true }}
                    />
                  </Grid>
                )}
              </Grid>
            </Box>
          </Collapse>
        </Box>
      )}
    </Paper>
  );
};

export default EnhancedAddressInput;
