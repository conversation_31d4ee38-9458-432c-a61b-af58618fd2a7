import React, { useState, useCallback, useRef, useEffect } from 'react';
import { GoogleMap, LoadScript, Marker, InfoWindow } from '@react-google-maps/api';
import { Box, Alert, CircularProgress, Typography, Fab, Tooltip } from '@mui/material';
import { MyLocation, Refresh, Fullscreen, FullscreenExit } from '@mui/icons-material';
import { GOOGLE_MAPS_API_KEY, GOOGLE_MAPS_LIBRARIES, DEFAULT_CENTER } from '../../utils/googleMaps';

const mapContainerStyle = {
  width: '100%',
  height: '100%'
};

const libraries = ['places', 'geometry'];

const GoogleMapComponent = ({
  center = DEFAULT_CENTER,
  zoom = 12,
  markers = [],
  onMapLoad,
  onMarkerClick,
  showUserLocation = false,
  enableFullscreen = true,
  enableRefresh = true,
  enableMyLocation = true,
  height = 400,
  style = {},
  children,
  ...props
}) => {
  const [map, setMap] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userLocation, setUserLocation] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedMarker, setSelectedMarker] = useState(null);

  const mapRef = useRef(null);

  const onLoad = useCallback((map) => {
    setMap(map);
    setLoading(false);
    setError(null);
    
    if (onMapLoad) {
      onMapLoad({ map, maps: window.google.maps });
    }
    
    console.log('Google Map loaded successfully');
  }, [onMapLoad]);

  const onUnmount = useCallback(() => {
    setMap(null);
  }, []);

  const handleLoadError = useCallback((error) => {
    console.error('Map loading error:', error);
    setError('Failed to load map. Please check your internet connection.');
    setLoading(false);
  }, []);

  // Handle user location
  const handleGetUserLocation = useCallback(() => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by this browser');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const location = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          accuracy: position.coords.accuracy
        };
        
        setUserLocation(location);
        
        if (map) {
          map.setCenter(location);
          map.setZoom(16);
        }
      },
      (error) => {
        console.error('Geolocation error:', error);
        setError('Failed to get your location. Please check location permissions.');
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    );
  }, [map]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    if (showUserLocation) {
      handleGetUserLocation();
    }
    
    if (props.onRefresh) {
      props.onRefresh();
    }
  }, [showUserLocation, handleGetUserLocation, props]);

  // Handle fullscreen toggle
  const handleFullscreenToggle = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  // Get marker icon based on type
  const getMarkerIcon = (type, status) => {
    const icons = {
      courier: {
        active: '🚚',
        inactive: '🚛',
        busy: '🚐'
      },
      delivery: {
        pending: '📦',
        in_transit: '🚛',
        delivered: '✅',
        failed: '❌'
      },
      pickup: {
        pending: '📋',
        ready: '📦',
        collected: '✅'
      },
      warehouse: {
        active: '🏢',
        inactive: '🏢'
      },
      user: {
        active: '📍'
      },
      default: {
        active: '📍'
      }
    };

    const typeIcons = icons[type] || icons.default;
    return typeIcons[status] || typeIcons.active || typeIcons[Object.keys(typeIcons)[0]];
  };

  const containerStyle = {
    height: isFullscreen ? '100vh' : height,
    width: '100%',
    position: isFullscreen ? 'fixed' : 'relative',
    top: isFullscreen ? 0 : 'auto',
    left: isFullscreen ? 0 : 'auto',
    zIndex: isFullscreen ? 9999 : 'auto',
    ...style
  };

  if (!GOOGLE_MAPS_API_KEY) {
    return (
      <Box sx={containerStyle} display="flex" alignItems="center" justifyContent="center">
        <Alert severity="error">
          Google Maps API key is not configured
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={containerStyle} ref={mapRef}>
      {loading && (
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 1000
          }}
        >
          <CircularProgress />
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
            Loading map...
          </Typography>
        </Box>
      )}

      {error && (
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 1000,
            width: '80%',
            maxWidth: 400
          }}
        >
          <Alert severity="error">{error}</Alert>
        </Box>
      )}

      <LoadScript
        googleMapsApiKey={GOOGLE_MAPS_API_KEY}
        libraries={libraries}
        onLoad={() => setLoading(false)}
        onError={handleLoadError}
      >
        <GoogleMap
          mapContainerStyle={mapContainerStyle}
          center={center}
          zoom={zoom}
          onLoad={onLoad}
          onUnmount={onUnmount}
          options={{
            fullscreenControl: true,
            streetViewControl: true,
            mapTypeControl: true,
            zoomControl: true,
            scaleControl: true,
            gestureHandling: "cooperative",
            mapTypeId: "roadmap",
            clickableIcons: false,
            disableDefaultUI: false
          }}
          {...props}
        >
          {/* Render markers */}
          {markers.map((marker, index) => (
            <Marker
              key={`marker-${index}`}
              position={marker.position}
              title={marker.title}
              icon={{
                url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
                  <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="16" cy="16" r="14" fill="#2196F3" stroke="white" stroke-width="2"/>
                    <text x="16" y="20" text-anchor="middle" fill="white" font-size="16">
                      ${getMarkerIcon(marker.type, marker.status)}
                    </text>
                  </svg>
                `)}`,
                scaledSize: new window.google.maps.Size(32, 32),
                anchor: new window.google.maps.Point(16, 16)
              }}
              onClick={() => {
                setSelectedMarker(marker);
                if (onMarkerClick) {
                  onMarkerClick(marker, index);
                }
              }}
            />
          ))}

          {/* User location marker */}
          {showUserLocation && userLocation && (
            <Marker
              position={userLocation}
              title={`Your Location (±${Math.round(userLocation.accuracy)}m)`}
              icon={{
                url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
                  <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="16" cy="16" r="14" fill="#4CAF50" stroke="white" stroke-width="2"/>
                    <text x="16" y="20" text-anchor="middle" fill="white" font-size="16">📍</text>
                  </svg>
                `)}`,
                scaledSize: new window.google.maps.Size(32, 32),
                anchor: new window.google.maps.Point(16, 16)
              }}
            />
          )}

          {/* Info window for selected marker */}
          {selectedMarker && (
            <InfoWindow
              position={selectedMarker.position}
              onCloseClick={() => setSelectedMarker(null)}
            >
              <div>
                <h4>{selectedMarker.title}</h4>
                {selectedMarker.data && (
                  <div>
                    {Object.entries(selectedMarker.data).map(([key, value]) => (
                      <p key={key}><strong>{key}:</strong> {value}</p>
                    ))}
                  </div>
                )}
              </div>
            </InfoWindow>
          )}

          {children}
        </GoogleMap>
      </LoadScript>

      {/* Control Buttons */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 16,
          right: 16,
          display: 'flex',
          flexDirection: 'column',
          gap: 1
        }}
      >
        {enableMyLocation && (
          <Tooltip title="My Location">
            <Fab
              size="small"
              color="primary"
              onClick={handleGetUserLocation}
              sx={{ backgroundColor: 'white', color: 'primary.main' }}
            >
              <MyLocation />
            </Fab>
          </Tooltip>
        )}

        {enableRefresh && (
          <Tooltip title="Refresh">
            <Fab
              size="small"
              onClick={handleRefresh}
              sx={{ backgroundColor: 'white', color: 'text.primary' }}
            >
              <Refresh />
            </Fab>
          </Tooltip>
        )}

        {enableFullscreen && (
          <Tooltip title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}>
            <Fab
              size="small"
              onClick={handleFullscreenToggle}
              sx={{ backgroundColor: 'white', color: 'text.primary' }}
            >
              {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
            </Fab>
          </Tooltip>
        )}
      </Box>

      {/* User Location Indicator */}
      {userLocation && (
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            left: 16,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            padding: 1,
            borderRadius: 1,
            boxShadow: 1
          }}
        >
          <Typography variant="caption" color="text.secondary">
            Your Location: {userLocation.lat.toFixed(6)}, {userLocation.lng.toFixed(6)}
          </Typography>
          <br />
          <Typography variant="caption" color="text.secondary">
            Accuracy: ±{Math.round(userLocation.accuracy)}m
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default GoogleMapComponent;
