/**
 * OTP Service for the Courier Management System
 * Provides functions for OTP verification and order-specific OTP handling
 * Works with the smsService.js for sending OTPs
 */

import { doc, updateDoc, serverTimestamp, getDoc } from 'firebase/firestore';
import { db } from '../firebase';
import { sendOTP } from './smsService';

// 2Factor API key - In production, this should be stored in environment variables
const TWO_FACTOR_API_KEY = "c824e20b-2b69-11f0-8b17-0200cd936042";

/**
 * Verify OTP using 2Factor.in API
 * @param {string} sessionId - Session ID from sendOTP
 * @param {string} otp - OTP to verify
 * @returns {Promise<Object>} Response from 2Factor API
 */
export const verifyOTP = async (sessionId, otp) => {
  try {
    if (!sessionId || !otp) {
      return {
        success: false,
        error: 'Session ID and OTP are required'
      };
    }
    
    // Create the API URL
    const apiUrl = `https://2factor.in/API/V1/${TWO_FACTOR_API_KEY}/SMS/VERIFY/${sessionId}/${otp}`;
    
    console.log(`Verifying OTP ${otp} with session ID ${sessionId}`);
    
    // Make the API request
    const response = await fetch(apiUrl);
    const data = await response.json();
    
    if (data.Status === 'Success') {
      console.log('OTP verified successfully');
      return {
        success: true,
        error: null,
        details: data.Details
      };
    } else {
      console.error('Failed to verify OTP:', data.Details);
      return {
        success: false,
        error: data.Details || 'Invalid OTP'
      };
    }
  } catch (error) {
    console.error('Error verifying OTP:', error);
    return {
      success: false,
      error: error.message || 'Failed to verify OTP'
    };
  }
};

/**
 * Send OTP to customer for order verification
 * @param {string} orderId - Order ID
 * @param {string} customerPhone - Customer phone number
 * @param {string} customerName - Customer name
 * @param {string} method - Method to send OTP ('sms' or 'call')
 * @returns {Promise<Object>} Result of OTP sending operation
 */
export const sendOTPForOrder = async (orderId, customerPhone, customerName = 'Customer', method = 'sms') => {
  if (!orderId) {
    return {
      success: false,
      error: 'Order ID is required',
      formattedPhone: null,
      sessionId: null
    };
  }

  if (!customerPhone) {
    return {
      success: false,
      error: 'Customer phone number is required',
      formattedPhone: null,
      sessionId: null
    };
  }

  try {
    // Check if order already has a recent OTP
    const orderRef = doc(db, 'orders', orderId);
    const orderDoc = await getDoc(orderRef);
    
    if (!orderDoc.exists()) {
      return {
        success: false,
        error: `Order ${orderId} not found`,
        formattedPhone: null,
        sessionId: null
      };
    }
    
    const order = orderDoc.data();
    
    // If order already has an OTP sent in the last 2 minutes, reuse it
    if (order.otpSentAt && order.otpSessionId) {
      const otpSentAt = order.otpSentAt.toDate();
      const now = new Date();
      const timeDiff = (now - otpSentAt) / (1000 * 60); // in minutes
      
      if (timeDiff < 2) {
        console.log(`Order ${orderId} already has a recently sent OTP (${timeDiff.toFixed(1)} minutes ago). Reusing it.`);
        return {
          success: true,
          error: null,
          formattedPhone: order.customerPhoneFormatted || order.customerPhone,
          reused: true,
          sessionId: order.otpSessionId || null
        };
      }
    }
    
    console.log(`Sending OTP via ${method} for order ${orderId} with customer phone ${customerPhone}`);
    
    // Send a new OTP
    const result = await sendOTP(customerPhone, method);
    
    if (result.success) {
      // Update the order with OTP information
      await updateDoc(orderRef, {
        otpSentToCustomer: true,
        otpSentMethod: '2factor',
        otpDeliveryMethod: method,
        otpSentAt: serverTimestamp(),
        otpSessionId: result.sessionId,
        customerPhoneFormatted: result.formattedPhone,
        updatedAt: serverTimestamp()
      });
      
      return {
        success: true,
        error: null,
        formattedPhone: result.formattedPhone,
        sessionId: result.sessionId,
        method: method
      };
    } else {
      return result;
    }
  } catch (error) {
    console.error(`Error sending OTP for order ${orderId}:`, error);
    return {
      success: false,
      error: error.message || 'Failed to send OTP',
      formattedPhone: customerPhone,
      sessionId: null
    };
  }
};

/**
 * Verify OTP for an order
 * @param {string} orderId - Order ID
 * @param {string} otp - OTP to verify
 * @returns {Promise<Object>} Result of OTP verification
 */
export const verifyOTPForOrder = async (orderId, otp) => {
  if (!orderId || !otp) {
    return {
      success: false,
      error: 'Order ID and OTP are required'
    };
  }
  
  try {
    // Get the order
    const orderRef = doc(db, 'orders', orderId);
    const orderDoc = await getDoc(orderRef);
    
    if (!orderDoc.exists()) {
      return {
        success: false,
        error: `Order ${orderId} not found`
      };
    }
    
    const order = orderDoc.data();
    
    // Check if the order has an OTP session
    if (!order.otpSessionId) {
      return {
        success: false,
        error: 'No OTP has been sent for this order'
      };
    }
    
    // Verify the OTP
    const result = await verifyOTP(order.otpSessionId, otp);
    
    if (result.success) {
      // Update the order to mark OTP as verified
      await updateDoc(orderRef, {
        otpVerified: true,
        otpVerifiedAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      
      return {
        success: true,
        error: null,
        orderId: orderId
      };
    } else {
      return result;
    }
  } catch (error) {
    console.error(`Error verifying OTP for order ${orderId}:`, error);
    return {
      success: false,
      error: error.message || 'Failed to verify OTP'
    };
  }
};
