{"version": 3, "sources": ["../../dompurify/src/utils.js", "../../dompurify/src/tags.js", "../../dompurify/src/attrs.js", "../../dompurify/src/regexp.js", "../../dompurify/src/purify.js"], "sourcesContent": ["const {\n  hasOwnProperty,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\nexport function unapply(func) {\n  return (thisArg, ...args) => apply(func, thisArg, args);\n}\n\nexport function unconstruct(func) {\n  return (...args) => construct(func, args);\n}\n\n/* Add properties to a lookup table */\nexport function addToSet(set, array, transformCaseFunc) {\n  transformCaseFunc = transformCaseFunc ?? stringToLowerCase;\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/* Shallow clone an object */\nexport function clone(object) {\n  const newObject = create(null);\n\n  let property;\n  for (property in object) {\n    if (apply(hasOwnProperty, object, [property]) === true) {\n      newObject[property] = object[property];\n    }\n  }\n\n  return newObject;\n}\n\n/* IE10 doesn't support __lookupGetter__ so lets'\n * simulate it. It also automatically checks\n * if the prop is function or getter and behaves\n * accordingly. */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(element) {\n    console.warn('fallback value for', element);\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  // Object\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  hasOwnProperty,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'fedropshadow',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n]);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n]);\n\nexport const text = freeze(['#text']);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'xmlns',\n  'slot',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "import * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  freeze,\n  arrayForEach,\n  arrayPop,\n  arrayPush,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n} from './utils.js';\n\nconst getGlobal = () => (typeof window === 'undefined' ? null : window);\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */\nconst _createTrustedTypesPolicy = function (trustedTypes, document) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (\n    document.currentScript &&\n    document.currentScript.hasAttribute(ATTR_NAME)\n  ) {\n    suffix = document.currentScript.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = (root) => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n\n  let { document } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  const trustedTypesPolicy = _createTrustedTypesPolicy(\n    trustedTypes,\n    originalDocument\n  );\n  const emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML('') : '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let documentMode = {};\n  try {\n    documentMode = clone(document).documentMode ? document.documentMode : {};\n  } catch (_) {}\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined &&\n    documentMode !== 9;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    Object.create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? (PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE)\n        : (PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE);\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS =\n      'ALLOWED_TAGS' in cfg\n        ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n        : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR =\n      'ALLOWED_ATTR' in cfg\n        ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n        : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES =\n      'ALLOWED_NAMESPACES' in cfg\n        ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n        : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES =\n      'ADD_URI_SAFE_ATTR' in cfg\n        ? addToSet(\n            clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n            cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS =\n      'ADD_DATA_URI_TAGS' in cfg\n        ? addToSet(\n            clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n            cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS =\n      'FORBID_CONTENTS' in cfg\n        ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n        : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS =\n      'FORBID_TAGS' in cfg\n        ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n        : {};\n    FORBID_ATTR =\n      'FORBID_ATTR' in cfg\n        ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n        : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...TAGS.text]);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  const HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, TAGS.svg);\n  addToSet(ALL_SVG_TAGS, TAGS.svgFilters);\n  addToSet(ALL_SVG_TAGS, TAGS.svgDisallowed);\n\n  const ALL_MATHML_TAGS = addToSet({}, TAGS.mathMl);\n  addToSet(ALL_MATHML_TAGS, TAGS.mathMlDisallowed);\n\n  /**\n   *\n   *\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element) {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function (node) {\n    arrayPush(DOMPurify.removed, { element: node });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      try {\n        node.outerHTML = emptyHTML;\n      } catch (_) {\n        node.remove();\n      }\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function (name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\"\" attributes\n    if (name === 'is' && !ALLOWED_ATTR[name]) {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(node);\n        } catch (_) {}\n      } else {\n        try {\n          node.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n  const _createIterator = function (root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null,\n      false\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function (elm) {\n    return (\n      elm instanceof HTMLFormElement &&\n      (typeof elm.nodeName !== 'string' ||\n        typeof elm.textContent !== 'string' ||\n        typeof elm.removeChild !== 'function' ||\n        !(elm.attributes instanceof NamedNodeMap) ||\n        typeof elm.removeAttribute !== 'function' ||\n        typeof elm.setAttribute !== 'function' ||\n        typeof elm.namespaceURI !== 'string' ||\n        typeof elm.insertBefore !== 'function' ||\n        typeof elm.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function (object) {\n    return typeof Node === 'object'\n      ? object instanceof Node\n      : object &&\n          typeof object === 'object' &&\n          typeof object.nodeType === 'number' &&\n          typeof object.nodeName === 'string';\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function (entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode) {\n    let content;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check if tagname contains Unicode */\n    if (regExpTest(/[\\u0080-\\uFFFF]/, currentNode.nodeName)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      (!_isNode(currentNode.content) ||\n        !_isNode(currentNode.content.firstElementChild)) &&\n      regExpTest(/<[/\\w]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Mitigate a problem with templates inside select */\n    if (\n      tagName === 'select' &&\n      regExpTest(/<template/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any ocurrence of processing instructions */\n    if (currentNode.nodeType === 7) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.nodeType === 8 &&\n      regExpTest(/<[/\\w]/g, currentNode.data)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        )\n          return false;\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        )\n          return false;\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = stringReplace(content, MUSTACHE_EXPR, ' ');\n      content = stringReplace(content, ERB_EXPR, ' ');\n      content = stringReplace(content, TMPLIT_EXPR, ' ');\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_basicCustomElementTest(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _basicCustomElementCheck\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   * @param {string} tagName name of the tag of the node to sanitize\n   */\n  const _basicCustomElementTest = function (tagName) {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let l;\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      attr = attributes[l];\n      const { name, namespaceURI } = attr;\n      value = name === 'value' ? attr.value : stringTrim(attr.value);\n      lcName = transformCaseFunc(name);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      _removeAttribute(name, currentNode);\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        value = stringReplace(value, MUSTACHE_EXPR, ' ');\n        value = stringReplace(value, ERB_EXPR, ' ');\n        value = stringReplace(value, TMPLIT_EXPR, ' ');\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        if (_isClobbered(currentNode)) {\n          _forceRemove(currentNode);\n        } else {\n          arrayPop(DOMPurify.removed);\n        }\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment) {\n    let shadowNode;\n    const shadowIterator = _createIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body;\n    let importedNode;\n    let currentNode;\n    let oldNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Check we can run. Otherwise fall back or ignore */\n    if (!DOMPurify.isSupported) {\n      if (\n        typeof window.toStaticHTML === 'object' ||\n        typeof window.toStaticHTML === 'function'\n      ) {\n        if (typeof dirty === 'string') {\n          return window.toStaticHTML(dirty);\n        }\n\n        if (_isNode(dirty)) {\n          return window.toStaticHTML(dirty.outerHTML);\n        }\n      }\n\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Fix IE's strange behavior with manipulated textNodes #89 */\n      if (currentNode.nodeType === 3 && currentNode === oldNode) {\n        continue;\n      }\n\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      oldNode = currentNode;\n    }\n\n    oldNode = null;\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, ERB_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function (cfg) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      return arrayPop(hooks[entryPoint]);\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDO,SAASA,QAAQC,MAAM;AAC5B,SAAO,SAACC,SAAO;AAAA,aAAAC,OAAAC,UAAAC,QAAKC,OAAI,IAAAC,MAAAJ,OAAAA,IAAAA,OAAA,IAAA,CAAA,GAAAK,OAAA,GAAAA,OAAAL,MAAAK,QAAA;AAAJF,WAAIE,OAAAJ,CAAAA,IAAAA,UAAAI,IAAA;IAAA;AAAA,WAAKC,MAAMR,MAAMC,SAASI,IAAI;EAAC;AACzD;AAEO,SAASI,YAAYT,MAAM;AAChC,SAAO,WAAA;AAAA,aAAAU,QAAAP,UAAAC,QAAIC,OAAIC,IAAAA,MAAAI,KAAA,GAAAC,QAAA,GAAAA,QAAAD,OAAAC,SAAA;AAAJN,WAAIM,KAAA,IAAAR,UAAAQ,KAAA;IAAA;AAAA,WAAKC,UAAUZ,MAAMK,IAAI;EAAC;AAC3C;AAGO,SAASQ,SAASC,KAAKC,OAAOC,mBAAmB;AAAA,MAAAC;AACtDD,uBAAiBC,qBAAGD,uBAAiB,QAAAC,uBAAA,SAAAA,qBAAIC;AACzC,MAAIC,gBAAgB;AAIlBA,mBAAeL,KAAK,IAAI;EAC1B;AAEA,MAAIM,IAAIL,MAAMX;AACd,SAAOgB,KAAK;AACV,QAAIC,UAAUN,MAAMK,CAAC;AACrB,QAAI,OAAOC,YAAY,UAAU;AAC/B,UAAMC,YAAYN,kBAAkBK,OAAO;AAC3C,UAAIC,cAAcD,SAAS;AAEzB,YAAI,CAACE,SAASR,KAAK,GAAG;AACpBA,gBAAMK,CAAC,IAAIE;QACb;AAEAD,kBAAUC;MACZ;IACF;AAEAR,QAAIO,OAAO,IAAI;EACjB;AAEA,SAAOP;AACT;AAGO,SAASU,MAAMC,QAAQ;AAC5B,MAAMC,YAAYC,OAAO,IAAI;AAE7B,MAAIC;AACJ,OAAKA,YAAYH,QAAQ;AACvB,QAAIjB,MAAMqB,gBAAgBJ,QAAQ,CAACG,QAAQ,CAAC,MAAM,MAAM;AACtDF,gBAAUE,QAAQ,IAAIH,OAAOG,QAAQ;IACvC;EACF;AAEA,SAAOF;AACT;AAMA,SAASI,aAAaL,QAAQM,MAAM;AAClC,SAAON,WAAW,MAAM;AACtB,QAAMO,OAAOC,yBAAyBR,QAAQM,IAAI;AAClD,QAAIC,MAAM;AACR,UAAIA,KAAKE,KAAK;AACZ,eAAOnC,QAAQiC,KAAKE,GAAG;MACzB;AAEA,UAAI,OAAOF,KAAKG,UAAU,YAAY;AACpC,eAAOpC,QAAQiC,KAAKG,KAAK;MAC3B;IACF;AAEAV,aAASW,eAAeX,MAAM;EAChC;AAEA,WAASY,cAAchB,SAAS;AAC9BiB,YAAQC,KAAK,sBAAsBlB,OAAO;AAC1C,WAAO;EACT;AAEA,SAAOgB;AACT;AI1DA,SAASG,kBAAsC;AAAA,MAAtBC,UAAMtC,UAAAC,SAAAD,KAAAA,UAAAuC,CAAAA,MAAAA,SAAAvC,UAAGwC,CAAAA,IAAAA,UAAS;AACzC,MAAMC,YAAY,SAAZA,WAAaC,MAAI;AAAA,WAAKL,gBAAgBK,IAAI;EAAC;AAMjDD,YAAUE,UAAUC;AAMpBH,YAAUI,UAAU,CAAA;AAEpB,MAAI,CAACP,WAAU,CAACA,QAAOQ,YAAYR,QAAOQ,SAASC,aAAa,GAAG;AAGjEN,cAAUO,cAAc;AAExB,WAAOP;EACT;AAEA,MAAMQ,mBAAmBX,QAAOQ;AAEhC,MAAMA,WAAaR,QAAbQ;AACN,MACEI,mBASEZ,QATFY,kBACAC,sBAQEb,QARFa,qBACAC,OAOEd,QAPFc,MACAC,UAMEf,QANFe,SACAC,aAKEhB,QALFgB,YAAUC,uBAKRjB,QAJFkB,cAAAA,eAAYD,yBAAA,SAAGjB,QAAOkB,gBAAgBlB,QAAOmB,kBAAeF,sBAC5DG,kBAGEpB,QAHFoB,iBACAC,YAEErB,QAFFqB,WACAC,eACEtB,QADFsB;AAGF,MAAMC,mBAAmBR,QAAQS;AAEjC,MAAMC,YAAYpC,aAAakC,kBAAkB,WAAW;AAC5D,MAAMG,iBAAiBrC,aAAakC,kBAAkB,aAAa;AACnE,MAAMI,gBAAgBtC,aAAakC,kBAAkB,YAAY;AACjE,MAAMK,gBAAgBvC,aAAakC,kBAAkB,YAAY;AAQjE,MAAI,OAAOV,wBAAwB,YAAY;AAC7C,QAAMgB,WAAWrB,SAASsB,cAAc,UAAU;AAClD,QAAID,SAASE,WAAWF,SAASE,QAAQC,eAAe;AACtDxB,iBAAWqB,SAASE,QAAQC;IAC9B;EACF;AAEA,MAAMC,qBAAqBC,0BACzBZ,cACAX,gBACF;AACA,MAAMwB,YAAYF,qBAAqBA,mBAAmBG,WAAW,EAAE,IAAI;AAE3E,MAAAC,YAKI7B,UAJF8B,iBAAcD,UAAdC,gBACAC,qBAAkBF,UAAlBE,oBACAC,yBAAsBH,UAAtBG,wBACAC,uBAAoBJ,UAApBI;AAEF,MAAQC,aAAe/B,iBAAf+B;AAER,MAAIC,eAAe,CAAA;AACnB,MAAI;AACFA,mBAAe5D,MAAMyB,QAAQ,EAAEmC,eAAenC,SAASmC,eAAe,CAAA;EACxE,SAASC,GAAG;EAAA;AAEZ,MAAIC,QAAQ,CAAA;AAKZ1C,YAAUO,cACR,OAAOkB,kBAAkB,cACzBU,kBACAA,eAAeQ,uBAAuB7C,UACtC0C,iBAAiB;AAEnB,MACEI,kBAQEC,eAPFC,aAOED,UANFE,gBAMEF,aALFG,cAKEH,WAJFI,cAIEJ,WAHFK,sBAGEL,mBAFFM,oBAEEN,iBADFO,mBACEP;AAEJ,MAAMQ,mBAAmBR;AAQzB,MAAIS,eAAe;AACnB,MAAMC,uBAAuBtF,SAAS,CAAA,GAAEuF,CAAAA,EAAAA,OAAAC,mBACnCC,MAAS,GAAAD,mBACTC,KAAQ,GAAAD,mBACRC,UAAe,GAAAD,mBACfC,QAAW,GAAAD,mBACXC,IAAS,CAAA,CACb;AAGD,MAAIC,eAAe;AACnB,MAAMC,uBAAuB3F,SAAS,CAAA,GAAE,CAAA,EAAAuF,OAAAC,mBACnCI,IAAU,GAAAJ,mBACVI,GAAS,GAAAJ,mBACTI,MAAY,GAAAJ,mBACZI,GAAS,CAAA,CACb;AAQD,MAAIC,0BAA0BC,OAAOC,KACnCD,OAAOhF,OAAO,MAAM;IAClBkF,cAAc;MACZC,UAAU;MACVC,cAAc;MACdC,YAAY;MACZ7E,OAAO;;IAET8E,oBAAoB;MAClBH,UAAU;MACVC,cAAc;MACdC,YAAY;MACZ7E,OAAO;;IAET+E,gCAAgC;MAC9BJ,UAAU;MACVC,cAAc;MACdC,YAAY;MACZ7E,OAAO;IACT;EACF,CAAC,CACH;AAGA,MAAIgF,cAAc;AAGlB,MAAIC,cAAc;AAGlB,MAAIC,kBAAkB;AAGtB,MAAIC,kBAAkB;AAGtB,MAAIC,0BAA0B;AAI9B,MAAIC,2BAA2B;AAK/B,MAAIC,qBAAqB;AAKzB,MAAIC,eAAe;AAGnB,MAAIC,iBAAiB;AAGrB,MAAIC,aAAa;AAIjB,MAAIC,aAAa;AAMjB,MAAIC,aAAa;AAIjB,MAAIC,sBAAsB;AAI1B,MAAIC,sBAAsB;AAK1B,MAAIC,eAAe;AAenB,MAAIC,uBAAuB;AAC3B,MAAMC,8BAA8B;AAGpC,MAAIC,eAAe;AAInB,MAAIC,WAAW;AAGf,MAAIC,eAAe,CAAA;AAGnB,MAAIC,kBAAkB;AACtB,MAAMC,0BAA0B3H,SAAS,CAAA,GAAI,CAC3C,kBACA,SACA,YACA,QACA,iBACA,QACA,UACA,QACA,MACA,MACA,MACA,MACA,SACA,WACA,YACA,YACA,aACA,UACA,SACA,OACA,YACA,SACA,SACA,SACA,KAAK,CACN;AAGD,MAAI4H,gBAAgB;AACpB,MAAMC,wBAAwB7H,SAAS,CAAA,GAAI,CACzC,SACA,SACA,OACA,UACA,SACA,OAAO,CACR;AAGD,MAAI8H,sBAAsB;AAC1B,MAAMC,8BAA8B/H,SAAS,CAAA,GAAI,CAC/C,OACA,SACA,OACA,MACA,SACA,QACA,WACA,eACA,QACA,WACA,SACA,SACA,SACA,OAAO,CACR;AAED,MAAMgI,mBAAmB;AACzB,MAAMC,gBAAgB;AACtB,MAAMC,iBAAiB;AAEvB,MAAIC,YAAYD;AAChB,MAAIE,iBAAiB;AAGrB,MAAIC,qBAAqB;AACzB,MAAMC,6BAA6BtI,SACjC,CAAA,GACA,CAACgI,kBAAkBC,eAAeC,cAAc,GAChDK,cACF;AAGA,MAAIC;AACJ,MAAMC,+BAA+B,CAAC,yBAAyB,WAAW;AAC1E,MAAMC,4BAA4B;AAClC,MAAIvI;AAGJ,MAAIwI,SAAS;AAKb,MAAMC,cAAcxG,SAASsB,cAAc,MAAM;AAEjD,MAAMmF,oBAAoB,SAApBA,mBAA8BC,WAAW;AAC7C,WAAOA,qBAAqBC,UAAUD,qBAAqBE;;AAS7D,MAAMC,eAAe,SAAfA,cAAyBC,KAAK;AAClC,QAAIP,UAAUA,WAAWO,KAAK;AAC5B;IACF;AAGA,QAAI,CAACA,OAAOC,QAAOD,GAAG,MAAK,UAAU;AACnCA,YAAM,CAAA;IACR;AAGAA,UAAMvI,MAAMuI,GAAG;AAEfV;IAEEC,6BAA6BW,QAAQF,IAAIV,iBAAiB,MAAM,KAC3DA,oBAAoBE,4BACpBF,oBAAoBU,IAAIV;AAG/BrI,wBACEqI,sBAAsB,0BAClBD,iBACAlI;AAGNgF,mBACE,kBAAkB6D,MACdlJ,SAAS,CAAA,GAAIkJ,IAAI7D,cAAclF,iBAAiB,IAChDmF;AACNI,mBACE,kBAAkBwD,MACdlJ,SAAS,CAAA,GAAIkJ,IAAIxD,cAAcvF,iBAAiB,IAChDwF;AACN0C,yBACE,wBAAwBa,MACpBlJ,SAAS,CAAA,GAAIkJ,IAAIb,oBAAoBE,cAAc,IACnDD;AACNR,0BACE,uBAAuBoB,MACnBlJ;MACEW,MAAMoH,2BAA2B;;MACjCmB,IAAIG;;MACJlJ;;IACF,IACA4H;AACNH,oBACE,uBAAuBsB,MACnBlJ;MACEW,MAAMkH,qBAAqB;;MAC3BqB,IAAII;;MACJnJ;;IACF,IACA0H;AACNH,sBACE,qBAAqBwB,MACjBlJ,SAAS,CAAA,GAAIkJ,IAAIxB,iBAAiBvH,iBAAiB,IACnDwH;AACNrB,kBACE,iBAAiB4C,MACblJ,SAAS,CAAA,GAAIkJ,IAAI5C,aAAanG,iBAAiB,IAC/C,CAAA;AACNoG,kBACE,iBAAiB2C,MACblJ,SAAS,CAAA,GAAIkJ,IAAI3C,aAAapG,iBAAiB,IAC/C,CAAA;AACNsH,mBAAe,kBAAkByB,MAAMA,IAAIzB,eAAe;AAC1DjB,sBAAkB0C,IAAI1C,oBAAoB;AAC1CC,sBAAkByC,IAAIzC,oBAAoB;AAC1CC,8BAA0BwC,IAAIxC,2BAA2B;AACzDC,+BAA2BuC,IAAIvC,6BAA6B;AAC5DC,yBAAqBsC,IAAItC,sBAAsB;AAC/CC,mBAAeqC,IAAIrC,iBAAiB;AACpCC,qBAAiBoC,IAAIpC,kBAAkB;AACvCG,iBAAaiC,IAAIjC,cAAc;AAC/BC,0BAAsBgC,IAAIhC,uBAAuB;AACjDC,0BAAsB+B,IAAI/B,uBAAuB;AACjDH,iBAAakC,IAAIlC,cAAc;AAC/BI,mBAAe8B,IAAI9B,iBAAiB;AACpCC,2BAAuB6B,IAAI7B,wBAAwB;AACnDE,mBAAe2B,IAAI3B,iBAAiB;AACpCC,eAAW0B,IAAI1B,YAAY;AAC3BpC,uBAAiB8D,IAAIK,sBAAsBnE;AAC3C+C,gBAAYe,IAAIf,aAAaD;AAC7BrC,8BAA0BqD,IAAIrD,2BAA2B,CAAA;AACzD,QACEqD,IAAIrD,2BACJgD,kBAAkBK,IAAIrD,wBAAwBG,YAAY,GAC1D;AACAH,8BAAwBG,eACtBkD,IAAIrD,wBAAwBG;IAChC;AAEA,QACEkD,IAAIrD,2BACJgD,kBAAkBK,IAAIrD,wBAAwBO,kBAAkB,GAChE;AACAP,8BAAwBO,qBACtB8C,IAAIrD,wBAAwBO;IAChC;AAEA,QACE8C,IAAIrD,2BACJ,OAAOqD,IAAIrD,wBAAwBQ,mCACjC,WACF;AACAR,8BAAwBQ,iCACtB6C,IAAIrD,wBAAwBQ;IAChC;AAEA,QAAIO,oBAAoB;AACtBH,wBAAkB;IACpB;AAEA,QAAIS,qBAAqB;AACvBD,mBAAa;IACf;AAGA,QAAIQ,cAAc;AAChBpC,qBAAerF,SAAS,CAAA,GAAEwF,mBAAMC,IAAS,CAAC;AAC1CC,qBAAe,CAAA;AACf,UAAI+B,aAAa+B,SAAS,MAAM;AAC9BxJ,iBAASqF,cAAcI,MAAS;AAChCzF,iBAAS0F,cAAcE,IAAU;MACnC;AAEA,UAAI6B,aAAagC,QAAQ,MAAM;AAC7BzJ,iBAASqF,cAAcI,KAAQ;AAC/BzF,iBAAS0F,cAAcE,GAAS;AAChC5F,iBAAS0F,cAAcE,GAAS;MAClC;AAEA,UAAI6B,aAAaiC,eAAe,MAAM;AACpC1J,iBAASqF,cAAcI,UAAe;AACtCzF,iBAAS0F,cAAcE,GAAS;AAChC5F,iBAAS0F,cAAcE,GAAS;MAClC;AAEA,UAAI6B,aAAakC,WAAW,MAAM;AAChC3J,iBAASqF,cAAcI,QAAW;AAClCzF,iBAAS0F,cAAcE,MAAY;AACnC5F,iBAAS0F,cAAcE,GAAS;MAClC;IACF;AAGA,QAAIsD,IAAIU,UAAU;AAChB,UAAIvE,iBAAiBC,sBAAsB;AACzCD,uBAAe1E,MAAM0E,YAAY;MACnC;AAEArF,eAASqF,cAAc6D,IAAIU,UAAUzJ,iBAAiB;IACxD;AAEA,QAAI+I,IAAIW,UAAU;AAChB,UAAInE,iBAAiBC,sBAAsB;AACzCD,uBAAe/E,MAAM+E,YAAY;MACnC;AAEA1F,eAAS0F,cAAcwD,IAAIW,UAAU1J,iBAAiB;IACxD;AAEA,QAAI+I,IAAIG,mBAAmB;AACzBrJ,eAAS8H,qBAAqBoB,IAAIG,mBAAmBlJ,iBAAiB;IACxE;AAEA,QAAI+I,IAAIxB,iBAAiB;AACvB,UAAIA,oBAAoBC,yBAAyB;AAC/CD,0BAAkB/G,MAAM+G,eAAe;MACzC;AAEA1H,eAAS0H,iBAAiBwB,IAAIxB,iBAAiBvH,iBAAiB;IAClE;AAGA,QAAIoH,cAAc;AAChBlC,mBAAa,OAAO,IAAI;IAC1B;AAGA,QAAIyB,gBAAgB;AAClB9G,eAASqF,cAAc,CAAC,QAAQ,QAAQ,MAAM,CAAC;IACjD;AAGA,QAAIA,aAAayE,OAAO;AACtB9J,eAASqF,cAAc,CAAC,OAAO,CAAC;AAChC,aAAOiB,YAAYyD;IACrB;AAIA,QAAIC,QAAQ;AACVA,aAAOd,GAAG;IACZ;AAEAP,aAASO;;AAGX,MAAMe,iCAAiCjK,SAAS,CAAA,GAAI,CAClD,MACA,MACA,MACA,MACA,OAAO,CACR;AAED,MAAMkK,0BAA0BlK,SAAS,CAAA,GAAI,CAAC,gBAAgB,CAAC;AAM/D,MAAMmK,+BAA+BnK,SAAS,CAAA,GAAI,CAChD,SACA,SACA,QACA,KACA,QAAQ,CACT;AAKD,MAAMoK,eAAepK,SAAS,CAAA,GAAIyF,KAAQ;AAC1CzF,WAASoK,cAAc3E,UAAe;AACtCzF,WAASoK,cAAc3E,aAAkB;AAEzC,MAAM4E,kBAAkBrK,SAAS,CAAA,GAAIyF,QAAW;AAChDzF,WAASqK,iBAAiB5E,gBAAqB;AAU/C,MAAM6E,uBAAuB,SAAvBA,sBAAiC9J,SAAS;AAC9C,QAAI+J,SAAS/G,cAAchD,OAAO;AAIlC,QAAI,CAAC+J,UAAU,CAACA,OAAOC,SAAS;AAC9BD,eAAS;QACPE,cAActC;QACdqC,SAAS;;IAEb;AAEA,QAAMA,UAAUnK,kBAAkBG,QAAQgK,OAAO;AACjD,QAAME,gBAAgBrK,kBAAkBkK,OAAOC,OAAO;AAEtD,QAAI,CAACnC,mBAAmB7H,QAAQiK,YAAY,GAAG;AAC7C,aAAO;IACT;AAEA,QAAIjK,QAAQiK,iBAAiBxC,eAAe;AAI1C,UAAIsC,OAAOE,iBAAiBvC,gBAAgB;AAC1C,eAAOsC,YAAY;MACrB;AAKA,UAAID,OAAOE,iBAAiBzC,kBAAkB;AAC5C,eACEwC,YAAY,UACXE,kBAAkB,oBACjBT,+BAA+BS,aAAa;MAElD;AAIA,aAAOC,QAAQP,aAAaI,OAAO,CAAC;IACtC;AAEA,QAAIhK,QAAQiK,iBAAiBzC,kBAAkB;AAI7C,UAAIuC,OAAOE,iBAAiBvC,gBAAgB;AAC1C,eAAOsC,YAAY;MACrB;AAIA,UAAID,OAAOE,iBAAiBxC,eAAe;AACzC,eAAOuC,YAAY,UAAUN,wBAAwBQ,aAAa;MACpE;AAIA,aAAOC,QAAQN,gBAAgBG,OAAO,CAAC;IACzC;AAEA,QAAIhK,QAAQiK,iBAAiBvC,gBAAgB;AAI3C,UACEqC,OAAOE,iBAAiBxC,iBACxB,CAACiC,wBAAwBQ,aAAa,GACtC;AACA,eAAO;MACT;AAEA,UACEH,OAAOE,iBAAiBzC,oBACxB,CAACiC,+BAA+BS,aAAa,GAC7C;AACA,eAAO;MACT;AAIA,aACE,CAACL,gBAAgBG,OAAO,MACvBL,6BAA6BK,OAAO,KAAK,CAACJ,aAAaI,OAAO;IAEnE;AAGA,QACEhC,sBAAsB,2BACtBH,mBAAmB7H,QAAQiK,YAAY,GACvC;AACA,aAAO;IACT;AAMA,WAAO;;AAQT,MAAMG,eAAe,SAAfA,cAAyBC,MAAM;AACnCC,cAAU/I,UAAUI,SAAS;MAAE3B,SAASqK;IAAK,CAAC;AAC9C,QAAI;AAEFA,WAAKE,WAAWC,YAAYH,IAAI;aACzBrG,GAAG;AACV,UAAI;AACFqG,aAAKI,YAAYlH;eACVS,IAAG;AACVqG,aAAKK,OAAM;MACb;IACF;;AASF,MAAMC,mBAAmB,SAAnBA,kBAA6BC,MAAMP,MAAM;AAC7C,QAAI;AACFC,gBAAU/I,UAAUI,SAAS;QAC3BkJ,WAAWR,KAAKS,iBAAiBF,IAAI;QACrCG,MAAMV;MACR,CAAC;aACMrG,GAAG;AACVsG,gBAAU/I,UAAUI,SAAS;QAC3BkJ,WAAW;QACXE,MAAMV;MACR,CAAC;IACH;AAEAA,SAAKW,gBAAgBJ,IAAI;AAGzB,QAAIA,SAAS,QAAQ,CAAC1F,aAAa0F,IAAI,GAAG;AACxC,UAAInE,cAAcC,qBAAqB;AACrC,YAAI;AACF0D,uBAAaC,IAAI;QACnB,SAASrG,GAAG;QAAA;MACd,OAAO;AACL,YAAI;AACFqG,eAAKY,aAAaL,MAAM,EAAE;QAC5B,SAAS5G,GAAG;QAAA;MACd;IACF;;AASF,MAAMkH,gBAAgB,SAAhBA,eAA0BC,OAAO;AAErC,QAAIC;AACJ,QAAIC;AAEJ,QAAI7E,YAAY;AACd2E,cAAQ,sBAAsBA;IAChC,OAAO;AAEL,UAAMG,UAAUC,YAAYJ,OAAO,aAAa;AAChDE,0BAAoBC,WAAWA,QAAQ,CAAC;IAC1C;AAEA,QACEtD,sBAAsB,2BACtBL,cAAcD,gBACd;AAEAyD,cACE,mEACAA,QACA;IACJ;AAEA,QAAMK,eAAenI,qBACjBA,mBAAmBG,WAAW2H,KAAK,IACnCA;AAKJ,QAAIxD,cAAcD,gBAAgB;AAChC,UAAI;AACF0D,cAAM,IAAI3I,UAAS,EAAGgJ,gBAAgBD,cAAcxD,iBAAiB;MACvE,SAAShE,GAAG;MAAA;IACd;AAGA,QAAI,CAACoH,OAAO,CAACA,IAAIM,iBAAiB;AAChCN,YAAM1H,eAAeiI,eAAehE,WAAW,YAAY,IAAI;AAC/D,UAAI;AACFyD,YAAIM,gBAAgBE,YAAYhE,iBAC5BrE,YACAiI;eACGxH,GAAG;MACV;IAEJ;AAEA,QAAM6H,OAAOT,IAAIS,QAAQT,IAAIM;AAE7B,QAAIP,SAASE,mBAAmB;AAC9BQ,WAAKC,aACHlK,SAASmK,eAAeV,iBAAiB,GACzCQ,KAAKG,WAAW,CAAC,KAAK,IACxB;IACF;AAGA,QAAIrE,cAAcD,gBAAgB;AAChC,aAAO7D,qBAAqBoI,KAC1Bb,KACA9E,iBAAiB,SAAS,MAC5B,EAAE,CAAC;IACL;AAEA,WAAOA,iBAAiB8E,IAAIM,kBAAkBG;;AAShD,MAAMK,kBAAkB,SAAlBA,iBAA4B1K,MAAM;AACtC,WAAOmC,mBAAmBsI;MACxBzK,KAAK4B,iBAAiB5B;MACtBA;;MAEAY,WAAW+J,eACT/J,WAAWgK,eACXhK,WAAWiK,YACXjK,WAAWkK,8BACXlK,WAAWmK;MACb;MACA;IACF;;AASF,MAAMC,eAAe,SAAfA,cAAyBC,KAAK;AAClC,WACEA,eAAejK,oBACd,OAAOiK,IAAIC,aAAa,YACvB,OAAOD,IAAIE,gBAAgB,YAC3B,OAAOF,IAAIjC,gBAAgB,cAC3B,EAAEiC,IAAIG,sBAAsBtK,iBAC5B,OAAOmK,IAAIzB,oBAAoB,cAC/B,OAAOyB,IAAIxB,iBAAiB,cAC5B,OAAOwB,IAAIxC,iBAAiB,YAC5B,OAAOwC,IAAIX,iBAAiB,cAC5B,OAAOW,IAAII,kBAAkB;;AAUnC,MAAMC,UAAU,SAAVA,SAAoB1M,QAAQ;AAChC,WAAOuI,QAAOzG,IAAI,MAAK,WACnB9B,kBAAkB8B,OAClB9B,UACEuI,QAAOvI,MAAM,MAAK,YAClB,OAAOA,OAAOyB,aAAa,YAC3B,OAAOzB,OAAOsM,aAAa;;AAWnC,MAAMK,eAAe,SAAfA,cAAyBC,YAAYC,aAAaC,MAAM;AAC5D,QAAI,CAACjJ,MAAM+I,UAAU,GAAG;AACtB;IACF;AAEAG,iBAAalJ,MAAM+I,UAAU,GAAG,SAACI,MAAS;AACxCA,WAAKnB,KAAK1K,WAAW0L,aAAaC,MAAM/E,MAAM;IAChD,CAAC;;AAaH,MAAMkF,oBAAoB,SAApBA,mBAA8BJ,aAAa;AAC/C,QAAI9J;AAGJ4J,iBAAa,0BAA0BE,aAAa,IAAI;AAGxD,QAAIT,aAAaS,WAAW,GAAG;AAC7B7C,mBAAa6C,WAAW;AACxB,aAAO;IACT;AAGA,QAAIK,WAAW,mBAAmBL,YAAYP,QAAQ,GAAG;AACvDtC,mBAAa6C,WAAW;AACxB,aAAO;IACT;AAGA,QAAMjD,UAAUrK,kBAAkBsN,YAAYP,QAAQ;AAGtDK,iBAAa,uBAAuBE,aAAa;MAC/CjD;MACAuD,aAAa1I;IACf,CAAC;AAGD,QACEoI,YAAYJ,cAAa,KACzB,CAACC,QAAQG,YAAYO,iBAAiB,MACrC,CAACV,QAAQG,YAAY9J,OAAO,KAC3B,CAAC2J,QAAQG,YAAY9J,QAAQqK,iBAAiB,MAChDF,WAAW,WAAWL,YAAYrB,SAAS,KAC3C0B,WAAW,WAAWL,YAAYN,WAAW,GAC7C;AACAvC,mBAAa6C,WAAW;AACxB,aAAO;IACT;AAGA,QACEjD,YAAY,YACZsD,WAAW,cAAcL,YAAYrB,SAAS,GAC9C;AACAxB,mBAAa6C,WAAW;AACxB,aAAO;IACT;AAGA,QAAIA,YAAYpL,aAAa,GAAG;AAC9BuI,mBAAa6C,WAAW;AACxB,aAAO;IACT;AAGA,QACE5G,gBACA4G,YAAYpL,aAAa,KACzByL,WAAW,WAAWL,YAAYC,IAAI,GACtC;AACA9C,mBAAa6C,WAAW;AACxB,aAAO;IACT;AAGA,QAAI,CAACpI,aAAamF,OAAO,KAAKlE,YAAYkE,OAAO,GAAG;AAElD,UAAI,CAAClE,YAAYkE,OAAO,KAAKyD,wBAAwBzD,OAAO,GAAG;AAC7D,YACE3E,wBAAwBG,wBAAwB+C,UAChD+E,WAAWjI,wBAAwBG,cAAcwE,OAAO,EAExD,QAAO;AACT,YACE3E,wBAAwBG,wBAAwBgD,YAChDnD,wBAAwBG,aAAawE,OAAO,EAE5C,QAAO;MACX;AAGA,UAAIjD,gBAAgB,CAACG,gBAAgB8C,OAAO,GAAG;AAC7C,YAAMO,aAAavH,cAAciK,WAAW,KAAKA,YAAY1C;AAC7D,YAAMyB,aAAajJ,cAAckK,WAAW,KAAKA,YAAYjB;AAE7D,YAAIA,cAAczB,YAAY;AAC5B,cAAMmD,aAAa1B,WAAWjN;AAE9B,mBAAS4O,IAAID,aAAa,GAAGC,KAAK,GAAG,EAAEA,GAAG;AACxC,gBAAMC,aAAa/K,UAAUmJ,WAAW2B,CAAC,GAAG,IAAI;AAChDC,uBAAWC,kBAAkBZ,YAAYY,kBAAkB,KAAK;AAChEtD,uBAAWuB,aAAa8B,YAAY9K,eAAemK,WAAW,CAAC;UACjE;QACF;MACF;AAEA7C,mBAAa6C,WAAW;AACxB,aAAO;IACT;AAGA,QAAIA,uBAAuB9K,WAAW,CAAC2H,qBAAqBmD,WAAW,GAAG;AACxE7C,mBAAa6C,WAAW;AACxB,aAAO;IACT;AAGA,SACGjD,YAAY,cACXA,YAAY,aACZA,YAAY,eACdsD,WAAW,+BAA+BL,YAAYrB,SAAS,GAC/D;AACAxB,mBAAa6C,WAAW;AACxB,aAAO;IACT;AAGA,QAAI7G,sBAAsB6G,YAAYpL,aAAa,GAAG;AAEpDsB,gBAAU8J,YAAYN;AACtBxJ,gBAAU2K,cAAc3K,SAASgB,iBAAe,GAAG;AACnDhB,gBAAU2K,cAAc3K,SAASkB,YAAU,GAAG;AAC9ClB,gBAAU2K,cAAc3K,SAASmB,eAAa,GAAG;AACjD,UAAI2I,YAAYN,gBAAgBxJ,SAAS;AACvCmH,kBAAU/I,UAAUI,SAAS;UAAE3B,SAASiN,YAAYpK,UAAS;QAAG,CAAC;AACjEoK,oBAAYN,cAAcxJ;MAC5B;IACF;AAGA4J,iBAAa,yBAAyBE,aAAa,IAAI;AAEvD,WAAO;;AAYT,MAAMc,oBAAoB,SAApBA,mBAA8BC,OAAOC,QAAQnN,OAAO;AAExD,QACE8F,iBACCqH,WAAW,QAAQA,WAAW,YAC9BnN,SAASc,YAAYd,SAASsH,cAC/B;AACA,aAAO;IACT;AAMA,QACEnC,mBACA,CAACF,YAAYkI,MAAM,KACnBX,WAAW/I,aAAW0J,MAAM,EAC5B;aAESjI,mBAAmBsH,WAAW9I,aAAWyJ,MAAM,EAAG;aAGlD,CAAC/I,aAAa+I,MAAM,KAAKlI,YAAYkI,MAAM,GAAG;AACvD;;;;QAIGR,wBAAwBO,KAAK,MAC1B3I,wBAAwBG,wBAAwB+C,UAChD+E,WAAWjI,wBAAwBG,cAAcwI,KAAK,KACrD3I,wBAAwBG,wBAAwBgD,YAC/CnD,wBAAwBG,aAAawI,KAAK,OAC5C3I,wBAAwBO,8BAA8B2C,UACtD+E,WAAWjI,wBAAwBO,oBAAoBqI,MAAM,KAC5D5I,wBAAwBO,8BAA8B4C,YACrDnD,wBAAwBO,mBAAmBqI,MAAM;;QAGtDA,WAAW,QACV5I,wBAAwBQ,mCACtBR,wBAAwBG,wBAAwB+C,UAChD+E,WAAWjI,wBAAwBG,cAAc1E,KAAK,KACrDuE,wBAAwBG,wBAAwBgD,YAC/CnD,wBAAwBG,aAAa1E,KAAK;OAChD;WAGK;AACL,eAAO;MACT;IAEF,WAAWwG,oBAAoB2G,MAAM,EAAG;aAKtCX,WAAW1I,kBAAgBkJ,cAAchN,OAAO4D,mBAAiB,EAAE,CAAC,EACpE;cAKCuJ,WAAW,SAASA,WAAW,gBAAgBA,WAAW,WAC3DD,UAAU,YACVE,cAAcpN,OAAO,OAAO,MAAM,KAClCsG,cAAc4G,KAAK,EACnB;aAMA9H,2BACA,CAACoH,WAAW7I,qBAAmBqJ,cAAchN,OAAO4D,mBAAiB,EAAE,CAAC,EACxE;aAGS5D,OAAO;AAChB,aAAO;IACT,MAAO;AAKP,WAAO;;AAST,MAAM2M,0BAA0B,SAA1BA,yBAAoCzD,SAAS;AACjD,WAAOA,YAAY,oBAAoBuB,YAAYvB,SAASrF,gBAAc;;AAa5E,MAAMwJ,sBAAsB,SAAtBA,qBAAgClB,aAAa;AACjD,QAAImB;AACJ,QAAItN;AACJ,QAAImN;AACJ,QAAIlO;AAEJgN,iBAAa,4BAA4BE,aAAa,IAAI;AAE1D,QAAQL,aAAeK,YAAfL;AAGR,QAAI,CAACA,cAAcJ,aAAaS,WAAW,GAAG;AAC5C;IACF;AAEA,QAAMoB,YAAY;MAChBC,UAAU;MACVC,WAAW;MACXC,UAAU;MACVC,mBAAmBvJ;;AAErBnF,QAAI6M,WAAW7N;AAGf,WAAOgB,KAAK;AACVqO,aAAOxB,WAAW7M,CAAC;AACnB,UAAA2O,QAA+BN,MAAvBxD,OAAI8D,MAAJ9D,MAAMX,eAAYyE,MAAZzE;AACdnJ,cAAQ8J,SAAS,UAAUwD,KAAKtN,QAAQ6N,WAAWP,KAAKtN,KAAK;AAC7DmN,eAAStO,kBAAkBiL,IAAI;AAG/ByD,gBAAUC,WAAWL;AACrBI,gBAAUE,YAAYzN;AACtBuN,gBAAUG,WAAW;AACrBH,gBAAUO,gBAAgBvN;AAC1B0L,mBAAa,yBAAyBE,aAAaoB,SAAS;AAC5DvN,cAAQuN,UAAUE;AAGlB,UAAIF,UAAUO,eAAe;AAC3B;MACF;AAGAjE,uBAAiBC,MAAMqC,WAAW;AAGlC,UAAI,CAACoB,UAAUG,UAAU;AACvB;MACF;AAGA,UAAI,CAACrI,4BAA4BmH,WAAW,QAAQxM,KAAK,GAAG;AAC1D6J,yBAAiBC,MAAMqC,WAAW;AAClC;MACF;AAGA,UAAI7G,oBAAoB;AACtBtF,gBAAQgN,cAAchN,OAAOqD,iBAAe,GAAG;AAC/CrD,gBAAQgN,cAAchN,OAAOuD,YAAU,GAAG;AAC1CvD,gBAAQgN,cAAchN,OAAOwD,eAAa,GAAG;MAC/C;AAGA,UAAM0J,QAAQrO,kBAAkBsN,YAAYP,QAAQ;AACpD,UAAI,CAACqB,kBAAkBC,OAAOC,QAAQnN,KAAK,GAAG;AAC5C;MACF;AAKA,UAAI+F,yBAAyBoH,WAAW,QAAQA,WAAW,SAAS;AAElEtD,yBAAiBC,MAAMqC,WAAW;AAGlCnM,gBAAQgG,8BAA8BhG;MACxC;AAGA,UAAIuF,gBAAgBiH,WAAW,iCAAiCxM,KAAK,GAAG;AACtE6J,yBAAiBC,MAAMqC,WAAW;AAClC;MACF;AAGA,UACE5J,sBACAsF,QAAOjG,YAAY,MAAK,YACxB,OAAOA,aAAamM,qBAAqB,YACzC;AACA,YAAI5E,aAAc;aAEX;AACL,kBAAQvH,aAAamM,iBAAiBb,OAAOC,MAAM,GAAC;YAClD,KAAK,eAAe;AAClBnN,sBAAQuC,mBAAmBG,WAAW1C,KAAK;AAC3C;YACF;YAEA,KAAK,oBAAoB;AACvBA,sBAAQuC,mBAAmByL,gBAAgBhO,KAAK;AAChD;YACF;UAKF;QACF;MACF;AAGA,UAAI;AACF,YAAImJ,cAAc;AAChBgD,sBAAY8B,eAAe9E,cAAcW,MAAM9J,KAAK;QACtD,OAAO;AAELmM,sBAAYhC,aAAaL,MAAM9J,KAAK;QACtC;AAEA,YAAI0L,aAAaS,WAAW,GAAG;AAC7B7C,uBAAa6C,WAAW;QAC1B,OAAO;AACL+B,mBAASzN,UAAUI,OAAO;QAC5B;MACF,SAASqC,GAAG;MAAA;IACd;AAGA+I,iBAAa,2BAA2BE,aAAa,IAAI;;AAQ3D,MAAMgC,qBAAqB,SAArBA,oBAA+BC,UAAU;AAC7C,QAAIC;AACJ,QAAMC,iBAAiBlD,gBAAgBgD,QAAQ;AAG/CnC,iBAAa,2BAA2BmC,UAAU,IAAI;AAEtD,WAAQC,aAAaC,eAAeC,SAAQ,GAAK;AAE/CtC,mBAAa,0BAA0BoC,YAAY,IAAI;AAEvD9B,wBAAkB8B,UAAU;AAG5BhB,0BAAoBgB,UAAU;AAG9B,UAAIA,WAAWhM,mBAAmBnB,kBAAkB;AAClDiN,QAAAA,oBAAmBE,WAAWhM,OAAO;MACvC;IACF;AAGA4J,iBAAa,0BAA0BmC,UAAU,IAAI;;AAWvD3N,YAAU+N,WAAW,SAAUnE,OAAiB;AAAA,QAAVzC,MAAG5J,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAuC,SAAAvC,UAAA,CAAA,IAAG,CAAA;AAC1C,QAAI+M;AACJ,QAAI0D;AACJ,QAAItC;AACJ,QAAIuC;AACJ,QAAIC;AAIJ7H,qBAAiB,CAACuD;AAClB,QAAIvD,gBAAgB;AAClBuD,cAAQ;IACV;AAGA,QAAI,OAAOA,UAAU,YAAY,CAAC2B,QAAQ3B,KAAK,GAAG;AAChD,UAAI,OAAOA,MAAMuE,aAAa,YAAY;AACxCvE,gBAAQA,MAAMuE,SAAQ;AACtB,YAAI,OAAOvE,UAAU,UAAU;AAC7B,gBAAMwE,gBAAgB,iCAAiC;QACzD;MACF,OAAO;AACL,cAAMA,gBAAgB,4BAA4B;MACpD;IACF;AAGA,QAAI,CAACpO,UAAUO,aAAa;AAC1B,UACE6G,QAAOvH,QAAOwO,YAAY,MAAK,YAC/B,OAAOxO,QAAOwO,iBAAiB,YAC/B;AACA,YAAI,OAAOzE,UAAU,UAAU;AAC7B,iBAAO/J,QAAOwO,aAAazE,KAAK;QAClC;AAEA,YAAI2B,QAAQ3B,KAAK,GAAG;AAClB,iBAAO/J,QAAOwO,aAAazE,MAAMV,SAAS;QAC5C;MACF;AAEA,aAAOU;IACT;AAGA,QAAI,CAAC5E,YAAY;AACfkC,mBAAaC,GAAG;IAClB;AAGAnH,cAAUI,UAAU,CAAA;AAGpB,QAAI,OAAOwJ,UAAU,UAAU;AAC7BnE,iBAAW;IACb;AAEA,QAAIA,UAAU;AAEZ,UAAImE,MAAMuB,UAAU;AAClB,YAAM1C,UAAUrK,kBAAkBwL,MAAMuB,QAAQ;AAChD,YAAI,CAAC7H,aAAamF,OAAO,KAAKlE,YAAYkE,OAAO,GAAG;AAClD,gBAAM2F,gBACJ,yDACF;QACF;MACF;IACF,WAAWxE,iBAAiBjJ,MAAM;AAGhC2J,aAAOX,cAAc,SAAS;AAC9BqE,qBAAe1D,KAAKzI,cAAcU,WAAWqH,OAAO,IAAI;AACxD,UAAIoE,aAAa1N,aAAa,KAAK0N,aAAa7C,aAAa,QAAQ;AAEnEb,eAAO0D;MACT,WAAWA,aAAa7C,aAAa,QAAQ;AAC3Cb,eAAO0D;MACT,OAAO;AAEL1D,aAAKgE,YAAYN,YAAY;MAC/B;IACF,OAAO;AAEL,UACE,CAAC9I,cACD,CAACL,sBACD,CAACE;MAED6E,MAAMvC,QAAQ,GAAG,MAAM,IACvB;AACA,eAAOvF,sBAAsBsD,sBACzBtD,mBAAmBG,WAAW2H,KAAK,IACnCA;MACN;AAGAU,aAAOX,cAAcC,KAAK;AAG1B,UAAI,CAACU,MAAM;AACT,eAAOpF,aAAa,OAAOE,sBAAsBpD,YAAY;MAC/D;IACF;AAGA,QAAIsI,QAAQrF,YAAY;AACtB4D,mBAAayB,KAAKiE,UAAU;IAC9B;AAGA,QAAMC,eAAe7D,gBAAgBlF,WAAWmE,QAAQU,IAAI;AAG5D,WAAQoB,cAAc8C,aAAaV,SAAQ,GAAK;AAE9C,UAAIpC,YAAYpL,aAAa,KAAKoL,gBAAgBuC,SAAS;AACzD;MACF;AAGAnC,wBAAkBJ,WAAW;AAG7BkB,0BAAoBlB,WAAW;AAG/B,UAAIA,YAAY9J,mBAAmBnB,kBAAkB;AACnDiN,2BAAmBhC,YAAY9J,OAAO;MACxC;AAEAqM,gBAAUvC;IACZ;AAEAuC,cAAU;AAGV,QAAIxI,UAAU;AACZ,aAAOmE;IACT;AAGA,QAAI1E,YAAY;AACd,UAAIC,qBAAqB;AACvB+I,qBAAa7L,uBAAuBqI,KAAKJ,KAAKzI,aAAa;AAE3D,eAAOyI,KAAKiE,YAAY;AAEtBL,qBAAWI,YAAYhE,KAAKiE,UAAU;QACxC;MACF,OAAO;AACLL,qBAAa5D;MACf;AAEA,UAAI3G,aAAa8K,cAAc9K,aAAa+K,eAAe;AAQzDR,qBAAa3L,WAAWmI,KAAKlK,kBAAkB0N,YAAY,IAAI;MACjE;AAEA,aAAOA;IACT;AAEA,QAAIS,iBAAiB5J,iBAAiBuF,KAAKpB,YAAYoB,KAAKD;AAG5D,QACEtF,kBACAzB,aAAa,UAAU,KACvBgH,KAAKzI,iBACLyI,KAAKzI,cAAc+M,WACnBtE,KAAKzI,cAAc+M,QAAQvF,QAC3B0C,WAAWlJ,cAA0ByH,KAAKzI,cAAc+M,QAAQvF,IAAI,GACpE;AACAsF,uBACE,eAAerE,KAAKzI,cAAc+M,QAAQvF,OAAO,QAAQsF;IAC7D;AAGA,QAAI9J,oBAAoB;AACtB8J,uBAAiBpC,cAAcoC,gBAAgB/L,iBAAe,GAAG;AACjE+L,uBAAiBpC,cAAcoC,gBAAgB7L,YAAU,GAAG;AAC5D6L,uBAAiBpC,cAAcoC,gBAAgB5L,eAAa,GAAG;IACjE;AAEA,WAAOjB,sBAAsBsD,sBACzBtD,mBAAmBG,WAAW0M,cAAc,IAC5CA;;AASN3O,YAAU6O,YAAY,SAAU1H,KAAK;AACnCD,iBAAaC,GAAG;AAChBnC,iBAAa;;AAQfhF,YAAU8O,cAAc,WAAY;AAClClI,aAAS;AACT5B,iBAAa;;AAafhF,YAAU+O,mBAAmB,SAAUC,KAAKnC,MAAMtN,OAAO;AAEvD,QAAI,CAACqH,QAAQ;AACXM,mBAAa,CAAA,CAAE;IACjB;AAEA,QAAMuF,QAAQrO,kBAAkB4Q,GAAG;AACnC,QAAMtC,SAAStO,kBAAkByO,IAAI;AACrC,WAAOL,kBAAkBC,OAAOC,QAAQnN,KAAK;;AAU/CS,YAAUiP,UAAU,SAAUxD,YAAYyD,cAAc;AACtD,QAAI,OAAOA,iBAAiB,YAAY;AACtC;IACF;AAEAxM,UAAM+I,UAAU,IAAI/I,MAAM+I,UAAU,KAAK,CAAA;AACzC1C,cAAUrG,MAAM+I,UAAU,GAAGyD,YAAY;;AAW3ClP,YAAUmP,aAAa,SAAU1D,YAAY;AAC3C,QAAI/I,MAAM+I,UAAU,GAAG;AACrB,aAAOgC,SAAS/K,MAAM+I,UAAU,CAAC;IACnC;;AASFzL,YAAUoP,cAAc,SAAU3D,YAAY;AAC5C,QAAI/I,MAAM+I,UAAU,GAAG;AACrB/I,YAAM+I,UAAU,IAAI,CAAA;IACtB;;AAQFzL,YAAUqP,iBAAiB,WAAY;AACrC3M,YAAQ,CAAA;;AAGV,SAAO1C;AACT;IJlpDEf,gBACAV,gBACAI,UACAa,gBACAH,0BAGI4I,QAAQjE,MAAMjF,QACpBuQ,MAAM1R,OAAOI,WA0BP4N,cAEA6B,UACA1E,WAGAzK,mBACAkI,gBACAwD,aACAuC,eACAI,eACAS,YAEArB,YAEAqC,iBChDO3G,QAyHAC,OA8CAC,YA+BA4H,eA0BA3H,UAkCA4H,kBAkBAC,MCpRAhI,MAgHAC,KAyLAE,QAwDA8H,KChWA9M,eACAE,UACAC,aACAC,WACAC,WACAI,gBAGAH,mBACAC,iBAGAwM,cACAvM,gBCKPrD,WAUAgC,2BAsnDN;;;AJrpDA,IACE9C,iBAKE8E,OALF9E;AADF,IAEEV,iBAIEwF,OAJFxF;AAFF,IAGEI,WAGEoF,OAHFpF;AAHF,IAIEa,iBAEEuE,OAFFvE;AAJF,IAKEH,2BACE0E,OADF1E;AAGF,IAAM4I,SAAyBlE,OAAzBkE;AAAN,IAAcjE,OAAiBD,OAAjBC;AAAd,IAAoBjF,SAAWgF,OAAXhF;AACpB,IAAAuQ,OAA2B,OAAOM,YAAY,eAAeA;AAA7D,IAAMhS,QAAK0R,KAAL1R;AAAN,IAAaI,YAASsR,KAATtR;AAEb,QAAI,CAACJ,OAAO;AACVA,cAAQ,SAAAA,OAAUiS,KAAKC,WAAWrS,MAAM;AACtC,eAAOoS,IAAIjS,MAAMkS,WAAWrS,IAAI;;IAEpC;AAEA,QAAI,CAACwK,QAAQ;AACXA,eAAS,SAAAA,QAAU8H,GAAG;AACpB,eAAOA;;IAEX;AAEA,QAAI,CAAC/L,MAAM;AACTA,aAAO,SAAAA,MAAU+L,GAAG;AAClB,eAAOA;;IAEX;AAEA,QAAI,CAAC/R,WAAW;AACdA,kBAAY,SAAAA,WAAUgS,MAAMvS,MAAM;AAChC,eAAAwS,WAAWD,MAAIvM,mBAAIhG,IAAI,CAAA;;IAE3B;AAEA,IAAMmO,eAAezO,QAAQO,MAAM2D,UAAU6O,OAAO;AAEpD,IAAMzC,WAAWtQ,QAAQO,MAAM2D,UAAU8O,GAAG;AAC5C,IAAMpH,YAAY5L,QAAQO,MAAM2D,UAAU+O,IAAI;AAG9C,IAAM9R,oBAAoBnB,QAAQkT,OAAOhP,UAAUiP,WAAW;AAC9D,IAAM9J,iBAAiBrJ,QAAQkT,OAAOhP,UAAU8M,QAAQ;AACxD,IAAMnE,cAAc7M,QAAQkT,OAAOhP,UAAUkP,KAAK;AAClD,IAAMhE,gBAAgBpP,QAAQkT,OAAOhP,UAAUmP,OAAO;AACtD,IAAM7D,gBAAgBxP,QAAQkT,OAAOhP,UAAUgG,OAAO;AACtD,IAAM+F,aAAajQ,QAAQkT,OAAOhP,UAAUoP,IAAI;AAEhD,IAAM1E,aAAa5O,QAAQ6J,OAAO3F,UAAUqP,IAAI;AAEhD,IAAMtC,kBAAkBvQ,YAAY8S,SAAS;AChDtC,IAAMlJ,SAAOQ,OAAO,CACzB,KACA,QACA,WACA,WACA,QACA,WACA,SACA,SACA,KACA,OACA,OACA,OACA,SACA,cACA,QACA,MACA,UACA,UACA,WACA,UACA,QACA,QACA,OACA,YACA,WACA,QACA,YACA,MACA,aACA,OACA,WACA,OACA,UACA,OACA,OACA,MACA,MACA,WACA,MACA,YACA,cACA,UACA,QACA,UACA,QACA,MACA,MACA,MACA,MACA,MACA,MACA,QACA,UACA,UACA,MACA,QACA,KACA,OACA,SACA,OACA,OACA,SACA,UACA,MACA,QACA,OACA,QACA,WACA,QACA,YACA,SACA,OACA,QACA,MACA,YACA,UACA,UACA,KACA,WACA,OACA,YACA,KACA,MACA,MACA,QACA,KACA,QACA,WACA,UACA,UACA,SACA,UACA,UACA,QACA,UACA,UACA,SACA,OACA,WACA,OACA,SACA,SACA,MACA,YACA,YACA,SACA,MACA,SACA,QACA,MACA,SACA,MACA,KACA,MACA,OACA,SACA,KAAK,CACN;AAGM,IAAMP,QAAMO,OAAO,CACxB,OACA,KACA,YACA,eACA,gBACA,gBACA,iBACA,oBACA,UACA,YACA,QACA,QACA,WACA,UACA,QACA,KACA,SACA,YACA,SACA,SACA,QACA,kBACA,UACA,QACA,YACA,SACA,QACA,WACA,WACA,YACA,kBACA,QACA,QACA,SACA,UACA,UACA,QACA,YACA,SACA,QACA,SACA,QACA,OAAO,CACR;AAEM,IAAMN,aAAaM,OAAO,CAC/B,WACA,iBACA,uBACA,eACA,oBACA,qBACA,qBACA,kBACA,WACA,WACA,WACA,WACA,WACA,kBACA,WACA,WACA,eACA,gBACA,YACA,gBACA,sBACA,eACA,UACA,cAAc,CACf;AAMM,IAAMsH,gBAAgBtH,OAAO,CAClC,WACA,iBACA,UACA,WACA,gBACA,aACA,oBACA,kBACA,iBACA,iBACA,iBACA,SACA,aACA,QACA,gBACA,aACA,WACA,iBACA,UACA,OACA,cACA,WACA,KAAK,CACN;AAEM,IAAML,WAASK,OAAO,CAC3B,QACA,YACA,UACA,WACA,SACA,UACA,MACA,cACA,iBACA,MACA,MACA,SACA,WACA,YACA,SACA,QACA,MACA,UACA,SACA,UACA,QACA,QACA,WACA,UACA,OACA,SACA,OACA,UACA,YAAY,CACb;AAIM,IAAMuH,mBAAmBvH,OAAO,CACrC,WACA,eACA,cACA,YACA,aACA,WACA,WACA,UACA,UACA,SACA,aACA,cACA,kBACA,eACA,MAAM,CACP;AAEM,IAAMwH,OAAOxH,OAAO,CAAC,OAAO,CAAC;ACpR7B,IAAMR,OAAOQ,OAAO,CACzB,UACA,UACA,SACA,OACA,kBACA,gBACA,wBACA,YACA,cACA,WACA,UACA,WACA,eACA,eACA,WACA,QACA,SACA,SACA,SACA,QACA,WACA,YACA,gBACA,UACA,eACA,YACA,YACA,WACA,OACA,YACA,2BACA,yBACA,YACA,aACA,WACA,gBACA,QACA,OACA,WACA,UACA,UACA,QACA,QACA,YACA,MACA,aACA,aACA,SACA,QACA,SACA,QACA,QACA,WACA,QACA,OACA,OACA,aACA,SACA,UACA,OACA,aACA,YACA,SACA,QACA,SACA,WACA,cACA,UACA,QACA,WACA,WACA,eACA,eACA,UACA,WACA,WACA,cACA,YACA,OACA,YACA,OACA,YACA,QACA,QACA,WACA,cACA,SACA,YACA,SACA,QACA,SACA,QACA,WACA,SACA,OACA,UACA,QACA,SACA,WACA,YACA,SACA,aACA,QACA,UACA,UACA,SACA,SACA,SACA,MAAM,CACP;AAEM,IAAMP,MAAMO,OAAO,CACxB,iBACA,cACA,YACA,sBACA,UACA,iBACA,iBACA,WACA,iBACA,kBACA,SACA,QACA,MACA,SACA,QACA,iBACA,aACA,aACA,SACA,uBACA,+BACA,iBACA,mBACA,MACA,MACA,KACA,MACA,MACA,mBACA,aACA,WACA,WACA,OACA,YACA,aACA,OACA,QACA,gBACA,aACA,UACA,eACA,eACA,iBACA,eACA,aACA,oBACA,gBACA,cACA,gBACA,eACA,MACA,MACA,MACA,MACA,cACA,YACA,iBACA,qBACA,UACA,QACA,MACA,mBACA,MACA,OACA,KACA,MACA,MACA,MACA,MACA,WACA,aACA,cACA,YACA,QACA,gBACA,kBACA,gBACA,oBACA,kBACA,SACA,cACA,cACA,gBACA,gBACA,eACA,eACA,oBACA,aACA,OACA,QACA,SACA,UACA,QACA,OACA,QACA,cACA,UACA,YACA,WACA,SACA,UACA,eACA,UACA,YACA,eACA,QACA,cACA,uBACA,oBACA,gBACA,UACA,iBACA,uBACA,kBACA,KACA,MACA,MACA,UACA,QACA,QACA,eACA,aACA,WACA,UACA,UACA,SACA,QACA,mBACA,oBACA,oBACA,gBACA,eACA,gBACA,eACA,cACA,gBACA,oBACA,qBACA,kBACA,mBACA,qBACA,kBACA,UACA,gBACA,SACA,gBACA,kBACA,YACA,WACA,WACA,aACA,oBACA,eACA,mBACA,kBACA,cACA,QACA,MACA,MACA,WACA,UACA,WACA,cACA,WACA,cACA,iBACA,iBACA,SACA,gBACA,QACA,gBACA,oBACA,oBACA,KACA,MACA,MACA,SACA,KACA,MACA,MACA,KACA,YAAY,CACb;AAEM,IAAML,SAASK,OAAO,CAC3B,UACA,eACA,SACA,YACA,SACA,gBACA,eACA,cACA,cACA,SACA,OACA,WACA,gBACA,YACA,SACA,SACA,UACA,QACA,MACA,WACA,UACA,iBACA,UACA,UACA,kBACA,aACA,YACA,eACA,WACA,WACA,iBACA,YACA,YACA,QACA,YACA,YACA,cACA,WACA,UACA,UACA,eACA,iBACA,wBACA,aACA,aACA,cACA,YACA,kBACA,kBACA,aACA,WACA,SACA,OAAO,CACR;AAEM,IAAMyH,MAAMzH,OAAO,CACxB,cACA,UACA,eACA,aACA,aAAa,CACd;ACtWM,IAAMrF,gBAAgBoB,KAAK,2BAA2B;AACtD,IAAMlB,WAAWkB,KAAK,uBAAuB;AAC7C,IAAMjB,cAAciB,KAAK,eAAe;AACxC,IAAMhB,YAAYgB,KAAK,8BAA8B;AACrD,IAAMf,YAAYe,KAAK,gBAAgB;AACvC,IAAMX,iBAAiBW;MAC5B;;IACF;AACO,IAAMd,oBAAoBc,KAAK,uBAAuB;AACtD,IAAMb,kBAAkBa;MAC7B;;IACF;AACO,IAAM2L,eAAe3L,KAAK,SAAS;AACnC,IAAMZ,iBAAiBY,KAAK,0BAA0B;ACK7D,IAAMjE,YAAY,SAAZA,aAAS;AAAA,aAAU,OAAOF,WAAW,cAAc,OAAOA;IAAM;AAUtE,IAAMkC,4BAA4B,SAA5BA,2BAAsCZ,cAAcd,UAAU;AAClE,UACE+G,QAAOjG,YAAY,MAAK,YACxB,OAAOA,aAAayP,iBAAiB,YACrC;AACA,eAAO;MACT;AAKA,UAAIC,SAAS;AACb,UAAMC,YAAY;AAClB,UACEzQ,SAAS0Q,iBACT1Q,SAAS0Q,cAAcC,aAAaF,SAAS,GAC7C;AACAD,iBAASxQ,SAAS0Q,cAAcE,aAAaH,SAAS;MACxD;AAEA,UAAMI,aAAa,eAAeL,SAAS,MAAMA,SAAS;AAE1D,UAAI;AACF,eAAO1P,aAAayP,aAAaM,YAAY;UAC3CjP,YAAU,SAAAA,WAACwF,OAAM;AACf,mBAAOA;;UAET8F,iBAAe,SAAAA,gBAAC4D,WAAW;AACzB,mBAAOA;UACT;QACF,CAAC;eACM1O,GAAG;AAIV/C,gBAAQC,KACN,yBAAyBuR,aAAa,wBACxC;AACA,eAAO;MACT;IACF;AA8kDA,IAAA,SAAetR,gBAAe;;;", "names": ["unapply", "func", "thisArg", "_len", "arguments", "length", "args", "Array", "_key", "apply", "unconstruct", "_len2", "_key2", "construct", "addToSet", "set", "array", "transformCaseFunc", "_transformCaseFunc", "stringToLowerCase", "setPrototypeOf", "l", "element", "lcElement", "isFrozen", "clone", "object", "newObject", "create", "property", "hasOwnProperty", "lookupGetter", "prop", "desc", "getOwnPropertyDescriptor", "get", "value", "getPrototypeOf", "fallback<PERSON><PERSON><PERSON>", "console", "warn", "createDOMPurify", "window", "undefined", "getGlobal", "DOMPurify", "root", "version", "VERSION", "removed", "document", "nodeType", "isSupported", "originalDocument", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "_window$NamedNodeMap", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trustedTypes", "ElementPrototype", "prototype", "cloneNode", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "_createTrustedTypesPolicy", "emptyHTML", "createHTML", "_document", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "documentMode", "_", "hooks", "createHTMLDocument", "MUSTACHE_EXPR", "EXPRESSIONS", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "CUSTOM_ELEMENT", "IS_ALLOWED_URI", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "concat", "_toConsumableArray", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "CUSTOM_ELEMENT_HANDLING", "Object", "seal", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "stringToString", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "RegExp", "Function", "_parseConfig", "cfg", "_typeof", "indexOf", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "html", "svg", "svgFilters", "mathMl", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "freeze", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "arrayPush", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "outerHTML", "remove", "_removeAttribute", "name", "attribute", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "stringMatch", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "arrayForEach", "hook", "_sanitizeElements", "regExpTest", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_basicCustomElementTest", "childCount", "i", "child<PERSON>lone", "__removalCount", "stringReplace", "_isValidAttribute", "lcTag", "lcName", "stringIndexOf", "_sanitizeAttributes", "attr", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "_attr", "stringTrim", "forceKeepAttr", "getAttributeType", "createScriptURL", "setAttributeNS", "arrayPop", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "oldNode", "returnNode", "toString", "typeErrorCreate", "toStaticHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmod", "serializedHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks", "_ref", "svgDisallowed", "mathMlDisallowed", "text", "xml", "DOCTYPE_NAME", "Reflect", "fun", "thisValue", "x", "Func", "_construct", "for<PERSON>ach", "pop", "push", "String", "toLowerCase", "match", "replace", "trim", "test", "TypeError", "createPolicy", "suffix", "ATTR_NAME", "currentScript", "hasAttribute", "getAttribute", "policyName", "scriptUrl"]}