
import { Box, Container, Typography, useTheme, useMediaQuery } from '@mui/material';
import CourierLayout from '../../components/layout/CourierLayout';
import CanceledOrdersReport from '../../components/reports/CanceledOrdersReport';
const CanceledOrdersPage = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <CourierLayout>
      <Container maxWidth="lg" sx={{ py: 4, px: isMobile ? 2 : 4 }}>
        <Typography variant="h4" fontWeight="bold" color="primary" gutterBottom>                  
          My Canceled Orders
        </Typography>
        <Box sx={{ mt: 3 }}>
          <CanceledOrdersReport userRole="courier" />
        </Box>
      </Container>
    </CourierLayout>
  );
};

export default CanceledOrdersPage;
