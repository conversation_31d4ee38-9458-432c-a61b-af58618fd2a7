{"version": 3, "sources": ["../../date-fns/esm/add/index.js", "../../date-fns/esm/isWeekend/index.js", "../../date-fns/esm/isSunday/index.js", "../../date-fns/esm/isSaturday/index.js", "../../date-fns/esm/addBusinessDays/index.js", "../../date-fns/esm/startOfISOWeek/index.js", "../../date-fns/esm/getISOWeekYear/index.js", "../../date-fns/esm/startOfISOWeekYear/index.js", "../../date-fns/esm/setISOWeekYear/index.js", "../../date-fns/esm/addISOWeekYears/index.js", "../../date-fns/esm/addQuarters/index.js", "../../date-fns/esm/areIntervalsOverlapping/index.js", "../../date-fns/esm/max/index.js", "../../date-fns/esm/min/index.js", "../../date-fns/esm/clamp/index.js", "../../date-fns/esm/closestIndexTo/index.js", "../../date-fns/esm/closestTo/index.js", "../../date-fns/esm/compareDesc/index.js", "../../date-fns/esm/daysToWeeks/index.js", "../../date-fns/esm/differenceInBusinessDays/index.js", "../../date-fns/esm/differenceInCalendarISOWeekYears/index.js", "../../date-fns/esm/differenceInCalendarISOWeeks/index.js", "../../date-fns/esm/getQuarter/index.js", "../../date-fns/esm/differenceInCalendarQuarters/index.js", "../../date-fns/esm/differenceInCalendarWeeks/index.js", "../../date-fns/esm/subISOWeekYears/index.js", "../../date-fns/esm/differenceInISOWeekYears/index.js", "../../date-fns/esm/eachHourOfInterval/index.js", "../../date-fns/esm/startOfMinute/index.js", "../../date-fns/esm/eachMinuteOfInterval/index.js", "../../date-fns/esm/eachMonthOfInterval/index.js", "../../date-fns/esm/startOfQuarter/index.js", "../../date-fns/esm/eachQuarterOfInterval/index.js", "../../date-fns/esm/eachWeekOfInterval/index.js", "../../date-fns/esm/eachWeekendOfInterval/index.js", "../../date-fns/esm/eachWeekendOfMonth/index.js", "../../date-fns/esm/eachWeekendOfYear/index.js", "../../date-fns/esm/eachYearOfInterval/index.js", "../../date-fns/esm/endOfDecade/index.js", "../../date-fns/esm/endOfHour/index.js", "../../date-fns/esm/endOfISOWeek/index.js", "../../date-fns/esm/endOfISOWeekYear/index.js", "../../date-fns/esm/endOfMinute/index.js", "../../date-fns/esm/endOfQuarter/index.js", "../../date-fns/esm/endOfSecond/index.js", "../../date-fns/esm/endOfToday/index.js", "../../date-fns/esm/endOfTomorrow/index.js", "../../date-fns/esm/endOfYesterday/index.js", "../../date-fns/esm/_lib/cloneObject/index.js", "../../date-fns/esm/formatDistance/index.js", "../../date-fns/esm/formatDistanceStrict/index.js", "../../date-fns/esm/formatDistanceToNow/index.js", "../../date-fns/esm/formatDistanceToNowStrict/index.js", "../../date-fns/esm/formatDuration/index.js", "../../date-fns/esm/formatISO9075/index.js", "../../date-fns/esm/formatISODuration/index.js", "../../date-fns/esm/formatRFC3339/index.js", "../../date-fns/esm/formatRFC7231/index.js", "../../date-fns/esm/formatRelative/index.js", "../../date-fns/esm/fromUnixTime/index.js", "../../date-fns/esm/getDayOfYear/index.js", "../../date-fns/esm/isLeapYear/index.js", "../../date-fns/esm/getDaysInYear/index.js", "../../date-fns/esm/getDecade/index.js", "../../date-fns/esm/getDefaultOptions/index.js", "../../date-fns/esm/getISODay/index.js", "../../date-fns/esm/getISOWeek/index.js", "../../date-fns/esm/getISOWeeksInYear/index.js", "../../date-fns/esm/getMilliseconds/index.js", "../../date-fns/esm/getOverlappingDaysInIntervals/index.js", "../../date-fns/esm/getTime/index.js", "../../date-fns/esm/getUnixTime/index.js", "../../date-fns/esm/getWeekYear/index.js", "../../date-fns/esm/startOfWeekYear/index.js", "../../date-fns/esm/getWeek/index.js", "../../date-fns/esm/getWeekOfMonth/index.js", "../../date-fns/esm/lastDayOfMonth/index.js", "../../date-fns/esm/getWeeksInMonth/index.js", "../../date-fns/esm/hoursToMilliseconds/index.js", "../../date-fns/esm/hoursToMinutes/index.js", "../../date-fns/esm/hoursToSeconds/index.js", "../../date-fns/esm/intervalToDuration/index.js", "../../date-fns/esm/intlFormat/index.js", "../../date-fns/esm/intlFormatDistance/index.js", "../../date-fns/esm/isExists/index.js", "../../date-fns/esm/isFirstDayOfMonth/index.js", "../../date-fns/esm/isFriday/index.js", "../../date-fns/esm/isFuture/index.js", "../../date-fns/esm/isMatch/index.js", "../../date-fns/esm/isMonday/index.js", "../../date-fns/esm/isPast/index.js", "../../date-fns/esm/isSameWeek/index.js", "../../date-fns/esm/isSameISOWeek/index.js", "../../date-fns/esm/isSameISOWeekYear/index.js", "../../date-fns/esm/isSameMinute/index.js", "../../date-fns/esm/isSameQuarter/index.js", "../../date-fns/esm/startOfSecond/index.js", "../../date-fns/esm/isSameSecond/index.js", "../../date-fns/esm/isThisHour/index.js", "../../date-fns/esm/isThisISOWeek/index.js", "../../date-fns/esm/isThisMinute/index.js", "../../date-fns/esm/isThisMonth/index.js", "../../date-fns/esm/isThisQuarter/index.js", "../../date-fns/esm/isThisSecond/index.js", "../../date-fns/esm/isThisWeek/index.js", "../../date-fns/esm/isThisYear/index.js", "../../date-fns/esm/isThursday/index.js", "../../date-fns/esm/isToday/index.js", "../../date-fns/esm/isTomorrow/index.js", "../../date-fns/esm/isTuesday/index.js", "../../date-fns/esm/isWednesday/index.js", "../../date-fns/esm/subDays/index.js", "../../date-fns/esm/isYesterday/index.js", "../../date-fns/esm/lastDayOfDecade/index.js", "../../date-fns/esm/lastDayOfWeek/index.js", "../../date-fns/esm/lastDayOfISOWeek/index.js", "../../date-fns/esm/lastDayOfISOWeekYear/index.js", "../../date-fns/esm/lastDayOfQuarter/index.js", "../../date-fns/esm/lastDayOfYear/index.js", "../../date-fns/esm/lightFormat/index.js", "../../date-fns/esm/milliseconds/index.js", "../../date-fns/esm/millisecondsToHours/index.js", "../../date-fns/esm/millisecondsToMinutes/index.js", "../../date-fns/esm/millisecondsToSeconds/index.js", "../../date-fns/esm/minutesToHours/index.js", "../../date-fns/esm/minutesToMilliseconds/index.js", "../../date-fns/esm/minutesToSeconds/index.js", "../../date-fns/esm/monthsToQuarters/index.js", "../../date-fns/esm/monthsToYears/index.js", "../../date-fns/esm/nextDay/index.js", "../../date-fns/esm/nextFriday/index.js", "../../date-fns/esm/nextMonday/index.js", "../../date-fns/esm/nextSaturday/index.js", "../../date-fns/esm/nextSunday/index.js", "../../date-fns/esm/nextThursday/index.js", "../../date-fns/esm/nextTuesday/index.js", "../../date-fns/esm/nextWednesday/index.js", "../../date-fns/esm/parseJSON/index.js", "../../date-fns/esm/previousDay/index.js", "../../date-fns/esm/previousFriday/index.js", "../../date-fns/esm/previousMonday/index.js", "../../date-fns/esm/previousSaturday/index.js", "../../date-fns/esm/previousSunday/index.js", "../../date-fns/esm/previousThursday/index.js", "../../date-fns/esm/previousTuesday/index.js", "../../date-fns/esm/previousWednesday/index.js", "../../date-fns/esm/quartersToMonths/index.js", "../../date-fns/esm/quartersToYears/index.js", "../../date-fns/esm/roundToNearestMinutes/index.js", "../../date-fns/esm/secondsToHours/index.js", "../../date-fns/esm/secondsToMilliseconds/index.js", "../../date-fns/esm/secondsToMinutes/index.js", "../../date-fns/esm/set/index.js", "../../date-fns/esm/setDay/index.js", "../../date-fns/esm/setDayOfYear/index.js", "../../date-fns/esm/setDefaultOptions/index.js", "../../date-fns/esm/setISODay/index.js", "../../date-fns/esm/setISOWeek/index.js", "../../date-fns/esm/setMilliseconds/index.js", "../../date-fns/esm/setQuarter/index.js", "../../date-fns/esm/setWeek/index.js", "../../date-fns/esm/setWeekYear/index.js", "../../date-fns/esm/startOfDecade/index.js", "../../date-fns/esm/startOfToday/index.js", "../../date-fns/esm/startOfTomorrow/index.js", "../../date-fns/esm/startOfYesterday/index.js", "../../date-fns/esm/subMonths/index.js", "../../date-fns/esm/sub/index.js", "../../date-fns/esm/subBusinessDays/index.js", "../../date-fns/esm/subHours/index.js", "../../date-fns/esm/subMinutes/index.js", "../../date-fns/esm/subQuarters/index.js", "../../date-fns/esm/subSeconds/index.js", "../../date-fns/esm/subWeeks/index.js", "../../date-fns/esm/subYears/index.js", "../../date-fns/esm/weeksToDays/index.js", "../../date-fns/esm/yearsToMonths/index.js", "../../date-fns/esm/yearsToQuarters/index.js"], "sourcesContent": ["function _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nimport addDays from \"../addDays/index.js\";\nimport addMonths from \"../addMonths/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n\n/**\n * @name add\n * @category Common Helpers\n * @summary Add the specified years, months, weeks, days, hours, minutes and seconds to the given date.\n *\n * @description\n * Add the specified years, months, weeks, days, hours, minutes and seconds to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Duration} duration - the object with years, months, weeks, days, hours, minutes and seconds to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n *\n * | Key            | Description                        |\n * |----------------|------------------------------------|\n * | years          | Amount of years to be added        |\n * | months         | Amount of months to be added       |\n * | weeks          | Amount of weeks to be added        |\n * | days           | Amount of days to be added         |\n * | hours          | Amount of hours to be added        |\n * | minutes        | Amount of minutes to be added      |\n * | seconds        | Amount of seconds to be added      |\n *\n * All values default to 0\n *\n * @returns {Date} the new date with the seconds added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add the following duration to 1 September 2014, 10:19:50\n * const result = add(new Date(2014, 8, 1, 10, 19, 50), {\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30,\n * })\n * //=> Thu Jun 15 2017 15:29:20\n */\nexport default function add(dirtyDate, duration) {\n  requiredArgs(2, arguments);\n  if (!duration || _typeof(duration) !== 'object') return new Date(NaN);\n  var years = duration.years ? toInteger(duration.years) : 0;\n  var months = duration.months ? toInteger(duration.months) : 0;\n  var weeks = duration.weeks ? toInteger(duration.weeks) : 0;\n  var days = duration.days ? toInteger(duration.days) : 0;\n  var hours = duration.hours ? toInteger(duration.hours) : 0;\n  var minutes = duration.minutes ? toInteger(duration.minutes) : 0;\n  var seconds = duration.seconds ? toInteger(duration.seconds) : 0; // Add years and months\n\n  var date = toDate(dirtyDate);\n  var dateWithMonths = months || years ? addMonths(date, months + years * 12) : date; // Add weeks and days\n\n  var dateWithDays = days || weeks ? addDays(dateWithMonths, days + weeks * 7) : dateWithMonths; // Add days, hours, minutes and seconds\n\n  var minutesToAdd = minutes + hours * 60;\n  var secondsToAdd = seconds + minutesToAdd * 60;\n  var msToAdd = secondsToAdd * 1000;\n  var finalDate = new Date(dateWithDays.getTime() + msToAdd);\n  return finalDate;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isWeekend\n * @category Weekday Helpers\n * @summary Does the given date fall on a weekend?\n *\n * @description\n * Does the given date fall on a weekend?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date falls on a weekend\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Does 5 October 2014 fall on a weekend?\n * const result = isWeekend(new Date(2014, 9, 5))\n * //=> true\n */\n\nexport default function isWeekend(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var day = date.getDay();\n  return day === 0 || day === 6;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSunday\n * @category Weekday Helpers\n * @summary Is the given date Sunday?\n *\n * @description\n * Is the given date Sunday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Sunday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 21 September 2014 Sunday?\n * const result = isSunday(new Date(2014, 8, 21))\n * //=> true\n */\n\nexport default function isSunday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 0;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSaturday\n * @category Weekday Helpers\n * @summary Is the given date Saturday?\n *\n * @description\n * Is the given date Saturday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Saturday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 27 September 2014 Saturday?\n * const result = isSaturday(new Date(2014, 8, 27))\n * //=> true\n */\n\nexport default function isSaturday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 6;\n}", "import isWeekend from \"../isWeekend/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport isSunday from \"../isSunday/index.js\";\nimport isSaturday from \"../isSaturday/index.js\";\n/**\n * @name addBusinessDays\n * @category Day Helpers\n * @summary Add the specified number of business days (mon - fri) to the given date.\n *\n * @description\n * Add the specified number of business days (mon - fri) to the given date, ignoring weekends.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of business days to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the business days added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add 10 business days to 1 September 2014:\n * const result = addBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Sep 15 2014 00:00:00 (skipped weekend days)\n */\n\nexport default function addBusinessDays(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var startedOnWeekend = isWeekend(date);\n  var amount = toInteger(dirtyAmount);\n  if (isNaN(amount)) return new Date(NaN);\n  var hours = date.getHours();\n  var sign = amount < 0 ? -1 : 1;\n  var fullWeeks = toInteger(amount / 5);\n  date.setDate(date.getDate() + fullWeeks * 7); // Get remaining days not part of a full week\n\n  var restDays = Math.abs(amount % 5); // Loops over remaining days\n\n  while (restDays > 0) {\n    date.setDate(date.getDate() + sign);\n    if (!isWeekend(date)) restDays -= 1;\n  } // If the date is a weekend day and we reduce a dividable of\n  // 5 from it, we land on a weekend date.\n  // To counter this, we add days accordingly to land on the next business day\n\n\n  if (startedOnWeekend && isWeekend(date) && amount !== 0) {\n    // If we're reducing days, we want to add days until we land on a weekday\n    // If we're adding days we want to reduce days until we land on a weekday\n    if (isSaturday(date)) date.setDate(date.getDate() + (sign < 0 ? 2 : -1));\n    if (isSunday(date)) date.setDate(date.getDate() + (sign < 0 ? 1 : -2));\n  } // Restore hours to avoid DST lag\n\n\n  date.setHours(hours);\n  return date;\n}", "import startOfWeek from \"../startOfWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name startOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the start of an ISO week for the given date.\n *\n * @description\n * Return the start of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the start of an ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The start of an ISO week for 2 September 2014 11:55:00:\n * const result = startOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Mon Sep 01 2014 00:00:00\n */\n\nexport default function startOfISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  return startOfWeek(dirtyDate, {\n    weekStartsOn: 1\n  });\n}", "import toDate from \"../toDate/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the ISO week-numbering year of the given date.\n *\n * @description\n * Get the ISO week-numbering year of the given date,\n * which always starts 3 days before the year's first Thursday.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the ISO week-numbering year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which ISO-week numbering year is 2 January 2005?\n * const result = getISOWeekYear(new Date(2005, 0, 2))\n * //=> 2004\n */\n\nexport default function getISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var fourthOfJanuaryOfNextYear = new Date(0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  var startOfNextYear = startOfISOWeek(fourthOfJanuaryOfNextYear);\n  var fourthOfJanuaryOfThisYear = new Date(0);\n  fourthOfJanuaryOfThisYear.setFullYear(year, 0, 4);\n  fourthOfJanuaryOfThisYear.setHours(0, 0, 0, 0);\n  var startOfThisYear = startOfISOWeek(fourthOfJanuaryOfThisYear);\n\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}", "import getISOWeekYear from \"../getISOWeekYear/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name startOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the start of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the start of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the start of an ISO week-numbering year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The start of an ISO week-numbering year for 2 July 2005:\n * const result = startOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Mon Jan 03 2005 00:00:00\n */\n\nexport default function startOfISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getISOWeekYear(dirtyDate);\n  var fourthOfJanuary = new Date(0);\n  fourthOfJanuary.setFullYear(year, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  var date = startOfISOWeek(fourthOfJanuary);\n  return date;\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport startOfISOWeekYear from \"../startOfISOWeekYear/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Set the ISO week-numbering year to the given date.\n *\n * @description\n * Set the ISO week-numbering year to the given date,\n * saving the week number and the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} isoWeekYear - the ISO week-numbering year of the new date\n * @returns {Date} the new date with the ISO week-numbering year set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set ISO week-numbering year 2007 to 29 December 2008:\n * const result = setISOWeekYear(new Date(2008, 11, 29), 2007)\n * //=> Mon Jan 01 2007 00:00:00\n */\n\nexport default function setISOWeekYear(dirtyDate, dirtyISOWeekYear) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var isoWeekYear = toInteger(dirtyISOWeekYear);\n  var diff = differenceInCalendarDays(date, startOfISOWeekYear(date));\n  var fourthOfJanuary = new Date(0);\n  fourthOfJanuary.setFullYear(isoWeekYear, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  date = startOfISOWeekYear(fourthOfJanuary);\n  date.setDate(date.getDate() + diff);\n  return date;\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport getISOWeekYear from \"../getISOWeekYear/index.js\";\nimport setISOWeekYear from \"../setISOWeekYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name addISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Add the specified number of ISO week-numbering years to the given date.\n *\n * @description\n * Add the specified number of ISO week-numbering years to the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of ISO week-numbering years to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the ISO week-numbering years added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add 5 ISO week-numbering years to 2 July 2010:\n * const result = addISOWeekYears(new Date(2010, 6, 2), 5)\n * //=> Fri Jun 26 2015 00:00:00\n */\n\nexport default function addISOWeekYears(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return setISOWeekYear(dirtyDate, getISOWeekYear(dirtyDate) + amount);\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport addMonths from \"../addMonths/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name addQuarters\n * @category Quarter Helpers\n * @summary Add the specified number of year quarters to the given date.\n *\n * @description\n * Add the specified number of year quarters to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of quarters to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the quarters added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add 1 quarter to 1 September 2014:\n * const result = addQuarters(new Date(2014, 8, 1), 1)\n * //=> Mon Dec 01 2014 00:00:00\n */\n\nexport default function addQuarters(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  var months = amount * 3;\n  return addMonths(dirtyDate, months);\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name areIntervalsOverlapping\n * @category Interval Helpers\n * @summary Is the given time interval overlapping with another time interval?\n *\n * @description\n * Is the given time interval overlapping with another time interval? Adjacent intervals do not count as overlapping.\n *\n * @param {Interval} intervalLeft - the first interval to compare. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Interval} intervalRight - the second interval to compare. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - the object with options\n * @param {Boolean} [options.inclusive=false] - whether the comparison is inclusive or not\n * @returns {<PERSON><PERSON><PERSON>} whether the time intervals are overlapping\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // For overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> true\n *\n * @example\n * // For non-overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> false\n *\n * @example\n * // For adjacent time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 30) }\n * )\n * //=> false\n *\n * @example\n * // Using the inclusive option:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) }\n * )\n * //=> false\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) },\n *   { inclusive: true }\n * )\n * //=> true\n */\n\nexport default function areIntervalsOverlapping(intervalLeft, intervalRight, options) {\n  requiredArgs(2, arguments);\n  var leftStartTime = toDate(intervalLeft === null || intervalLeft === void 0 ? void 0 : intervalLeft.start).getTime();\n  var leftEndTime = toDate(intervalLeft === null || intervalLeft === void 0 ? void 0 : intervalLeft.end).getTime();\n  var rightStartTime = toDate(intervalRight === null || intervalRight === void 0 ? void 0 : intervalRight.start).getTime();\n  var rightEndTime = toDate(intervalRight === null || intervalRight === void 0 ? void 0 : intervalRight.end).getTime(); // Throw an exception if start date is after end date or if any date is `Invalid Date`\n\n  if (!(leftStartTime <= leftEndTime && rightStartTime <= rightEndTime)) {\n    throw new RangeError('Invalid interval');\n  }\n\n  if (options !== null && options !== void 0 && options.inclusive) {\n    return leftStartTime <= rightEndTime && rightStartTime <= leftEndTime;\n  }\n\n  return leftStartTime < rightEndTime && rightStartTime < leftEndTime;\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name max\n * @category Common Helpers\n * @summary Return the latest of the given dates.\n *\n * @description\n * Return the latest of the given dates.\n *\n * @param {Date[]|Number[]} datesArray - the dates to compare\n * @returns {Date} the latest of the dates\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which of these dates is the latest?\n * const result = max([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Sun Jul 02 1995 00:00:00\n */\n\nexport default function max(dirtyDatesArray) {\n  requiredArgs(1, arguments);\n  var datesArray; // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n\n  if (dirtyDatesArray && typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray; // If `dirtyDatesArray` is Array-like Object, convert to Array.\n  } else if (_typeof(dirtyDatesArray) === 'object' && dirtyDatesArray !== null) {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  } else {\n    // `dirtyDatesArray` is non-iterable, return Invalid Date\n    return new Date(NaN);\n  }\n\n  var result;\n  datesArray.forEach(function (dirtyDate) {\n    var currentDate = toDate(dirtyDate);\n\n    if (result === undefined || result < currentDate || isNaN(Number(currentDate))) {\n      result = currentDate;\n    }\n  });\n  return result || new Date(NaN);\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name min\n * @category Common Helpers\n * @summary Returns the earliest of the given dates.\n *\n * @description\n * Returns the earliest of the given dates.\n *\n * @param {Date[]|Number[]} datesArray - the dates to compare\n * @returns {Date} - the earliest of the dates\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which of these dates is the earliest?\n * const result = min([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Wed Feb 11 1987 00:00:00\n */\n\nexport default function min(dirtyDatesArray) {\n  requiredArgs(1, arguments);\n  var datesArray; // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n\n  if (dirtyDatesArray && typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray; // If `dirtyDatesArray` is Array-like Object, convert to Array.\n  } else if (_typeof(dirtyDatesArray) === 'object' && dirtyDatesArray !== null) {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  } else {\n    // `dirtyDatesArray` is non-iterable, return Invalid Date\n    return new Date(NaN);\n  }\n\n  var result;\n  datesArray.forEach(function (dirtyDate) {\n    var currentDate = toDate(dirtyDate);\n\n    if (result === undefined || result > currentDate || isNaN(currentDate.getDate())) {\n      result = currentDate;\n    }\n  });\n  return result || new Date(NaN);\n}", "import max from \"../max/index.js\";\nimport min from \"../min/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name clamp\n * @category Interval Helpers\n * @summary Return a date bounded by the start and the end of the given interval\n *\n * @description\n * Clamps a date to the lower bound with the start of the interval and the upper\n * bound with the end of the interval.\n *\n * - When the date is less than the start of the interval, the start is returned.\n * - When the date is greater than the end of the interval, the end is returned.\n * - Otherwise the date is returned.\n *\n * @example\n * // What is Mar, 21, 2021 bounded to an interval starting at Mar, 22, 2021 and ending at Apr, 01, 2021\n * const result = clamp(new Date(2021, 2, 21), {\n *   start: new Date(2021, 2, 22),\n *   end: new Date(2021, 3, 1),\n * })\n * //=> Mon Mar 22 2021 00:00:00\n *\n * @param {Date | Number} date - the date to be bounded\n * @param {Interval} interval - the interval to bound to\n * @returns {Date} the date bounded by the start and the end of the interval\n * @throws {TypeError} 2 arguments required\n */\n\nexport default function clamp(date, _ref) {\n  var start = _ref.start,\n      end = _ref.end;\n  requiredArgs(2, arguments);\n  return min([max([date, start]), end]);\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name closestIndexTo\n * @category Common Helpers\n * @summary Return an index of the closest date from the array comparing to the given date.\n *\n * @description\n * Return an index of the closest date from the array comparing to the given date.\n *\n * @param {Date | Number} dateToCompare - the date to compare with\n * @param {Array<Date> | Array<number>} datesArray - the array to search\n * @returns {Number | undefined} an index of the date closest to the given date or undefined if no valid value is given\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Which date is closer to 6 September 2015?\n * const dateToCompare = new Date(2015, 8, 6)\n * const datesArray = [\n *   new Date(2015, 0, 1),\n *   new Date(2016, 0, 1),\n *   new Date(2017, 0, 1)\n * ]\n * const result = closestIndexTo(dateToCompare, datesArray)\n * //=> 1\n */\n\nexport default function closestIndexTo(dirtyDateToCompare, dirtyDatesArray) {\n  requiredArgs(2, arguments);\n  var dateToCompare = toDate(dirtyDateToCompare);\n  if (isNaN(Number(dateToCompare))) return NaN;\n  var timeToCompare = dateToCompare.getTime();\n  var datesArray; // `dirtyDatesArray` is undefined or null\n\n  if (dirtyDatesArray == null) {\n    datesArray = []; // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  } else if (typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray; // If `dirtyDatesArray` is Array-like Object, convert to Array. Otherwise, make it empty Array\n  } else {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  }\n\n  var result;\n  var minDistance;\n  datesArray.forEach(function (dirtyDate, index) {\n    var currentDate = toDate(dirtyDate);\n\n    if (isNaN(Number(currentDate))) {\n      result = NaN;\n      minDistance = NaN;\n      return;\n    }\n\n    var distance = Math.abs(timeToCompare - currentDate.getTime());\n\n    if (result == null || distance < Number(minDistance)) {\n      result = index;\n      minDistance = distance;\n    }\n  });\n  return result;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name closestTo\n * @category Common Helpers\n * @summary Return a date from the array closest to the given date.\n *\n * @description\n * Return a date from the array closest to the given date.\n *\n * @param {Date | Number} dateToCompare - the date to compare with\n * @param {Array<Date> | Array<number>} datesArray - the array to search\n * @returns {Date | undefined} the date from the array closest to the given date or undefined if no valid value is given\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Which date is closer to 6 September 2015: 1 January 2000 or 1 January 2030?\n * const dateToCompare = new Date(2015, 8, 6)\n * const result = closestTo(dateToCompare, [\n *   new Date(2000, 0, 1),\n *   new Date(2030, 0, 1)\n * ])\n * //=> Tue Jan 01 2030 00:00:00\n */\n\nexport default function closestTo(dirtyDateToCompare, dirtyDatesArray) {\n  requiredArgs(2, arguments);\n  var dateToCompare = toDate(dirtyDateToCompare);\n  if (isNaN(Number(dateToCompare))) return new Date(NaN);\n  var timeToCompare = dateToCompare.getTime();\n  var datesArray; // `dirtyDatesArray` is undefined or null\n\n  if (dirtyDatesArray == null) {\n    datesArray = []; // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  } else if (typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray; // If `dirtyDatesArray` is Array-like Object, convert to Array. Otherwise, make it empty Array\n  } else {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  }\n\n  var result;\n  var minDistance;\n  datesArray.forEach(function (dirtyDate) {\n    var currentDate = toDate(dirtyDate);\n\n    if (isNaN(Number(currentDate))) {\n      result = new Date(NaN);\n      minDistance = NaN;\n      return;\n    }\n\n    var distance = Math.abs(timeToCompare - currentDate.getTime());\n\n    if (result == null || distance < Number(minDistance)) {\n      result = currentDate;\n      minDistance = distance;\n    }\n  });\n  return result;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name compareDesc\n * @category Common Helpers\n * @summary Compare the two dates reverse chronologically and return -1, 0 or 1.\n *\n * @description\n * Compare the two dates and return -1 if the first date is after the second,\n * 1 if the first date is before the second or 0 if dates are equal.\n *\n * @param {Date|Number} dateLeft - the first date to compare\n * @param {Date|Number} dateRight - the second date to compare\n * @returns {Number} the result of the comparison\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Compare 11 February 1987 and 10 July 1989 reverse chronologically:\n * const result = compareDesc(new Date(1987, 1, 11), new Date(1989, 6, 10))\n * //=> 1\n *\n * @example\n * // Sort the array of dates in reverse chronological order:\n * const result = [\n *   new Date(1995, 6, 2),\n *   new Date(1987, 1, 11),\n *   new Date(1989, 6, 10)\n * ].sort(compareDesc)\n * //=> [\n * //   Sun Jul 02 1995 00:00:00,\n * //   Mon Jul 10 1989 00:00:00,\n * //   Wed Feb 11 1987 00:00:00\n * // ]\n */\n\nexport default function compareDesc(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var diff = dateLeft.getTime() - dateRight.getTime();\n\n  if (diff > 0) {\n    return -1;\n  } else if (diff < 0) {\n    return 1; // Return 0 if diff is 0; return NaN if diff is NaN\n  } else {\n    return diff;\n  }\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { daysInWeek } from \"../constants/index.js\";\n/**\n * @name daysToWeeks\n * @category Conversion Helpers\n * @summary Convert days to weeks.\n *\n * @description\n * Convert a number of days to a full number of weeks.\n *\n * @param {number} days - number of days to be converted\n *\n * @returns {number} the number of days converted in weeks\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 14 days to weeks:\n * const result = daysToWeeks(14)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = daysToWeeks(13)\n * //=> 1\n */\n\nexport default function daysToWeeks(days) {\n  requiredArgs(1, arguments);\n  var weeks = days / daysInWeek;\n  return Math.floor(weeks);\n}", "import addDays from \"../addDays/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport isSameDay from \"../isSameDay/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport isWeekend from \"../isWeekend/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name differenceInBusinessDays\n * @category Day Helpers\n * @summary Get the number of business days between the given dates.\n *\n * @description\n * Get the number of business day periods between the given dates.\n * Business days being days that arent in the weekend.\n * Like `differenceInCalendarDays`, the function removes the times from\n * the dates before calculating the difference.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of business days\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many business days are between\n * // 10 January 2014 and 20 July 2014?\n * const result = differenceInBusinessDays(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 0, 10)\n * )\n * //=> 136\n *\n * // How many business days are between\n * // 30 November 2021 and 1 November 2021?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 30),\n *   new Date(2021, 10, 1)\n * )\n * //=> 21\n *\n * // How many business days are between\n * // 1 November 2021 and 1 December 2021?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 1),\n *   new Date(2021, 11, 1)\n * )\n * //=> -22\n *\n * // How many business days are between\n * // 1 November 2021 and 1 November 2021 ?\n * const result = differenceInBusinessDays(\n *   new Date(2021, 10, 1),\n *   new Date(2021, 10, 1)\n * )\n * //=> 0\n */\n\nexport default function differenceInBusinessDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  if (!isValid(dateLeft) || !isValid(dateRight)) return NaN;\n  var calendarDifference = differenceInCalendarDays(dateLeft, dateRight);\n  var sign = calendarDifference < 0 ? -1 : 1;\n  var weeks = toInteger(calendarDifference / 7);\n  var result = weeks * 5;\n  dateRight = addDays(dateRight, weeks * 7); // the loop below will run at most 6 times to account for the remaining days that don't makeup a full week\n\n  while (!isSameDay(dateLeft, dateRight)) {\n    // sign is used to account for both negative and positive differences\n    result += isWeekend(dateRight) ? 0 : sign;\n    dateRight = addDays(dateRight, sign);\n  }\n\n  return result === 0 ? 0 : result;\n}", "import getISOWeekYear from \"../getISOWeekYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInCalendarISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of calendar ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of calendar ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar ISO week-numbering years\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar ISO week-numbering years are 1 January 2010 and 1 January 2012?\n * const result = differenceInCalendarISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * //=> 2\n */\n\nexport default function differenceInCalendarISOWeekYears(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  return getISOWeekYear(dirtyDateLeft) - getISOWeekYear(dirtyDateRight);\n}", "import getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\n/**\n * @name differenceInCalendarISOWeeks\n * @category ISO Week Helpers\n * @summary Get the number of calendar ISO weeks between the given dates.\n *\n * @description\n * Get the number of calendar ISO weeks between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar ISO weeks\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar ISO weeks are between 6 July 2014 and 21 July 2014?\n * const result = differenceInCalendarISOWeeks(\n *   new Date(2014, 6, 21),\n *   new Date(2014, 6, 6)\n * )\n * //=> 3\n */\n\nexport default function differenceInCalendarISOWeeks(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var startOfISOWeekLeft = startOfISOWeek(dirtyDateLeft);\n  var startOfISOWeekRight = startOfISOWeek(dirtyDateRight);\n  var timestampLeft = startOfISOWeekLeft.getTime() - getTimezoneOffsetInMilliseconds(startOfISOWeekLeft);\n  var timestampRight = startOfISOWeekRight.getTime() - getTimezoneOffsetInMilliseconds(startOfISOWeekRight); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round((timestampLeft - timestampRight) / MILLISECONDS_IN_WEEK);\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getQuarter\n * @category Quarter Helpers\n * @summary Get the year quarter of the given date.\n *\n * @description\n * Get the year quarter of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the quarter\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which quarter is 2 July 2014?\n * const result = getQuarter(new Date(2014, 6, 2))\n * //=> 3\n */\n\nexport default function getQuarter(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var quarter = Math.floor(date.getMonth() / 3) + 1;\n  return quarter;\n}", "import getQuarter from \"../getQuarter/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInCalendarQuarters\n * @category Quarter Helpers\n * @summary Get the number of calendar quarters between the given dates.\n *\n * @description\n * Get the number of calendar quarters between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar quarters\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInCalendarQuarters(\n *   new Date(2014, 6, 2),\n *   new Date(2013, 11, 31)\n * )\n * //=> 3\n */\n\nexport default function differenceInCalendarQuarters(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var yearDiff = dateLeft.getFullYear() - dateRight.getFullYear();\n  var quarterDiff = getQuarter(dateLeft) - getQuarter(dateRight);\n  return yearDiff * 4 + quarterDiff;\n}", "import startOfWeek from \"../startOfWeek/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\n/**\n * @name differenceInCalendarWeeks\n * @category Week Helpers\n * @summary Get the number of calendar weeks between the given dates.\n *\n * @description\n * Get the number of calendar weeks between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Number} the number of calendar weeks\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n *\n * @example\n * // How many calendar weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInCalendarWeeks(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 6, 5)\n * )\n * //=> 3\n *\n * @example\n * // If the week starts on Monday,\n * // how many calendar weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInCalendarWeeks(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 6, 5),\n *   { weekStartsOn: 1 }\n * )\n * //=> 2\n */\n\nexport default function differenceInCalendarWeeks(dirtyDateLeft, dirtyDateRight, options) {\n  requiredArgs(2, arguments);\n  var startOfWeekLeft = startOfWeek(dirtyDateLeft, options);\n  var startOfWeekRight = startOfWeek(dirtyDateRight, options);\n  var timestampLeft = startOfWeekLeft.getTime() - getTimezoneOffsetInMilliseconds(startOfWeekLeft);\n  var timestampRight = startOfWeekRight.getTime() - getTimezoneOffsetInMilliseconds(startOfWeekRight); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round((timestampLeft - timestampRight) / MILLISECONDS_IN_WEEK);\n}", "import addISOWeekYears from \"../addISOWeekYears/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name subISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Subtract the specified number of ISO week-numbering years from the given date.\n *\n * @description\n * Subtract the specified number of ISO week-numbering years from the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of ISO week-numbering years to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the ISO week-numbering years subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 5 ISO week-numbering years from 1 September 2014:\n * const result = subISOWeekYears(new Date(2014, 8, 1), 5)\n * //=> Mon Aug 31 2009 00:00:00\n */\n\nexport default function subISOWeekYears(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addISOWeekYears(dirtyDate, -amount);\n}", "import toDate from \"../toDate/index.js\";\nimport differenceInCalendarISOWeekYears from \"../differenceInCalendarISOWeekYears/index.js\";\nimport compareAsc from \"../compareAsc/index.js\";\nimport subISOWeekYears from \"../subISOWeekYears/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of full ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of full ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full ISO week-numbering years\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full ISO week-numbering years are between 1 January 2010 and 1 January 2012?\n * const result = differenceInISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * //=> 1\n */\n\nexport default function differenceInISOWeekYears(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarISOWeekYears(dateLeft, dateRight));\n  dateLeft = subISOWeekYears(dateLeft, sign * difference); // Math.abs(diff in full ISO years - diff in calendar ISO years) === 1\n  // if last calendar ISO year is not full\n  // If so, result must be decreased by 1 in absolute value\n\n  var isLastISOWeekYearNotFull = Number(compareAsc(dateLeft, dateRight) === -sign);\n  var result = sign * (difference - isLastISOWeekYearNotFull); // Prevent negative zero\n\n  return result === 0 ? 0 : result;\n}", "import addHours from \"../addHours/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name eachHourOfInterval\n * @category Interval Helpers\n * @summary Return the array of hours within the specified time interval.\n *\n * @description\n * Return the array of hours within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.step=1] - the step to increment by. The value should be more than 1.\n * @returns {Date[]} the array with starts of hours from the hour of the interval start to the hour of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.step` must be a number greater than 1\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each hour between 6 October 2014, 12:00 and 6 October 2014, 15:00\n * const result = eachHourOfInterval({\n *   start: new Date(2014, 9, 6, 12),\n *   end: new Date(2014, 9, 6, 15)\n * })\n * //=> [\n * //   Mon Oct 06 2014 12:00:00,\n * //   Mon Oct 06 2014 13:00:00,\n * //   Mon Oct 06 2014 14:00:00,\n * //   Mon Oct 06 2014 15:00:00\n * // ]\n */\nexport default function eachHourOfInterval(dirtyInterval, options) {\n  var _options$step;\n\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var startTime = startDate.getTime();\n  var endTime = endDate.getTime(); // Throw an exception if start date is after end date or if any date is `Invalid Date`\n\n  if (!(startTime <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n\n  var dates = [];\n  var currentDate = startDate;\n  currentDate.setMinutes(0, 0, 0);\n  var step = Number((_options$step = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step !== void 0 ? _options$step : 1);\n  if (step < 1 || isNaN(step)) throw new RangeError('`options.step` must be a number greater than 1');\n\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate = addHours(currentDate, step);\n  }\n\n  return dates;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name startOfMinute\n * @category Minute Helpers\n * @summary Return the start of a minute for the given date.\n *\n * @description\n * Return the start of a minute for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the start of a minute\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The start of a minute for 1 December 2014 22:15:45.400:\n * const result = startOfMinute(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:00\n */\n\nexport default function startOfMinute(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  date.setSeconds(0, 0);\n  return date;\n}", "import addMinutes from \"../addMinutes/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport startOfMinute from \"../startOfMinute/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name eachMinuteOfInterval\n * @category Interval Helpers\n * @summary Return the array of minutes within the specified time interval.\n *\n * @description\n * Returns the array of minutes within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.step=1] - the step to increment by. The step must be equal to or greater than 1\n * @throws {TypeError} 1 argument required\n * @returns {Date[]} the array with starts of minutes from the minute of the interval start to the minute of the interval end\n * @throws {RangeError} `options.step` must be a number equal to or greater than 1\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each minute between 14 October 2020, 13:00 and 14 October 2020, 13:03\n * const result = eachMinuteOfInterval({\n *   start: new Date(2014, 9, 14, 13),\n *   end: new Date(2014, 9, 14, 13, 3)\n * })\n * //=> [\n * //   Wed Oct 14 2014 13:00:00,\n * //   Wed Oct 14 2014 13:01:00,\n * //   Wed Oct 14 2014 13:02:00,\n * //   Wed Oct 14 2014 13:03:00\n * // ]\n */\nexport default function eachMinuteOfInterval(interval, options) {\n  var _options$step;\n\n  requiredArgs(1, arguments);\n  var startDate = startOfMinute(toDate(interval.start));\n  var endDate = toDate(interval.end);\n  var startTime = startDate.getTime();\n  var endTime = endDate.getTime();\n\n  if (startTime >= endTime) {\n    throw new RangeError('Invalid interval');\n  }\n\n  var dates = [];\n  var currentDate = startDate;\n  var step = Number((_options$step = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step !== void 0 ? _options$step : 1);\n  if (step < 1 || isNaN(step)) throw new RangeError('`options.step` must be a number equal to or greater than 1');\n\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate = addMinutes(currentDate, step);\n  }\n\n  return dates;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachMonthOfInterval\n * @category Interval Helpers\n * @summary Return the array of months within the specified time interval.\n *\n * @description\n * Return the array of months within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} the array with starts of months from the month of the interval start to the month of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each month between 6 February 2014 and 10 August 2014:\n * const result = eachMonthOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Sat Feb 01 2014 00:00:00,\n * //   Sat Mar 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Thu May 01 2014 00:00:00,\n * //   Sun Jun 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * //   Fri Aug 01 2014 00:00:00\n * // ]\n */\n\nexport default function eachMonthOfInterval(dirtyInterval) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime();\n  var dates = []; // Throw an exception if start date is after end date or if any date is `Invalid Date`\n\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n\n  var currentDate = startDate;\n  currentDate.setHours(0, 0, 0, 0);\n  currentDate.setDate(1);\n\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate.setMonth(currentDate.getMonth() + 1);\n  }\n\n  return dates;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name startOfQuarter\n * @category Quarter Helpers\n * @summary Return the start of a year quarter for the given date.\n *\n * @description\n * Return the start of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the start of a quarter\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The start of a quarter for 2 September 2014 11:55:00:\n * const result = startOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Jul 01 2014 00:00:00\n */\n\nexport default function startOfQuarter(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var currentMonth = date.getMonth();\n  var month = currentMonth - currentMonth % 3;\n  date.setMonth(month, 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}", "import addQuarters from \"../addQuarters/index.js\";\nimport startOfQuarter from \"../startOfQuarter/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachQuarterOfInterval\n * @category Interval Helpers\n * @summary Return the array of quarters within the specified time interval.\n *\n * @description\n * Return the array of quarters within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} the array with starts of quarters from the quarter of the interval start to the quarter of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each quarter within interval 6 February 2014 - 10 August 2014:\n * const result = eachQuarterOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * // ]\n */\n\nexport default function eachQuarterOfInterval(dirtyInterval) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime(); // Throw an exception if start date is after end date or if any date is `Invalid Date`\n\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n\n  var startDateQuarter = startOfQuarter(startDate);\n  var endDateQuarter = startOfQuarter(endDate);\n  endTime = endDateQuarter.getTime();\n  var quarters = [];\n  var currentQuarter = startDateQuarter;\n\n  while (currentQuarter.getTime() <= endTime) {\n    quarters.push(toDate(currentQuarter));\n    currentQuarter = addQuarters(currentQuarter, 1);\n  }\n\n  return quarters;\n}", "import addWeeks from \"../addWeeks/index.js\";\nimport startOfWeek from \"../startOfWeek/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekOfInterval\n * @category Interval Helpers\n * @summary Return the array of weeks within the specified time interval.\n *\n * @description\n * Return the array of weeks within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Date[]} the array with starts of weeks from the week of the interval start to the week of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be 0, 1, ..., 6\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each week within interval 6 October 2014 - 23 November 2014:\n * const result = eachWeekOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 10, 23)\n * })\n * //=> [\n * //   Sun Oct 05 2014 00:00:00,\n * //   Sun Oct 12 2014 00:00:00,\n * //   Sun Oct 19 2014 00:00:00,\n * //   Sun Oct 26 2014 00:00:00,\n * //   Sun Nov 02 2014 00:00:00,\n * //   Sun Nov 09 2014 00:00:00,\n * //   Sun Nov 16 2014 00:00:00,\n * //   Sun Nov 23 2014 00:00:00\n * // ]\n */\n\nexport default function eachWeekOfInterval(dirtyInterval, options) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime(); // Throw an exception if start date is after end date or if any date is `Invalid Date`\n\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n\n  var startDateWeek = startOfWeek(startDate, options);\n  var endDateWeek = startOfWeek(endDate, options); // Some timezones switch DST at midnight, making start of day unreliable in these timezones, 3pm is a safe bet\n\n  startDateWeek.setHours(15);\n  endDateWeek.setHours(15);\n  endTime = endDateWeek.getTime();\n  var weeks = [];\n  var currentWeek = startDateWeek;\n\n  while (currentWeek.getTime() <= endTime) {\n    currentWeek.setHours(0);\n    weeks.push(toDate(currentWeek));\n    currentWeek = addWeeks(currentWeek, 1);\n    currentWeek.setHours(15);\n  }\n\n  return weeks;\n}", "import eachDayOfInterval from \"../eachDayOfInterval/index.js\";\nimport isSunday from \"../isSunday/index.js\";\nimport isWeekend from \"../isWeekend/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekendOfInterval\n * @category Interval Helpers\n * @summary List all the Saturdays and Sundays in the given date interval.\n *\n * @description\n * Get all the Saturdays and Sundays in the given date interval.\n *\n * @param {Interval} interval - the given interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} an array containing all the Saturdays and Sundays\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Lists all Saturdays and Sundays in the given date interval\n * const result = eachWeekendOfInterval({\n *   start: new Date(2018, 8, 17),\n *   end: new Date(2018, 8, 30)\n * })\n * //=> [\n * //   Sat Sep 22 2018 00:00:00,\n * //   Sun Sep 23 2018 00:00:00,\n * //   Sat Sep 29 2018 00:00:00,\n * //   Sun Sep 30 2018 00:00:00\n * // ]\n */\n\nexport default function eachWeekendOfInterval(interval) {\n  requiredArgs(1, arguments);\n  var dateInterval = eachDayOfInterval(interval);\n  var weekends = [];\n  var index = 0;\n\n  while (index < dateInterval.length) {\n    var date = dateInterval[index++];\n\n    if (isWeekend(date)) {\n      weekends.push(date);\n      if (isSunday(date)) index = index + 5;\n    }\n  }\n\n  return weekends;\n}", "import eachWeekendOfInterval from \"../eachWeekendOfInterval/index.js\";\nimport startOfMonth from \"../startOfMonth/index.js\";\nimport endOfMonth from \"../endOfMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekendOfMonth\n * @category Month Helpers\n * @summary List all the Saturdays and Sundays in the given month.\n *\n * @description\n * Get all the Saturdays and Sundays in the given month.\n *\n * @param {Date|Number} date - the given month\n * @returns {Date[]} an array containing all the Saturdays and Sundays\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The passed date is invalid\n *\n * @example\n * // Lists all Saturdays and Sundays in the given month\n * const result = eachWeekendOfMonth(new Date(2022, 1, 1))\n * //=> [\n * //   Sat Feb 05 2022 00:00:00,\n * //   Sun Feb 06 2022 00:00:00,\n * //   Sat Feb 12 2022 00:00:00,\n * //   Sun Feb 13 2022 00:00:00,\n * //   Sat Feb 19 2022 00:00:00,\n * //   Sun Feb 20 2022 00:00:00,\n * //   Sat Feb 26 2022 00:00:00,\n * //   Sun Feb 27 2022 00:00:00\n * // ]\n */\n\nexport default function eachWeekendOfMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  var startDate = startOfMonth(dirtyDate);\n  if (isNaN(startDate.getTime())) throw new RangeError('The passed date is invalid');\n  var endDate = endOfMonth(dirtyDate);\n  return eachWeekendOfInterval({\n    start: startDate,\n    end: endDate\n  });\n}", "import eachWeekendOfInterval from \"../eachWeekendOfInterval/index.js\";\nimport endOfYear from \"../endOfYear/index.js\";\nimport startOfYear from \"../startOfYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekendOfYear\n * @category Year Helpers\n * @summary List all the Saturdays and Sundays in the year.\n *\n * @description\n * Get all the Saturdays and Sundays in the year.\n *\n * @param {Date|Number} date - the given year\n * @returns {Date[]} an array containing all the Saturdays and Sundays\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The passed date is invalid\n *\n * @example\n * // Lists all Saturdays and Sundays in the year\n * const result = eachWeekendOfYear(new Date(2020, 1, 1))\n * //=> [\n * //   Sat Jan 03 2020 00:00:00,\n * //   Sun Jan 04 2020 00:00:00,\n * //   ...\n * //   Sun Dec 27 2020 00:00:00\n * // ]\n * ]\n */\n\nexport default function eachWeekendOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var startDate = startOfYear(dirtyDate);\n  var endDate = endOfYear(dirtyDate);\n  return eachWeekendOfInterval({\n    start: startDate,\n    end: endDate\n  });\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachYearOfInterval\n * @category Interval Helpers\n * @summary Return the array of yearly timestamps within the specified time interval.\n *\n * @description\n * Return the array of yearly timestamps within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} the array with starts of yearly timestamps from the month of the interval start to the month of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each year between 6 February 2014 and 10 August 2017:\n * const result = eachYearOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2017, 7, 10)\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Thu Jan 01 2015 00:00:00,\n * //   Fri Jan 01 2016 00:00:00,\n * //   Sun Jan 01 2017 00:00:00\n * // ]\n */\n\nexport default function eachYearOfInterval(dirtyInterval) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime(); // Throw an exception if start date is after end date or if any date is `Invalid Date`\n\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n\n  var dates = [];\n  var currentDate = startDate;\n  currentDate.setHours(0, 0, 0, 0);\n  currentDate.setMonth(0, 1);\n\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate.setFullYear(currentDate.getFullYear() + 1);\n  }\n\n  return dates;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfDecade\n * @category Decade Helpers\n * @summary Return the end of a decade for the given date.\n *\n * @description\n * Return the end of a decade for the given date.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of a decade\n * @param {Object} [options] - an object with options.\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link https://date-fns.org/docs/toDate}\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.additionalDigits` must be 0, 1 or 2\n *\n * @example\n * // The end of a decade for 12 May 1984 00:00:00:\n * const result = endOfDecade(new Date(1984, 4, 12, 00, 00, 00))\n * //=> Dec 31 1989 23:59:59.999\n */\n\nexport default function endOfDecade(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var decade = 9 + Math.floor(year / 10) * 10;\n  date.setFullYear(decade, 11, 31);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfHour\n * @category Hour Helpers\n * @summary Return the end of an hour for the given date.\n *\n * @description\n * Return the end of an hour for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of an hour\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of an hour for 2 September 2014 11:55:00:\n * const result = endOfHour(new Date(2014, 8, 2, 11, 55))\n * //=> Tue Sep 02 2014 11:59:59.999\n */\n\nexport default function endOfHour(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  date.setMinutes(59, 59, 999);\n  return date;\n}", "import endOfWeek from \"../endOfWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the end of an ISO week for the given date.\n *\n * @description\n * Return the end of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of an ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of an ISO week for 2 September 2014 11:55:00:\n * const result = endOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Sep 07 2014 23:59:59.999\n */\n\nexport default function endOfISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  return endOfWeek(dirtyDate, {\n    weekStartsOn: 1\n  });\n}", "import getISOWeekYear from \"../getISOWeekYear/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the end of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the end of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of an ISO week-numbering year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of an ISO week-numbering year for 2 July 2005:\n * const result = endOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Sun Jan 01 2006 23:59:59.999\n */\n\nexport default function endOfISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getISOWeekYear(dirtyDate);\n  var fourthOfJanuaryOfNextYear = new Date(0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  var date = startOfISOWeek(fourthOfJanuaryOfNextYear);\n  date.setMilliseconds(date.getMilliseconds() - 1);\n  return date;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfMinute\n * @category Minute Helpers\n * @summary Return the end of a minute for the given date.\n *\n * @description\n * Return the end of a minute for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of a minute\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of a minute for 1 December 2014 22:15:45.400:\n * const result = endOfMinute(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:59.999\n */\n\nexport default function endOfMinute(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  date.setSeconds(59, 999);\n  return date;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfQuarter\n * @category Quarter Helpers\n * @summary Return the end of a year quarter for the given date.\n *\n * @description\n * Return the end of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of a quarter\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of a quarter for 2 September 2014 11:55:00:\n * const result = endOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 23:59:59.999\n */\n\nexport default function endOfQuarter(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var currentMonth = date.getMonth();\n  var month = currentMonth - currentMonth % 3 + 3;\n  date.setMonth(month, 0);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfSecond\n * @category Second Helpers\n * @summary Return the end of a second for the given date.\n *\n * @description\n * Return the end of a second for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of a second\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of a second for 1 December 2014 22:15:45.400:\n * const result = endOfSecond(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:45.999\n */\n\nexport default function endOfSecond(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  date.setMilliseconds(999);\n  return date;\n}", "import endOfDay from \"../endOfDay/index.js\";\n/**\n * @name endOfToday\n * @category Day Helpers\n * @summary Return the end of today.\n * @pure false\n *\n * @description\n * Return the end of today.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the end of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfToday()\n * //=> Mon Oct 6 2014 23:59:59.999\n */\n\nexport default function endOfToday() {\n  return endOfDay(Date.now());\n}", "/**\n * @name endOfTomorrow\n * @category Day Helpers\n * @summary Return the end of tomorrow.\n * @pure false\n *\n * @description\n * Return the end of tomorrow.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `new Date()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the end of tomorrow\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfTomorrow()\n * //=> Tue Oct 7 2014 23:59:59.999\n */\nexport default function endOfTomorrow() {\n  var now = new Date();\n  var year = now.getFullYear();\n  var month = now.getMonth();\n  var day = now.getDate();\n  var date = new Date(0);\n  date.setFullYear(year, month, day + 1);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}", "/**\n * @name endOfYesterday\n * @category Day Helpers\n * @summary Return the end of yesterday.\n * @pure false\n *\n * @description\n * Return the end of yesterday.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `new Date()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the end of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfYesterday()\n * //=> Sun Oct 5 2014 23:59:59.999\n */\nexport default function endOfYesterday() {\n  var now = new Date();\n  var year = now.getFullYear();\n  var month = now.getMonth();\n  var day = now.getDate();\n  var date = new Date(0);\n  date.setFullYear(year, month, day - 1);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}", "import assign from \"../assign/index.js\";\nexport default function cloneObject(object) {\n  return assign({}, object);\n}", "import { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport compareAsc from \"../compareAsc/index.js\";\nimport differenceInMonths from \"../differenceInMonths/index.js\";\nimport differenceInSeconds from \"../differenceInSeconds/index.js\";\nimport defaultLocale from \"../_lib/defaultLocale/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport cloneObject from \"../_lib/cloneObject/index.js\";\nimport assign from \"../_lib/assign/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MINUTES_IN_DAY = 1440;\nvar MINUTES_IN_ALMOST_TWO_DAYS = 2520;\nvar MINUTES_IN_MONTH = 43200;\nvar MINUTES_IN_TWO_MONTHS = 86400;\n/**\n * @name formatDistance\n * @category Common Helpers\n * @summary Return the distance between the given dates in words.\n *\n * @description\n * Return the distance between the given dates in words.\n *\n * | Distance between dates                                            | Result              |\n * |-------------------------------------------------------------------|---------------------|\n * | 0 ... 30 secs                                                     | less than a minute  |\n * | 30 secs ... 1 min 30 secs                                         | 1 minute            |\n * | 1 min 30 secs ... 44 mins 30 secs                                 | [2..44] minutes     |\n * | 44 mins ... 30 secs ... 89 mins 30 secs                           | about 1 hour        |\n * | 89 mins 30 secs ... 23 hrs 59 mins 30 secs                        | about [2..24] hours |\n * | 23 hrs 59 mins 30 secs ... 41 hrs 59 mins 30 secs                 | 1 day               |\n * | 41 hrs 59 mins 30 secs ... 29 days 23 hrs 59 mins 30 secs         | [2..30] days        |\n * | 29 days 23 hrs 59 mins 30 secs ... 44 days 23 hrs 59 mins 30 secs | about 1 month       |\n * | 44 days 23 hrs 59 mins 30 secs ... 59 days 23 hrs 59 mins 30 secs | about 2 months      |\n * | 59 days 23 hrs 59 mins 30 secs ... 1 yr                           | [2..12] months      |\n * | 1 yr ... 1 yr 3 months                                            | about 1 year        |\n * | 1 yr 3 months ... 1 yr 9 month s                                  | over 1 year         |\n * | 1 yr 9 months ... 2 yrs                                           | almost 2 years      |\n * | N yrs ... N yrs 3 months                                          | about N years       |\n * | N yrs 3 months ... N yrs 9 months                                 | over N years        |\n * | N yrs 9 months ... N+1 yrs                                        | almost N+1 years    |\n *\n * With `options.includeSeconds == true`:\n * | Distance between dates | Result               |\n * |------------------------|----------------------|\n * | 0 secs ... 5 secs      | less than 5 seconds  |\n * | 5 secs ... 10 secs     | less than 10 seconds |\n * | 10 secs ... 20 secs    | less than 20 seconds |\n * | 20 secs ... 40 secs    | half a minute        |\n * | 40 secs ... 60 secs    | less than a minute   |\n * | 60 secs ... 90 secs    | 1 minute             |\n *\n * @param {Date|Number} date - the date\n * @param {Date|Number} baseDate - the date to compare with\n * @param {Object} [options] - an object with options.\n * @param {Boolean} [options.includeSeconds=false] - distances less than a minute are more detailed\n * @param {Boolean} [options.addSuffix=false] - result indicates if the second date is earlier or later than the first\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @returns {String} the distance in words\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.locale` must contain `formatDistance` property\n *\n * @example\n * // What is the distance between 2 July 2014 and 1 January 2015?\n * const result = formatDistance(new Date(2014, 6, 2), new Date(2015, 0, 1))\n * //=> '6 months'\n *\n * @example\n * // What is the distance between 1 January 2015 00:00:15\n * // and 1 January 2015 00:00:00, including seconds?\n * const result = formatDistance(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   new Date(2015, 0, 1, 0, 0, 0),\n *   { includeSeconds: true }\n * )\n * //=> 'less than 20 seconds'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, with a suffix?\n * const result = formatDistance(new Date(2015, 0, 1), new Date(2016, 0, 1), {\n *   addSuffix: true\n * })\n * //=> 'about 1 year ago'\n *\n * @example\n * // What is the distance between 1 August 2016 and 1 January 2015 in Esperanto?\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = formatDistance(new Date(2016, 7, 1), new Date(2015, 0, 1), {\n *   locale: eoLocale\n * })\n * //=> 'pli ol 1 jaro'\n */\n\nexport default function formatDistance(dirtyDate, dirtyBaseDate, options) {\n  var _ref, _options$locale;\n\n  requiredArgs(2, arguments);\n  var defaultOptions = getDefaultOptions();\n  var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : defaultLocale;\n\n  if (!locale.formatDistance) {\n    throw new RangeError('locale must contain formatDistance property');\n  }\n\n  var comparison = compareAsc(dirtyDate, dirtyBaseDate);\n\n  if (isNaN(comparison)) {\n    throw new RangeError('Invalid time value');\n  }\n\n  var localizeOptions = assign(cloneObject(options), {\n    addSuffix: Boolean(options === null || options === void 0 ? void 0 : options.addSuffix),\n    comparison: comparison\n  });\n  var dateLeft;\n  var dateRight;\n\n  if (comparison > 0) {\n    dateLeft = toDate(dirtyBaseDate);\n    dateRight = toDate(dirtyDate);\n  } else {\n    dateLeft = toDate(dirtyDate);\n    dateRight = toDate(dirtyBaseDate);\n  }\n\n  var seconds = differenceInSeconds(dateRight, dateLeft);\n  var offsetInSeconds = (getTimezoneOffsetInMilliseconds(dateRight) - getTimezoneOffsetInMilliseconds(dateLeft)) / 1000;\n  var minutes = Math.round((seconds - offsetInSeconds) / 60);\n  var months; // 0 up to 2 mins\n\n  if (minutes < 2) {\n    if (options !== null && options !== void 0 && options.includeSeconds) {\n      if (seconds < 5) {\n        return locale.formatDistance('lessThanXSeconds', 5, localizeOptions);\n      } else if (seconds < 10) {\n        return locale.formatDistance('lessThanXSeconds', 10, localizeOptions);\n      } else if (seconds < 20) {\n        return locale.formatDistance('lessThanXSeconds', 20, localizeOptions);\n      } else if (seconds < 40) {\n        return locale.formatDistance('halfAMinute', 0, localizeOptions);\n      } else if (seconds < 60) {\n        return locale.formatDistance('lessThanXMinutes', 1, localizeOptions);\n      } else {\n        return locale.formatDistance('xMinutes', 1, localizeOptions);\n      }\n    } else {\n      if (minutes === 0) {\n        return locale.formatDistance('lessThanXMinutes', 1, localizeOptions);\n      } else {\n        return locale.formatDistance('xMinutes', minutes, localizeOptions);\n      }\n    } // 2 mins up to 0.75 hrs\n\n  } else if (minutes < 45) {\n    return locale.formatDistance('xMinutes', minutes, localizeOptions); // 0.75 hrs up to 1.5 hrs\n  } else if (minutes < 90) {\n    return locale.formatDistance('aboutXHours', 1, localizeOptions); // 1.5 hrs up to 24 hrs\n  } else if (minutes < MINUTES_IN_DAY) {\n    var hours = Math.round(minutes / 60);\n    return locale.formatDistance('aboutXHours', hours, localizeOptions); // 1 day up to 1.75 days\n  } else if (minutes < MINUTES_IN_ALMOST_TWO_DAYS) {\n    return locale.formatDistance('xDays', 1, localizeOptions); // 1.75 days up to 30 days\n  } else if (minutes < MINUTES_IN_MONTH) {\n    var days = Math.round(minutes / MINUTES_IN_DAY);\n    return locale.formatDistance('xDays', days, localizeOptions); // 1 month up to 2 months\n  } else if (minutes < MINUTES_IN_TWO_MONTHS) {\n    months = Math.round(minutes / MINUTES_IN_MONTH);\n    return locale.formatDistance('aboutXMonths', months, localizeOptions);\n  }\n\n  months = differenceInMonths(dateRight, dateLeft); // 2 months up to 12 months\n\n  if (months < 12) {\n    var nearestMonth = Math.round(minutes / MINUTES_IN_MONTH);\n    return locale.formatDistance('xMonths', nearestMonth, localizeOptions); // 1 year up to max Date\n  } else {\n    var monthsSinceStartOfYear = months % 12;\n    var years = Math.floor(months / 12); // N years up to 1 years 3 months\n\n    if (monthsSinceStartOfYear < 3) {\n      return locale.formatDistance('aboutXYears', years, localizeOptions); // N years 3 months up to N years 9 months\n    } else if (monthsSinceStartOfYear < 9) {\n      return locale.formatDistance('overXYears', years, localizeOptions); // N years 9 months up to N year 12 months\n    } else {\n      return locale.formatDistance('almostXYears', years + 1, localizeOptions);\n    }\n  }\n}", "import { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport compareAsc from \"../compareAsc/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport cloneObject from \"../_lib/cloneObject/index.js\";\nimport assign from \"../_lib/assign/index.js\";\nimport defaultLocale from \"../_lib/defaultLocale/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_MINUTE = 1000 * 60;\nvar MINUTES_IN_DAY = 60 * 24;\nvar MINUTES_IN_MONTH = MINUTES_IN_DAY * 30;\nvar MINUTES_IN_YEAR = MINUTES_IN_DAY * 365;\n/**\n * @name formatDistanceStrict\n * @category Common Helpers\n * @summary Return the distance between the given dates in words.\n *\n * @description\n * Return the distance between the given dates in words, using strict units.\n * This is like `formatDistance`, but does not use helpers like 'almost', 'over',\n * 'less than' and the like.\n *\n * | Distance between dates | Result              |\n * |------------------------|---------------------|\n * | 0 ... 59 secs          | [0..59] seconds     |\n * | 1 ... 59 mins          | [1..59] minutes     |\n * | 1 ... 23 hrs           | [1..23] hours       |\n * | 1 ... 29 days          | [1..29] days        |\n * | 1 ... 11 months        | [1..11] months      |\n * | 1 ... N years          | [1..N]  years       |\n *\n * @param {Date|Number} date - the date\n * @param {Date|Number} baseDate - the date to compare with\n * @param {Object} [options] - an object with options.\n * @param {Boolean} [options.addSuffix=false] - result indicates if the second date is earlier or later than the first\n * @param {'second'|'minute'|'hour'|'day'|'month'|'year'} [options.unit] - if specified, will force a unit\n * @param {'floor'|'ceil'|'round'} [options.roundingMethod='round'] - which way to round partial units\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @returns {String} the distance in words\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.roundingMethod` must be 'floor', 'ceil' or 'round'\n * @throws {RangeError} `options.unit` must be 'second', 'minute', 'hour', 'day', 'month' or 'year'\n * @throws {RangeError} `options.locale` must contain `formatDistance` property\n *\n * @example\n * // What is the distance between 2 July 2014 and 1 January 2015?\n * const result = formatDistanceStrict(new Date(2014, 6, 2), new Date(2015, 0, 2))\n * //=> '6 months'\n *\n * @example\n * // What is the distance between 1 January 2015 00:00:15\n * // and 1 January 2015 00:00:00?\n * const result = formatDistanceStrict(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   new Date(2015, 0, 1, 0, 0, 0)\n * )\n * //=> '15 seconds'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, with a suffix?\n * const result = formatDistanceStrict(new Date(2015, 0, 1), new Date(2016, 0, 1), {\n *   addSuffix: true\n * })\n * //=> '1 year ago'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, in minutes?\n * const result = formatDistanceStrict(new Date(2016, 0, 1), new Date(2015, 0, 1), {\n *   unit: 'minute'\n * })\n * //=> '525600 minutes'\n *\n * @example\n * // What is the distance from 1 January 2015\n * // to 28 January 2015, in months, rounded up?\n * const result = formatDistanceStrict(new Date(2015, 0, 28), new Date(2015, 0, 1), {\n *   unit: 'month',\n *   roundingMethod: 'ceil'\n * })\n * //=> '1 month'\n *\n * @example\n * // What is the distance between 1 August 2016 and 1 January 2015 in Esperanto?\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = formatDistanceStrict(new Date(2016, 7, 1), new Date(2015, 0, 1), {\n *   locale: eoLocale\n * })\n * //=> '1 jaro'\n */\n\nexport default function formatDistanceStrict(dirtyDate, dirtyBaseDate, options) {\n  var _ref, _options$locale, _options$roundingMeth;\n\n  requiredArgs(2, arguments);\n  var defaultOptions = getDefaultOptions();\n  var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : defaultLocale;\n\n  if (!locale.formatDistance) {\n    throw new RangeError('locale must contain localize.formatDistance property');\n  }\n\n  var comparison = compareAsc(dirtyDate, dirtyBaseDate);\n\n  if (isNaN(comparison)) {\n    throw new RangeError('Invalid time value');\n  }\n\n  var localizeOptions = assign(cloneObject(options), {\n    addSuffix: Boolean(options === null || options === void 0 ? void 0 : options.addSuffix),\n    comparison: comparison\n  });\n  var dateLeft;\n  var dateRight;\n\n  if (comparison > 0) {\n    dateLeft = toDate(dirtyBaseDate);\n    dateRight = toDate(dirtyDate);\n  } else {\n    dateLeft = toDate(dirtyDate);\n    dateRight = toDate(dirtyBaseDate);\n  }\n\n  var roundingMethod = String((_options$roundingMeth = options === null || options === void 0 ? void 0 : options.roundingMethod) !== null && _options$roundingMeth !== void 0 ? _options$roundingMeth : 'round');\n  var roundingMethodFn;\n\n  if (roundingMethod === 'floor') {\n    roundingMethodFn = Math.floor;\n  } else if (roundingMethod === 'ceil') {\n    roundingMethodFn = Math.ceil;\n  } else if (roundingMethod === 'round') {\n    roundingMethodFn = Math.round;\n  } else {\n    throw new RangeError(\"roundingMethod must be 'floor', 'ceil' or 'round'\");\n  }\n\n  var milliseconds = dateRight.getTime() - dateLeft.getTime();\n  var minutes = milliseconds / MILLISECONDS_IN_MINUTE;\n  var timezoneOffset = getTimezoneOffsetInMilliseconds(dateRight) - getTimezoneOffsetInMilliseconds(dateLeft); // Use DST-normalized difference in minutes for years, months and days;\n  // use regular difference in minutes for hours, minutes and seconds.\n\n  var dstNormalizedMinutes = (milliseconds - timezoneOffset) / MILLISECONDS_IN_MINUTE;\n  var defaultUnit = options === null || options === void 0 ? void 0 : options.unit;\n  var unit;\n\n  if (!defaultUnit) {\n    if (minutes < 1) {\n      unit = 'second';\n    } else if (minutes < 60) {\n      unit = 'minute';\n    } else if (minutes < MINUTES_IN_DAY) {\n      unit = 'hour';\n    } else if (dstNormalizedMinutes < MINUTES_IN_MONTH) {\n      unit = 'day';\n    } else if (dstNormalizedMinutes < MINUTES_IN_YEAR) {\n      unit = 'month';\n    } else {\n      unit = 'year';\n    }\n  } else {\n    unit = String(defaultUnit);\n  } // 0 up to 60 seconds\n\n\n  if (unit === 'second') {\n    var seconds = roundingMethodFn(milliseconds / 1000);\n    return locale.formatDistance('xSeconds', seconds, localizeOptions); // 1 up to 60 mins\n  } else if (unit === 'minute') {\n    var roundedMinutes = roundingMethodFn(minutes);\n    return locale.formatDistance('xMinutes', roundedMinutes, localizeOptions); // 1 up to 24 hours\n  } else if (unit === 'hour') {\n    var hours = roundingMethodFn(minutes / 60);\n    return locale.formatDistance('xHours', hours, localizeOptions); // 1 up to 30 days\n  } else if (unit === 'day') {\n    var days = roundingMethodFn(dstNormalizedMinutes / MINUTES_IN_DAY);\n    return locale.formatDistance('xDays', days, localizeOptions); // 1 up to 12 months\n  } else if (unit === 'month') {\n    var months = roundingMethodFn(dstNormalizedMinutes / MINUTES_IN_MONTH);\n    return months === 12 && defaultUnit !== 'month' ? locale.formatDistance('xYears', 1, localizeOptions) : locale.formatDistance('xMonths', months, localizeOptions); // 1 year up to max Date\n  } else if (unit === 'year') {\n    var years = roundingMethodFn(dstNormalizedMinutes / MINUTES_IN_YEAR);\n    return locale.formatDistance('xYears', years, localizeOptions);\n  }\n\n  throw new RangeError(\"unit must be 'second', 'minute', 'hour', 'day', 'month' or 'year'\");\n}", "import distanceInWords from \"../formatDistance/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name formatDistanceToNow\n * @category Common Helpers\n * @summary Return the distance between the given date and now in words.\n * @pure false\n *\n * @description\n * Return the distance between the given date and now in words.\n *\n * | Distance to now                                                   | Result              |\n * |-------------------------------------------------------------------|---------------------|\n * | 0 ... 30 secs                                                     | less than a minute  |\n * | 30 secs ... 1 min 30 secs                                         | 1 minute            |\n * | 1 min 30 secs ... 44 mins 30 secs                                 | [2..44] minutes     |\n * | 44 mins ... 30 secs ... 89 mins 30 secs                           | about 1 hour        |\n * | 89 mins 30 secs ... 23 hrs 59 mins 30 secs                        | about [2..24] hours |\n * | 23 hrs 59 mins 30 secs ... 41 hrs 59 mins 30 secs                 | 1 day               |\n * | 41 hrs 59 mins 30 secs ... 29 days 23 hrs 59 mins 30 secs         | [2..30] days        |\n * | 29 days 23 hrs 59 mins 30 secs ... 44 days 23 hrs 59 mins 30 secs | about 1 month       |\n * | 44 days 23 hrs 59 mins 30 secs ... 59 days 23 hrs 59 mins 30 secs | about 2 months      |\n * | 59 days 23 hrs 59 mins 30 secs ... 1 yr                           | [2..12] months      |\n * | 1 yr ... 1 yr 3 months                                            | about 1 year        |\n * | 1 yr 3 months ... 1 yr 9 month s                                  | over 1 year         |\n * | 1 yr 9 months ... 2 yrs                                           | almost 2 years      |\n * | N yrs ... N yrs 3 months                                          | about N years       |\n * | N yrs 3 months ... N yrs 9 months                                 | over N years        |\n * | N yrs 9 months ... N+1 yrs                                        | almost N+1 years    |\n *\n * With `options.includeSeconds == true`:\n * | Distance to now     | Result               |\n * |---------------------|----------------------|\n * | 0 secs ... 5 secs   | less than 5 seconds  |\n * | 5 secs ... 10 secs  | less than 10 seconds |\n * | 10 secs ... 20 secs | less than 20 seconds |\n * | 20 secs ... 40 secs | half a minute        |\n * | 40 secs ... 60 secs | less than a minute   |\n * | 60 secs ... 90 secs | 1 minute             |\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the given date\n * @param {Object} [options] - the object with options\n * @param {Boolean} [options.includeSeconds=false] - distances less than a minute are more detailed\n * @param {Boolean} [options.addSuffix=false] - result specifies if now is earlier or later than the passed date\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @returns {String} the distance in words\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `options.locale` must contain `formatDistance` property\n *\n * @example\n * // If today is 1 January 2015, what is the distance to 2 July 2014?\n * const result = formatDistanceToNow(\n *   new Date(2014, 6, 2)\n * )\n * //=> '6 months'\n *\n * @example\n * // If now is 1 January 2015 00:00:00,\n * // what is the distance to 1 January 2015 00:00:15, including seconds?\n * const result = formatDistanceToNow(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   {includeSeconds: true}\n * )\n * //=> 'less than 20 seconds'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 January 2016, with a suffix?\n * const result = formatDistanceToNow(\n *   new Date(2016, 0, 1),\n *   {addSuffix: true}\n * )\n * //=> 'in about 1 year'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 August 2016 in Esperanto?\n * const eoLocale = require('date-fns/locale/eo')\n * const result = formatDistanceToNow(\n *   new Date(2016, 7, 1),\n *   {locale: eoLocale}\n * )\n * //=> 'pli ol 1 jaro'\n */\nexport default function formatDistanceToNow(dirtyDate, options) {\n  requiredArgs(1, arguments);\n  return distanceInWords(dirtyDate, Date.now(), options);\n}", "import formatDistanceStrict from \"../formatDistanceStrict/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name formatDistanceToNowStrict\n * @category Common Helpers\n * @summary Return the distance between the given date and now in words.\n * @pure false\n *\n * @description\n * Return the distance between the given dates in words, using strict units.\n * This is like `formatDistance`, but does not use helpers like 'almost', 'over',\n * 'less than' and the like.\n *\n * | Distance between dates | Result              |\n * |------------------------|---------------------|\n * | 0 ... 59 secs          | [0..59] seconds     |\n * | 1 ... 59 mins          | [1..59] minutes     |\n * | 1 ... 23 hrs           | [1..23] hours       |\n * | 1 ... 29 days          | [1..29] days        |\n * | 1 ... 11 months        | [1..11] months      |\n * | 1 ... N years          | [1..N]  years       |\n *\n * @param {Date|Number} date - the given date\n * @param {Object} [options] - an object with options.\n * @param {Boolean} [options.addSuffix=false] - result indicates if the second date is earlier or later than the first\n * @param {'second'|'minute'|'hour'|'day'|'month'|'year'} [options.unit] - if specified, will force a unit\n * @param {'floor'|'ceil'|'round'} [options.roundingMethod='round'] - which way to round partial units\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @returns {String} the distance in words\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `options.locale` must contain `formatDistance` property\n *\n * @example\n * // If today is 1 January 2015, what is the distance to 2 July 2014?\n * const result = formatDistanceToNowStrict(\n *   new Date(2014, 6, 2)\n * )\n * //=> '6 months'\n *\n * @example\n * // If now is 1 January 2015 00:00:00,\n * // what is the distance to 1 January 2015 00:00:15, including seconds?\n * const result = formatDistanceToNowStrict(\n *   new Date(2015, 0, 1, 0, 0, 15)\n * )\n * //=> '15 seconds'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 January 2016, with a suffix?\n * const result = formatDistanceToNowStrict(\n *   new Date(2016, 0, 1),\n *   {addSuffix: true}\n * )\n * //=> 'in 1 year'\n *\n * @example\n * // If today is 28 January 2015,\n * // what is the distance to 1 January 2015, in months, rounded up??\n * const result = formatDistanceToNowStrict(new Date(2015, 0, 1), {\n *   unit: 'month',\n *   roundingMethod: 'ceil'\n * })\n * //=> '1 month'\n *\n * @example\n * // If today is 1 January 2015,\n * // what is the distance to 1 January 2016 in Esperanto?\n * const eoLocale = require('date-fns/locale/eo')\n * const result = formatDistanceToNowStrict(\n *   new Date(2016, 0, 1),\n *   {locale: eoLocale}\n * )\n * //=> '1 jaro'\n */\n\nexport default function formatDistanceToNowStrict(dirtyDate, options) {\n  requiredArgs(1, arguments);\n  return formatDistanceStrict(dirtyDate, Date.now(), options);\n}", "import { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport defaultLocale from \"../_lib/defaultLocale/index.js\";\nvar defaultFormat = ['years', 'months', 'weeks', 'days', 'hours', 'minutes', 'seconds'];\n/**\n * @name formatDuration\n * @category Common Helpers\n * @summary Formats a duration in human-readable format\n *\n * @description\n * Return human-readable duration string i.e. \"9 months 2 days\"\n *\n * @param {Duration} duration - the duration to format\n * @param {Object} [options] - an object with options.\n * @param {string[]} [options.format=['years', 'months', 'weeks', 'days', 'hours', 'minutes', 'seconds']] - the array of units to format\n * @param {boolean} [options.zero=false] - should zeros be included in the output?\n * @param {string} [options.delimiter=' '] - delimiter string\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @returns {string} the formatted date string\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Format full duration\n * formatDuration({\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> '2 years 9 months 1 week 7 days 5 hours 9 minutes 30 seconds'\n *\n * @example\n * // Format partial duration\n * formatDuration({ months: 9, days: 2 })\n * //=> '9 months 2 days'\n *\n * @example\n * // Customize the format\n * formatDuration(\n *   {\n *     years: 2,\n *     months: 9,\n *     weeks: 1,\n *     days: 7,\n *     hours: 5,\n *     minutes: 9,\n *     seconds: 30\n *   },\n *   { format: ['months', 'weeks'] }\n * ) === '9 months 1 week'\n *\n * @example\n * // Customize the zeros presence\n * formatDuration({ years: 0, months: 9 })\n * //=> '9 months'\n * formatDuration({ years: 0, months: 9 }, { zero: true })\n * //=> '0 years 9 months'\n *\n * @example\n * // Customize the delimiter\n * formatDuration({ years: 2, months: 9, weeks: 3 }, { delimiter: ', ' })\n * //=> '2 years, 9 months, 3 weeks'\n */\n\nexport default function formatDuration(duration, options) {\n  var _ref, _options$locale, _options$format, _options$zero, _options$delimiter;\n\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 argument required, but only \".concat(arguments.length, \" present\"));\n  }\n\n  var defaultOptions = getDefaultOptions();\n  var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : defaultLocale;\n  var format = (_options$format = options === null || options === void 0 ? void 0 : options.format) !== null && _options$format !== void 0 ? _options$format : defaultFormat;\n  var zero = (_options$zero = options === null || options === void 0 ? void 0 : options.zero) !== null && _options$zero !== void 0 ? _options$zero : false;\n  var delimiter = (_options$delimiter = options === null || options === void 0 ? void 0 : options.delimiter) !== null && _options$delimiter !== void 0 ? _options$delimiter : ' ';\n\n  if (!locale.formatDistance) {\n    return '';\n  }\n\n  var result = format.reduce(function (acc, unit) {\n    var token = \"x\".concat(unit.replace(/(^.)/, function (m) {\n      return m.toUpperCase();\n    }));\n    var value = duration[unit];\n\n    if (typeof value === 'number' && (zero || duration[unit])) {\n      return acc.concat(locale.formatDistance(token, value));\n    }\n\n    return acc;\n  }, []).join(delimiter);\n  return result;\n}", "import toDate from \"../toDate/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport addLeadingZeros from \"../_lib/addLeadingZeros/index.js\";\n\n/**\n * @name formatISO9075\n * @category Common Helpers\n * @summary Format the date according to the ISO 9075 standard (https://dev.mysql.com/doc/refman/5.7/en/date-and-time-functions.html#function_get-format).\n *\n * @description\n * Return the formatted date string in ISO 9075 format. Options may be passed to control the parts and notations of the date.\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {'extended'|'basic'} [options.format='extended'] - if 'basic', hide delimiters between date and time values.\n * @param {'complete'|'date'|'time'} [options.representation='complete'] - format date, time, or both.\n * @returns {String} the formatted date string\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `options.format` must be 'extended' or 'basic'\n * @throws {RangeError} `options.representation` must be 'date', 'time' or 'complete'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18 19:00:52'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075, short format:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { format: 'basic' })\n * //=> '20190918 190052'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format, date only:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { representation: 'date' })\n * //=> '2019-09-18'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format, time only:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { representation: 'time' })\n * //=> '19:00:52'\n */\nexport default function formatISO9075(dirtyDate, options) {\n  var _options$format, _options$representati;\n\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 argument required, but only \".concat(arguments.length, \" present\"));\n  }\n\n  var originalDate = toDate(dirtyDate);\n\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  }\n\n  var format = String((_options$format = options === null || options === void 0 ? void 0 : options.format) !== null && _options$format !== void 0 ? _options$format : 'extended');\n  var representation = String((_options$representati = options === null || options === void 0 ? void 0 : options.representation) !== null && _options$representati !== void 0 ? _options$representati : 'complete');\n\n  if (format !== 'extended' && format !== 'basic') {\n    throw new RangeError(\"format must be 'extended' or 'basic'\");\n  }\n\n  if (representation !== 'date' && representation !== 'time' && representation !== 'complete') {\n    throw new RangeError(\"representation must be 'date', 'time', or 'complete'\");\n  }\n\n  var result = '';\n  var dateDelimiter = format === 'extended' ? '-' : '';\n  var timeDelimiter = format === 'extended' ? ':' : ''; // Representation is either 'date' or 'complete'\n\n  if (representation !== 'time') {\n    var day = addLeadingZeros(originalDate.getDate(), 2);\n    var month = addLeadingZeros(originalDate.getMonth() + 1, 2);\n    var year = addLeadingZeros(originalDate.getFullYear(), 4); // yyyyMMdd or yyyy-MM-dd.\n\n    result = \"\".concat(year).concat(dateDelimiter).concat(month).concat(dateDelimiter).concat(day);\n  } // Representation is either 'time' or 'complete'\n\n\n  if (representation !== 'date') {\n    var hour = addLeadingZeros(originalDate.getHours(), 2);\n    var minute = addLeadingZeros(originalDate.getMinutes(), 2);\n    var second = addLeadingZeros(originalDate.getSeconds(), 2); // If there's also date, separate it with time with a space\n\n    var separator = result === '' ? '' : ' '; // HHmmss or HH:mm:ss.\n\n    result = \"\".concat(result).concat(separator).concat(hour).concat(timeDelimiter).concat(minute).concat(timeDelimiter).concat(second);\n  }\n\n  return result;\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name formatISODuration\n * @category Common Helpers\n * @summary Format a duration object according as ISO 8601 duration string\n *\n * @description\n * Format a duration object according to the ISO 8601 duration standard (https://www.digi.com/resources/documentation/digidocs/90001437-13/reference/r_iso_8601_duration_format.htm)\n *\n * @param {Duration} duration - the duration to format\n *\n * @returns {String} The ISO 8601 duration string\n * @throws {TypeError} Requires 1 argument\n * @throws {Error} Argument must be an object\n *\n * @example\n * // Format the given duration as ISO 8601 string\n * const result = formatISODuration({\n *   years: 39,\n *   months: 2,\n *   days: 20,\n *   hours: 7,\n *   minutes: 5,\n *   seconds: 0\n * })\n * //=> 'P39Y2M20DT0H0M0S'\n */\n\nexport default function formatISODuration(duration) {\n  requiredArgs(1, arguments);\n  if (_typeof(duration) !== 'object') throw new Error('Duration must be an object');\n  var _duration$years = duration.years,\n      years = _duration$years === void 0 ? 0 : _duration$years,\n      _duration$months = duration.months,\n      months = _duration$months === void 0 ? 0 : _duration$months,\n      _duration$days = duration.days,\n      days = _duration$days === void 0 ? 0 : _duration$days,\n      _duration$hours = duration.hours,\n      hours = _duration$hours === void 0 ? 0 : _duration$hours,\n      _duration$minutes = duration.minutes,\n      minutes = _duration$minutes === void 0 ? 0 : _duration$minutes,\n      _duration$seconds = duration.seconds,\n      seconds = _duration$seconds === void 0 ? 0 : _duration$seconds;\n  return \"P\".concat(years, \"Y\").concat(months, \"M\").concat(days, \"DT\").concat(hours, \"H\").concat(minutes, \"M\").concat(seconds, \"S\");\n}", "import toDate from \"../toDate/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport addLeadingZeros from \"../_lib/addLeadingZeros/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n\n/**\n * @name formatRFC3339\n * @category Common Helpers\n * @summary Format the date according to the RFC 3339 standard (https://tools.ietf.org/html/rfc3339#section-5.6).\n *\n * @description\n * Return the formatted date string in RFC 3339 format. Options may be passed to control the parts and notations of the date.\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {0|1|2|3} [options.fractionDigits=0] - number of digits after the decimal point after seconds\n * @returns {String} the formatted date string\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `options.fractionDigits` must be between 0 and 3\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format:\n * const result = formatRFC3339(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18T19:00:52Z'\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format, 2 digits of second fraction:\n * const result = formatRFC3339(new Date(2019, 8, 18, 19, 0, 52, 234), { fractionDigits: 2 })\n * //=> '2019-09-18T19:00:52.23Z'\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format, 3 digits of second fraction\n * const result = formatRFC3339(new Date(2019, 8, 18, 19, 0, 52, 234), { fractionDigits: 3 })\n * //=> '2019-09-18T19:00:52.234Z'\n */\nexport default function formatRFC3339(dirtyDate, options) {\n  var _options$fractionDigi;\n\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 arguments required, but only \".concat(arguments.length, \" present\"));\n  }\n\n  var originalDate = toDate(dirtyDate);\n\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  }\n\n  var fractionDigits = Number((_options$fractionDigi = options === null || options === void 0 ? void 0 : options.fractionDigits) !== null && _options$fractionDigi !== void 0 ? _options$fractionDigi : 0); // Test if fractionDigits is between 0 and 3 _and_ is not NaN\n\n  if (!(fractionDigits >= 0 && fractionDigits <= 3)) {\n    throw new RangeError('fractionDigits must be between 0 and 3 inclusively');\n  }\n\n  var day = addLeadingZeros(originalDate.getDate(), 2);\n  var month = addLeadingZeros(originalDate.getMonth() + 1, 2);\n  var year = originalDate.getFullYear();\n  var hour = addLeadingZeros(originalDate.getHours(), 2);\n  var minute = addLeadingZeros(originalDate.getMinutes(), 2);\n  var second = addLeadingZeros(originalDate.getSeconds(), 2);\n  var fractionalSecond = '';\n\n  if (fractionDigits > 0) {\n    var milliseconds = originalDate.getMilliseconds();\n    var fractionalSeconds = Math.floor(milliseconds * Math.pow(10, fractionDigits - 3));\n    fractionalSecond = '.' + addLeadingZeros(fractionalSeconds, fractionDigits);\n  }\n\n  var offset = '';\n  var tzOffset = originalDate.getTimezoneOffset();\n\n  if (tzOffset !== 0) {\n    var absoluteOffset = Math.abs(tzOffset);\n    var hourOffset = addLeadingZeros(toInteger(absoluteOffset / 60), 2);\n    var minuteOffset = addLeadingZeros(absoluteOffset % 60, 2); // If less than 0, the sign is +, because it is ahead of time.\n\n    var sign = tzOffset < 0 ? '+' : '-';\n    offset = \"\".concat(sign).concat(hourOffset, \":\").concat(minuteOffset);\n  } else {\n    offset = 'Z';\n  }\n\n  return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \"T\").concat(hour, \":\").concat(minute, \":\").concat(second).concat(fractionalSecond).concat(offset);\n}", "import toDate from \"../toDate/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport addLeadingZeros from \"../_lib/addLeadingZeros/index.js\";\nvar days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\nvar months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n/**\n * @name formatRFC7231\n * @category Common Helpers\n * @summary Format the date according to the RFC 7231 standard (https://tools.ietf.org/html/rfc7231#section-*******).\n *\n * @description\n * Return the formatted date string in RFC 7231 format.\n * The result will always be in UTC timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {String} the formatted date string\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in RFC 7231 format:\n * const result = formatRFC7231(new Date(2019, 8, 18, 19, 0, 52))\n * //=> 'Wed, 18 Sep 2019 19:00:52 GMT'\n */\n\nexport default function formatRFC7231(dirtyDate) {\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 arguments required, but only \".concat(arguments.length, \" present\"));\n  }\n\n  var originalDate = toDate(dirtyDate);\n\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  }\n\n  var dayName = days[originalDate.getUTCDay()];\n  var dayOfMonth = addLeadingZeros(originalDate.getUTCDate(), 2);\n  var monthName = months[originalDate.getUTCMonth()];\n  var year = originalDate.getUTCFullYear();\n  var hour = addLeadingZeros(originalDate.getUTCHours(), 2);\n  var minute = addLeadingZeros(originalDate.getUTCMinutes(), 2);\n  var second = addLeadingZeros(originalDate.getUTCSeconds(), 2); // Result variables.\n\n  return \"\".concat(dayName, \", \").concat(dayOfMonth, \" \").concat(monthName, \" \").concat(year, \" \").concat(hour, \":\").concat(minute, \":\").concat(second, \" GMT\");\n}", "import { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport format from \"../format/index.js\";\nimport defaultLocale from \"../_lib/defaultLocale/index.js\";\nimport subMilliseconds from \"../subMilliseconds/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name formatRelative\n * @category Common Helpers\n * @summary Represent the date in words relative to the given base date.\n *\n * @description\n * Represent the date in words relative to the given base date.\n *\n * | Distance to the base date | Result                    |\n * |---------------------------|---------------------------|\n * | Previous 6 days           | last Sunday at 04:30 AM   |\n * | Last day                  | yesterday at 04:30 AM     |\n * | Same day                  | today at 04:30 AM         |\n * | Next day                  | tomorrow at 04:30 AM      |\n * | Next 6 days               | Sunday at 04:30 AM        |\n * | Other                     | 12/31/2017                |\n *\n * @param {Date|Number} date - the date to format\n * @param {Date|Number} baseDate - the date to compare with\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {String} the date in words\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.locale` must contain `localize` property\n * @throws {RangeError} `options.locale` must contain `formatLong` property\n * @throws {RangeError} `options.locale` must contain `formatRelative` property\n *\n * @example\n * // Represent the date of 6 days ago in words relative to the given base date. In this example, today is Wednesday\n * const result = formatRelative(addDays(new Date(), -6), new Date())\n * //=> \"last Thursday at 12:45 AM\"\n */\n\nexport default function formatRelative(dirtyDate, dirtyBaseDate, options) {\n  var _ref, _options$locale, _ref2, _ref3, _ref4, _options$weekStartsOn, _options$locale2, _options$locale2$opti, _defaultOptions$local, _defaultOptions$local2;\n\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var baseDate = toDate(dirtyBaseDate);\n  var defaultOptions = getDefaultOptions();\n  var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : defaultLocale;\n  var weekStartsOn = toInteger((_ref2 = (_ref3 = (_ref4 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale2 = options.locale) === null || _options$locale2 === void 0 ? void 0 : (_options$locale2$opti = _options$locale2.options) === null || _options$locale2$opti === void 0 ? void 0 : _options$locale2$opti.weekStartsOn) !== null && _ref4 !== void 0 ? _ref4 : defaultOptions.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : 0);\n\n  if (!locale.localize) {\n    throw new RangeError('locale must contain localize property');\n  }\n\n  if (!locale.formatLong) {\n    throw new RangeError('locale must contain formatLong property');\n  }\n\n  if (!locale.formatRelative) {\n    throw new RangeError('locale must contain formatRelative property');\n  }\n\n  var diff = differenceInCalendarDays(date, baseDate);\n\n  if (isNaN(diff)) {\n    throw new RangeError('Invalid time value');\n  }\n\n  var token;\n\n  if (diff < -6) {\n    token = 'other';\n  } else if (diff < -1) {\n    token = 'lastWeek';\n  } else if (diff < 0) {\n    token = 'yesterday';\n  } else if (diff < 1) {\n    token = 'today';\n  } else if (diff < 2) {\n    token = 'tomorrow';\n  } else if (diff < 7) {\n    token = 'nextWeek';\n  } else {\n    token = 'other';\n  }\n\n  var utcDate = subMilliseconds(date, getTimezoneOffsetInMilliseconds(date));\n  var utcBaseDate = subMilliseconds(baseDate, getTimezoneOffsetInMilliseconds(baseDate));\n  var formatStr = locale.formatRelative(token, utcDate, utcBaseDate, {\n    locale: locale,\n    weekStartsOn: weekStartsOn\n  });\n  return format(date, formatStr, {\n    locale: locale,\n    weekStartsOn: weekStartsOn\n  });\n}", "import toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name fromUnixTime\n * @category Timestamp Helpers\n * @summary Create a date from a Unix timestamp.\n *\n * @description\n * Create a date from a Unix timestamp (in seconds). Decimal values will be discarded.\n *\n * @param {Number} unixTime - the given Unix timestamp (in seconds)\n * @returns {Date} the date\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Create the date 29 February 2012 11:45:05:\n * const result = fromUnixTime(1330515905)\n * //=> Wed Feb 29 2012 11:45:05\n */\n\nexport default function fromUnixTime(dirtyUnixTime) {\n  requiredArgs(1, arguments);\n  var unixTime = toInteger(dirtyUnixTime);\n  return toDate(unixTime * 1000);\n}", "import toDate from \"../toDate/index.js\";\nimport startOfYear from \"../startOfYear/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDayOfYear\n * @category Day Helpers\n * @summary Get the day of the year of the given date.\n *\n * @description\n * Get the day of the year of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the day of year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which day of the year is 2 July 2014?\n * const result = getDayOfYear(new Date(2014, 6, 2))\n * //=> 183\n */\n\nexport default function getDayOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = differenceInCalendarDays(date, startOfYear(date));\n  var dayOfYear = diff + 1;\n  return dayOfYear;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isLeapYear\n * @category Year Helpers\n * @summary Is the given date in the leap year?\n *\n * @description\n * Is the given date in the leap year?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in the leap year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 1 September 2012 in the leap year?\n * const result = isLeapYear(new Date(2012, 8, 1))\n * //=> true\n */\n\nexport default function isLeapYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;\n}", "import toDate from \"../toDate/index.js\";\nimport isLeapYear from \"../isLeapYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDaysInYear\n * @category Year Helpers\n * @summary Get the number of days in a year of the given date.\n *\n * @description\n * Get the number of days in a year of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the number of days in a year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // How many days are in 2012?\n * const result = getDaysInYear(new Date(2012, 0, 1))\n * //=> 366\n */\n\nexport default function getDaysInYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n\n  if (String(new Date(date)) === 'Invalid Date') {\n    return NaN;\n  }\n\n  return isLeapYear(date) ? 366 : 365;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDecade\n * @category Decade Helpers\n * @summary Get the decade of the given date.\n *\n * @description\n * Get the decade of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the year of decade\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which decade belongs 27 November 1942?\n * const result = getDecade(new Date(1942, 10, 27))\n * //=> 1940\n */\n\nexport default function getDecade(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var decade = Math.floor(year / 10) * 10;\n  return decade;\n}", "import { getDefaultOptions as getInternalDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport assign from \"../_lib/assign/index.js\";\n/**\n * @name getDefaultOptions\n * @category Common Helpers\n * @summary Get default options.\n * @pure false\n *\n * @description\n * Returns an object that contains defaults for\n * `options.locale`, `options.weekStartsOn` and `options.firstWeekContainsDate`\n * arguments for all functions.\n *\n * You can change these with [setDefaultOptions]{@link https://date-fns.org/docs/setDefaultOptions}.\n *\n * @returns {Object} default options\n *\n * @example\n * const result = getDefaultOptions()\n * //=> {}\n *\n * @example\n * setDefaultOptions({ weekStarsOn: 1, firstWeekContainsDate: 4 })\n * const result = getDefaultOptions()\n * //=> { weekStarsOn: 1, firstWeekContainsDate: 4 }\n */\n\nexport default function getDefaultOptions() {\n  return assign({}, getInternalDefaultOptions());\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getISODay\n * @category Weekday Helpers\n * @summary Get the day of the ISO week of the given date.\n *\n * @description\n * Get the day of the ISO week of the given date,\n * which is 7 for Sunday, 1 for Monday etc.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the day of ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which day of the ISO week is 26 February 2012?\n * const result = getISODay(new Date(2012, 1, 26))\n * //=> 7\n */\n\nexport default function getISODay(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var day = date.getDay();\n\n  if (day === 0) {\n    day = 7;\n  }\n\n  return day;\n}", "import toDate from \"../toDate/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport startOfISOWeekYear from \"../startOfISOWeekYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\n/**\n * @name getISOWeek\n * @category ISO Week Helpers\n * @summary Get the ISO week of the given date.\n *\n * @description\n * Get the ISO week of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which week of the ISO-week numbering year is 2 January 2005?\n * const result = getISOWeek(new Date(2005, 0, 2))\n * //=> 53\n */\n\nexport default function getISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfISOWeek(date).getTime() - startOfISOWeekYear(date).getTime(); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}", "import startOfISOWeekYear from \"../startOfISOWeekYear/index.js\";\nimport addWeeks from \"../addWeeks/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\n/**\n * @name getISOWeeksInYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * @description\n * Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the number of ISO weeks in a year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // How many weeks are in ISO week-numbering year 2015?\n * const result = getISOWeeksInYear(new Date(2015, 1, 11))\n * //=> 53\n */\n\nexport default function getISOWeeksInYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var thisYear = startOfISOWeekYear(dirtyDate);\n  var nextYear = startOfISOWeekYear(addWeeks(thisYear, 60));\n  var diff = nextYear.valueOf() - thisYear.valueOf(); // Round the number of weeks to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round(diff / MILLISECONDS_IN_WEEK);\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getMilliseconds\n * @category Millisecond Helpers\n * @summary Get the milliseconds of the given date.\n *\n * @description\n * Get the milliseconds of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Get the milliseconds of 29 February 2012 11:45:05.123:\n * const result = getMilliseconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 123\n */\n\nexport default function getMilliseconds(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var milliseconds = date.getMilliseconds();\n  return milliseconds;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_DAY = 24 * 60 * 60 * 1000;\n/**\n * @name getOverlappingDaysInIntervals\n * @category Interval Helpers\n * @summary Get the number of days that overlap in two time intervals\n *\n * @description\n * Get the number of days that overlap in two time intervals\n *\n * @param {Interval} intervalLeft - the first interval to compare. See [Interval]{@link docs/Interval}\n * @param {Interval} intervalRight - the second interval to compare. See [Interval]{@link docs/Interval}\n * @returns {Number} the number of days that overlap in two time intervals\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // For overlapping time intervals adds 1 for each started overlapping day:\n * getOverlappingDaysInIntervals(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> 3\n *\n * @example\n * // For non-overlapping time intervals returns 0:\n * getOverlappingDaysInIntervals(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> 0\n */\n\nexport default function getOverlappingDaysInIntervals(dirtyIntervalLeft, dirtyIntervalRight) {\n  requiredArgs(2, arguments);\n  var intervalLeft = dirtyIntervalLeft || {};\n  var intervalRight = dirtyIntervalRight || {};\n  var leftStartTime = toDate(intervalLeft.start).getTime();\n  var leftEndTime = toDate(intervalLeft.end).getTime();\n  var rightStartTime = toDate(intervalRight.start).getTime();\n  var rightEndTime = toDate(intervalRight.end).getTime(); // Throw an exception if start date is after end date or if any date is `Invalid Date`\n\n  if (!(leftStartTime <= leftEndTime && rightStartTime <= rightEndTime)) {\n    throw new RangeError('Invalid interval');\n  }\n\n  var isOverlapping = leftStartTime < rightEndTime && rightStartTime < leftEndTime;\n\n  if (!isOverlapping) {\n    return 0;\n  }\n\n  var overlapStartDate = rightStartTime < leftStartTime ? leftStartTime : rightStartTime;\n  var overlapEndDate = rightEndTime > leftEndTime ? leftEndTime : rightEndTime;\n  var differenceInMs = overlapEndDate - overlapStartDate;\n  return Math.ceil(differenceInMs / MILLISECONDS_IN_DAY);\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getTime\n * @category Timestamp Helpers\n * @summary Get the milliseconds timestamp of the given date.\n *\n * @description\n * Get the milliseconds timestamp of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the timestamp\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Get the timestamp of 29 February 2012 11:45:05.123:\n * const result = getTime(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 1330515905123\n */\n\nexport default function getTime(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var timestamp = date.getTime();\n  return timestamp;\n}", "import getTime from \"../getTime/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getUnixTime\n * @category Timestamp Helpers\n * @summary Get the seconds timestamp of the given date.\n *\n * @description\n * Get the seconds timestamp of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the timestamp\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Get the timestamp of 29 February 2012 11:45:05 CET:\n * const result = getUnixTime(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 1330512305\n */\n\nexport default function getUnixTime(dirtyDate) {\n  requiredArgs(1, arguments);\n  return Math.floor(getTime(dirtyDate) / 1000);\n}", "import startOfWeek from \"../startOfWeek/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\n/**\n * @name getWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Get the local week-numbering year of the given date.\n *\n * @description\n * Get the local week-numbering year of the given date.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#Week_numbering\n *\n * @param {Date|Number} date - the given date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [options.firstWeekContainsDate=1] - the day of January, which is always in the first week of the year\n * @returns {Number} the local week-numbering year\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n *\n * @example\n * // Which week numbering year is 26 December 2004 with the default settings?\n * const result = getWeekYear(new Date(2004, 11, 26))\n * //=> 2005\n *\n * @example\n * // Which week numbering year is 26 December 2004 if week starts on Saturday?\n * const result = getWeekYear(new Date(2004, 11, 26), { weekStartsOn: 6 })\n * //=> 2004\n *\n * @example\n * // Which week numbering year is 26 December 2004 if the first week contains 4 January?\n * const result = getWeekYear(new Date(2004, 11, 26), { firstWeekContainsDate: 4 })\n * //=> 2004\n */\n\nexport default function getWeekYear(dirtyDate, options) {\n  var _ref, _ref2, _ref3, _options$firstWeekCon, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var defaultOptions = getDefaultOptions();\n  var firstWeekContainsDate = toInteger((_ref = (_ref2 = (_ref3 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref !== void 0 ? _ref : 1); // Test if weekStartsOn is between 1 and 7 _and_ is not NaN\n\n  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {\n    throw new RangeError('firstWeekContainsDate must be between 1 and 7 inclusively');\n  }\n\n  var firstWeekOfNextYear = new Date(0);\n  firstWeekOfNextYear.setFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setHours(0, 0, 0, 0);\n  var startOfNextYear = startOfWeek(firstWeekOfNextYear, options);\n  var firstWeekOfThisYear = new Date(0);\n  firstWeekOfThisYear.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setHours(0, 0, 0, 0);\n  var startOfThisYear = startOfWeek(firstWeekOfThisYear, options);\n\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}", "import getWeekYear from \"../getWeekYear/index.js\";\nimport startOfWeek from \"../startOfWeek/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\n/**\n * @name startOfWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Return the start of a local week-numbering year for the given date.\n *\n * @description\n * Return the start of a local week-numbering year.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#Week_numbering\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [options.firstWeekContainsDate=1] - the day of January, which is always in the first week of the year\n * @returns {Date} the start of a week-numbering year\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n *\n * @example\n * // The start of an a week-numbering year for 2 July 2005 with default settings:\n * const result = startOfWeekYear(new Date(2005, 6, 2))\n * //=> Sun Dec 26 2004 00:00:00\n *\n * @example\n * // The start of a week-numbering year for 2 July 2005\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = startOfWeekYear(new Date(2005, 6, 2), {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Mon Jan 03 2005 00:00:00\n */\n\nexport default function startOfWeekYear(dirtyDate, options) {\n  var _ref, _ref2, _ref3, _options$firstWeekCon, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n\n  requiredArgs(1, arguments);\n  var defaultOptions = getDefaultOptions();\n  var firstWeekContainsDate = toInteger((_ref = (_ref2 = (_ref3 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref !== void 0 ? _ref : 1);\n  var year = getWeekYear(dirtyDate, options);\n  var firstWeek = new Date(0);\n  firstWeek.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n  var date = startOfWeek(firstWeek, options);\n  return date;\n}", "import startOfWeek from \"../startOfWeek/index.js\";\nimport startOfWeekYear from \"../startOfWeekYear/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\n/**\n * @name getWeek\n * @category Week Helpers\n * @summary Get the local week index of the given date.\n *\n * @description\n * Get the local week index of the given date.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#Week_numbering\n *\n * @param {Date|Number} date - the given date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [options.firstWeekContainsDate=1] - the day of January, which is always in the first week of the year\n * @returns {Number} the week\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n *\n * @example\n * // Which week of the local week numbering year is 2 January 2005 with default options?\n * const result = getWeek(new Date(2005, 0, 2))\n * //=> 2\n *\n * // Which week of the local week numbering year is 2 January 2005,\n * // if Monday is the first day of the week,\n * // and the first week of the year always contains 4 January?\n * const result = getWeek(new Date(2005, 0, 2), {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> 53\n */\n\nexport default function getWeek(dirtyDate, options) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfWeek(date, options).getTime() - startOfWeekYear(date, options).getTime(); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}", "import { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport getDate from \"../getDate/index.js\";\nimport getDay from \"../getDay/index.js\";\nimport startOfMonth from \"../startOfMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name getWeekOfMonth\n * @category Week Helpers\n * @summary Get the week of the month of the given date.\n *\n * @description\n * Get the week of the month of the given date.\n *\n * @param {Date|Number} date - the given date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Number} the week of month\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6 inclusively\n *\n * @example\n * // Which week of the month is 9 November 2017?\n * const result = getWeekOfMonth(new Date(2017, 10, 9))\n * //=> 2\n */\n\nexport default function getWeekOfMonth(date, options) {\n  var _ref, _ref2, _ref3, _options$weekStartsOn, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n\n  requiredArgs(1, arguments);\n  var defaultOptions = getDefaultOptions();\n  var weekStartsOn = toInteger((_ref = (_ref2 = (_ref3 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.weekStartsOn) !== null && _ref !== void 0 ? _ref : 0);\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n\n  var currentDayOfMonth = getDate(date);\n  if (isNaN(currentDayOfMonth)) return NaN;\n  var startWeekDay = getDay(startOfMonth(date));\n  var lastDayOfFirstWeek = weekStartsOn - startWeekDay;\n  if (lastDayOfFirstWeek <= 0) lastDayOfFirstWeek += 7;\n  var remainingDaysAfterFirstWeek = currentDayOfMonth - lastDayOfFirstWeek;\n  return Math.ceil(remainingDaysAfterFirstWeek / 7) + 1;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name lastDayOfMonth\n * @category Month Helpers\n * @summary Return the last day of a month for the given date.\n *\n * @description\n * Return the last day of a month for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the last day of a month\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The last day of a month for 2 September 2014 11:55:00:\n * const result = lastDayOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\n\nexport default function lastDayOfMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var month = date.getMonth();\n  date.setFullYear(date.getFullYear(), month + 1, 0);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}", "import differenceInCalendarWeeks from \"../differenceInCalendarWeeks/index.js\";\nimport lastDayOfMonth from \"../lastDayOfMonth/index.js\";\nimport startOfMonth from \"../startOfMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name getWeeksInMonth\n * @category Week Helpers\n * @summary Get the number of calendar weeks a month spans.\n *\n * @description\n * Get the number of calendar weeks the month in the given date spans.\n *\n * @param {Date|Number} date - the given date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Number} the number of calendar weeks\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n *\n * @example\n * // How many calendar weeks does February 2015 span?\n * const result = getWeeksInMonth(new Date(2015, 1, 8))\n * //=> 4\n *\n * @example\n * // If the week starts on Monday,\n * // how many calendar weeks does July 2017 span?\n * const result = getWeeksInMonth(new Date(2017, 6, 5), { weekStartsOn: 1 })\n * //=> 6\n */\nexport default function getWeeksInMonth(date, options) {\n  requiredArgs(1, arguments);\n  return differenceInCalendarWeeks(lastDayOfMonth(date), startOfMonth(date), options) + 1;\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInHour } from \"../constants/index.js\";\n/**\n * @name hoursToMilliseconds\n * @category  Conversion Helpers\n * @summary Convert hours to milliseconds.\n *\n * @description\n * Convert a number of hours to a full number of milliseconds.\n *\n * @param {number} hours - number of hours to be converted\n *\n * @returns {number} the number of hours converted to milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 hours to milliseconds:\n * const result = hoursToMilliseconds(2)\n * //=> 7200000\n */\n\nexport default function hoursToMilliseconds(hours) {\n  requiredArgs(1, arguments);\n  return Math.floor(hours * millisecondsInHour);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { minutesInHour } from \"../constants/index.js\";\n/**\n * @name hoursToMinutes\n * @category Conversion Helpers\n * @summary Convert hours to minutes.\n *\n * @description\n * Convert a number of hours to a full number of minutes.\n *\n * @param {number} hours - number of hours to be converted\n *\n * @returns {number} the number of hours converted in minutes\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 hours to minutes:\n * const result = hoursToMinutes(2)\n * //=> 120\n */\n\nexport default function hoursToMinutes(hours) {\n  requiredArgs(1, arguments);\n  return Math.floor(hours * minutesInHour);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { secondsInHour } from \"../constants/index.js\";\n/**\n * @name hoursToSeconds\n * @category Conversion Helpers\n * @summary Convert hours to seconds.\n *\n * @description\n * Convert a number of hours to a full number of seconds.\n *\n * @param {number} hours - number of hours to be converted\n *\n * @returns {number} the number of hours converted in seconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 hours to seconds:\n * const result = hoursToSeconds(2)\n * //=> 7200\n */\n\nexport default function hoursToSeconds(hours) {\n  requiredArgs(1, arguments);\n  return Math.floor(hours * secondsInHour);\n}", "import compareAsc from \"../compareAsc/index.js\";\nimport add from \"../add/index.js\";\nimport differenceInDays from \"../differenceInDays/index.js\";\nimport differenceInHours from \"../differenceInHours/index.js\";\nimport differenceInMinutes from \"../differenceInMinutes/index.js\";\nimport differenceInMonths from \"../differenceInMonths/index.js\";\nimport differenceInSeconds from \"../differenceInSeconds/index.js\";\nimport differenceInYears from \"../differenceInYears/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name intervalToDuration\n * @category Common Helpers\n * @summary Convert interval to duration\n *\n * @description\n * Convert a interval object to a duration object.\n *\n * @param {Interval} interval - the interval to convert to duration\n *\n * @returns {Duration} The duration Object\n * @throws {TypeError} Requires 2 arguments\n * @throws {RangeError} `start` must not be Invalid Date\n * @throws {RangeError} `end` must not be Invalid Date\n *\n * @example\n * // Get the duration between January 15, 1929 and April 4, 1968.\n * intervalToDuration({\n *   start: new Date(1929, 0, 15, 12, 0, 0),\n *   end: new Date(1968, 3, 4, 19, 5, 0)\n * })\n * // => { years: 39, months: 2, days: 20, hours: 7, minutes: 5, seconds: 0 }\n */\n\nexport default function intervalToDuration(interval) {\n  requiredArgs(1, arguments);\n  var start = toDate(interval.start);\n  var end = toDate(interval.end);\n  if (isNaN(start.getTime())) throw new RangeError('Start Date is invalid');\n  if (isNaN(end.getTime())) throw new RangeError('End Date is invalid');\n  var duration = {};\n  duration.years = Math.abs(differenceInYears(end, start));\n  var sign = compareAsc(end, start);\n  var remainingMonths = add(start, {\n    years: sign * duration.years\n  });\n  duration.months = Math.abs(differenceInMonths(end, remainingMonths));\n  var remainingDays = add(remainingMonths, {\n    months: sign * duration.months\n  });\n  duration.days = Math.abs(differenceInDays(end, remainingDays));\n  var remainingHours = add(remainingDays, {\n    days: sign * duration.days\n  });\n  duration.hours = Math.abs(differenceInHours(end, remainingHours));\n  var remainingMinutes = add(remainingHours, {\n    hours: sign * duration.hours\n  });\n  duration.minutes = Math.abs(differenceInMinutes(end, remainingMinutes));\n  var remainingSeconds = add(remainingMinutes, {\n    minutes: sign * duration.minutes\n  });\n  duration.seconds = Math.abs(differenceInSeconds(end, remainingSeconds));\n  return duration;\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name intlFormat\n * @category Common Helpers\n * @summary  Format the date with Intl.DateTimeFormat (https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat).\n *\n * @description\n * Return the formatted date string in the given format.\n * The method uses [`Intl.DateTimeFormat`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat) inside.\n * formatOptions are the same as [`Intl.DateTimeFormat` options](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat#using_options)\n *\n * > ⚠️ Please note that before Node version 13.0.0, only the locale data for en-US is available by default.\n *\n * @param {Date|Number} argument - the original date.\n * @param {Object} [formatOptions] - an object with options.\n * @param {'lookup'|'best fit'} [formatOptions.localeMatcher='best fit'] - locale selection algorithm.\n * @param {'narrow'|'short'|'long'} [formatOptions.weekday] - representation the days of the week.\n * @param {'narrow'|'short'|'long'} [formatOptions.era] - representation of eras.\n * @param {'numeric'|'2-digit'} [formatOptions.year] - representation of years.\n * @param {'numeric'|'2-digit'|'narrow'|'short'|'long'} [formatOptions.month='numeric'] - representation of month.\n * @param {'numeric'|'2-digit'} [formatOptions.day='numeric'] - representation of day.\n * @param {'numeric'|'2-digit'} [formatOptions.hour='numeric'] - representation of hours.\n * @param {'numeric'|'2-digit'} [formatOptions.minute] - representation of minutes.\n * @param {'numeric'|'2-digit'} [formatOptions.second] - representation of seconds.\n * @param {'short'|'long'} [formatOptions.timeZoneName] - representation of names of time zones.\n * @param {'basic'|'best fit'} [formatOptions.formatMatcher='best fit'] - format selection algorithm.\n * @param {Boolean} [formatOptions.hour12] - determines whether to use 12-hour time format.\n * @param {String} [formatOptions.timeZone] - the time zone to use.\n * @param {Object} [localeOptions] - an object with locale.\n * @param {String|String[]} [localeOptions.locale] - the locale code\n * @returns {String} the formatted date string.\n * @throws {TypeError} 1 argument required.\n * @throws {RangeError} `date` must not be Invalid Date\n *\n * @example\n * // Represent 10 October 2019 in German.\n * // Convert the date with format's options and locale's options.\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *      weekday: 'long',\n *      year: 'numeric',\n *      month: 'long',\n *      day: 'numeric',\n *    }, {\n *      locale: 'de-DE',\n *  })\n * //=> Freitag, 4. Oktober 2019\n *\n * @example\n * // Represent 10 October 2019.\n * // Convert the date with format's options.\n * const result = intlFormat.default(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *      year: 'numeric',\n *      month: 'numeric',\n *      day: 'numeric',\n *      hour: 'numeric',\n *  })\n * //=> 10/4/2019, 12 PM\n *\n * @example\n * // Represent 10 October 2019 in Korean.\n * // Convert the date with locale's options.\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *      locale: 'ko-KR',\n *  })\n * //=> 2019. 10. 4.\n *\n * @example\n * // Represent 10 October 2019 in middle-endian format:\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456))\n * //=> 10/4/2019\n */\nexport default function intlFormat(date, formatOrLocale, localeOptions) {\n  var _localeOptions;\n\n  requiredArgs(1, arguments);\n  var formatOptions;\n\n  if (isFormatOptions(formatOrLocale)) {\n    formatOptions = formatOrLocale;\n  } else {\n    localeOptions = formatOrLocale;\n  }\n\n  return new Intl.DateTimeFormat((_localeOptions = localeOptions) === null || _localeOptions === void 0 ? void 0 : _localeOptions.locale, formatOptions).format(date);\n}\n\nfunction isFormatOptions(opts) {\n  return opts !== undefined && !('locale' in opts);\n}", "import { secondsInDay, secondsInHour, secondsInMinute, secondsInMonth, secondsInQuarter, secondsInWeek, secondsInYear } from \"../constants/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport differenceInCalendarMonths from \"../differenceInCalendarMonths/index.js\";\nimport differenceInCalendarQuarters from \"../differenceInCalendarQuarters/index.js\";\nimport differenceInCalendarWeeks from \"../differenceInCalendarWeeks/index.js\";\nimport differenceInCalendarYears from \"../differenceInCalendarYears/index.js\";\nimport differenceInHours from \"../differenceInHours/index.js\";\nimport differenceInMinutes from \"../differenceInMinutes/index.js\";\nimport differenceInSeconds from \"../differenceInSeconds/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name intlFormatDistance\n * @category Common Helpers\n * @summary Formats distance between two dates in a human-readable format\n * @description\n * The function calculates the difference between two dates and formats it as a human-readable string.\n *\n * The function will pick the most appropriate unit depending on the distance between dates. For example, if the distance is a few hours, it might return `x hours`. If the distance is a few months, it might return `x months`.\n *\n * You can also specify a unit to force using it regardless of the distance to get a result like `123456 hours`.\n *\n * See the table below for the unit picking logic:\n *\n * | Distance between dates | Result (past)  | Result (future) |\n * | ---------------------- | -------------- | --------------- |\n * | 0 seconds              | now            | now             |\n * | 1-59 seconds           | X seconds ago  | in X seconds    |\n * | 1-59 minutes           | X minutes ago  | in X minutes    |\n * | 1-23 hours             | X hours ago    | in X hours      |\n * | 1 day                  | yesterday      | tomorrow        |\n * | 2-6 days               | X days ago     | in X days       |\n * | 7 days                 | last week      | next week       |\n * | 8 days-1 month         | X weeks ago    | in X weeks      |\n * | 1 month                | last month     | next month      |\n * | 2-3 months             | X months ago   | in X months     |\n * | 1 quarter              | last quarter   | next quarter    |\n * | 2-3 quarters           | X quarters ago | in X quarters   |\n * | 1 year                 | last year      | next year       |\n * | 2+ years               | X years ago    | in X years      |\n *\n * @param {Date|Number} date - the date\n * @param {Date|Number} baseDate - the date to compare with.\n * @param {Object} [options] - an object with options.\n * @param {String} [options.unit] - formats the distance with the given unit ('year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second').\n * @param {String|String[]} [options.locale] - the locale to use.\n * @param {String} [options.localeMatcher='best fit'] - the locale matching algorithm to use. Other value: 'lookup'.\n * See MDN for details [Locale identification and negotiation](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl#locale_identification_and_negotiation)\n * @param {String} [options.numeric='auto'] - the output message format. The values are 'auto' (e.g. `yesterday`), 'always'(e.g. `1 day ago`).\n * @param {String} [options.style='long'] - the length of the result. The values are: 'long' (e.g. `1 month`), 'short' (e.g. 'in 1 mo.'), 'narrow' (e.g. 'in 1 mo.').\n * The narrow one could be similar to the short one for some locales.\n * @returns {String} the distance in words according to language-sensitive relative time formatting.\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.unit` must not be invalid Unit\n * @throws {RangeError} `options.locale` must not be invalid locale\n * @throws {RangeError} `options.localeMatcher` must not be invalid localeMatcher\n * @throws {RangeError} `options.numeric` must not be invalid numeric\n * @throws {RangeError} `options.style` must not be invalid style\n *\n * @example\n * // What is the distance between the dates when the fist date is after the second?\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0)\n * )\n * //=> 'in 1 hour'\n *\n * // What is the distance between the dates when the fist date is before the second?\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0)\n * )\n * //=> '1 hour ago'\n *\n * @example\n * // Use the unit option to force the function to output the result in quarters. Without setting it, the example would return \"next year\"\n * intlFormatDistance(\n *   new Date(1987, 6, 4, 10, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   { unit: 'quarter' }\n * )\n * //=> 'in 5 quarters'\n *\n * @example\n * // Use the locale option to get the result in Spanish. Without setting it, the example would return \"in 1 hour\".\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   { locale: 'es' }\n * )\n * //=> 'dentro de 1 hora'\n *\n * @example\n * // Use the numeric option to force the function to use numeric values. Without setting it, the example would return \"tomorrow\".\n * intlFormatDistance(\n *   new Date(1986, 3, 5, 11, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   { numeric: 'always' }\n * )\n * //=> 'in 1 day'\n *\n * @example\n * // Use the style option to force the function to use short values. Without setting it, the example would return \"in 2 years\".\n * intlFormatDistance(\n *   new Date(1988, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   { style: 'short' }\n * )\n * //=> 'in 2 yr'\n */\nexport default function intlFormatDistance(date, baseDate, options) {\n  requiredArgs(2, arguments);\n  var value = 0;\n  var unit;\n  var dateLeft = toDate(date);\n  var dateRight = toDate(baseDate);\n\n  if (!(options !== null && options !== void 0 && options.unit)) {\n    // Get the unit based on diffInSeconds calculations if no unit is specified\n    var diffInSeconds = differenceInSeconds(dateLeft, dateRight); // The smallest unit\n\n    if (Math.abs(diffInSeconds) < secondsInMinute) {\n      value = differenceInSeconds(dateLeft, dateRight);\n      unit = 'second';\n    } else if (Math.abs(diffInSeconds) < secondsInHour) {\n      value = differenceInMinutes(dateLeft, dateRight);\n      unit = 'minute';\n    } else if (Math.abs(diffInSeconds) < secondsInDay && Math.abs(differenceInCalendarDays(dateLeft, dateRight)) < 1) {\n      value = differenceInHours(dateLeft, dateRight);\n      unit = 'hour';\n    } else if (Math.abs(diffInSeconds) < secondsInWeek && (value = differenceInCalendarDays(dateLeft, dateRight)) && Math.abs(value) < 7) {\n      unit = 'day';\n    } else if (Math.abs(diffInSeconds) < secondsInMonth) {\n      value = differenceInCalendarWeeks(dateLeft, dateRight);\n      unit = 'week';\n    } else if (Math.abs(diffInSeconds) < secondsInQuarter) {\n      value = differenceInCalendarMonths(dateLeft, dateRight);\n      unit = 'month';\n    } else if (Math.abs(diffInSeconds) < secondsInYear) {\n      if (differenceInCalendarQuarters(dateLeft, dateRight) < 4) {\n        // To filter out cases that are less than a year but match 4 quarters\n        value = differenceInCalendarQuarters(dateLeft, dateRight);\n        unit = 'quarter';\n      } else {\n        value = differenceInCalendarYears(dateLeft, dateRight);\n        unit = 'year';\n      }\n    } else {\n      value = differenceInCalendarYears(dateLeft, dateRight);\n      unit = 'year';\n    }\n  } else {\n    // Get the value if unit is specified\n    unit = options === null || options === void 0 ? void 0 : options.unit;\n\n    if (unit === 'second') {\n      value = differenceInSeconds(dateLeft, dateRight);\n    } else if (unit === 'minute') {\n      value = differenceInMinutes(dateLeft, dateRight);\n    } else if (unit === 'hour') {\n      value = differenceInHours(dateLeft, dateRight);\n    } else if (unit === 'day') {\n      value = differenceInCalendarDays(dateLeft, dateRight);\n    } else if (unit === 'week') {\n      value = differenceInCalendarWeeks(dateLeft, dateRight);\n    } else if (unit === 'month') {\n      value = differenceInCalendarMonths(dateLeft, dateRight);\n    } else if (unit === 'quarter') {\n      value = differenceInCalendarQuarters(dateLeft, dateRight);\n    } else if (unit === 'year') {\n      value = differenceInCalendarYears(dateLeft, dateRight);\n    }\n  }\n\n  var rtf = new Intl.RelativeTimeFormat(options === null || options === void 0 ? void 0 : options.locale, {\n    localeMatcher: options === null || options === void 0 ? void 0 : options.localeMatcher,\n    numeric: (options === null || options === void 0 ? void 0 : options.numeric) || 'auto',\n    style: options === null || options === void 0 ? void 0 : options.style\n  });\n  return rtf.format(value, unit);\n}", "/**\n * @name isExists\n * @category Common Helpers\n * @summary Is the given date exists?\n *\n * @description\n * Checks if the given arguments convert to an existing date.\n *\n * @param {Number} year of the date to check\n * @param {Number} month of the date to check\n * @param {Number} day of the date to check\n * @returns {<PERSON><PERSON>an} the date exists\n * @throws {TypeError} 3 arguments required\n *\n * @example\n * // For the valid date:\n * const result = isExists(2018, 0, 31)\n * //=> true\n *\n * @example\n * // For the invalid date:\n * const result = isExists(2018, 1, 31)\n * //=> false\n */\nexport default function isExists(year, month, day) {\n  if (arguments.length < 3) {\n    throw new TypeError('3 argument required, but only ' + arguments.length + ' present');\n  }\n\n  var date = new Date(year, month, day);\n  return date.getFullYear() === year && date.getMonth() === month && date.getDate() === day;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isFirstDayOfMonth\n * @category Month Helpers\n * @summary Is the given date the first day of a month?\n *\n * @description\n * Is the given date the first day of a month?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is the first day of a month\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 1 September 2014 the first day of a month?\n * const result = isFirstDayOfMonth(new Date(2014, 8, 1))\n * //=> true\n */\n\nexport default function isFirstDayOfMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDate() === 1;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isFriday\n * @category Weekday Helpers\n * @summary Is the given date Friday?\n *\n * @description\n * Is the given date Friday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Friday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 26 September 2014 Friday?\n * const result = isFriday(new Date(2014, 8, 26))\n * //=> true\n */\n\nexport default function isFriday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 5;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isFuture\n * @category Common Helpers\n * @summary Is the given date in the future?\n * @pure false\n *\n * @description\n * Is the given date in the future?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in the future\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 31 December 2014 in the future?\n * const result = isFuture(new Date(2014, 11, 31))\n * //=> true\n */\n\nexport default function isFuture(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getTime() > Date.now();\n}", "import parse from \"../parse/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name isMatch\n * @category Common Helpers\n * @summary validates the date string against given formats\n *\n * @description\n * Return the true if given date is string correct against the given format else\n * will return false.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters in the format string wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the format string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 5 below the table).\n *\n * Not all tokens are compatible. Combinations that don't make sense or could lead to bugs are prohibited\n * and will throw `RangeError`. For example usage of 24-hour format token with AM/PM token will throw an exception:\n *\n * ```javascript\n * isMatch('23 AM', 'HH a')\n * //=> RangeError: The format string mustn't contain `HH` and `a` at the same time\n * ```\n *\n * See the compatibility table: https://docs.google.com/spreadsheets/d/e/2PACX-1vQOPU3xUhplll6dyoMmVUXHKl_8CRDs6_ueLmex3SoqwhuolkuN3O05l4rqx5h1dKX8eb46Ul-CCSrq/pubhtml?gid=0&single=true\n *\n * Accepted format string patterns:\n * | Unit                            |Prior| Pattern | Result examples                   | Notes |\n * |---------------------------------|-----|---------|-----------------------------------|-------|\n * | Era                             | 140 | G..GGG  | AD, BC                            |       |\n * |                                 |     | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 |     | GGGGG   | A, B                              |       |\n * | Calendar year                   | 130 | y       | 44, 1, 1900, 2017, 9999           | 4     |\n * |                                 |     | yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | yy      | 44, 01, 00, 17                    | 4     |\n * |                                 |     | yyy     | 044, 001, 123, 999                | 4     |\n * |                                 |     | yyyy    | 0044, 0001, 1900, 2017            | 4     |\n * |                                 |     | yyyyy   | ...                               | 2,4   |\n * | Local week-numbering year       | 130 | Y       | 44, 1, 1900, 2017, 9000           | 4     |\n * |                                 |     | Yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | YY      | 44, 01, 00, 17                    | 4,6   |\n * |                                 |     | YYY     | 044, 001, 123, 999                | 4     |\n * |                                 |     | YYYY    | 0044, 0001, 1900, 2017            | 4,6   |\n * |                                 |     | YYYYY   | ...                               | 2,4   |\n * | ISO week-numbering year         | 130 | R       | -43, 1, 1900, 2017, 9999, -9999   | 4,5   |\n * |                                 |     | RR      | -43, 01, 00, 17                   | 4,5   |\n * |                                 |     | RRR     | -043, 001, 123, 999, -999         | 4,5   |\n * |                                 |     | RRRR    | -0043, 0001, 2017, 9999, -9999    | 4,5   |\n * |                                 |     | RRRRR   | ...                               | 2,4,5 |\n * | Extended year                   | 130 | u       | -43, 1, 1900, 2017, 9999, -999    | 4     |\n * |                                 |     | uu      | -43, 01, 99, -99                  | 4     |\n * |                                 |     | uuu     | -043, 001, 123, 999, -999         | 4     |\n * |                                 |     | uuuu    | -0043, 0001, 2017, 9999, -9999    | 4     |\n * |                                 |     | uuuuu   | ...                               | 2,4   |\n * | Quarter (formatting)            | 120 | Q       | 1, 2, 3, 4                        |       |\n * |                                 |     | Qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | QQ      | 01, 02, 03, 04                    |       |\n * |                                 |     | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | 120 | q       | 1, 2, 3, 4                        |       |\n * |                                 |     | qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | qq      | 01, 02, 03, 04                    |       |\n * |                                 |     | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | qqqqq   | 1, 2, 3, 4                        | 3     |\n * | Month (formatting)              | 110 | M       | 1, 2, ..., 12                     |       |\n * |                                 |     | Mo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | MM      | 01, 02, ..., 12                   |       |\n * |                                 |     | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | MMMM    | January, February, ..., December  | 2     |\n * |                                 |     | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | 110 | L       | 1, 2, ..., 12                     |       |\n * |                                 |     | Lo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | LL      | 01, 02, ..., 12                   |       |\n * |                                 |     | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | LLLL    | January, February, ..., December  | 2     |\n * |                                 |     | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | 100 | w       | 1, 2, ..., 53                     |       |\n * |                                 |     | wo      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | 100 | I       | 1, 2, ..., 53                     | 5     |\n * |                                 |     | Io      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | II      | 01, 02, ..., 53                   | 5     |\n * | Day of month                    |  90 | d       | 1, 2, ..., 31                     |       |\n * |                                 |     | do      | 1st, 2nd, ..., 31st               | 5     |\n * |                                 |     | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     |  90 | D       | 1, 2, ..., 365, 366               | 7     |\n * |                                 |     | Do      | 1st, 2nd, ..., 365th, 366th       | 5     |\n * |                                 |     | DD      | 01, 02, ..., 365, 366             | 7     |\n * |                                 |     | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 |     | DDDD    | ...                               | 2     |\n * | Day of week (formatting)        |  90 | E..EEE  | Mon, Tue, Wed, ..., Su            |       |\n * |                                 |     | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 |     | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    |  90 | i       | 1, 2, 3, ..., 7                   | 5     |\n * |                                 |     | io      | 1st, 2nd, ..., 7th                | 5     |\n * |                                 |     | ii      | 01, 02, ..., 07                   | 5     |\n * |                                 |     | iii     | Mon, Tue, Wed, ..., Su            | 5     |\n * |                                 |     | iiii    | Monday, Tuesday, ..., Sunday      | 2,5   |\n * |                                 |     | iiiii   | M, T, W, T, F, S, S               | 5     |\n * |                                 |     | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 5     |\n * | Local day of week (formatting)  |  90 | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | eo      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | ee      | 02, 03, ..., 01                   |       |\n * |                                 |     | eee     | Mon, Tue, Wed, ..., Su            |       |\n * |                                 |     | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 |     | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) |  90 | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | co      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | cc      | 02, 03, ..., 01                   |       |\n * |                                 |     | ccc     | Mon, Tue, Wed, ..., Su            |       |\n * |                                 |     | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 |     | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          |  80 | a..aaa  | AM, PM                            |       |\n * |                                 |     | aaaa    | a.m., p.m.                        | 2     |\n * |                                 |     | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          |  80 | b..bbb  | AM, PM, noon, midnight            |       |\n * |                                 |     | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 |     | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             |  80 | B..BBB  | at night, in the morning, ...     |       |\n * |                                 |     | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 |     | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     |  70 | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 |     | ho      | 1st, 2nd, ..., 11th, 12th         | 5     |\n * |                                 |     | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     |  70 | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 |     | Ho      | 0th, 1st, 2nd, ..., 23rd          | 5     |\n * |                                 |     | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     |  70 | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 |     | Ko      | 1st, 2nd, ..., 11th, 0th          | 5     |\n * |                                 |     | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     |  70 | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 |     | ko      | 24th, 1st, 2nd, ..., 23rd         | 5     |\n * |                                 |     | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          |  60 | m       | 0, 1, ..., 59                     |       |\n * |                                 |     | mo      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | mm      | 00, 01, ..., 59                   |       |\n * | Second                          |  50 | s       | 0, 1, ..., 59                     |       |\n * |                                 |     | so      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | ss      | 00, 01, ..., 59                   |       |\n * | Seconds timestamp               |  40 | t       | 512969520                         |       |\n * |                                 |     | tt      | ...                               | 2     |\n * | Fraction of second              |  30 | S       | 0, 1, ..., 9                      |       |\n * |                                 |     | SS      | 00, 01, ..., 99                   |       |\n * |                                 |     | SSS     | 000, 001, ..., 999                |       |\n * |                                 |     | SSSS    | ...                               | 2     |\n * | Milliseconds timestamp          |  20 | T       | 512969520900                      |       |\n * |                                 |     | TT      | ...                               | 2     |\n * | Timezone (ISO-8601 w/ Z)        |  10 | X       | -08, +0530, Z                     |       |\n * |                                 |     | XX      | -0800, +0530, Z                   |       |\n * |                                 |     | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 |     | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 |     | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       |  10 | x       | -08, +0530, +00                   |       |\n * |                                 |     | xx      | -0800, +0530, +0000               |       |\n * |                                 |     | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 |     | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 |     | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Long localized date             |  NA | P       | 05/29/1453                        | 5,8   |\n * |                                 |     | PP      | May 29, 1453                      |       |\n * |                                 |     | PPP     | May 29th, 1453                    |       |\n * |                                 |     | PPPP    | Sunday, May 29th, 1453            | 2,5,8 |\n * | Long localized time             |  NA | p       | 12:00 AM                          | 5,8   |\n * |                                 |     | pp      | 12:00:00 AM                       |       |\n * | Combination of date and time    |  NA | Pp      | 05/29/1453, 12:00 AM              |       |\n * |                                 |     | PPpp    | May 29, 1453, 12:00:00 AM         |       |\n * |                                 |     | PPPpp   | May 29th, 1453 at ...             |       |\n * |                                 |     | PPPPpp  | Sunday, May 29th, 1453 at ...     | 2,5,8 |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular.\n *    In `format` function, they will produce different result:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n *    `isMatch` will try to match both formatting and stand-alone units interchangably.\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table:\n *    - for numerical units (`yyyyyyyy`) `isMatch` will try to match a number\n *      as wide as the sequence\n *    - for text units (`MMMMMMMM`) `isMatch` will try to match the widest variation of the unit.\n *      These variations are marked with \"2\" in the last column of the table.\n *\n * 3. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 4. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` will try to guess the century of two digit year by proximity with `referenceDate`:\n *\n *    `isMatch('50', 'yy') //=> true`\n *\n *    `isMatch('75', 'yy') //=> true`\n *\n *    while `uu` will use the year as is:\n *\n *    `isMatch('50', 'uu') //=> true`\n *\n *    `isMatch('75', 'uu') //=> true`\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [setISOWeekYear]{@link https://date-fns.org/docs/setISOWeekYear}\n *    and [setWeekYear]{@link https://date-fns.org/docs/setWeekYear}).\n *\n * 5. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 6. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 7. `D` and `DD` tokens represent days of the year but they are ofthen confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 8. `P+` tokens do not have a defined priority since they are merely aliases to other tokens based\n *    on the given locale.\n *\n *    using `en-US` locale: `P` => `MM/dd/yyyy`\n *    using `en-US` locale: `p` => `hh:mm a`\n *    using `pt-BR` locale: `P` => `dd/MM/yyyy`\n *    using `pt-BR` locale: `p` => `HH:mm`\n *\n * Values will be checked in the descending order of its unit's priority.\n * Units of an equal priority overwrite each other in the order of appearance.\n *\n * If no values of higher priority are matched (e.g. when matching string 'January 1st' without a year),\n * the values will be taken from today's using `new Date()` date which works as a context of parsing.\n *\n * The result may vary by locale.\n *\n * If `formatString` matches with `dateString` but does not provides tokens, `referenceDate` will be returned.\n *\n *\n *\n * @param {String} dateString - the date string to verify\n * @param {String} formatString - the string of tokens\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [options.firstWeekContainsDate=1] - the day of January, which is always in the first week of the year\n * @param {Boolean} [options.useAdditionalWeekYearTokens=false] - if true, allows usage of the week-numbering year tokens `YY` and `YYYY`;\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @param {Boolean} [options.useAdditionalDayOfYearTokens=false] - if true, allows usage of the day of year tokens `D` and `DD`;\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @returns {Boolean}\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n * @throws {RangeError} `options.locale` must contain `match` property\n * @throws {RangeError} use `yyyy` instead of `YYYY` for formatting years; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws {RangeError} use `yy` instead of `YY` for formatting years; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws {RangeError} use `d` instead of `D` for formatting days of the month; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws {RangeError} use `dd` instead of `DD` for formatting days of the month; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws {RangeError} format string contains an unescaped latin alphabet character\n *\n * @example\n * // Match 11 February 2014 from middle-endian format:\n * const result = isMatch('02/11/2014', 'MM/dd/yyyy')\n * //=> true\n *\n * @example\n * // Match 28th of February in Esperanto locale in the context of 2010 year:\n * import eo from 'date-fns/locale/eo'\n * const result = isMatch('28-a de februaro', \"do 'de' MMMM\", {\n *   locale: eo\n * })\n * //=> true\n */\nexport default function isMatch(dateString, formatString, options) {\n  requiredArgs(2, arguments);\n  return isValid(parse(dateString, formatString, new Date(), options));\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isMonday\n * @category Weekday Helpers\n * @summary Is the given date Monday?\n *\n * @description\n * Is the given date Monday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Monday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 22 September 2014 Monday?\n * const result = isMonday(new Date(2014, 8, 22))\n * //=> true\n */\n\nexport default function isMonday(date) {\n  requiredArgs(1, arguments);\n  return toDate(date).getDay() === 1;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isPast\n * @category Common Helpers\n * @summary Is the given date in the past?\n * @pure false\n *\n * @description\n * Is the given date in the past?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in the past\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 2 July 2014 in the past?\n * const result = isPast(new Date(2014, 6, 2))\n * //=> true\n */\n\nexport default function isPast(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getTime() < Date.now();\n}", "import startOfWeek from \"../startOfWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Boolean} the dates are in the same week (and month and year)\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */\nexport default function isSameWeek(dirtyDateLeft, dirtyDateRight, options) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfWeek = startOfWeek(dirtyDateLeft, options);\n  var dateRightStartOfWeek = startOfWeek(dirtyDateRight, options);\n  return dateLeftStartOfWeek.getTime() === dateRightStartOfWeek.getTime();\n}", "import isSameWeek from \"../isSameWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameISOWeek\n * @category ISO Week Helpers\n * @summary Are the given dates in the same ISO week (and year)?\n *\n * @description\n * Are the given dates in the same ISO week (and year)?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same ISO week (and year)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 1 September 2014 and 7 September 2014 in the same ISO week?\n * const result = isSameISOWeek(new Date(2014, 8, 1), new Date(2014, 8, 7))\n * //=> true\n *\n * @example\n * // Are 1 September 2014 and 1 September 2015 in the same ISO week?\n * const result = isSameISOWeek(new Date(2014, 8, 1), new Date(2015, 8, 1))\n * //=> false\n */\n\nexport default function isSameISOWeek(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  return isSameWeek(dirtyDateLeft, dirtyDateRight, {\n    weekStartsOn: 1\n  });\n}", "import startOfISOWeekYear from \"../startOfISOWeekYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Are the given dates in the same ISO week-numbering year?\n *\n * @description\n * Are the given dates in the same ISO week-numbering year?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same ISO week-numbering year\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 29 December 2003 and 2 January 2005 in the same ISO week-numbering year?\n * const result = isSameISOWeekYear(new Date(2003, 11, 29), new Date(2005, 0, 2))\n * //=> true\n */\n\nexport default function isSameISOWeekYear(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfYear = startOfISOWeekYear(dirtyDateLeft);\n  var dateRightStartOfYear = startOfISOWeekYear(dirtyDateRight);\n  return dateLeftStartOfYear.getTime() === dateRightStartOfYear.getTime();\n}", "import startOfMinute from \"../startOfMinute/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameMinute\n * @category Minute Helpers\n * @summary Are the given dates in the same minute (and hour and day)?\n *\n * @description\n * Are the given dates in the same minute (and hour and day)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same minute (and hour and day)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 4 September 2014 06:30:00 and 4 September 2014 06:30:15 in the same minute?\n * const result = isSameMinute(\n *   new Date(2014, 8, 4, 6, 30),\n *   new Date(2014, 8, 4, 6, 30, 15)\n * )\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:30:00 and 5 September 2014 06:30:00 in the same minute?\n * const result = isSameMinute(\n *   new Date(2014, 8, 4, 6, 30),\n *   new Date(2014, 8, 5, 6, 30)\n * )\n * //=> false\n */\n\nexport default function isSameMinute(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfMinute = startOfMinute(dirtyDateLeft);\n  var dateRightStartOfMinute = startOfMinute(dirtyDateRight);\n  return dateLeftStartOfMinute.getTime() === dateRightStartOfMinute.getTime();\n}", "import startOfQuarter from \"../startOfQuarter/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameQuarter\n * @category Quarter Helpers\n * @summary Are the given dates in the same quarter (and year)?\n *\n * @description\n * Are the given dates in the same quarter (and year)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same quarter (and year)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 1 January 2014 and 8 March 2014 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2014, 2, 8))\n * //=> true\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */\n\nexport default function isSameQuarter(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfQuarter = startOfQuarter(dirtyDateLeft);\n  var dateRightStartOfQuarter = startOfQuarter(dirtyDateRight);\n  return dateLeftStartOfQuarter.getTime() === dateRightStartOfQuarter.getTime();\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name startOfSecond\n * @category Second Helpers\n * @summary Return the start of a second for the given date.\n *\n * @description\n * Return the start of a second for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the start of a second\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The start of a second for 1 December 2014 22:15:45.400:\n * const result = startOfSecond(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:45.000\n */\n\nexport default function startOfSecond(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  date.setMilliseconds(0);\n  return date;\n}", "import startOfSecond from \"../startOfSecond/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameSecond\n * @category Second Helpers\n * @summary Are the given dates in the same second (and hour and day)?\n *\n * @description\n * Are the given dates in the same second (and hour and day)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same second (and hour and day)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 4 September 2014 06:30:15.000 and 4 September 2014 06:30.15.500 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 30, 15),\n *   new Date(2014, 8, 4, 6, 30, 15, 500)\n * )\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:00:15.000 and 4 September 2014 06:01.15.000 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 0, 15),\n *   new Date(2014, 8, 4, 6, 1, 15)\n * )\n * //=> false\n *\n * @example\n * // Are 4 September 2014 06:00:15.000 and 5 September 2014 06:00.15.000 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 0, 15),\n *   new Date(2014, 8, 5, 6, 0, 15)\n * )\n * //=> false\n */\n\nexport default function isSameSecond(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfSecond = startOfSecond(dirtyDateLeft);\n  var dateRightStartOfSecond = startOfSecond(dirtyDateRight);\n  return dateLeftStartOfSecond.getTime() === dateRightStartOfSecond.getTime();\n}", "import isSameHour from \"../isSameHour/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisHour\n * @category Hour Helpers\n * @summary Is the given date in the same hour as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same hour as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this hour\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:00:00 in this hour?\n * const result = isThisHour(new Date(2014, 8, 25, 18))\n * //=> true\n */\n\nexport default function isThisHour(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameHour(Date.now(), dirtyDate);\n}", "import isSameISOWeek from \"../isSameISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisISOWeek\n * @category ISO Week Helpers\n * @summary Is the given date in the same ISO week as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same ISO week as the current date?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 25 September 2014, is 22 September 2014 in this ISO week?\n * const result = isThisISOWeek(new Date(2014, 8, 22))\n * //=> true\n */\n\nexport default function isThisISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameISOWeek(dirtyDate, Date.now());\n}", "import isSameMinute from \"../isSameMinute/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisMinute\n * @category Minute Helpers\n * @summary Is the given date in the same minute as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same minute as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this minute\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:30:00 in this minute?\n * const result = isThisMinute(new Date(2014, 8, 25, 18, 30))\n * //=> true\n */\n\nexport default function isThisMinute(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameMinute(Date.now(), dirtyDate);\n}", "import isSameMonth from \"../isSameMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisMonth\n * @category Month Helpers\n * @summary Is the given date in the same month as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same month as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this month\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 25 September 2014, is 15 September 2014 in this month?\n * const result = isThisMonth(new Date(2014, 8, 15))\n * //=> true\n */\n\nexport default function isThisMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameMonth(Date.now(), dirtyDate);\n}", "import isSameQuarter from \"../isSameQuarter/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisQuarter\n * @category Quarter Helpers\n * @summary Is the given date in the same quarter as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same quarter as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this quarter\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this quarter?\n * const result = isThisQuarter(new Date(2014, 6, 2))\n * //=> true\n */\n\nexport default function isThisQuarter(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameQuarter(Date.now(), dirtyDate);\n}", "import isSameSecond from \"../isSameSecond/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisSecond\n * @category Second Helpers\n * @summary Is the given date in the same second as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same second as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this second\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:30:15.000 in this second?\n * const result = isThisSecond(new Date(2014, 8, 25, 18, 30, 15))\n * //=> true\n */\n\nexport default function isThisSecond(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameSecond(Date.now(), dirtyDate);\n}", "import isSameWeek from \"../isSameWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name isThisWeek\n * @category Week Helpers\n * @summary Is the given date in the same week as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same week as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @param {Object} [options] - the object with options\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Boolean} the date is in this week\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n *\n * @example\n * // If today is 25 September 2014, is 21 September 2014 in this week?\n * const result = isThisWeek(new Date(2014, 8, 21))\n * //=> true\n *\n * @example\n * // If today is 25 September 2014 and week starts with Monday\n * // is 21 September 2014 in this week?\n * const result = isThisWeek(new Date(2014, 8, 21), { weekStartsOn: 1 })\n * //=> false\n */\nexport default function isThisWeek(dirtyDate, options) {\n  requiredArgs(1, arguments);\n  return isSameWeek(dirtyDate, Date.now(), options);\n}", "import isSameYear from \"../isSameYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisYear\n * @category Year Helpers\n * @summary Is the given date in the same year as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same year as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this year?\n * const result = isThisYear(new Date(2014, 6, 2))\n * //=> true\n */\n\nexport default function isThisYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameYear(dirtyDate, Date.now());\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThursday\n * @category Weekday Helpers\n * @summary Is the given date Thursday?\n *\n * @description\n * Is the given date Thursday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Thursday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 25 September 2014 Thursday?\n * const result = isThursday(new Date(2014, 8, 25))\n * //=> true\n */\n\nexport default function isThursday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 4;\n}", "import isSameDay from \"../isSameDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isToday\n * @category Day Helpers\n * @summary Is the given date today?\n * @pure false\n *\n * @description\n * Is the given date today?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is today\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 6 October 14:00:00 today?\n * const result = isToday(new Date(2014, 9, 6, 14, 0))\n * //=> true\n */\n\nexport default function isToday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameDay(dirtyDate, Date.now());\n}", "import addDays from \"../addDays/index.js\";\nimport isSameDay from \"../isSameDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isTomorrow\n * @category Day Helpers\n * @summary Is the given date tomorrow?\n * @pure false\n *\n * @description\n * Is the given date tomorrow?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is tomorrow\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 7 October 14:00:00 tomorrow?\n * const result = isTomorrow(new Date(2014, 9, 7, 14, 0))\n * //=> true\n */\n\nexport default function isTomorrow(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameDay(dirtyDate, addDays(Date.now(), 1));\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isTuesday\n * @category Weekday Helpers\n * @summary Is the given date Tuesday?\n *\n * @description\n * Is the given date Tuesday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Tuesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 23 September 2014 Tuesday?\n * const result = isTuesday(new Date(2014, 8, 23))\n * //=> true\n */\n\nexport default function isTuesday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 2;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isWednesday\n * @category Weekday Helpers\n * @summary Is the given date Wednesday?\n *\n * @description\n * Is the given date Wednesday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Wednesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 24 September 2014 Wednesday?\n * const result = isWednesday(new Date(2014, 8, 24))\n * //=> true\n */\n\nexport default function isWednesday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 3;\n}", "import addDays from \"../addDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name subDays\n * @category Day Helpers\n * @summary Subtract the specified number of days from the given date.\n *\n * @description\n * Subtract the specified number of days from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of days to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the days subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 10 days from 1 September 2014:\n * const result = subDays(new Date(2014, 8, 1), 10)\n * //=> Fri Aug 22 2014 00:00:00\n */\n\nexport default function subDays(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addDays(dirtyDate, -amount);\n}", "import isSameDay from \"../isSameDay/index.js\";\nimport subDays from \"../subDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isYesterday\n * @category Day Helpers\n * @summary Is the given date yesterday?\n * @pure false\n *\n * @description\n * Is the given date yesterday?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is yesterday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 5 October 14:00:00 yesterday?\n * const result = isYesterday(new Date(2014, 9, 5, 14, 0))\n * //=> true\n */\n\nexport default function isYesterday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameDay(dirtyDate, subDays(Date.now(), 1));\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name lastDayOfDecade\n * @category Decade Helpers\n * @summary Return the last day of a decade for the given date.\n *\n * @description\n * Return the last day of a decade for the given date.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the last day of a decade\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The last day of a decade for 21 December 2012 21:12:00:\n * const result = lastDayOfDecade(new Date(2012, 11, 21, 21, 12, 00))\n * //=> Wed Dec 31 2019 00:00:00\n */\n\nexport default function lastDayOfDecade(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var decade = 9 + Math.floor(year / 10) * 10;\n  date.setFullYear(decade + 1, 0, 0);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}", "import toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\n/**\n * @name lastDayOfWeek\n * @category Week Helpers\n * @summary Return the last day of a week for the given date.\n *\n * @description\n * Return the last day of a week for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Date} the last day of a week\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n *\n * @example\n * // The last day of a week for 2 September 2014 11:55:00:\n * const result = lastDayOfWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sat Sep 06 2014 00:00:00\n *\n * @example\n * // If the week starts on Monday, the last day of the week for 2 September 2014 11:55:00:\n * const result = lastDayOfWeek(new Date(2014, 8, 2, 11, 55, 0), { weekStartsOn: 1 })\n * //=> Sun Sep 07 2014 00:00:00\n */\n\nexport default function lastDayOfWeek(dirtyDate, options) {\n  var _ref, _ref2, _ref3, _options$weekStartsOn, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n\n  requiredArgs(1, arguments);\n  var defaultOptions = getDefaultOptions();\n  var weekStartsOn = toInteger((_ref = (_ref2 = (_ref3 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.weekStartsOn) !== null && _ref !== void 0 ? _ref : 0); // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6');\n  }\n\n  var date = toDate(dirtyDate);\n  var day = date.getDay();\n  var diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);\n  date.setHours(0, 0, 0, 0);\n  date.setDate(date.getDate() + diff);\n  return date;\n}", "import lastDayOfWeek from \"../lastDayOfWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name lastDayOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the last day of an ISO week for the given date.\n *\n * @description\n * Return the last day of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the last day of an ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The last day of an ISO week for 2 September 2014 11:55:00:\n * const result = lastDayOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Sep 07 2014 00:00:00\n */\n\nexport default function lastDayOfISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  return lastDayOfWeek(dirtyDate, {\n    weekStartsOn: 1\n  });\n}", "import getISOWeekYear from \"../getISOWeekYear/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name lastDayOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the last day of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the last day of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of an ISO week-numbering year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The last day of an ISO week-numbering year for 2 July 2005:\n * const result = lastDayOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Sun Jan 01 2006 00:00:00\n */\n\nexport default function lastDayOfISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getISOWeekYear(dirtyDate);\n  var fourthOfJanuary = new Date(0);\n  fourthOfJanuary.setFullYear(year + 1, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  var date = startOfISOWeek(fourthOfJanuary);\n  date.setDate(date.getDate() - 1);\n  return date;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name lastDayOfQuarter\n * @category Quarter Helpers\n * @summary Return the last day of a year quarter for the given date.\n *\n * @description\n * Return the last day of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {0|1|2} [options.additionalDigits=2] - passed to `toDate`. See [toDate]{@link https://date-fns.org/docs/toDate}\n * @returns {Date} the last day of a quarter\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.additionalDigits` must be 0, 1 or 2\n *\n * @example\n * // The last day of a quarter for 2 September 2014 11:55:00:\n * const result = lastDayOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\n\nexport default function lastDayOfQuarter(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var currentMonth = date.getMonth();\n  var month = currentMonth - currentMonth % 3 + 3;\n  date.setMonth(month, 0);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name lastDayOfYear\n * @category Year Helpers\n * @summary Return the last day of a year for the given date.\n *\n * @description\n * Return the last day of a year for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the last day of a year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The last day of a year for 2 September 2014 11:55:00:\n * const result = lastDayOfYear(new Date(2014, 8, 2, 11, 55, 00))\n * //=> Wed Dec 31 2014 00:00:00\n */\n\nexport default function lastDayOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  date.setFullYear(year + 1, 0, 0);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}", "import toDate from \"../toDate/index.js\";\nimport formatters from \"../_lib/format/lightFormatters/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport subMilliseconds from \"../subMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\"; // This RegExp consists of three parts separated by `|`:\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\n\nvar formattingTokensRegExp = /(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\nvar escapedStringRegExp = /^'([^]*?)'?$/;\nvar doubleQuoteRegExp = /''/g;\nvar unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n/**\n * @name lightFormat\n * @category Common Helpers\n * @summary Format the date.\n *\n * @description\n * Return the formatted date string in the given format. Unlike `format`,\n * `lightFormat` doesn't use locales and outputs date using the most popular tokens.\n *\n * > ⚠️ Please note that the `lightFormat` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n *\n * Accepted patterns:\n * | Unit                            | Pattern | Result examples                   |\n * |---------------------------------|---------|-----------------------------------|\n * | AM, PM                          | a..aaa  | AM, PM                            |\n * |                                 | aaaa    | a.m., p.m.                        |\n * |                                 | aaaaa   | a, p                              |\n * | Calendar year                   | y       | 44, 1, 1900, 2017                 |\n * |                                 | yy      | 44, 01, 00, 17                    |\n * |                                 | yyy     | 044, 001, 000, 017                |\n * |                                 | yyyy    | 0044, 0001, 1900, 2017            |\n * | Month (formatting)              | M       | 1, 2, ..., 12                     |\n * |                                 | MM      | 01, 02, ..., 12                   |\n * | Day of month                    | d       | 1, 2, ..., 31                     |\n * |                                 | dd      | 01, 02, ..., 31                   |\n * | Hour [1-12]                     | h       | 1, 2, ..., 11, 12                 |\n * |                                 | hh      | 01, 02, ..., 11, 12               |\n * | Hour [0-23]                     | H       | 0, 1, 2, ..., 23                  |\n * |                                 | HH      | 00, 01, 02, ..., 23               |\n * | Minute                          | m       | 0, 1, ..., 59                     |\n * |                                 | mm      | 00, 01, ..., 59                   |\n * | Second                          | s       | 0, 1, ..., 59                     |\n * |                                 | ss      | 00, 01, ..., 59                   |\n * | Fraction of second              | S       | 0, 1, ..., 9                      |\n * |                                 | SS      | 00, 01, ..., 99                   |\n * |                                 | SSS     | 000, 001, ..., 999                |\n * |                                 | SSSS    | ...                               |\n *\n * @param {Date|Number} date - the original date\n * @param {String} format - the string of tokens\n * @returns {String} the formatted date string\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} format string contains an unescaped latin alphabet character\n *\n * @example\n * const result = lightFormat(new Date(2014, 1, 11), 'yyyy-MM-dd')\n * //=> '2014-02-11'\n */\n\nexport default function lightFormat(dirtyDate, formatStr) {\n  requiredArgs(2, arguments);\n  var originalDate = toDate(dirtyDate);\n\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  } // Convert the date in system timezone to the same date in UTC+00:00 timezone.\n  // This ensures that when UTC functions will be implemented, locales will be compatible with them.\n  // See an issue about UTC functions: https://github.com/date-fns/date-fns/issues/376\n\n\n  var timezoneOffset = getTimezoneOffsetInMilliseconds(originalDate);\n  var utcDate = subMilliseconds(originalDate, timezoneOffset);\n  var tokens = formatStr.match(formattingTokensRegExp); // The only case when formattingTokensRegExp doesn't match the string is when it's empty\n\n  if (!tokens) return '';\n  var result = tokens.map(function (substring) {\n    // Replace two single quote characters with one single quote character\n    if (substring === \"''\") {\n      return \"'\";\n    }\n\n    var firstCharacter = substring[0];\n\n    if (firstCharacter === \"'\") {\n      return cleanEscapedString(substring);\n    }\n\n    var formatter = formatters[firstCharacter];\n\n    if (formatter) {\n      return formatter(utcDate, substring);\n    }\n\n    if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n      throw new RangeError('Format string contains an unescaped latin alphabet character `' + firstCharacter + '`');\n    }\n\n    return substring;\n  }).join('');\n  return result;\n}\n\nfunction cleanEscapedString(input) {\n  var matches = input.match(escapedStringRegExp);\n\n  if (!matches) {\n    return input;\n  }\n\n  return matches[1].replace(doubleQuoteRegExp, \"'\");\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\n// Leap year occures every 4 years, except for years that are divisable by 100 and not divisable by 400.\n// 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\nvar daysInYear = 365.2425;\n/**\n * @name milliseconds\n * @category Millisecond Helpers\n * @summary\n * Returns the number of milliseconds in the specified, years, months, weeks, days, hours, minutes and seconds.\n *\n * @description\n * Returns the number of milliseconds in the specified, years, months, weeks, days, hours, minutes and seconds.\n *\n * One years equals 365.2425 days according to the formula:\n *\n * > Leap year occures every 4 years, except for years that are divisable by 100 and not divisable by 400.\n * > 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\n *\n * One month is a year divided by 12.\n *\n * @param {Duration} duration - the object with years, months, weeks, days, hours, minutes and seconds to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {number} the milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // 1 year in milliseconds\n * milliseconds({ years: 1 })\n * //=> 31556952000\n *\n * // 3 months in milliseconds\n * milliseconds({ months: 3 })\n * //=> 7889238000\n */\n\nexport default function milliseconds(_ref) {\n  var years = _ref.years,\n      months = _ref.months,\n      weeks = _ref.weeks,\n      days = _ref.days,\n      hours = _ref.hours,\n      minutes = _ref.minutes,\n      seconds = _ref.seconds;\n  requiredArgs(1, arguments);\n  var totalDays = 0;\n  if (years) totalDays += years * daysInYear;\n  if (months) totalDays += months * (daysInYear / 12);\n  if (weeks) totalDays += weeks * 7;\n  if (days) totalDays += days;\n  var totalSeconds = totalDays * 24 * 60 * 60;\n  if (hours) totalSeconds += hours * 60 * 60;\n  if (minutes) totalSeconds += minutes * 60;\n  if (seconds) totalSeconds += seconds;\n  return Math.round(totalSeconds * 1000);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInHour } from \"../constants/index.js\";\n/**\n * @name millisecondsToHours\n * @category Conversion Helpers\n * @summary Convert milliseconds to hours.\n *\n * @description\n * Convert a number of milliseconds to a full number of hours.\n *\n * @param {number} milliseconds - number of milliseconds to be converted\n *\n * @returns {number} the number of milliseconds converted in hours\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 7200000 milliseconds to hours:\n * const result = millisecondsToHours(7200000)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToHours(7199999)\n * //=> 1\n */\n\nexport default function millisecondsToHours(milliseconds) {\n  requiredArgs(1, arguments);\n  var hours = milliseconds / millisecondsInHour;\n  return Math.floor(hours);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInMinute } from \"../constants/index.js\";\n/**\n * @name millisecondsToMinutes\n * @category Conversion Helpers\n * @summary Convert milliseconds to minutes.\n *\n * @description\n * Convert a number of milliseconds to a full number of minutes.\n *\n * @param {number} milliseconds - number of milliseconds to be converted.\n *\n * @returns {number} the number of milliseconds converted in minutes\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 60000 milliseconds to minutes:\n * const result = millisecondsToMinutes(60000)\n * //=> 1\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToMinutes(119999)\n * //=> 1\n */\n\nexport default function millisecondsToMinutes(milliseconds) {\n  requiredArgs(1, arguments);\n  var minutes = milliseconds / millisecondsInMinute;\n  return Math.floor(minutes);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInSecond } from \"../constants/index.js\";\n/**\n * @name millisecondsToSeconds\n * @category Conversion Helpers\n * @summary Convert milliseconds to seconds.\n *\n * @description\n * Convert a number of milliseconds to a full number of seconds.\n *\n * @param {number} milliseconds - number of milliseconds to be converted\n *\n * @returns {number} the number of milliseconds converted in seconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 1000 miliseconds to seconds:\n * const result = millisecondsToSeconds(1000)\n * //=> 1\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToSeconds(1999)\n * //=> 1\n */\n\nexport default function millisecondsToSeconds(milliseconds) {\n  requiredArgs(1, arguments);\n  var seconds = milliseconds / millisecondsInSecond;\n  return Math.floor(seconds);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { minutesInHour } from \"../constants/index.js\";\n/**\n * @name minutesToHours\n * @category Conversion Helpers\n * @summary Convert minutes to hours.\n *\n * @description\n * Convert a number of minutes to a full number of hours.\n *\n * @param {number} minutes - number of minutes to be converted\n *\n * @returns {number} the number of minutes converted in hours\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 140 minutes to hours:\n * const result = minutesToHours(120)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = minutesToHours(179)\n * //=> 2\n */\n\nexport default function minutesToHours(minutes) {\n  requiredArgs(1, arguments);\n  var hours = minutes / minutesInHour;\n  return Math.floor(hours);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInMinute } from \"../constants/index.js\";\n/**\n * @name minutesToMilliseconds\n * @category Conversion Helpers\n * @summary Convert minutes to milliseconds.\n *\n * @description\n * Convert a number of minutes to a full number of milliseconds.\n *\n * @param {number} minutes - number of minutes to be converted\n *\n * @returns {number} the number of minutes converted in milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 minutes to milliseconds\n * const result = minutesToMilliseconds(2)\n * //=> 120000\n */\n\nexport default function minutesToMilliseconds(minutes) {\n  requiredArgs(1, arguments);\n  return Math.floor(minutes * millisecondsInMinute);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { secondsInMinute } from \"../constants/index.js\";\n/**\n * @name minutesToSeconds\n * @category Conversion Helpers\n * @summary Convert minutes to seconds.\n *\n * @description\n * Convert a number of minutes to a full number of seconds.\n *\n * @param { number } minutes - number of minutes to be converted\n *\n * @returns {number} the number of minutes converted in seconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 minutes to seconds\n * const result = minutesToSeconds(2)\n * //=> 120\n */\n\nexport default function minutesToSeconds(minutes) {\n  requiredArgs(1, arguments);\n  return Math.floor(minutes * secondsInMinute);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInQuarter } from \"../constants/index.js\";\n/**\n * @name monthsToQuarters\n * @category Conversion Helpers\n * @summary Convert number of months to quarters.\n *\n * @description\n * Convert a number of months to a full number of quarters.\n *\n * @param {number} months - number of months to be converted.\n *\n * @returns {number} the number of months converted in quarters\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 6 months to quarters:\n * const result = monthsToQuarters(6)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = monthsToQuarters(7)\n * //=> 2\n */\n\nexport default function monthsToQuarters(months) {\n  requiredArgs(1, arguments);\n  var quarters = months / monthsInQuarter;\n  return Math.floor(quarters);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInYear } from \"../constants/index.js\";\n/**\n * @name monthsToYears\n * @category Conversion Helpers\n * @summary Convert number of months to years.\n *\n * @description\n * Convert a number of months to a full number of years.\n *\n * @param {number} months - number of months to be converted\n *\n * @returns {number} the number of months converted in years\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 36 months to years:\n * const result = monthsToYears(36)\n * //=> 3\n *\n * // It uses floor rounding:\n * const result = monthsToYears(40)\n * //=> 3\n */\n\nexport default function monthsToYears(months) {\n  requiredArgs(1, arguments);\n  var years = months / monthsInYear;\n  return Math.floor(years);\n}", "import addDays from \"../addDays/index.js\";\nimport getDay from \"../getDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name nextDay\n * @category Weekday Helpers\n * @summary When is the next day of the week?\n *\n * @description\n * When is the next day of the week? 0-6 the day of the week, 0 represents Sunday.\n *\n * @param {Date | number} date - the date to check\n * @param {Day} day - day of the week\n * @returns {Date} - the date is the next day of week\n * @throws {TypeError} - 2 arguments required\n *\n * @example\n * // When is the next Monday after Mar, 20, 2020?\n * const result = nextDay(new Date(2020, 2, 20), 1)\n * //=> Mon Mar 23 2020 00:00:00\n *\n * @example\n * // When is the next Tuesday after Mar, 21, 2020?\n * const result = nextDay(new Date(2020, 2, 21), 2)\n * //=> Tue Mar 24 2020 00:00:00\n */\n\nexport default function nextDay(date, day) {\n  requiredArgs(2, arguments);\n  var delta = day - getDay(date);\n  if (delta <= 0) delta += 7;\n  return addDays(date, delta);\n}", "import nextDay from \"../nextDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name nextFriday\n * @category Weekday Helpers\n * @summary When is the next Friday?\n *\n * @description\n * When is the next Friday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the next Friday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the next Friday after Mar, 22, 2020?\n * const result = nextFriday(new Date(2020, 2, 22))\n * //=> Fri Mar 27 2020 00:00:00\n */\n\nexport default function nextFriday(date) {\n  requiredArgs(1, arguments);\n  return nextDay(date, 5);\n}", "import nextDay from \"../nextDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name nextMonday\n * @category Weekday Helpers\n * @summary When is the next Monday?\n *\n * @description\n * When is the next Monday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the next Monday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the next Monday after Mar, 22, 2020?\n * const result = nextMonday(new Date(2020, 2, 22))\n * //=> Mon Mar 23 2020 00:00:00\n */\n\nexport default function nextMonday(date) {\n  requiredArgs(1, arguments);\n  return nextDay(date, 1);\n}", "import nextDay from \"../nextDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name nextSaturday\n * @category Weekday Helpers\n * @summary When is the next Saturday?\n *\n * @description\n * When is the next Saturday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the next Saturday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the next Saturday after Mar, 22, 2020?\n * const result = nextSaturday(new Date(2020, 2, 22))\n * //=> Sat Mar 28 2020 00:00:00\n */\n\nexport default function nextSaturday(date) {\n  requiredArgs(1, arguments);\n  return nextDay(date, 6);\n}", "import nextDay from \"../nextDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name nextSunday\n * @category Weekday Helpers\n * @summary When is the next Sunday?\n *\n * @description\n * When is the next Sunday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the next Sunday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the next Sunday after Mar, 22, 2020?\n * const result = nextSunday(new Date(2020, 2, 22))\n * //=> Sun Mar 29 2020 00:00:00\n */\n\nexport default function nextSunday(date) {\n  requiredArgs(1, arguments);\n  return nextDay(date, 0);\n}", "import nextDay from \"../nextDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name nextThursday\n * @category Weekday Helpers\n * @summary When is the next Thursday?\n *\n * @description\n * When is the next Thursday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the next Thursday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the next Thursday after Mar, 22, 2020?\n * const result = nextThursday(new Date(2020, 2, 22))\n * //=> Thur Mar 26 2020 00:00:00\n */\n\nexport default function nextThursday(date) {\n  requiredArgs(1, arguments);\n  return nextDay(date, 4);\n}", "import nextDay from \"../nextDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name nextTuesday\n * @category Weekday Helpers\n * @summary When is the next Tuesday?\n *\n * @description\n * When is the next Tuesday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the next Tuesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the next Tuesday after Mar, 22, 2020?\n * const result = nextTuesday(new Date(2020, 2, 22))\n * //=> Tue Mar 24 2020 00:00:00\n */\n\nexport default function nextTuesday(date) {\n  requiredArgs(1, arguments);\n  return nextDay(date, 2);\n}", "import nextDay from \"../nextDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name nextWednesday\n * @category Weekday Helpers\n * @summary When is the next Wednesday?\n *\n * @description\n * When is the next Wednesday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the next Wednesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the next Wednesday after Mar, 22, 2020?\n * const result = nextWednesday(new Date(2020, 2, 22))\n * //=> Wed Mar 25 2020 00:00:00\n */\n\nexport default function nextWednesday(date) {\n  requiredArgs(1, arguments);\n  return nextDay(date, 3);\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name parseJSON\n * @category Common Helpers\n * @summary Parse a JSON date string\n *\n * @description\n * Converts a complete ISO date string in UTC time, the typical format for transmitting\n * a date in JSON, to a JavaScript `Date` instance.\n *\n * This is a minimal implementation for converting dates retrieved from a JSON API to\n * a `Date` instance which can be used with other functions in the `date-fns` library.\n * The following formats are supported:\n *\n * - `2000-03-15T05:20:10.123Z`: The output of `.toISOString()` and `JSON.stringify(new Date())`\n * - `2000-03-15T05:20:10Z`: Without milliseconds\n * - `2000-03-15T05:20:10+00:00`: With a zero offset, the default JSON encoded format in some other languages\n * - `2000-03-15T05:20:10+05:45`: With a positive or negative offset, the default JSON encoded format in some other languages\n * - `2000-03-15T05:20:10+0000`: With a zero offset without a colon\n * - `2000-03-15T05:20:10`: Without a trailing 'Z' symbol\n * - `2000-03-15T05:20:10.1234567`: Up to 7 digits in milliseconds field. Only first 3 are taken into account since JS does not allow fractional milliseconds\n * - `2000-03-15 05:20:10`: With a space instead of a 'T' separator for APIs returning a SQL date without reformatting\n *\n * For convenience and ease of use these other input types are also supported\n * via [toDate]{@link https://date-fns.org/docs/toDate}:\n *\n * - A `Date` instance will be cloned\n * - A `number` will be treated as a timestamp\n *\n * Any other input type or invalid date strings will return an `Invalid Date`.\n *\n * @param {String|Number|Date} argument A fully formed ISO8601 date string to convert\n * @returns {Date} the parsed date in the local time zone\n * @throws {TypeError} 1 argument required\n */\n\nexport default function parseJSON(argument) {\n  requiredArgs(1, arguments);\n\n  if (typeof argument === 'string') {\n    var parts = argument.match(/(\\d{4})-(\\d{2})-(\\d{2})[T ](\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d{0,7}))?(?:Z|(.)(\\d{2}):?(\\d{2})?)?/);\n\n    if (parts) {\n      // Group 8 matches the sign\n      return new Date(Date.UTC(+parts[1], +parts[2] - 1, +parts[3], +parts[4] - (+parts[9] || 0) * (parts[8] == '-' ? -1 : 1), +parts[5] - (+parts[10] || 0) * (parts[8] == '-' ? -1 : 1), +parts[6], +((parts[7] || '0') + '00').substring(0, 3)));\n    }\n\n    return new Date(NaN);\n  }\n\n  return toDate(argument);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport getDay from \"../getDay/index.js\";\nimport subDays from \"../subDays/index.js\";\n\n/**\n * @name previousDay\n * @category Weekday Helpers\n * @summary When is the previous day of the week?\n *\n * @description\n * When is the previous day of the week? 0-6 the day of the week, 0 represents Sunday.\n *\n * @param {Date | number} date - the date to check\n * @param {number} day - day of the week\n * @returns {Date} - the date is the previous day of week\n * @throws {TypeError} - 2 arguments required\n *\n * @example\n * // When is the previous Monday before Mar, 20, 2020?\n * const result = previousDay(new Date(2020, 2, 20), 1)\n * //=> Mon Mar 16 2020 00:00:00\n *\n * @example\n * // When is the previous Tuesday before Mar, 21, 2020?\n * const result = previousDay(new Date(2020, 2, 21), 2)\n * //=> Tue Mar 17 2020 00:00:00\n */\nexport default function previousDay(date, day) {\n  requiredArgs(2, arguments);\n  var delta = getDay(date) - day;\n  if (delta <= 0) delta += 7;\n  return subDays(date, delta);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousFriday\n * @category Weekday Helpers\n * @summary When is the previous Friday?\n *\n * @description\n * When is the previous Friday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Friday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Friday before Jun, 19, 2021?\n * const result = previousFriday(new Date(2021, 5, 19))\n * //=> Fri June 18 2021 00:00:00\n */\n\nexport default function previousFriday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 5);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousMonday\n * @category Weekday Helpers\n * @summary When is the previous Monday?\n *\n * @description\n * When is the previous Monday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Monday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Monday before Jun, 18, 2021?\n * const result = previousMonday(new Date(2021, 5, 18))\n * //=> Mon June 14 2021 00:00:00\n */\n\nexport default function previousMonday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 1);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousSaturday\n * @category Weekday Helpers\n * @summary When is the previous Saturday?\n *\n * @description\n * When is the previous Saturday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Saturday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Saturday before Jun, 20, 2021?\n * const result = previousSaturday(new Date(2021, 5, 20))\n * //=> Sat June 19 2021 00:00:00\n */\n\nexport default function previousSaturday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 6);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousSunday\n * @category Weekday Helpers\n * @summary When is the previous Sunday?\n *\n * @description\n * When is the previous Sunday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Sunday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Sunday before Jun, 21, 2021?\n * const result = previousSunday(new Date(2021, 5, 21))\n * //=> Sun June 20 2021 00:00:00\n */\n\nexport default function previousSunday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 0);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousThursday\n * @category Weekday Helpers\n * @summary When is the previous Thursday?\n *\n * @description\n * When is the previous Thursday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Thursday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Thursday before Jun, 18, 2021?\n * const result = previousThursday(new Date(2021, 5, 18))\n * //=> Thu June 17 2021 00:00:00\n */\n\nexport default function previousThursday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 4);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousTuesday\n * @category Weekday Helpers\n * @summary When is the previous Tuesday?\n *\n * @description\n * When is the previous Tuesday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Tuesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Tuesday before Jun, 18, 2021?\n * const result = previousTuesday(new Date(2021, 5, 18))\n * //=> Tue June 15 2021 00:00:00\n */\n\nexport default function previousTuesday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 2);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousWednesday\n * @category Weekday Helpers\n * @summary When is the previous Wednesday?\n *\n * @description\n * When is the previous Wednesday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Wednesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Wednesday before Jun, 18, 2021?\n * const result = previousWednesday(new Date(2021, 5, 18))\n * //=> Wed June 16 2021 00:00:00\n */\n\nexport default function previousWednesday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 3);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInQuarter } from \"../constants/index.js\";\n/**\n * @name quartersToMonths\n * @category Conversion Helpers\n * @summary Convert number of quarters to months.\n *\n * @description\n * Convert a number of quarters to a full number of months.\n *\n * @param {number} quarters - number of quarters to be converted\n *\n * @returns {number} the number of quarters converted in months\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 quarters to months\n * const result = quartersToMonths(2)\n * //=> 6\n */\n\nexport default function quartersToMonths(quarters) {\n  requiredArgs(1, arguments);\n  return Math.floor(quarters * monthsInQuarter);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { quartersInYear } from \"../constants/index.js\";\n/**\n * @name quartersToYears\n * @category Conversion Helpers\n * @summary Convert number of quarters to years.\n *\n * @description\n * Convert a number of quarters to a full number of years.\n *\n * @param {number} quarters - number of quarters to be converted\n *\n * @returns {number} the number of quarters converted in years\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 8 quarters to years\n * const result = quartersToYears(8)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = quartersToYears(11)\n * //=> 2\n */\n\nexport default function quartersToYears(quarters) {\n  requiredArgs(1, arguments);\n  var years = quarters / quartersInYear;\n  return Math.floor(years);\n}", "import toDate from \"../toDate/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name roundToNearestMinutes\n * @category Minute Helpers\n * @summary Rounds the given date to the nearest minute\n *\n * @description\n * Rounds the given date to the nearest minute (or number of minutes).\n * Rounds up when the given date is exactly between the nearest round minutes.\n *\n * @param {Date|Number} date - the date to round\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.nearestTo=1] - nearest number of minutes to round to. E.g. `15` to round to quarter hours.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Date} the new date rounded to the closest minute\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.nearestTo` must be between 1 and 30\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34))\n * //=> Thu Jul 10 2014 12:13:00\n *\n * @example\n * // Round 10 July 2014 12:07:30 to nearest quarter hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { nearestTo: 15 })\n * // rounds up because given date is exactly between 12:00:00 and 12:15:00\n * //=> Thu Jul 10 2014 12:15:00\n */\n\nexport default function roundToNearestMinutes(dirtyDate, options) {\n  var _options$nearestTo;\n\n  if (arguments.length < 1) {\n    throw new TypeError('1 argument required, but only none provided present');\n  }\n\n  var nearestTo = toInteger((_options$nearestTo = options === null || options === void 0 ? void 0 : options.nearestTo) !== null && _options$nearestTo !== void 0 ? _options$nearestTo : 1);\n\n  if (nearestTo < 1 || nearestTo > 30) {\n    throw new RangeError('`options.nearestTo` must be between 1 and 30');\n  }\n\n  var date = toDate(dirtyDate);\n  var seconds = date.getSeconds(); // relevant if nearestTo is 1, which is the default case\n\n  var minutes = date.getMinutes() + seconds / 60;\n  var roundingMethod = getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod);\n  var roundedMinutes = roundingMethod(minutes / nearestTo) * nearestTo;\n  var remainderMinutes = minutes % nearestTo;\n  var addedMinutes = Math.round(remainderMinutes / nearestTo) * nearestTo;\n  return new Date(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), roundedMinutes + addedMinutes);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { secondsInHour } from \"../constants/index.js\";\n/**\n * @name secondsToHours\n * @category Conversion Helpers\n * @summary Convert seconds to hours.\n *\n * @description\n * Convert a number of seconds to a full number of hours.\n *\n * @param {number} seconds - number of seconds to be converted\n *\n * @returns {number} the number of seconds converted in hours\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 7200 seconds into hours\n * const result = secondsToHours(7200)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = secondsToHours(7199)\n * //=> 1\n */\n\nexport default function secondsToHours(seconds) {\n  requiredArgs(1, arguments);\n  var hours = seconds / secondsInHour;\n  return Math.floor(hours);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInSecond } from \"../constants/index.js\";\n/**\n * @name secondsToMilliseconds\n * @category Conversion Helpers\n * @summary Convert seconds to milliseconds.\n *\n * @description\n * Convert a number of seconds to a full number of milliseconds.\n *\n * @param {number} seconds - number of seconds to be converted\n *\n * @returns {number} the number of seconds converted in milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 seconds into milliseconds\n * const result = secondsToMilliseconds(2)\n * //=> 2000\n */\n\nexport default function secondsToMilliseconds(seconds) {\n  requiredArgs(1, arguments);\n  return seconds * millisecondsInSecond;\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { secondsInMinute } from \"../constants/index.js\";\n/**\n * @name secondsToMinutes\n * @category Conversion Helpers\n * @summary Convert seconds to minutes.\n *\n * @description\n * Convert a number of seconds to a full number of minutes.\n *\n * @param {number} seconds - number of seconds to be converted\n *\n * @returns {number} the number of seconds converted in minutes\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 120 seconds into minutes\n * const result = secondsToMinutes(120)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = secondsToMinutes(119)\n * //=> 1\n */\n\nexport default function secondsToMinutes(seconds) {\n  requiredArgs(1, arguments);\n  var minutes = seconds / secondsInMinute;\n  return Math.floor(minutes);\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nimport toDate from \"../toDate/index.js\";\nimport setMonth from \"../setMonth/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name set\n * @category Common Helpers\n * @summary Set date values to a given date.\n *\n * @description\n * Set date values to a given date.\n *\n * Sets time values to date from object `values`.\n * A value is not set if it is undefined or null or doesn't exist in `values`.\n *\n * Note about bundle size: `set` does not internally use `setX` functions from date-fns but instead opts\n * to use native `Date#setX` methods. If you use this function, you may not want to include the\n * other `setX` functions that date-fns provides if you are concerned about the bundle size.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Object} values - an object with options\n * @param {Number} [values.year] - the number of years to be set\n * @param {Number} [values.month] - the number of months to be set\n * @param {Number} [values.date] - the number of days to be set\n * @param {Number} [values.hours] - the number of hours to be set\n * @param {Number} [values.minutes] - the number of minutes to be set\n * @param {Number} [values.seconds] - the number of seconds to be set\n * @param {Number} [values.milliseconds] - the number of milliseconds to be set\n * @returns {Date} the new date with options set\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `values` must be an object\n *\n * @example\n * // Transform 1 September 2014 into 20 October 2015 in a single line:\n * const result = set(new Date(2014, 8, 20), { year: 2015, month: 9, date: 20 })\n * //=> Tue Oct 20 2015 00:00:00\n *\n * @example\n * // Set 12 PM to 1 September 2014 01:23:45 to 1 September 2014 12:00:00:\n * const result = set(new Date(2014, 8, 1, 1, 23, 45), { hours: 12 })\n * //=> Mon Sep 01 2014 12:23:45\n */\nexport default function set(dirtyDate, values) {\n  requiredArgs(2, arguments);\n\n  if (_typeof(values) !== 'object' || values === null) {\n    throw new RangeError('values parameter must be an object');\n  }\n\n  var date = toDate(dirtyDate); // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n\n  if (isNaN(date.getTime())) {\n    return new Date(NaN);\n  }\n\n  if (values.year != null) {\n    date.setFullYear(values.year);\n  }\n\n  if (values.month != null) {\n    date = setMonth(date, values.month);\n  }\n\n  if (values.date != null) {\n    date.setDate(toInteger(values.date));\n  }\n\n  if (values.hours != null) {\n    date.setHours(toInteger(values.hours));\n  }\n\n  if (values.minutes != null) {\n    date.setMinutes(toInteger(values.minutes));\n  }\n\n  if (values.seconds != null) {\n    date.setSeconds(toInteger(values.seconds));\n  }\n\n  if (values.milliseconds != null) {\n    date.setMilliseconds(toInteger(values.milliseconds));\n  }\n\n  return date;\n}", "import addDays from \"../addDays/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\n/**\n * @name setDay\n * @category Weekday Helpers\n * @summary Set the day of the week to the given date.\n *\n * @description\n * Set the day of the week to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} day - the day of the week of the new date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Date} the new date with the day of the week set\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n *\n * @example\n * // Set week day to Sunday, with the default weekStartsOn of Sunday:\n * const result = setDay(new Date(2014, 8, 1), 0)\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Set week day to Sunday, with a weekStartsOn of Monday:\n * const result = setDay(new Date(2014, 8, 1), 0, { weekStartsOn: 1 })\n * //=> Sun Sep 07 2014 00:00:00\n */\n\nexport default function setDay(dirtyDate, dirtyDay, options) {\n  var _ref, _ref2, _ref3, _options$weekStartsOn, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n\n  requiredArgs(2, arguments);\n  var defaultOptions = getDefaultOptions();\n  var weekStartsOn = toInteger((_ref = (_ref2 = (_ref3 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.weekStartsOn) !== null && _ref !== void 0 ? _ref : 0); // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n\n  var date = toDate(dirtyDate);\n  var day = toInteger(dirtyDay);\n  var currentDay = date.getDay();\n  var remainder = day % 7;\n  var dayIndex = (remainder + 7) % 7;\n  var delta = 7 - weekStartsOn;\n  var diff = day < 0 || day > 6 ? day - (currentDay + delta) % 7 : (dayIndex + delta) % 7 - (currentDay + delta) % 7;\n  return addDays(date, diff);\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setDayOfYear\n * @category Day Helpers\n * @summary Set the day of the year to the given date.\n *\n * @description\n * Set the day of the year to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} dayOfYear - the day of the year of the new date\n * @returns {Date} the new date with the day of the year set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set the 2nd day of the year to 2 July 2014:\n * const result = setDayOfYear(new Date(2014, 6, 2), 2)\n * //=> Thu Jan 02 2014 00:00:00\n */\n\nexport default function setDayOfYear(dirtyDate, dirtyDayOfYear) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var dayOfYear = toInteger(dirtyDayOfYear);\n  date.setMonth(0);\n  date.setDate(dayOfYear);\n  return date;\n}", "import { getDefaultOptions, setDefaultOptions as setInternalDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setDefaultOptions\n * @category Common Helpers\n * @summary Set default options including locale.\n * @pure false\n *\n * @description\n * Sets the defaults for\n * `options.locale`, `options.weekStartsOn` and `options.firstWeekContainsDate`\n * arguments for all functions.\n *\n * @param {Object} newOptions - an object with options.\n * @param {Locale} [newOptions.locale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [newOptions.weekStartsOn] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [newOptions.firstWeekContainsDate] - the day of January, which is always in the first week of the year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Set global locale:\n * import { es } from 'date-fns/locale'\n * setDefaultOptions({ locale: es })\n * const result = format(new Date(2014, 8, 2), 'PPPP')\n * //=> 'martes, 2 de septiembre de 2014'\n *\n * @example\n * // Start of the week for 2 September 2014:\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Start of the week for 2 September 2014,\n * // when we set that week starts on Monday by default:\n * setDefaultOptions({ weekStartsOn: 1 })\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Mon Sep 01 2014 00:00:00\n *\n * @example\n * // Manually set options take priority over default options:\n * setDefaultOptions({ weekStartsOn: 1 })\n * const result = startOfWeek(new Date(2014, 8, 2), { weekStartsOn: 0 })\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Remove the option by setting it to `undefined`:\n * setDefaultOptions({ weekStartsOn: 1 })\n * setDefaultOptions({ weekStartsOn: undefined })\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Sun Aug 31 2014 00:00:00\n */\n\nexport default function setDefaultOptions(newOptions) {\n  requiredArgs(1, arguments);\n  var result = {};\n  var defaultOptions = getDefaultOptions();\n\n  for (var property in defaultOptions) {\n    if (Object.prototype.hasOwnProperty.call(defaultOptions, property)) {\n      ;\n      result[property] = defaultOptions[property];\n    }\n  }\n\n  for (var _property in newOptions) {\n    if (Object.prototype.hasOwnProperty.call(newOptions, _property)) {\n      if (newOptions[_property] === undefined) {\n        delete result[_property];\n      } else {\n        ;\n        result[_property] = newOptions[_property];\n      }\n    }\n  }\n\n  setInternalDefaultOptions(result);\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport addDays from \"../addDays/index.js\";\nimport getISODay from \"../getISODay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setISODay\n * @category Weekday Helpers\n * @summary Set the day of the ISO week to the given date.\n *\n * @description\n * Set the day of the ISO week to the given date.\n * ISO week starts with Monday.\n * 7 is the index of Sunday, 1 is the index of Monday etc.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} day - the day of the ISO week of the new date\n * @returns {Date} the new date with the day of the ISO week set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set Sunday to 1 September 2014:\n * const result = setISODay(new Date(2014, 8, 1), 7)\n * //=> Sun Sep 07 2014 00:00:00\n */\n\nexport default function setISODay(dirtyDate, dirtyDay) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var day = toInteger(dirtyDay);\n  var currentDay = getISODay(date);\n  var diff = day - currentDay;\n  return addDays(date, diff);\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport getISOWeek from \"../getISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setISOWeek\n * @category ISO Week Helpers\n * @summary Set the ISO week to the given date.\n *\n * @description\n * Set the ISO week to the given date, saving the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} isoWeek - the ISO week of the new date\n * @returns {Date} the new date with the ISO week set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set the 53rd ISO week to 7 August 2004:\n * const result = setISOWeek(new Date(2004, 7, 7), 53)\n * //=> Sat Jan 01 2005 00:00:00\n */\n\nexport default function setISOWeek(dirtyDate, dirtyISOWeek) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var isoWeek = toInteger(dirtyISOWeek);\n  var diff = getISOWeek(date) - isoWeek;\n  date.setDate(date.getDate() - diff * 7);\n  return date;\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setMilliseconds\n * @category Millisecond Helpers\n * @summary Set the milliseconds to the given date.\n *\n * @description\n * Set the milliseconds to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} milliseconds - the milliseconds of the new date\n * @returns {Date} the new date with the milliseconds set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set 300 milliseconds to 1 September 2014 11:30:40.500:\n * const result = setMilliseconds(new Date(2014, 8, 1, 11, 30, 40, 500), 300)\n * //=> Mon Sep 01 2014 11:30:40.300\n */\n\nexport default function setMilliseconds(dirtyDate, dirtyMilliseconds) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var milliseconds = toInteger(dirtyMilliseconds);\n  date.setMilliseconds(milliseconds);\n  return date;\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport setMonth from \"../setMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setQuarter\n * @category Quarter Helpers\n * @summary Set the year quarter to the given date.\n *\n * @description\n * Set the year quarter to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} quarter - the quarter of the new date\n * @returns {Date} the new date with the quarter set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set the 2nd quarter to 2 July 2014:\n * const result = setQuarter(new Date(2014, 6, 2), 2)\n * //=> Wed Apr 02 2014 00:00:00\n */\n\nexport default function setQuarter(dirtyDate, dirtyQuarter) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var quarter = toInteger(dirtyQuarter);\n  var oldQuarter = Math.floor(date.getMonth() / 3) + 1;\n  var diff = quarter - oldQuarter;\n  return setMonth(date, date.getMonth() + diff * 3);\n}", "import getWeek from \"../getWeek/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name setWeek\n * @category Week Helpers\n * @summary Set the local week to the given date.\n *\n * @description\n * Set the local week to the given date, saving the weekday number.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#Week_numbering\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} week - the week of the new date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [options.firstWeekContainsDate=1] - the day of January, which is always in the first week of the year\n * @returns {Date} the new date with the local week set\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n *\n * @example\n * // Set the 1st week to 2 January 2005 with default options:\n * const result = setWeek(new Date(2005, 0, 2), 1)\n * //=> Sun Dec 26 2004 00:00:00\n *\n * @example\n * // Set the 1st week to 2 January 2005,\n * // if Monday is the first day of the week,\n * // and the first week of the year always contains 4 January:\n * const result = setWeek(new Date(2005, 0, 2), 1, {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Sun Jan 4 2004 00:00:00\n */\n\nexport default function setWeek(dirtyDate, dirtyWeek, options) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var week = toInteger(dirtyWeek);\n  var diff = getWeek(date, options) - week;\n  date.setDate(date.getDate() - diff * 7);\n  return date;\n}", "import differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport startOfWeekYear from \"../startOfWeekYear/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\n/**\n * @name setWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Set the local week-numbering year to the given date.\n *\n * @description\n * Set the local week-numbering year to the given date,\n * saving the week number and the weekday number.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#Week_numbering\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} weekYear - the local week-numbering year of the new date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [options.firstWeekContainsDate=1] - the day of January, which is always in the first week of the year\n * @returns {Date} the new date with the local week-numbering year set\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n *\n * @example\n * // Set the local week-numbering year 2004 to 2 January 2010 with default options:\n * const result = setWeekYear(new Date(2010, 0, 2), 2004)\n * //=> Sat Jan 03 2004 00:00:00\n *\n * @example\n * // Set the local week-numbering year 2004 to 2 January 2010,\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = setWeekYear(new Date(2010, 0, 2), 2004, {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Sat Jan 01 2005 00:00:00\n */\n\nexport default function setWeekYear(dirtyDate, dirtyWeekYear, options) {\n  var _ref, _ref2, _ref3, _options$firstWeekCon, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n\n  requiredArgs(2, arguments);\n  var defaultOptions = getDefaultOptions();\n  var firstWeekContainsDate = toInteger((_ref = (_ref2 = (_ref3 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref !== void 0 ? _ref : 1);\n  var date = toDate(dirtyDate);\n  var weekYear = toInteger(dirtyWeekYear);\n  var diff = differenceInCalendarDays(date, startOfWeekYear(date, options));\n  var firstWeek = new Date(0);\n  firstWeek.setFullYear(weekYear, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n  date = startOfWeekYear(firstWeek, options);\n  date.setDate(date.getDate() + diff);\n  return date;\n}", "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name startOfDecade\n * @category Decade Helpers\n * @summary Return the start of a decade for the given date.\n *\n * @description\n * Return the start of a decade for the given date.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the start of a decade\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The start of a decade for 21 October 2015 00:00:00:\n * const result = startOfDecade(new Date(2015, 9, 21, 00, 00, 00))\n * //=> Jan 01 2010 00:00:00\n */\n\nexport default function startOfDecade(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var decade = Math.floor(year / 10) * 10;\n  date.setFullYear(decade, 0, 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}", "import startOfDay from \"../startOfDay/index.js\";\n/**\n * @name startOfToday\n * @category Day Helpers\n * @summary Return the start of today.\n * @pure false\n *\n * @description\n * Return the start of today.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the start of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfToday()\n * //=> Mon Oct 6 2014 00:00:00\n */\n\nexport default function startOfToday() {\n  return startOfDay(Date.now());\n}", "/**\n * @name startOfTomorrow\n * @category Day Helpers\n * @summary Return the start of tomorrow.\n * @pure false\n *\n * @description\n * Return the start of tomorrow.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `new Date()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the start of tomorrow\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfTomorrow()\n * //=> Tue Oct 7 2014 00:00:00\n */\nexport default function startOfTomorrow() {\n  var now = new Date();\n  var year = now.getFullYear();\n  var month = now.getMonth();\n  var day = now.getDate();\n  var date = new Date(0);\n  date.setFullYear(year, month, day + 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}", "/**\n * @name startOfYesterday\n * @category Day Helpers\n * @summary Return the start of yesterday.\n * @pure false\n *\n * @description\n * Return the start of yesterday.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `new Date()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the start of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfYesterday()\n * //=> Sun Oct 5 2014 00:00:00\n */\nexport default function startOfYesterday() {\n  var now = new Date();\n  var year = now.getFullYear();\n  var month = now.getMonth();\n  var day = now.getDate();\n  var date = new Date(0);\n  date.setFullYear(year, month, day - 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport addMonths from \"../addMonths/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name subMonths\n * @category Month Helpers\n * @summary Subtract the specified number of months from the given date.\n *\n * @description\n * Subtract the specified number of months from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of months to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the months subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 5 months from 1 February 2015:\n * const result = subMonths(new Date(2015, 1, 1), 5)\n * //=> Mon Sep 01 2014 00:00:00\n */\n\nexport default function subMonths(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addMonths(dirtyDate, -amount);\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nimport subDays from \"../subDays/index.js\";\nimport subMonths from \"../subMonths/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name sub\n * @category Common Helpers\n * @summary Subtract the specified years, months, weeks, days, hours, minutes and seconds from the given date.\n *\n * @description\n * Subtract the specified years, months, weeks, days, hours, minutes and seconds from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Duration} duration - the object with years, months, weeks, days, hours, minutes and seconds to be subtracted\n *\n * | Key     | Description                        |\n * |---------|------------------------------------|\n * | years   | Amount of years to be subtracted   |\n * | months  | Amount of months to be subtracted  |\n * | weeks   | Amount of weeks to be subtracted   |\n * | days    | Amount of days to be subtracted    |\n * | hours   | Amount of hours to be subtracted   |\n * | minutes | Amount of minutes to be subtracted |\n * | seconds | Amount of seconds to be subtracted |\n *\n * All values default to 0\n *\n * @returns {Date} the new date with the seconds subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract the following duration from 15 June 2017 15:29:20\n * const result = sub(new Date(2017, 5, 15, 15, 29, 20), {\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> Mon Sep 1 2014 10:19:50\n */\n\nexport default function sub(date, duration) {\n  requiredArgs(2, arguments);\n  if (!duration || _typeof(duration) !== 'object') return new Date(NaN);\n  var years = duration.years ? toInteger(duration.years) : 0;\n  var months = duration.months ? toInteger(duration.months) : 0;\n  var weeks = duration.weeks ? toInteger(duration.weeks) : 0;\n  var days = duration.days ? toInteger(duration.days) : 0;\n  var hours = duration.hours ? toInteger(duration.hours) : 0;\n  var minutes = duration.minutes ? toInteger(duration.minutes) : 0;\n  var seconds = duration.seconds ? toInteger(duration.seconds) : 0; // Subtract years and months\n\n  var dateWithoutMonths = subMonths(date, months + years * 12); // Subtract weeks and days\n\n  var dateWithoutDays = subDays(dateWithoutMonths, days + weeks * 7); // Subtract hours, minutes and seconds\n\n  var minutestoSub = minutes + hours * 60;\n  var secondstoSub = seconds + minutestoSub * 60;\n  var mstoSub = secondstoSub * 1000;\n  var finalDate = new Date(dateWithoutDays.getTime() - mstoSub);\n  return finalDate;\n}", "import addBusinessDays from \"../addBusinessDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name subBusinessDays\n * @category Day Helpers\n * @summary Substract the specified number of business days (mon - fri) to the given date.\n *\n * @description\n * Substract the specified number of business days (mon - fri) to the given date, ignoring weekends.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of business days to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the business days subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Substract 10 business days from 1 September 2014:\n * const result = subBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Aug 18 2014 00:00:00 (skipped weekend days)\n */\n\nexport default function subBusinessDays(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addBusinessDays(dirtyDate, -amount);\n}", "import addHours from \"../addHours/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name subHours\n * @category Hour Helpers\n * @summary Subtract the specified number of hours from the given date.\n *\n * @description\n * Subtract the specified number of hours from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of hours to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the hours subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 2 hours from 11 July 2014 01:00:00:\n * const result = subHours(new Date(2014, 6, 11, 1, 0), 2)\n * //=> Thu Jul 10 2014 23:00:00\n */\n\nexport default function subHours(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addHours(dirtyDate, -amount);\n}", "import addMinutes from \"../addMinutes/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name subMinutes\n * @category Minute Helpers\n * @summary Subtract the specified number of minutes from the given date.\n *\n * @description\n * Subtract the specified number of minutes from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of minutes to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the minutes subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 30 minutes from 10 July 2014 12:00:00:\n * const result = subMinutes(new Date(2014, 6, 10, 12, 0), 30)\n * //=> Thu Jul 10 2014 11:30:00\n */\n\nexport default function subMinutes(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addMinutes(dirtyDate, -amount);\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport addQuarters from \"../addQuarters/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name subQuarters\n * @category Quarter Helpers\n * @summary Subtract the specified number of year quarters from the given date.\n *\n * @description\n * Subtract the specified number of year quarters from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of quarters to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the quarters subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 3 quarters from 1 September 2014:\n * const result = subQuarters(new Date(2014, 8, 1), 3)\n * //=> Sun Dec 01 2013 00:00:00\n */\n\nexport default function subQuarters(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addQuarters(dirtyDate, -amount);\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport addSeconds from \"../addSeconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name subSeconds\n * @category Second Helpers\n * @summary Subtract the specified number of seconds from the given date.\n *\n * @description\n * Subtract the specified number of seconds from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of seconds to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the seconds subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 30 seconds from 10 July 2014 12:45:00:\n * const result = subSeconds(new Date(2014, 6, 10, 12, 45, 0), 30)\n * //=> Thu Jul 10 2014 12:44:30\n */\n\nexport default function subSeconds(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addSeconds(dirtyDate, -amount);\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport addWeeks from \"../addWeeks/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name subWeeks\n * @category Week Helpers\n * @summary Subtract the specified number of weeks from the given date.\n *\n * @description\n * Subtract the specified number of weeks from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of weeks to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the weeks subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 4 weeks from 1 September 2014:\n * const result = subWeeks(new Date(2014, 8, 1), 4)\n * //=> Mon Aug 04 2014 00:00:00\n */\n\nexport default function subWeeks(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addWeeks(dirtyDate, -amount);\n}", "import toInteger from \"../_lib/toInteger/index.js\";\nimport addYears from \"../addYears/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name subYears\n * @category Year Helpers\n * @summary Subtract the specified number of years from the given date.\n *\n * @description\n * Subtract the specified number of years from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of years to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the years subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 5 years from 1 September 2014:\n * const result = subYears(new Date(2014, 8, 1), 5)\n * //=> Tue Sep 01 2009 00:00:00\n */\n\nexport default function subYears(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addYears(dirtyDate, -amount);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { daysInWeek } from \"../constants/index.js\";\n/**\n * @name weeksToDays\n * @category Conversion Helpers\n * @summary Convert weeks to days.\n *\n * @description\n * Convert a number of weeks to a full number of days.\n *\n * @param {number} weeks - number of weeks to be converted\n *\n * @returns {number} the number of weeks converted in days\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 weeks into days\n * const result = weeksToDays(2)\n * //=> 14\n */\n\nexport default function weeksToDays(weeks) {\n  requiredArgs(1, arguments);\n  return Math.floor(weeks * daysInWeek);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInYear } from \"../constants/index.js\";\n/**\n * @name yearsToMonths\n * @category Conversion Helpers\n * @summary Convert years to months.\n *\n * @description\n * Convert a number of years to a full number of months.\n *\n * @param {number} years - number of years to be converted\n *\n * @returns {number} the number of years converted in months\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 years into months\n * const result = yearsToMonths(2)\n * //=> 24\n */\n\nexport default function yearsToMonths(years) {\n  requiredArgs(1, arguments);\n  return Math.floor(years * monthsInYear);\n}", "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { quartersInYear } from \"../constants/index.js\";\n/**\n * @name yearsToQuarters\n * @category Conversion Helpers\n * @summary Convert years to quarters.\n *\n * @description\n * Convert a number of years to a full number of quarters.\n *\n * @param {number} years - number of years to be converted\n *\n * @returns {number} the number of years converted in quarters\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 years to quarters\n * const result = yearsToQuarters(2)\n * //=> 8\n */\n\nexport default function yearsToQuarters(years) {\n  requiredArgs(1, arguments);\n  return Math.floor(years * quartersInYear);\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,QAAQ,KAAK;AAAE;AAA2B,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AAAE,cAAU,SAASA,SAAQC,MAAK;AAAE,aAAO,OAAOA;AAAA,IAAK;AAAA,EAAG,OAAO;AAAE,cAAU,SAASD,SAAQC,MAAK;AAAE,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAAK;AAAA,EAAG;AAAE,SAAO,QAAQ,GAAG;AAAG;AA+C1W,SAAR,IAAqB,WAAW,UAAU;AAC/C,eAAa,GAAG,SAAS;AACzB,MAAI,CAAC,YAAY,QAAQ,QAAQ,MAAM,SAAU,QAAO,oBAAI,KAAK,GAAG;AACpE,MAAI,QAAQ,SAAS,QAAQ,UAAU,SAAS,KAAK,IAAI;AACzD,MAAIC,UAAS,SAAS,SAAS,UAAU,SAAS,MAAM,IAAI;AAC5D,MAAI,QAAQ,SAAS,QAAQ,UAAU,SAAS,KAAK,IAAI;AACzD,MAAIC,QAAO,SAAS,OAAO,UAAU,SAAS,IAAI,IAAI;AACtD,MAAI,QAAQ,SAAS,QAAQ,UAAU,SAAS,KAAK,IAAI;AACzD,MAAI,UAAU,SAAS,UAAU,UAAU,SAAS,OAAO,IAAI;AAC/D,MAAI,UAAU,SAAS,UAAU,UAAU,SAAS,OAAO,IAAI;AAE/D,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,iBAAiBD,WAAU,QAAQ,UAAU,MAAMA,UAAS,QAAQ,EAAE,IAAI;AAE9E,MAAI,eAAeC,SAAQ,QAAQ,QAAQ,gBAAgBA,QAAO,QAAQ,CAAC,IAAI;AAE/E,MAAI,eAAe,UAAU,QAAQ;AACrC,MAAI,eAAe,UAAU,eAAe;AAC5C,MAAI,UAAU,eAAe;AAC7B,MAAI,YAAY,IAAI,KAAK,aAAa,QAAQ,IAAI,OAAO;AACzD,SAAO;AACT;;;AChDe,SAAR,UAA2B,WAAW;AAC3C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,MAAM,KAAK,OAAO;AACtB,SAAO,QAAQ,KAAK,QAAQ;AAC9B;;;ACLe,SAAR,SAA0B,WAAW;AAC1C,eAAa,GAAG,SAAS;AACzB,SAAO,OAAO,SAAS,EAAE,OAAO,MAAM;AACxC;;;ACHe,SAAR,WAA4B,WAAW;AAC5C,eAAa,GAAG,SAAS;AACzB,SAAO,OAAO,SAAS,EAAE,OAAO,MAAM;AACxC;;;ACEe,SAAR,gBAAiC,WAAW,aAAa;AAC9D,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,mBAAmB,UAAU,IAAI;AACrC,MAAI,SAAS,UAAU,WAAW;AAClC,MAAI,MAAM,MAAM,EAAG,QAAO,oBAAI,KAAK,GAAG;AACtC,MAAI,QAAQ,KAAK,SAAS;AAC1B,MAAI,OAAO,SAAS,IAAI,KAAK;AAC7B,MAAI,YAAY,UAAU,SAAS,CAAC;AACpC,OAAK,QAAQ,KAAK,QAAQ,IAAI,YAAY,CAAC;AAE3C,MAAI,WAAW,KAAK,IAAI,SAAS,CAAC;AAElC,SAAO,WAAW,GAAG;AACnB,SAAK,QAAQ,KAAK,QAAQ,IAAI,IAAI;AAClC,QAAI,CAAC,UAAU,IAAI,EAAG,aAAY;AAAA,EACpC;AAKA,MAAI,oBAAoB,UAAU,IAAI,KAAK,WAAW,GAAG;AAGvD,QAAI,WAAW,IAAI,EAAG,MAAK,QAAQ,KAAK,QAAQ,KAAK,OAAO,IAAI,IAAI,GAAG;AACvE,QAAI,SAAS,IAAI,EAAG,MAAK,QAAQ,KAAK,QAAQ,KAAK,OAAO,IAAI,IAAI,GAAG;AAAA,EACvE;AAGA,OAAK,SAAS,KAAK;AACnB,SAAO;AACT;;;ACjCe,SAAR,eAAgC,WAAW;AAChD,eAAa,GAAG,SAAS;AACzB,SAAO,YAAY,WAAW;AAAA,IAC5B,cAAc;AAAA,EAChB,CAAC;AACH;;;ACJe,SAAR,eAAgC,WAAW;AAChD,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,KAAK,YAAY;AAC5B,MAAI,4BAA4B,oBAAI,KAAK,CAAC;AAC1C,4BAA0B,YAAY,OAAO,GAAG,GAAG,CAAC;AACpD,4BAA0B,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7C,MAAI,kBAAkB,eAAe,yBAAyB;AAC9D,MAAI,4BAA4B,oBAAI,KAAK,CAAC;AAC1C,4BAA0B,YAAY,MAAM,GAAG,CAAC;AAChD,4BAA0B,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7C,MAAI,kBAAkB,eAAe,yBAAyB;AAE9D,MAAI,KAAK,QAAQ,KAAK,gBAAgB,QAAQ,GAAG;AAC/C,WAAO,OAAO;AAAA,EAChB,WAAW,KAAK,QAAQ,KAAK,gBAAgB,QAAQ,GAAG;AACtD,WAAO;AAAA,EACT,OAAO;AACL,WAAO,OAAO;AAAA,EAChB;AACF;;;ACnBe,SAAR,mBAAoC,WAAW;AACpD,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,eAAe,SAAS;AACnC,MAAI,kBAAkB,oBAAI,KAAK,CAAC;AAChC,kBAAgB,YAAY,MAAM,GAAG,CAAC;AACtC,kBAAgB,SAAS,GAAG,GAAG,GAAG,CAAC;AACnC,MAAI,OAAO,eAAe,eAAe;AACzC,SAAO;AACT;;;ACNe,SAAR,eAAgC,WAAW,kBAAkB;AAClE,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,cAAc,UAAU,gBAAgB;AAC5C,MAAI,OAAO,yBAAyB,MAAM,mBAAmB,IAAI,CAAC;AAClE,MAAI,kBAAkB,oBAAI,KAAK,CAAC;AAChC,kBAAgB,YAAY,aAAa,GAAG,CAAC;AAC7C,kBAAgB,SAAS,GAAG,GAAG,GAAG,CAAC;AACnC,SAAO,mBAAmB,eAAe;AACzC,OAAK,QAAQ,KAAK,QAAQ,IAAI,IAAI;AAClC,SAAO;AACT;;;ACbe,SAAR,gBAAiC,WAAW,aAAa;AAC9D,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,eAAe,WAAW,eAAe,SAAS,IAAI,MAAM;AACrE;;;ACPe,SAAR,YAA6B,WAAW,aAAa;AAC1D,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,MAAIC,UAAS,SAAS;AACtB,SAAO,UAAU,WAAWA,OAAM;AACpC;;;AC+Be,SAAR,wBAAyC,cAAc,eAAe,SAAS;AACpF,eAAa,GAAG,SAAS;AACzB,MAAI,gBAAgB,OAAO,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,KAAK,EAAE,QAAQ;AACnH,MAAI,cAAc,OAAO,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,GAAG,EAAE,QAAQ;AAC/G,MAAI,iBAAiB,OAAO,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,KAAK,EAAE,QAAQ;AACvH,MAAI,eAAe,OAAO,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,GAAG,EAAE,QAAQ;AAEnH,MAAI,EAAE,iBAAiB,eAAe,kBAAkB,eAAe;AACrE,UAAM,IAAI,WAAW,kBAAkB;AAAA,EACzC;AAEA,MAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,WAAW;AAC/D,WAAO,iBAAiB,gBAAgB,kBAAkB;AAAA,EAC5D;AAEA,SAAO,gBAAgB,gBAAgB,iBAAiB;AAC1D;;;AC1EA,SAASC,SAAQ,KAAK;AAAE;AAA2B,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AAAE,IAAAA,WAAU,SAASA,SAAQC,MAAK;AAAE,aAAO,OAAOA;AAAA,IAAK;AAAA,EAAG,OAAO;AAAE,IAAAD,WAAU,SAASA,SAAQC,MAAK;AAAE,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAAK;AAAA,EAAG;AAAE,SAAOD,SAAQ,GAAG;AAAG;AA2B1W,SAAR,IAAqB,iBAAiB;AAC3C,eAAa,GAAG,SAAS;AACzB,MAAI;AAEJ,MAAI,mBAAmB,OAAO,gBAAgB,YAAY,YAAY;AACpE,iBAAa;AAAA,EACf,WAAWA,SAAQ,eAAe,MAAM,YAAY,oBAAoB,MAAM;AAC5E,iBAAa,MAAM,UAAU,MAAM,KAAK,eAAe;AAAA,EACzD,OAAO;AAEL,WAAO,oBAAI,KAAK,GAAG;AAAA,EACrB;AAEA,MAAI;AACJ,aAAW,QAAQ,SAAU,WAAW;AACtC,QAAI,cAAc,OAAO,SAAS;AAElC,QAAI,WAAW,UAAa,SAAS,eAAe,MAAM,OAAO,WAAW,CAAC,GAAG;AAC9E,eAAS;AAAA,IACX;AAAA,EACF,CAAC;AACD,SAAO,UAAU,oBAAI,KAAK,GAAG;AAC/B;;;ACjDA,SAASE,SAAQ,KAAK;AAAE;AAA2B,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AAAE,IAAAA,WAAU,SAASA,SAAQC,MAAK;AAAE,aAAO,OAAOA;AAAA,IAAK;AAAA,EAAG,OAAO;AAAE,IAAAD,WAAU,SAASA,SAAQC,MAAK;AAAE,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAAK;AAAA,EAAG;AAAE,SAAOD,SAAQ,GAAG;AAAG;AA2B1W,SAAR,IAAqB,iBAAiB;AAC3C,eAAa,GAAG,SAAS;AACzB,MAAI;AAEJ,MAAI,mBAAmB,OAAO,gBAAgB,YAAY,YAAY;AACpE,iBAAa;AAAA,EACf,WAAWA,SAAQ,eAAe,MAAM,YAAY,oBAAoB,MAAM;AAC5E,iBAAa,MAAM,UAAU,MAAM,KAAK,eAAe;AAAA,EACzD,OAAO;AAEL,WAAO,oBAAI,KAAK,GAAG;AAAA,EACrB;AAEA,MAAI;AACJ,aAAW,QAAQ,SAAU,WAAW;AACtC,QAAI,cAAc,OAAO,SAAS;AAElC,QAAI,WAAW,UAAa,SAAS,eAAe,MAAM,YAAY,QAAQ,CAAC,GAAG;AAChF,eAAS;AAAA,IACX;AAAA,EACF,CAAC;AACD,SAAO,UAAU,oBAAI,KAAK,GAAG;AAC/B;;;ACnBe,SAAR,MAAuB,MAAM,MAAM;AACxC,MAAI,QAAQ,KAAK,OACb,MAAM,KAAK;AACf,eAAa,GAAG,SAAS;AACzB,SAAO,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,GAAG,CAAC;AACtC;;;ACRe,SAAR,eAAgC,oBAAoB,iBAAiB;AAC1E,eAAa,GAAG,SAAS;AACzB,MAAI,gBAAgB,OAAO,kBAAkB;AAC7C,MAAI,MAAM,OAAO,aAAa,CAAC,EAAG,QAAO;AACzC,MAAI,gBAAgB,cAAc,QAAQ;AAC1C,MAAI;AAEJ,MAAI,mBAAmB,MAAM;AAC3B,iBAAa,CAAC;AAAA,EAChB,WAAW,OAAO,gBAAgB,YAAY,YAAY;AACxD,iBAAa;AAAA,EACf,OAAO;AACL,iBAAa,MAAM,UAAU,MAAM,KAAK,eAAe;AAAA,EACzD;AAEA,MAAI;AACJ,MAAI;AACJ,aAAW,QAAQ,SAAU,WAAW,OAAO;AAC7C,QAAI,cAAc,OAAO,SAAS;AAElC,QAAI,MAAM,OAAO,WAAW,CAAC,GAAG;AAC9B,eAAS;AACT,oBAAc;AACd;AAAA,IACF;AAEA,QAAI,WAAW,KAAK,IAAI,gBAAgB,YAAY,QAAQ,CAAC;AAE7D,QAAI,UAAU,QAAQ,WAAW,OAAO,WAAW,GAAG;AACpD,eAAS;AACT,oBAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO;AACT;;;ACpCe,SAAR,UAA2B,oBAAoB,iBAAiB;AACrE,eAAa,GAAG,SAAS;AACzB,MAAI,gBAAgB,OAAO,kBAAkB;AAC7C,MAAI,MAAM,OAAO,aAAa,CAAC,EAAG,QAAO,oBAAI,KAAK,GAAG;AACrD,MAAI,gBAAgB,cAAc,QAAQ;AAC1C,MAAI;AAEJ,MAAI,mBAAmB,MAAM;AAC3B,iBAAa,CAAC;AAAA,EAChB,WAAW,OAAO,gBAAgB,YAAY,YAAY;AACxD,iBAAa;AAAA,EACf,OAAO;AACL,iBAAa,MAAM,UAAU,MAAM,KAAK,eAAe;AAAA,EACzD;AAEA,MAAI;AACJ,MAAI;AACJ,aAAW,QAAQ,SAAU,WAAW;AACtC,QAAI,cAAc,OAAO,SAAS;AAElC,QAAI,MAAM,OAAO,WAAW,CAAC,GAAG;AAC9B,eAAS,oBAAI,KAAK,GAAG;AACrB,oBAAc;AACd;AAAA,IACF;AAEA,QAAI,WAAW,KAAK,IAAI,gBAAgB,YAAY,QAAQ,CAAC;AAE7D,QAAI,UAAU,QAAQ,WAAW,OAAO,WAAW,GAAG;AACpD,eAAS;AACT,oBAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO;AACT;;;ACxBe,SAAR,YAA6B,eAAe,gBAAgB;AACjE,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,OAAO,aAAa;AACnC,MAAI,YAAY,OAAO,cAAc;AACrC,MAAI,OAAO,SAAS,QAAQ,IAAI,UAAU,QAAQ;AAElD,MAAI,OAAO,GAAG;AACZ,WAAO;AAAA,EACT,WAAW,OAAO,GAAG;AACnB,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;;;ACtBe,SAAR,YAA6BE,OAAM;AACxC,eAAa,GAAG,SAAS;AACzB,MAAI,QAAQA,QAAO;AACnB,SAAO,KAAK,MAAM,KAAK;AACzB;;;AC4Be,SAAR,yBAA0C,eAAe,gBAAgB;AAC9E,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,OAAO,aAAa;AACnC,MAAI,YAAY,OAAO,cAAc;AACrC,MAAI,CAAC,QAAQ,QAAQ,KAAK,CAAC,QAAQ,SAAS,EAAG,QAAO;AACtD,MAAI,qBAAqB,yBAAyB,UAAU,SAAS;AACrE,MAAI,OAAO,qBAAqB,IAAI,KAAK;AACzC,MAAI,QAAQ,UAAU,qBAAqB,CAAC;AAC5C,MAAI,SAAS,QAAQ;AACrB,cAAY,QAAQ,WAAW,QAAQ,CAAC;AAExC,SAAO,CAAC,UAAU,UAAU,SAAS,GAAG;AAEtC,cAAU,UAAU,SAAS,IAAI,IAAI;AACrC,gBAAY,QAAQ,WAAW,IAAI;AAAA,EACrC;AAEA,SAAO,WAAW,IAAI,IAAI;AAC5B;;;AClDe,SAAR,iCAAkD,eAAe,gBAAgB;AACtF,eAAa,GAAG,SAAS;AACzB,SAAO,eAAe,aAAa,IAAI,eAAe,cAAc;AACtE;;;AC1BA,IAAI,uBAAuB;AAyBZ,SAAR,6BAA8C,eAAe,gBAAgB;AAClF,eAAa,GAAG,SAAS;AACzB,MAAI,qBAAqB,eAAe,aAAa;AACrD,MAAI,sBAAsB,eAAe,cAAc;AACvD,MAAI,gBAAgB,mBAAmB,QAAQ,IAAI,gCAAgC,kBAAkB;AACrG,MAAI,iBAAiB,oBAAoB,QAAQ,IAAI,gCAAgC,mBAAmB;AAIxG,SAAO,KAAK,OAAO,gBAAgB,kBAAkB,oBAAoB;AAC3E;;;AClBe,SAAR,WAA4B,WAAW;AAC5C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,UAAU,KAAK,MAAM,KAAK,SAAS,IAAI,CAAC,IAAI;AAChD,SAAO;AACT;;;ACAe,SAAR,6BAA8C,eAAe,gBAAgB;AAClF,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,OAAO,aAAa;AACnC,MAAI,YAAY,OAAO,cAAc;AACrC,MAAI,WAAW,SAAS,YAAY,IAAI,UAAU,YAAY;AAC9D,MAAI,cAAc,WAAW,QAAQ,IAAI,WAAW,SAAS;AAC7D,SAAO,WAAW,IAAI;AACxB;;;AC7BA,IAAIC,wBAAuB;AAqCZ,SAAR,0BAA2C,eAAe,gBAAgB,SAAS;AACxF,eAAa,GAAG,SAAS;AACzB,MAAI,kBAAkB,YAAY,eAAe,OAAO;AACxD,MAAI,mBAAmB,YAAY,gBAAgB,OAAO;AAC1D,MAAI,gBAAgB,gBAAgB,QAAQ,IAAI,gCAAgC,eAAe;AAC/F,MAAI,iBAAiB,iBAAiB,QAAQ,IAAI,gCAAgC,gBAAgB;AAIlG,SAAO,KAAK,OAAO,gBAAgB,kBAAkBA,qBAAoB;AAC3E;;;AC1Be,SAAR,gBAAiC,WAAW,aAAa;AAC9D,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,gBAAgB,WAAW,CAAC,MAAM;AAC3C;;;ACCe,SAAR,yBAA0C,eAAe,gBAAgB;AAC9E,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,OAAO,aAAa;AACnC,MAAI,YAAY,OAAO,cAAc;AACrC,MAAI,OAAO,WAAW,UAAU,SAAS;AACzC,MAAI,aAAa,KAAK,IAAI,iCAAiC,UAAU,SAAS,CAAC;AAC/E,aAAW,gBAAgB,UAAU,OAAO,UAAU;AAItD,MAAI,2BAA2B,OAAO,WAAW,UAAU,SAAS,MAAM,CAAC,IAAI;AAC/E,MAAI,SAAS,QAAQ,aAAa;AAElC,SAAO,WAAW,IAAI,IAAI;AAC5B;;;ACTe,SAAR,mBAAoC,eAAe,SAAS;AACjE,MAAI;AAEJ,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,iBAAiB,CAAC;AACjC,MAAI,YAAY,OAAO,SAAS,KAAK;AACrC,MAAI,UAAU,OAAO,SAAS,GAAG;AACjC,MAAI,YAAY,UAAU,QAAQ;AAClC,MAAI,UAAU,QAAQ,QAAQ;AAE9B,MAAI,EAAE,aAAa,UAAU;AAC3B,UAAM,IAAI,WAAW,kBAAkB;AAAA,EACzC;AAEA,MAAI,QAAQ,CAAC;AACb,MAAI,cAAc;AAClB,cAAY,WAAW,GAAG,GAAG,CAAC;AAC9B,MAAI,OAAO,QAAQ,gBAAgB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,QAAQ,kBAAkB,SAAS,gBAAgB,CAAC;AAC3J,MAAI,OAAO,KAAK,MAAM,IAAI,EAAG,OAAM,IAAI,WAAW,gDAAgD;AAElG,SAAO,YAAY,QAAQ,KAAK,SAAS;AACvC,UAAM,KAAK,OAAO,WAAW,CAAC;AAC9B,kBAAc,SAAS,aAAa,IAAI;AAAA,EAC1C;AAEA,SAAO;AACT;;;ACvCe,SAAR,cAA+B,WAAW;AAC/C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,OAAK,WAAW,GAAG,CAAC;AACpB,SAAO;AACT;;;ACSe,SAAR,qBAAsC,UAAU,SAAS;AAC9D,MAAI;AAEJ,eAAa,GAAG,SAAS;AACzB,MAAI,YAAY,cAAc,OAAO,SAAS,KAAK,CAAC;AACpD,MAAI,UAAU,OAAO,SAAS,GAAG;AACjC,MAAI,YAAY,UAAU,QAAQ;AAClC,MAAI,UAAU,QAAQ,QAAQ;AAE9B,MAAI,aAAa,SAAS;AACxB,UAAM,IAAI,WAAW,kBAAkB;AAAA,EACzC;AAEA,MAAI,QAAQ,CAAC;AACb,MAAI,cAAc;AAClB,MAAI,OAAO,QAAQ,gBAAgB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,QAAQ,kBAAkB,SAAS,gBAAgB,CAAC;AAC3J,MAAI,OAAO,KAAK,MAAM,IAAI,EAAG,OAAM,IAAI,WAAW,4DAA4D;AAE9G,SAAO,YAAY,QAAQ,KAAK,SAAS;AACvC,UAAM,KAAK,OAAO,WAAW,CAAC;AAC9B,kBAAc,WAAW,aAAa,IAAI;AAAA,EAC5C;AAEA,SAAO;AACT;;;AC1Be,SAAR,oBAAqC,eAAe;AACzD,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,iBAAiB,CAAC;AACjC,MAAI,YAAY,OAAO,SAAS,KAAK;AACrC,MAAI,UAAU,OAAO,SAAS,GAAG;AACjC,MAAI,UAAU,QAAQ,QAAQ;AAC9B,MAAI,QAAQ,CAAC;AAEb,MAAI,EAAE,UAAU,QAAQ,KAAK,UAAU;AACrC,UAAM,IAAI,WAAW,kBAAkB;AAAA,EACzC;AAEA,MAAI,cAAc;AAClB,cAAY,SAAS,GAAG,GAAG,GAAG,CAAC;AAC/B,cAAY,QAAQ,CAAC;AAErB,SAAO,YAAY,QAAQ,KAAK,SAAS;AACvC,UAAM,KAAK,OAAO,WAAW,CAAC;AAC9B,gBAAY,SAAS,YAAY,SAAS,IAAI,CAAC;AAAA,EACjD;AAEA,SAAO;AACT;;;AClCe,SAAR,eAAgC,WAAW;AAChD,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,eAAe,KAAK,SAAS;AACjC,MAAI,QAAQ,eAAe,eAAe;AAC1C,OAAK,SAAS,OAAO,CAAC;AACtB,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;;;ACEe,SAAR,sBAAuC,eAAe;AAC3D,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,iBAAiB,CAAC;AACjC,MAAI,YAAY,OAAO,SAAS,KAAK;AACrC,MAAI,UAAU,OAAO,SAAS,GAAG;AACjC,MAAI,UAAU,QAAQ,QAAQ;AAE9B,MAAI,EAAE,UAAU,QAAQ,KAAK,UAAU;AACrC,UAAM,IAAI,WAAW,kBAAkB;AAAA,EACzC;AAEA,MAAI,mBAAmB,eAAe,SAAS;AAC/C,MAAI,iBAAiB,eAAe,OAAO;AAC3C,YAAU,eAAe,QAAQ;AACjC,MAAI,WAAW,CAAC;AAChB,MAAI,iBAAiB;AAErB,SAAO,eAAe,QAAQ,KAAK,SAAS;AAC1C,aAAS,KAAK,OAAO,cAAc,CAAC;AACpC,qBAAiB,YAAY,gBAAgB,CAAC;AAAA,EAChD;AAEA,SAAO;AACT;;;ACde,SAAR,mBAAoC,eAAe,SAAS;AACjE,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,iBAAiB,CAAC;AACjC,MAAI,YAAY,OAAO,SAAS,KAAK;AACrC,MAAI,UAAU,OAAO,SAAS,GAAG;AACjC,MAAI,UAAU,QAAQ,QAAQ;AAE9B,MAAI,EAAE,UAAU,QAAQ,KAAK,UAAU;AACrC,UAAM,IAAI,WAAW,kBAAkB;AAAA,EACzC;AAEA,MAAI,gBAAgB,YAAY,WAAW,OAAO;AAClD,MAAI,cAAc,YAAY,SAAS,OAAO;AAE9C,gBAAc,SAAS,EAAE;AACzB,cAAY,SAAS,EAAE;AACvB,YAAU,YAAY,QAAQ;AAC9B,MAAI,QAAQ,CAAC;AACb,MAAI,cAAc;AAElB,SAAO,YAAY,QAAQ,KAAK,SAAS;AACvC,gBAAY,SAAS,CAAC;AACtB,UAAM,KAAK,OAAO,WAAW,CAAC;AAC9B,kBAAc,SAAS,aAAa,CAAC;AACrC,gBAAY,SAAS,EAAE;AAAA,EACzB;AAEA,SAAO;AACT;;;ACpCe,SAAR,sBAAuC,UAAU;AACtD,eAAa,GAAG,SAAS;AACzB,MAAI,eAAe,kBAAkB,QAAQ;AAC7C,MAAI,WAAW,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,QAAQ,aAAa,QAAQ;AAClC,QAAI,OAAO,aAAa,OAAO;AAE/B,QAAI,UAAU,IAAI,GAAG;AACnB,eAAS,KAAK,IAAI;AAClB,UAAI,SAAS,IAAI,EAAG,SAAQ,QAAQ;AAAA,IACtC;AAAA,EACF;AAEA,SAAO;AACT;;;AChBe,SAAR,mBAAoC,WAAW;AACpD,eAAa,GAAG,SAAS;AACzB,MAAI,YAAY,aAAa,SAAS;AACtC,MAAI,MAAM,UAAU,QAAQ,CAAC,EAAG,OAAM,IAAI,WAAW,4BAA4B;AACjF,MAAI,UAAU,WAAW,SAAS;AAClC,SAAO,sBAAsB;AAAA,IAC3B,OAAO;AAAA,IACP,KAAK;AAAA,EACP,CAAC;AACH;;;ACZe,SAAR,kBAAmC,WAAW;AACnD,eAAa,GAAG,SAAS;AACzB,MAAI,YAAY,YAAY,SAAS;AACrC,MAAI,UAAU,UAAU,SAAS;AACjC,SAAO,sBAAsB;AAAA,IAC3B,OAAO;AAAA,IACP,KAAK;AAAA,EACP,CAAC;AACH;;;ACPe,SAAR,mBAAoC,eAAe;AACxD,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,iBAAiB,CAAC;AACjC,MAAI,YAAY,OAAO,SAAS,KAAK;AACrC,MAAI,UAAU,OAAO,SAAS,GAAG;AACjC,MAAI,UAAU,QAAQ,QAAQ;AAE9B,MAAI,EAAE,UAAU,QAAQ,KAAK,UAAU;AACrC,UAAM,IAAI,WAAW,kBAAkB;AAAA,EACzC;AAEA,MAAI,QAAQ,CAAC;AACb,MAAI,cAAc;AAClB,cAAY,SAAS,GAAG,GAAG,GAAG,CAAC;AAC/B,cAAY,SAAS,GAAG,CAAC;AAEzB,SAAO,YAAY,QAAQ,KAAK,SAAS;AACvC,UAAM,KAAK,OAAO,WAAW,CAAC;AAC9B,gBAAY,YAAY,YAAY,YAAY,IAAI,CAAC;AAAA,EACvD;AAEA,SAAO;AACT;;;AC7Be,SAAR,YAA6B,WAAW;AAC7C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,KAAK,YAAY;AAC5B,MAAI,SAAS,IAAI,KAAK,MAAM,OAAO,EAAE,IAAI;AACzC,OAAK,YAAY,QAAQ,IAAI,EAAE;AAC/B,OAAK,SAAS,IAAI,IAAI,IAAI,GAAG;AAC7B,SAAO;AACT;;;ACVe,SAAR,UAA2B,WAAW;AAC3C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,OAAK,WAAW,IAAI,IAAI,GAAG;AAC3B,SAAO;AACT;;;ACHe,SAAR,aAA8B,WAAW;AAC9C,eAAa,GAAG,SAAS;AACzB,SAAO,UAAU,WAAW;AAAA,IAC1B,cAAc;AAAA,EAChB,CAAC;AACH;;;ACHe,SAAR,iBAAkC,WAAW;AAClD,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,eAAe,SAAS;AACnC,MAAI,4BAA4B,oBAAI,KAAK,CAAC;AAC1C,4BAA0B,YAAY,OAAO,GAAG,GAAG,CAAC;AACpD,4BAA0B,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7C,MAAI,OAAO,eAAe,yBAAyB;AACnD,OAAK,gBAAgB,KAAK,gBAAgB,IAAI,CAAC;AAC/C,SAAO;AACT;;;ACbe,SAAR,YAA6B,WAAW;AAC7C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,OAAK,WAAW,IAAI,GAAG;AACvB,SAAO;AACT;;;ACLe,SAAR,aAA8B,WAAW;AAC9C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,eAAe,KAAK,SAAS;AACjC,MAAI,QAAQ,eAAe,eAAe,IAAI;AAC9C,OAAK,SAAS,OAAO,CAAC;AACtB,OAAK,SAAS,IAAI,IAAI,IAAI,GAAG;AAC7B,SAAO;AACT;;;ACRe,SAAR,YAA6B,WAAW;AAC7C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,OAAK,gBAAgB,GAAG;AACxB,SAAO;AACT;;;ACLe,SAAR,aAA8B;AACnC,SAAO,SAAS,KAAK,IAAI,CAAC;AAC5B;;;ACJe,SAAR,gBAAiC;AACtC,MAAI,MAAM,oBAAI,KAAK;AACnB,MAAI,OAAO,IAAI,YAAY;AAC3B,MAAI,QAAQ,IAAI,SAAS;AACzB,MAAI,MAAM,IAAI,QAAQ;AACtB,MAAI,OAAO,oBAAI,KAAK,CAAC;AACrB,OAAK,YAAY,MAAM,OAAO,MAAM,CAAC;AACrC,OAAK,SAAS,IAAI,IAAI,IAAI,GAAG;AAC7B,SAAO;AACT;;;ACTe,SAAR,iBAAkC;AACvC,MAAI,MAAM,oBAAI,KAAK;AACnB,MAAI,OAAO,IAAI,YAAY;AAC3B,MAAI,QAAQ,IAAI,SAAS;AACzB,MAAI,MAAM,IAAI,QAAQ;AACtB,MAAI,OAAO,oBAAI,KAAK,CAAC;AACrB,OAAK,YAAY,MAAM,OAAO,MAAM,CAAC;AACrC,OAAK,SAAS,IAAI,IAAI,IAAI,GAAG;AAC7B,SAAO;AACT;;;AC3Be,SAAR,YAA6B,QAAQ;AAC1C,SAAO,OAAO,CAAC,GAAG,MAAM;AAC1B;;;ACOA,IAAI,iBAAiB;AACrB,IAAI,6BAA6B;AACjC,IAAI,mBAAmB;AACvB,IAAI,wBAAwB;AAkFb,SAAR,eAAgC,WAAW,eAAe,SAAS;AACxE,MAAI,MAAM;AAEV,eAAa,GAAG,SAAS;AACzB,MAAI,iBAAiB,kBAAkB;AACvC,MAAI,UAAU,QAAQ,kBAAkB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,kBAAkB,eAAe,YAAY,QAAQ,SAAS,SAAS,OAAO;AAEjO,MAAI,CAAC,OAAO,gBAAgB;AAC1B,UAAM,IAAI,WAAW,6CAA6C;AAAA,EACpE;AAEA,MAAI,aAAa,WAAW,WAAW,aAAa;AAEpD,MAAI,MAAM,UAAU,GAAG;AACrB,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,MAAI,kBAAkB,OAAO,YAAY,OAAO,GAAG;AAAA,IACjD,WAAW,QAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,SAAS;AAAA,IACtF;AAAA,EACF,CAAC;AACD,MAAI;AACJ,MAAI;AAEJ,MAAI,aAAa,GAAG;AAClB,eAAW,OAAO,aAAa;AAC/B,gBAAY,OAAO,SAAS;AAAA,EAC9B,OAAO;AACL,eAAW,OAAO,SAAS;AAC3B,gBAAY,OAAO,aAAa;AAAA,EAClC;AAEA,MAAI,UAAU,oBAAoB,WAAW,QAAQ;AACrD,MAAI,mBAAmB,gCAAgC,SAAS,IAAI,gCAAgC,QAAQ,KAAK;AACjH,MAAI,UAAU,KAAK,OAAO,UAAU,mBAAmB,EAAE;AACzD,MAAIC;AAEJ,MAAI,UAAU,GAAG;AACf,QAAI,YAAY,QAAQ,YAAY,UAAU,QAAQ,gBAAgB;AACpE,UAAI,UAAU,GAAG;AACf,eAAO,OAAO,eAAe,oBAAoB,GAAG,eAAe;AAAA,MACrE,WAAW,UAAU,IAAI;AACvB,eAAO,OAAO,eAAe,oBAAoB,IAAI,eAAe;AAAA,MACtE,WAAW,UAAU,IAAI;AACvB,eAAO,OAAO,eAAe,oBAAoB,IAAI,eAAe;AAAA,MACtE,WAAW,UAAU,IAAI;AACvB,eAAO,OAAO,eAAe,eAAe,GAAG,eAAe;AAAA,MAChE,WAAW,UAAU,IAAI;AACvB,eAAO,OAAO,eAAe,oBAAoB,GAAG,eAAe;AAAA,MACrE,OAAO;AACL,eAAO,OAAO,eAAe,YAAY,GAAG,eAAe;AAAA,MAC7D;AAAA,IACF,OAAO;AACL,UAAI,YAAY,GAAG;AACjB,eAAO,OAAO,eAAe,oBAAoB,GAAG,eAAe;AAAA,MACrE,OAAO;AACL,eAAO,OAAO,eAAe,YAAY,SAAS,eAAe;AAAA,MACnE;AAAA,IACF;AAAA,EAEF,WAAW,UAAU,IAAI;AACvB,WAAO,OAAO,eAAe,YAAY,SAAS,eAAe;AAAA,EACnE,WAAW,UAAU,IAAI;AACvB,WAAO,OAAO,eAAe,eAAe,GAAG,eAAe;AAAA,EAChE,WAAW,UAAU,gBAAgB;AACnC,QAAI,QAAQ,KAAK,MAAM,UAAU,EAAE;AACnC,WAAO,OAAO,eAAe,eAAe,OAAO,eAAe;AAAA,EACpE,WAAW,UAAU,4BAA4B;AAC/C,WAAO,OAAO,eAAe,SAAS,GAAG,eAAe;AAAA,EAC1D,WAAW,UAAU,kBAAkB;AACrC,QAAIC,QAAO,KAAK,MAAM,UAAU,cAAc;AAC9C,WAAO,OAAO,eAAe,SAASA,OAAM,eAAe;AAAA,EAC7D,WAAW,UAAU,uBAAuB;AAC1C,IAAAD,UAAS,KAAK,MAAM,UAAU,gBAAgB;AAC9C,WAAO,OAAO,eAAe,gBAAgBA,SAAQ,eAAe;AAAA,EACtE;AAEA,EAAAA,UAAS,mBAAmB,WAAW,QAAQ;AAE/C,MAAIA,UAAS,IAAI;AACf,QAAI,eAAe,KAAK,MAAM,UAAU,gBAAgB;AACxD,WAAO,OAAO,eAAe,WAAW,cAAc,eAAe;AAAA,EACvE,OAAO;AACL,QAAI,yBAAyBA,UAAS;AACtC,QAAI,QAAQ,KAAK,MAAMA,UAAS,EAAE;AAElC,QAAI,yBAAyB,GAAG;AAC9B,aAAO,OAAO,eAAe,eAAe,OAAO,eAAe;AAAA,IACpE,WAAW,yBAAyB,GAAG;AACrC,aAAO,OAAO,eAAe,cAAc,OAAO,eAAe;AAAA,IACnE,OAAO;AACL,aAAO,OAAO,eAAe,gBAAgB,QAAQ,GAAG,eAAe;AAAA,IACzE;AAAA,EACF;AACF;;;ACrLA,IAAI,yBAAyB,MAAO;AACpC,IAAIE,kBAAiB,KAAK;AAC1B,IAAIC,oBAAmBD,kBAAiB;AACxC,IAAI,kBAAkBA,kBAAiB;AAmFxB,SAAR,qBAAsC,WAAW,eAAe,SAAS;AAC9E,MAAI,MAAM,iBAAiB;AAE3B,eAAa,GAAG,SAAS;AACzB,MAAI,iBAAiB,kBAAkB;AACvC,MAAI,UAAU,QAAQ,kBAAkB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,kBAAkB,eAAe,YAAY,QAAQ,SAAS,SAAS,OAAO;AAEjO,MAAI,CAAC,OAAO,gBAAgB;AAC1B,UAAM,IAAI,WAAW,sDAAsD;AAAA,EAC7E;AAEA,MAAI,aAAa,WAAW,WAAW,aAAa;AAEpD,MAAI,MAAM,UAAU,GAAG;AACrB,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,MAAI,kBAAkB,OAAO,YAAY,OAAO,GAAG;AAAA,IACjD,WAAW,QAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,SAAS;AAAA,IACtF;AAAA,EACF,CAAC;AACD,MAAI;AACJ,MAAI;AAEJ,MAAI,aAAa,GAAG;AAClB,eAAW,OAAO,aAAa;AAC/B,gBAAY,OAAO,SAAS;AAAA,EAC9B,OAAO;AACL,eAAW,OAAO,SAAS;AAC3B,gBAAY,OAAO,aAAa;AAAA,EAClC;AAEA,MAAI,iBAAiB,QAAQ,wBAAwB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,oBAAoB,QAAQ,0BAA0B,SAAS,wBAAwB,OAAO;AAC7M,MAAI;AAEJ,MAAI,mBAAmB,SAAS;AAC9B,uBAAmB,KAAK;AAAA,EAC1B,WAAW,mBAAmB,QAAQ;AACpC,uBAAmB,KAAK;AAAA,EAC1B,WAAW,mBAAmB,SAAS;AACrC,uBAAmB,KAAK;AAAA,EAC1B,OAAO;AACL,UAAM,IAAI,WAAW,mDAAmD;AAAA,EAC1E;AAEA,MAAIE,gBAAe,UAAU,QAAQ,IAAI,SAAS,QAAQ;AAC1D,MAAI,UAAUA,gBAAe;AAC7B,MAAI,iBAAiB,gCAAgC,SAAS,IAAI,gCAAgC,QAAQ;AAG1G,MAAI,wBAAwBA,gBAAe,kBAAkB;AAC7D,MAAI,cAAc,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAC5E,MAAI;AAEJ,MAAI,CAAC,aAAa;AAChB,QAAI,UAAU,GAAG;AACf,aAAO;AAAA,IACT,WAAW,UAAU,IAAI;AACvB,aAAO;AAAA,IACT,WAAW,UAAUF,iBAAgB;AACnC,aAAO;AAAA,IACT,WAAW,uBAAuBC,mBAAkB;AAClD,aAAO;AAAA,IACT,WAAW,uBAAuB,iBAAiB;AACjD,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,WAAO,OAAO,WAAW;AAAA,EAC3B;AAGA,MAAI,SAAS,UAAU;AACrB,QAAI,UAAU,iBAAiBC,gBAAe,GAAI;AAClD,WAAO,OAAO,eAAe,YAAY,SAAS,eAAe;AAAA,EACnE,WAAW,SAAS,UAAU;AAC5B,QAAI,iBAAiB,iBAAiB,OAAO;AAC7C,WAAO,OAAO,eAAe,YAAY,gBAAgB,eAAe;AAAA,EAC1E,WAAW,SAAS,QAAQ;AAC1B,QAAI,QAAQ,iBAAiB,UAAU,EAAE;AACzC,WAAO,OAAO,eAAe,UAAU,OAAO,eAAe;AAAA,EAC/D,WAAW,SAAS,OAAO;AACzB,QAAIC,QAAO,iBAAiB,uBAAuBH,eAAc;AACjE,WAAO,OAAO,eAAe,SAASG,OAAM,eAAe;AAAA,EAC7D,WAAW,SAAS,SAAS;AAC3B,QAAIC,UAAS,iBAAiB,uBAAuBH,iBAAgB;AACrE,WAAOG,YAAW,MAAM,gBAAgB,UAAU,OAAO,eAAe,UAAU,GAAG,eAAe,IAAI,OAAO,eAAe,WAAWA,SAAQ,eAAe;AAAA,EAClK,WAAW,SAAS,QAAQ;AAC1B,QAAI,QAAQ,iBAAiB,uBAAuB,eAAe;AACnE,WAAO,OAAO,eAAe,UAAU,OAAO,eAAe;AAAA,EAC/D;AAEA,QAAM,IAAI,WAAW,mEAAmE;AAC1F;;;ACnGe,SAAR,oBAAqC,WAAW,SAAS;AAC9D,eAAa,GAAG,SAAS;AACzB,SAAO,eAAgB,WAAW,KAAK,IAAI,GAAG,OAAO;AACvD;;;ACfe,SAAR,0BAA2C,WAAW,SAAS;AACpE,eAAa,GAAG,SAAS;AACzB,SAAO,qBAAqB,WAAW,KAAK,IAAI,GAAG,OAAO;AAC5D;;;AC9EA,IAAI,gBAAgB,CAAC,SAAS,UAAU,SAAS,QAAQ,SAAS,WAAW,SAAS;AAgEvE,SAAR,eAAgC,UAAU,SAAS;AACxD,MAAI,MAAM,iBAAiB,iBAAiB,eAAe;AAE3D,MAAI,UAAU,SAAS,GAAG;AACxB,UAAM,IAAI,UAAU,iCAAiC,OAAO,UAAU,QAAQ,UAAU,CAAC;AAAA,EAC3F;AAEA,MAAI,iBAAiB,kBAAkB;AACvC,MAAI,UAAU,QAAQ,kBAAkB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,kBAAkB,eAAe,YAAY,QAAQ,SAAS,SAAS,OAAO;AACjO,MAAIC,WAAU,kBAAkB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,kBAAkB;AAC7J,MAAI,QAAQ,gBAAgB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,QAAQ,kBAAkB,SAAS,gBAAgB;AACnJ,MAAI,aAAa,qBAAqB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,eAAe,QAAQ,uBAAuB,SAAS,qBAAqB;AAE5K,MAAI,CAAC,OAAO,gBAAgB;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,QAAO,OAAO,SAAU,KAAK,MAAM;AAC9C,QAAI,QAAQ,IAAI,OAAO,KAAK,QAAQ,QAAQ,SAAU,GAAG;AACvD,aAAO,EAAE,YAAY;AAAA,IACvB,CAAC,CAAC;AACF,QAAI,QAAQ,SAAS,IAAI;AAEzB,QAAI,OAAO,UAAU,aAAa,QAAQ,SAAS,IAAI,IAAI;AACzD,aAAO,IAAI,OAAO,OAAO,eAAe,OAAO,KAAK,CAAC;AAAA,IACvD;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC,EAAE,KAAK,SAAS;AACrB,SAAO;AACT;;;ACtDe,SAAR,cAA+B,WAAW,SAAS;AACxD,MAAI,iBAAiB;AAErB,MAAI,UAAU,SAAS,GAAG;AACxB,UAAM,IAAI,UAAU,iCAAiC,OAAO,UAAU,QAAQ,UAAU,CAAC;AAAA,EAC3F;AAEA,MAAI,eAAe,OAAO,SAAS;AAEnC,MAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,MAAIC,UAAS,QAAQ,kBAAkB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,kBAAkB,UAAU;AAC9K,MAAI,iBAAiB,QAAQ,wBAAwB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,oBAAoB,QAAQ,0BAA0B,SAAS,wBAAwB,UAAU;AAEhN,MAAIA,YAAW,cAAcA,YAAW,SAAS;AAC/C,UAAM,IAAI,WAAW,sCAAsC;AAAA,EAC7D;AAEA,MAAI,mBAAmB,UAAU,mBAAmB,UAAU,mBAAmB,YAAY;AAC3F,UAAM,IAAI,WAAW,sDAAsD;AAAA,EAC7E;AAEA,MAAI,SAAS;AACb,MAAI,gBAAgBA,YAAW,aAAa,MAAM;AAClD,MAAI,gBAAgBA,YAAW,aAAa,MAAM;AAElD,MAAI,mBAAmB,QAAQ;AAC7B,QAAI,MAAM,gBAAgB,aAAa,QAAQ,GAAG,CAAC;AACnD,QAAI,QAAQ,gBAAgB,aAAa,SAAS,IAAI,GAAG,CAAC;AAC1D,QAAI,OAAO,gBAAgB,aAAa,YAAY,GAAG,CAAC;AAExD,aAAS,GAAG,OAAO,IAAI,EAAE,OAAO,aAAa,EAAE,OAAO,KAAK,EAAE,OAAO,aAAa,EAAE,OAAO,GAAG;AAAA,EAC/F;AAGA,MAAI,mBAAmB,QAAQ;AAC7B,QAAI,OAAO,gBAAgB,aAAa,SAAS,GAAG,CAAC;AACrD,QAAI,SAAS,gBAAgB,aAAa,WAAW,GAAG,CAAC;AACzD,QAAI,SAAS,gBAAgB,aAAa,WAAW,GAAG,CAAC;AAEzD,QAAI,YAAY,WAAW,KAAK,KAAK;AAErC,aAAS,GAAG,OAAO,MAAM,EAAE,OAAO,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,aAAa,EAAE,OAAO,MAAM,EAAE,OAAO,aAAa,EAAE,OAAO,MAAM;AAAA,EACpI;AAEA,SAAO;AACT;;;AC1FA,SAASC,SAAQ,KAAK;AAAE;AAA2B,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AAAE,IAAAA,WAAU,SAASA,SAAQC,MAAK;AAAE,aAAO,OAAOA;AAAA,IAAK;AAAA,EAAG,OAAO;AAAE,IAAAD,WAAU,SAASA,SAAQC,MAAK;AAAE,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAAK;AAAA,EAAG;AAAE,SAAOD,SAAQ,GAAG;AAAG;AA8B1W,SAAR,kBAAmC,UAAU;AAClD,eAAa,GAAG,SAAS;AACzB,MAAIA,SAAQ,QAAQ,MAAM,SAAU,OAAM,IAAI,MAAM,4BAA4B;AAChF,MAAI,kBAAkB,SAAS,OAC3B,QAAQ,oBAAoB,SAAS,IAAI,iBACzC,mBAAmB,SAAS,QAC5BE,UAAS,qBAAqB,SAAS,IAAI,kBAC3C,iBAAiB,SAAS,MAC1BC,QAAO,mBAAmB,SAAS,IAAI,gBACvC,kBAAkB,SAAS,OAC3B,QAAQ,oBAAoB,SAAS,IAAI,iBACzC,oBAAoB,SAAS,SAC7B,UAAU,sBAAsB,SAAS,IAAI,mBAC7C,oBAAoB,SAAS,SAC7B,UAAU,sBAAsB,SAAS,IAAI;AACjD,SAAO,IAAI,OAAO,OAAO,GAAG,EAAE,OAAOD,SAAQ,GAAG,EAAE,OAAOC,OAAM,IAAI,EAAE,OAAO,OAAO,GAAG,EAAE,OAAO,SAAS,GAAG,EAAE,OAAO,SAAS,GAAG;AAClI;;;ACVe,SAAR,cAA+B,WAAW,SAAS;AACxD,MAAI;AAEJ,MAAI,UAAU,SAAS,GAAG;AACxB,UAAM,IAAI,UAAU,kCAAkC,OAAO,UAAU,QAAQ,UAAU,CAAC;AAAA,EAC5F;AAEA,MAAI,eAAe,OAAO,SAAS;AAEnC,MAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,MAAI,iBAAiB,QAAQ,wBAAwB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,oBAAoB,QAAQ,0BAA0B,SAAS,wBAAwB,CAAC;AAEvM,MAAI,EAAE,kBAAkB,KAAK,kBAAkB,IAAI;AACjD,UAAM,IAAI,WAAW,oDAAoD;AAAA,EAC3E;AAEA,MAAI,MAAM,gBAAgB,aAAa,QAAQ,GAAG,CAAC;AACnD,MAAI,QAAQ,gBAAgB,aAAa,SAAS,IAAI,GAAG,CAAC;AAC1D,MAAI,OAAO,aAAa,YAAY;AACpC,MAAI,OAAO,gBAAgB,aAAa,SAAS,GAAG,CAAC;AACrD,MAAI,SAAS,gBAAgB,aAAa,WAAW,GAAG,CAAC;AACzD,MAAI,SAAS,gBAAgB,aAAa,WAAW,GAAG,CAAC;AACzD,MAAI,mBAAmB;AAEvB,MAAI,iBAAiB,GAAG;AACtB,QAAIC,gBAAe,aAAa,gBAAgB;AAChD,QAAI,oBAAoB,KAAK,MAAMA,gBAAe,KAAK,IAAI,IAAI,iBAAiB,CAAC,CAAC;AAClF,uBAAmB,MAAM,gBAAgB,mBAAmB,cAAc;AAAA,EAC5E;AAEA,MAAI,SAAS;AACb,MAAI,WAAW,aAAa,kBAAkB;AAE9C,MAAI,aAAa,GAAG;AAClB,QAAI,iBAAiB,KAAK,IAAI,QAAQ;AACtC,QAAI,aAAa,gBAAgB,UAAU,iBAAiB,EAAE,GAAG,CAAC;AAClE,QAAI,eAAe,gBAAgB,iBAAiB,IAAI,CAAC;AAEzD,QAAI,OAAO,WAAW,IAAI,MAAM;AAChC,aAAS,GAAG,OAAO,IAAI,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,YAAY;AAAA,EACtE,OAAO;AACL,aAAS;AAAA,EACX;AAEA,SAAO,GAAG,OAAO,MAAM,GAAG,EAAE,OAAO,OAAO,GAAG,EAAE,OAAO,KAAK,GAAG,EAAE,OAAO,MAAM,GAAG,EAAE,OAAO,QAAQ,GAAG,EAAE,OAAO,MAAM,EAAE,OAAO,gBAAgB,EAAE,OAAO,MAAM;AAC7J;;;ACjFA,IAAI,OAAO,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAC3D,IAAI,SAAS,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAqBjF,SAAR,cAA+B,WAAW;AAC/C,MAAI,UAAU,SAAS,GAAG;AACxB,UAAM,IAAI,UAAU,kCAAkC,OAAO,UAAU,QAAQ,UAAU,CAAC;AAAA,EAC5F;AAEA,MAAI,eAAe,OAAO,SAAS;AAEnC,MAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,MAAI,UAAU,KAAK,aAAa,UAAU,CAAC;AAC3C,MAAI,aAAa,gBAAgB,aAAa,WAAW,GAAG,CAAC;AAC7D,MAAI,YAAY,OAAO,aAAa,YAAY,CAAC;AACjD,MAAI,OAAO,aAAa,eAAe;AACvC,MAAI,OAAO,gBAAgB,aAAa,YAAY,GAAG,CAAC;AACxD,MAAI,SAAS,gBAAgB,aAAa,cAAc,GAAG,CAAC;AAC5D,MAAI,SAAS,gBAAgB,aAAa,cAAc,GAAG,CAAC;AAE5D,SAAO,GAAG,OAAO,SAAS,IAAI,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,WAAW,GAAG,EAAE,OAAO,MAAM,GAAG,EAAE,OAAO,MAAM,GAAG,EAAE,OAAO,QAAQ,GAAG,EAAE,OAAO,QAAQ,MAAM;AAC9J;;;ACCe,SAAR,eAAgC,WAAW,eAAe,SAAS;AACxE,MAAI,MAAM,iBAAiB,OAAO,OAAO,OAAO,uBAAuB,kBAAkB,uBAAuB,uBAAuB;AAEvI,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,WAAW,OAAO,aAAa;AACnC,MAAI,iBAAiB,kBAAkB;AACvC,MAAI,UAAU,QAAQ,kBAAkB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,kBAAkB,eAAe,YAAY,QAAQ,SAAS,SAAS,OAAO;AACjO,MAAI,eAAe,WAAW,SAAS,SAAS,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,kBAAkB,QAAQ,0BAA0B,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,UAAU,mBAAmB,QAAQ,YAAY,QAAQ,qBAAqB,SAAS,UAAU,wBAAwB,iBAAiB,aAAa,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,kBAAkB,QAAQ,UAAU,SAAS,QAAQ,eAAe,kBAAkB,QAAQ,UAAU,SAAS,SAAS,wBAAwB,eAAe,YAAY,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,aAAa,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,kBAAkB,QAAQ,UAAU,SAAS,QAAQ,CAAC;AAE14B,MAAI,CAAC,OAAO,UAAU;AACpB,UAAM,IAAI,WAAW,uCAAuC;AAAA,EAC9D;AAEA,MAAI,CAAC,OAAO,YAAY;AACtB,UAAM,IAAI,WAAW,yCAAyC;AAAA,EAChE;AAEA,MAAI,CAAC,OAAO,gBAAgB;AAC1B,UAAM,IAAI,WAAW,6CAA6C;AAAA,EACpE;AAEA,MAAI,OAAO,yBAAyB,MAAM,QAAQ;AAElD,MAAI,MAAM,IAAI,GAAG;AACf,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,MAAI;AAEJ,MAAI,OAAO,IAAI;AACb,YAAQ;AAAA,EACV,WAAW,OAAO,IAAI;AACpB,YAAQ;AAAA,EACV,WAAW,OAAO,GAAG;AACnB,YAAQ;AAAA,EACV,WAAW,OAAO,GAAG;AACnB,YAAQ;AAAA,EACV,WAAW,OAAO,GAAG;AACnB,YAAQ;AAAA,EACV,WAAW,OAAO,GAAG;AACnB,YAAQ;AAAA,EACV,OAAO;AACL,YAAQ;AAAA,EACV;AAEA,MAAI,UAAU,gBAAgB,MAAM,gCAAgC,IAAI,CAAC;AACzE,MAAI,cAAc,gBAAgB,UAAU,gCAAgC,QAAQ,CAAC;AACrF,MAAI,YAAY,OAAO,eAAe,OAAO,SAAS,aAAa;AAAA,IACjE;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO,OAAO,MAAM,WAAW;AAAA,IAC7B;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACjFe,SAAR,aAA8B,eAAe;AAClD,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,UAAU,aAAa;AACtC,SAAO,OAAO,WAAW,GAAI;AAC/B;;;ACHe,SAAR,aAA8B,WAAW;AAC9C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,yBAAyB,MAAM,YAAY,IAAI,CAAC;AAC3D,MAAI,YAAY,OAAO;AACvB,SAAO;AACT;;;ACRe,SAAR,WAA4B,WAAW;AAC5C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,KAAK,YAAY;AAC5B,SAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,KAAK,OAAO,QAAQ;AAC9D;;;ACJe,SAAR,cAA+B,WAAW;AAC/C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAE3B,MAAI,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,gBAAgB;AAC7C,WAAO;AAAA,EACT;AAEA,SAAO,WAAW,IAAI,IAAI,MAAM;AAClC;;;ACVe,SAAR,UAA2B,WAAW;AAC3C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,KAAK,YAAY;AAC5B,MAAI,SAAS,KAAK,MAAM,OAAO,EAAE,IAAI;AACrC,SAAO;AACT;;;ACCe,SAARC,qBAAqC;AAC1C,SAAO,OAAO,CAAC,GAAG,kBAA0B,CAAC;AAC/C;;;ACNe,SAAR,UAA2B,WAAW;AAC3C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,MAAM,KAAK,OAAO;AAEtB,MAAI,QAAQ,GAAG;AACb,UAAM;AAAA,EACR;AAEA,SAAO;AACT;;;AC7BA,IAAIC,wBAAuB;AAqBZ,SAAR,WAA4B,WAAW;AAC5C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,eAAe,IAAI,EAAE,QAAQ,IAAI,mBAAmB,IAAI,EAAE,QAAQ;AAI7E,SAAO,KAAK,MAAM,OAAOA,qBAAoB,IAAI;AACnD;;;AC9BA,IAAIC,wBAAuB;AAqBZ,SAAR,kBAAmC,WAAW;AACnD,eAAa,GAAG,SAAS;AACzB,MAAI,WAAW,mBAAmB,SAAS;AAC3C,MAAI,WAAW,mBAAmB,SAAS,UAAU,EAAE,CAAC;AACxD,MAAI,OAAO,SAAS,QAAQ,IAAI,SAAS,QAAQ;AAIjD,SAAO,KAAK,MAAM,OAAOA,qBAAoB;AAC/C;;;ACbe,SAAR,gBAAiC,WAAW;AACjD,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAIC,gBAAe,KAAK,gBAAgB;AACxC,SAAOA;AACT;;;ACvBA,IAAI,sBAAsB,KAAK,KAAK,KAAK;AAiC1B,SAAR,8BAA+C,mBAAmB,oBAAoB;AAC3F,eAAa,GAAG,SAAS;AACzB,MAAI,eAAe,qBAAqB,CAAC;AACzC,MAAI,gBAAgB,sBAAsB,CAAC;AAC3C,MAAI,gBAAgB,OAAO,aAAa,KAAK,EAAE,QAAQ;AACvD,MAAI,cAAc,OAAO,aAAa,GAAG,EAAE,QAAQ;AACnD,MAAI,iBAAiB,OAAO,cAAc,KAAK,EAAE,QAAQ;AACzD,MAAI,eAAe,OAAO,cAAc,GAAG,EAAE,QAAQ;AAErD,MAAI,EAAE,iBAAiB,eAAe,kBAAkB,eAAe;AACrE,UAAM,IAAI,WAAW,kBAAkB;AAAA,EACzC;AAEA,MAAI,gBAAgB,gBAAgB,gBAAgB,iBAAiB;AAErE,MAAI,CAAC,eAAe;AAClB,WAAO;AAAA,EACT;AAEA,MAAI,mBAAmB,iBAAiB,gBAAgB,gBAAgB;AACxE,MAAI,iBAAiB,eAAe,cAAc,cAAc;AAChE,MAAI,iBAAiB,iBAAiB;AACtC,SAAO,KAAK,KAAK,iBAAiB,mBAAmB;AACvD;;;ACtCe,SAAR,QAAyB,WAAW;AACzC,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,YAAY,KAAK,QAAQ;AAC7B,SAAO;AACT;;;ACLe,SAAR,YAA6B,WAAW;AAC7C,eAAa,GAAG,SAAS;AACzB,SAAO,KAAK,MAAM,QAAQ,SAAS,IAAI,GAAI;AAC7C;;;ACsBe,SAAR,YAA6B,WAAW,SAAS;AACtD,MAAI,MAAM,OAAO,OAAO,uBAAuB,iBAAiB,uBAAuB,uBAAuB;AAE9G,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,KAAK,YAAY;AAC5B,MAAI,iBAAiB,kBAAkB;AACvC,MAAI,wBAAwB,WAAW,QAAQ,SAAS,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,2BAA2B,QAAQ,0BAA0B,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,UAAU,kBAAkB,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,UAAU,wBAAwB,gBAAgB,aAAa,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,2BAA2B,QAAQ,UAAU,SAAS,QAAQ,eAAe,2BAA2B,QAAQ,UAAU,SAAS,SAAS,wBAAwB,eAAe,YAAY,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,aAAa,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,2BAA2B,QAAQ,SAAS,SAAS,OAAO,CAAC;AAEj7B,MAAI,EAAE,yBAAyB,KAAK,yBAAyB,IAAI;AAC/D,UAAM,IAAI,WAAW,2DAA2D;AAAA,EAClF;AAEA,MAAI,sBAAsB,oBAAI,KAAK,CAAC;AACpC,sBAAoB,YAAY,OAAO,GAAG,GAAG,qBAAqB;AAClE,sBAAoB,SAAS,GAAG,GAAG,GAAG,CAAC;AACvC,MAAI,kBAAkB,YAAY,qBAAqB,OAAO;AAC9D,MAAI,sBAAsB,oBAAI,KAAK,CAAC;AACpC,sBAAoB,YAAY,MAAM,GAAG,qBAAqB;AAC9D,sBAAoB,SAAS,GAAG,GAAG,GAAG,CAAC;AACvC,MAAI,kBAAkB,YAAY,qBAAqB,OAAO;AAE9D,MAAI,KAAK,QAAQ,KAAK,gBAAgB,QAAQ,GAAG;AAC/C,WAAO,OAAO;AAAA,EAChB,WAAW,KAAK,QAAQ,KAAK,gBAAgB,QAAQ,GAAG;AACtD,WAAO;AAAA,EACT,OAAO;AACL,WAAO,OAAO;AAAA,EAChB;AACF;;;AC7Be,SAAR,gBAAiC,WAAW,SAAS;AAC1D,MAAI,MAAM,OAAO,OAAO,uBAAuB,iBAAiB,uBAAuB,uBAAuB;AAE9G,eAAa,GAAG,SAAS;AACzB,MAAI,iBAAiB,kBAAkB;AACvC,MAAI,wBAAwB,WAAW,QAAQ,SAAS,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,2BAA2B,QAAQ,0BAA0B,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,UAAU,kBAAkB,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,UAAU,wBAAwB,gBAAgB,aAAa,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,2BAA2B,QAAQ,UAAU,SAAS,QAAQ,eAAe,2BAA2B,QAAQ,UAAU,SAAS,SAAS,wBAAwB,eAAe,YAAY,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,aAAa,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,2BAA2B,QAAQ,SAAS,SAAS,OAAO,CAAC;AACj7B,MAAI,OAAO,YAAY,WAAW,OAAO;AACzC,MAAI,YAAY,oBAAI,KAAK,CAAC;AAC1B,YAAU,YAAY,MAAM,GAAG,qBAAqB;AACpD,YAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7B,MAAI,OAAO,YAAY,WAAW,OAAO;AACzC,SAAO;AACT;;;ACrDA,IAAIC,wBAAuB;AAwCZ,SAAR,QAAyB,WAAW,SAAS;AAClD,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,YAAY,MAAM,OAAO,EAAE,QAAQ,IAAI,gBAAgB,MAAM,OAAO,EAAE,QAAQ;AAIzF,SAAO,KAAK,MAAM,OAAOA,qBAAoB,IAAI;AACnD;;;ACxBe,SAAR,eAAgC,MAAM,SAAS;AACpD,MAAI,MAAM,OAAO,OAAO,uBAAuB,iBAAiB,uBAAuB,uBAAuB;AAE9G,eAAa,GAAG,SAAS;AACzB,MAAI,iBAAiB,kBAAkB;AACvC,MAAI,eAAe,WAAW,QAAQ,SAAS,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,kBAAkB,QAAQ,0BAA0B,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,UAAU,kBAAkB,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,UAAU,wBAAwB,gBAAgB,aAAa,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,kBAAkB,QAAQ,UAAU,SAAS,QAAQ,eAAe,kBAAkB,QAAQ,UAAU,SAAS,SAAS,wBAAwB,eAAe,YAAY,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,aAAa,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,kBAAkB,QAAQ,SAAS,SAAS,OAAO,CAAC;AAEp4B,MAAI,EAAE,gBAAgB,KAAK,gBAAgB,IAAI;AAC7C,UAAM,IAAI,WAAW,kDAAkD;AAAA,EACzE;AAEA,MAAI,oBAAoB,QAAQ,IAAI;AACpC,MAAI,MAAM,iBAAiB,EAAG,QAAO;AACrC,MAAI,eAAe,OAAO,aAAa,IAAI,CAAC;AAC5C,MAAI,qBAAqB,eAAe;AACxC,MAAI,sBAAsB,EAAG,uBAAsB;AACnD,MAAI,8BAA8B,oBAAoB;AACtD,SAAO,KAAK,KAAK,8BAA8B,CAAC,IAAI;AACtD;;;ACzBe,SAAR,eAAgC,WAAW;AAChD,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,QAAQ,KAAK,SAAS;AAC1B,OAAK,YAAY,KAAK,YAAY,GAAG,QAAQ,GAAG,CAAC;AACjD,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;;;ACIe,SAAR,gBAAiC,MAAM,SAAS;AACrD,eAAa,GAAG,SAAS;AACzB,SAAO,0BAA0B,eAAe,IAAI,GAAG,aAAa,IAAI,GAAG,OAAO,IAAI;AACxF;;;ACde,SAAR,oBAAqC,OAAO;AACjD,eAAa,GAAG,SAAS;AACzB,SAAO,KAAK,MAAM,QAAQ,kBAAkB;AAC9C;;;ACHe,SAAR,eAAgC,OAAO;AAC5C,eAAa,GAAG,SAAS;AACzB,SAAO,KAAK,MAAM,QAAQ,aAAa;AACzC;;;ACHe,SAAR,eAAgC,OAAO;AAC5C,eAAa,GAAG,SAAS;AACzB,SAAO,KAAK,MAAM,QAAQ,aAAa;AACzC;;;ACUe,SAAR,mBAAoC,UAAU;AACnD,eAAa,GAAG,SAAS;AACzB,MAAI,QAAQ,OAAO,SAAS,KAAK;AACjC,MAAI,MAAM,OAAO,SAAS,GAAG;AAC7B,MAAI,MAAM,MAAM,QAAQ,CAAC,EAAG,OAAM,IAAI,WAAW,uBAAuB;AACxE,MAAI,MAAM,IAAI,QAAQ,CAAC,EAAG,OAAM,IAAI,WAAW,qBAAqB;AACpE,MAAI,WAAW,CAAC;AAChB,WAAS,QAAQ,KAAK,IAAI,kBAAkB,KAAK,KAAK,CAAC;AACvD,MAAI,OAAO,WAAW,KAAK,KAAK;AAChC,MAAI,kBAAkB,IAAI,OAAO;AAAA,IAC/B,OAAO,OAAO,SAAS;AAAA,EACzB,CAAC;AACD,WAAS,SAAS,KAAK,IAAI,mBAAmB,KAAK,eAAe,CAAC;AACnE,MAAI,gBAAgB,IAAI,iBAAiB;AAAA,IACvC,QAAQ,OAAO,SAAS;AAAA,EAC1B,CAAC;AACD,WAAS,OAAO,KAAK,IAAI,iBAAiB,KAAK,aAAa,CAAC;AAC7D,MAAI,iBAAiB,IAAI,eAAe;AAAA,IACtC,MAAM,OAAO,SAAS;AAAA,EACxB,CAAC;AACD,WAAS,QAAQ,KAAK,IAAI,kBAAkB,KAAK,cAAc,CAAC;AAChE,MAAI,mBAAmB,IAAI,gBAAgB;AAAA,IACzC,OAAO,OAAO,SAAS;AAAA,EACzB,CAAC;AACD,WAAS,UAAU,KAAK,IAAI,oBAAoB,KAAK,gBAAgB,CAAC;AACtE,MAAI,mBAAmB,IAAI,kBAAkB;AAAA,IAC3C,SAAS,OAAO,SAAS;AAAA,EAC3B,CAAC;AACD,WAAS,UAAU,KAAK,IAAI,oBAAoB,KAAK,gBAAgB,CAAC;AACtE,SAAO;AACT;;;ACQe,SAAR,WAA4B,MAAM,gBAAgB,eAAe;AACtE,MAAI;AAEJ,eAAa,GAAG,SAAS;AACzB,MAAI;AAEJ,MAAI,gBAAgB,cAAc,GAAG;AACnC,oBAAgB;AAAA,EAClB,OAAO;AACL,oBAAgB;AAAA,EAClB;AAEA,SAAO,IAAI,KAAK,gBAAgB,iBAAiB,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,QAAQ,aAAa,EAAE,OAAO,IAAI;AACpK;AAEA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,UAAa,EAAE,YAAY;AAC7C;;;ACwBe,SAAR,mBAAoC,MAAM,UAAU,SAAS;AAClE,eAAa,GAAG,SAAS;AACzB,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI,WAAW,OAAO,IAAI;AAC1B,MAAI,YAAY,OAAO,QAAQ;AAE/B,MAAI,EAAE,YAAY,QAAQ,YAAY,UAAU,QAAQ,OAAO;AAE7D,QAAI,gBAAgB,oBAAoB,UAAU,SAAS;AAE3D,QAAI,KAAK,IAAI,aAAa,IAAI,iBAAiB;AAC7C,cAAQ,oBAAoB,UAAU,SAAS;AAC/C,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,aAAa,IAAI,eAAe;AAClD,cAAQ,oBAAoB,UAAU,SAAS;AAC/C,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,aAAa,IAAI,gBAAgB,KAAK,IAAI,yBAAyB,UAAU,SAAS,CAAC,IAAI,GAAG;AAChH,cAAQ,kBAAkB,UAAU,SAAS;AAC7C,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,aAAa,IAAI,kBAAkB,QAAQ,yBAAyB,UAAU,SAAS,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG;AACpI,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,aAAa,IAAI,gBAAgB;AACnD,cAAQ,0BAA0B,UAAU,SAAS;AACrD,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,aAAa,IAAI,kBAAkB;AACrD,cAAQ,2BAA2B,UAAU,SAAS;AACtD,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,aAAa,IAAI,eAAe;AAClD,UAAI,6BAA6B,UAAU,SAAS,IAAI,GAAG;AAEzD,gBAAQ,6BAA6B,UAAU,SAAS;AACxD,eAAO;AAAA,MACT,OAAO;AACL,gBAAQ,0BAA0B,UAAU,SAAS;AACrD,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,cAAQ,0BAA0B,UAAU,SAAS;AACrD,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AAEL,WAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAEjE,QAAI,SAAS,UAAU;AACrB,cAAQ,oBAAoB,UAAU,SAAS;AAAA,IACjD,WAAW,SAAS,UAAU;AAC5B,cAAQ,oBAAoB,UAAU,SAAS;AAAA,IACjD,WAAW,SAAS,QAAQ;AAC1B,cAAQ,kBAAkB,UAAU,SAAS;AAAA,IAC/C,WAAW,SAAS,OAAO;AACzB,cAAQ,yBAAyB,UAAU,SAAS;AAAA,IACtD,WAAW,SAAS,QAAQ;AAC1B,cAAQ,0BAA0B,UAAU,SAAS;AAAA,IACvD,WAAW,SAAS,SAAS;AAC3B,cAAQ,2BAA2B,UAAU,SAAS;AAAA,IACxD,WAAW,SAAS,WAAW;AAC7B,cAAQ,6BAA6B,UAAU,SAAS;AAAA,IAC1D,WAAW,SAAS,QAAQ;AAC1B,cAAQ,0BAA0B,UAAU,SAAS;AAAA,IACvD;AAAA,EACF;AAEA,MAAI,MAAM,IAAI,KAAK,mBAAmB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ;AAAA,IACtG,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,IACzE,UAAU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY;AAAA,IAChF,OAAO,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,EACnE,CAAC;AACD,SAAO,IAAI,OAAO,OAAO,IAAI;AAC/B;;;AC/Je,SAAR,SAA0B,MAAM,OAAO,KAAK;AACjD,MAAI,UAAU,SAAS,GAAG;AACxB,UAAM,IAAI,UAAU,mCAAmC,UAAU,SAAS,UAAU;AAAA,EACtF;AAEA,MAAI,OAAO,IAAI,KAAK,MAAM,OAAO,GAAG;AACpC,SAAO,KAAK,YAAY,MAAM,QAAQ,KAAK,SAAS,MAAM,SAAS,KAAK,QAAQ,MAAM;AACxF;;;ACXe,SAAR,kBAAmC,WAAW;AACnD,eAAa,GAAG,SAAS;AACzB,SAAO,OAAO,SAAS,EAAE,QAAQ,MAAM;AACzC;;;ACHe,SAAR,SAA0B,WAAW;AAC1C,eAAa,GAAG,SAAS;AACzB,SAAO,OAAO,SAAS,EAAE,OAAO,MAAM;AACxC;;;ACCe,SAAR,SAA0B,WAAW;AAC1C,eAAa,GAAG,SAAS;AACzB,SAAO,OAAO,SAAS,EAAE,QAAQ,IAAI,KAAK,IAAI;AAChD;;;AC6Qe,SAAR,QAAyB,YAAY,cAAc,SAAS;AACjE,eAAa,GAAG,SAAS;AACzB,SAAO,QAAQ,MAAM,YAAY,cAAc,oBAAI,KAAK,GAAG,OAAO,CAAC;AACrE;;;ACvRe,SAAR,SAA0B,MAAM;AACrC,eAAa,GAAG,SAAS;AACzB,SAAO,OAAO,IAAI,EAAE,OAAO,MAAM;AACnC;;;ACCe,SAAR,OAAwB,WAAW;AACxC,eAAa,GAAG,SAAS;AACzB,SAAO,OAAO,SAAS,EAAE,QAAQ,IAAI,KAAK,IAAI;AAChD;;;ACWe,SAAR,WAA4B,eAAe,gBAAgB,SAAS;AACzE,eAAa,GAAG,SAAS;AACzB,MAAI,sBAAsB,YAAY,eAAe,OAAO;AAC5D,MAAI,uBAAuB,YAAY,gBAAgB,OAAO;AAC9D,SAAO,oBAAoB,QAAQ,MAAM,qBAAqB,QAAQ;AACxE;;;ACfe,SAAR,cAA+B,eAAe,gBAAgB;AACnE,eAAa,GAAG,SAAS;AACzB,SAAO,WAAW,eAAe,gBAAgB;AAAA,IAC/C,cAAc;AAAA,EAChB,CAAC;AACH;;;ACVe,SAAR,kBAAmC,eAAe,gBAAgB;AACvE,eAAa,GAAG,SAAS;AACzB,MAAI,sBAAsB,mBAAmB,aAAa;AAC1D,MAAI,uBAAuB,mBAAmB,cAAc;AAC5D,SAAO,oBAAoB,QAAQ,MAAM,qBAAqB,QAAQ;AACxE;;;ACIe,SAAR,aAA8B,eAAe,gBAAgB;AAClE,eAAa,GAAG,SAAS;AACzB,MAAI,wBAAwB,cAAc,aAAa;AACvD,MAAI,yBAAyB,cAAc,cAAc;AACzD,SAAO,sBAAsB,QAAQ,MAAM,uBAAuB,QAAQ;AAC5E;;;ACXe,SAAR,cAA+B,eAAe,gBAAgB;AACnE,eAAa,GAAG,SAAS;AACzB,MAAI,yBAAyB,eAAe,aAAa;AACzD,MAAI,0BAA0B,eAAe,cAAc;AAC3D,SAAO,uBAAuB,QAAQ,MAAM,wBAAwB,QAAQ;AAC9E;;;ACVe,SAAR,cAA+B,WAAW;AAC/C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,OAAK,gBAAgB,CAAC;AACtB,SAAO;AACT;;;ACce,SAAR,aAA8B,eAAe,gBAAgB;AAClE,eAAa,GAAG,SAAS;AACzB,MAAI,wBAAwB,cAAc,aAAa;AACvD,MAAI,yBAAyB,cAAc,cAAc;AACzD,SAAO,sBAAsB,QAAQ,MAAM,uBAAuB,QAAQ;AAC5E;;;ACpBe,SAAR,WAA4B,WAAW;AAC5C,eAAa,GAAG,SAAS;AACzB,SAAO,WAAW,KAAK,IAAI,GAAG,SAAS;AACzC;;;ACFe,SAAR,cAA+B,WAAW;AAC/C,eAAa,GAAG,SAAS;AACzB,SAAO,cAAc,WAAW,KAAK,IAAI,CAAC;AAC5C;;;ACJe,SAAR,aAA8B,WAAW;AAC9C,eAAa,GAAG,SAAS;AACzB,SAAO,aAAa,KAAK,IAAI,GAAG,SAAS;AAC3C;;;ACJe,SAAR,YAA6B,WAAW;AAC7C,eAAa,GAAG,SAAS;AACzB,SAAO,YAAY,KAAK,IAAI,GAAG,SAAS;AAC1C;;;ACHe,SAAR,cAA+B,WAAW;AAC/C,eAAa,GAAG,SAAS;AACzB,SAAO,cAAc,KAAK,IAAI,GAAG,SAAS;AAC5C;;;ACFe,SAAR,aAA8B,WAAW;AAC9C,eAAa,GAAG,SAAS;AACzB,SAAO,aAAa,KAAK,IAAI,GAAG,SAAS;AAC3C;;;ACMe,SAAR,WAA4B,WAAW,SAAS;AACrD,eAAa,GAAG,SAAS;AACzB,SAAO,WAAW,WAAW,KAAK,IAAI,GAAG,OAAO;AAClD;;;ACbe,SAAR,WAA4B,WAAW;AAC5C,eAAa,GAAG,SAAS;AACzB,SAAO,WAAW,WAAW,KAAK,IAAI,CAAC;AACzC;;;ACPe,SAAR,WAA4B,WAAW;AAC5C,eAAa,GAAG,SAAS;AACzB,SAAO,OAAO,SAAS,EAAE,OAAO,MAAM;AACxC;;;ACCe,SAAR,QAAyB,WAAW;AACzC,eAAa,GAAG,SAAS;AACzB,SAAO,UAAU,WAAW,KAAK,IAAI,CAAC;AACxC;;;ACFe,SAAR,WAA4B,WAAW;AAC5C,eAAa,GAAG,SAAS;AACzB,SAAO,UAAU,WAAW,QAAQ,KAAK,IAAI,GAAG,CAAC,CAAC;AACpD;;;ACRe,SAAR,UAA2B,WAAW;AAC3C,eAAa,GAAG,SAAS;AACzB,SAAO,OAAO,SAAS,EAAE,OAAO,MAAM;AACxC;;;ACHe,SAAR,YAA6B,WAAW;AAC7C,eAAa,GAAG,SAAS;AACzB,SAAO,OAAO,SAAS,EAAE,OAAO,MAAM;AACxC;;;ACDe,SAAR,QAAyB,WAAW,aAAa;AACtD,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,QAAQ,WAAW,CAAC,MAAM;AACnC;;;ACDe,SAAR,YAA6B,WAAW;AAC7C,eAAa,GAAG,SAAS;AACzB,SAAO,UAAU,WAAW,QAAQ,KAAK,IAAI,GAAG,CAAC,CAAC;AACpD;;;ACRe,SAAR,gBAAiC,WAAW;AACjD,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,KAAK,YAAY;AAC5B,MAAI,SAAS,IAAI,KAAK,MAAM,OAAO,EAAE,IAAI;AACzC,OAAK,YAAY,SAAS,GAAG,GAAG,CAAC;AACjC,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;;;ACIe,SAAR,cAA+B,WAAW,SAAS;AACxD,MAAI,MAAM,OAAO,OAAO,uBAAuB,iBAAiB,uBAAuB,uBAAuB;AAE9G,eAAa,GAAG,SAAS;AACzB,MAAI,iBAAiB,kBAAkB;AACvC,MAAI,eAAe,WAAW,QAAQ,SAAS,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,kBAAkB,QAAQ,0BAA0B,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,UAAU,kBAAkB,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,UAAU,wBAAwB,gBAAgB,aAAa,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,kBAAkB,QAAQ,UAAU,SAAS,QAAQ,eAAe,kBAAkB,QAAQ,UAAU,SAAS,SAAS,wBAAwB,eAAe,YAAY,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,aAAa,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,kBAAkB,QAAQ,SAAS,SAAS,OAAO,CAAC;AAEp4B,MAAI,EAAE,gBAAgB,KAAK,gBAAgB,IAAI;AAC7C,UAAM,IAAI,WAAW,sCAAsC;AAAA,EAC7D;AAEA,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,MAAM,KAAK,OAAO;AACtB,MAAI,QAAQ,MAAM,eAAe,KAAK,KAAK,KAAK,MAAM;AACtD,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,OAAK,QAAQ,KAAK,QAAQ,IAAI,IAAI;AAClC,SAAO;AACT;;;AC1Be,SAAR,iBAAkC,WAAW;AAClD,eAAa,GAAG,SAAS;AACzB,SAAO,cAAc,WAAW;AAAA,IAC9B,cAAc;AAAA,EAChB,CAAC;AACH;;;ACHe,SAAR,qBAAsC,WAAW;AACtD,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,eAAe,SAAS;AACnC,MAAI,kBAAkB,oBAAI,KAAK,CAAC;AAChC,kBAAgB,YAAY,OAAO,GAAG,GAAG,CAAC;AAC1C,kBAAgB,SAAS,GAAG,GAAG,GAAG,CAAC;AACnC,MAAI,OAAO,eAAe,eAAe;AACzC,OAAK,QAAQ,KAAK,QAAQ,IAAI,CAAC;AAC/B,SAAO;AACT;;;ACVe,SAAR,iBAAkC,WAAW;AAClD,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,eAAe,KAAK,SAAS;AACjC,MAAI,QAAQ,eAAe,eAAe,IAAI;AAC9C,OAAK,SAAS,OAAO,CAAC;AACtB,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;;;ACXe,SAAR,cAA+B,WAAW;AAC/C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,KAAK,YAAY;AAC5B,OAAK,YAAY,OAAO,GAAG,GAAG,CAAC;AAC/B,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;;;ACbA,IAAI,yBAAyB;AAC7B,IAAI,sBAAsB;AAC1B,IAAI,oBAAoB;AACxB,IAAI,gCAAgC;AAyDrB,SAAR,YAA6B,WAAW,WAAW;AACxD,eAAa,GAAG,SAAS;AACzB,MAAI,eAAe,OAAO,SAAS;AAEnC,MAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAKA,MAAI,iBAAiB,gCAAgC,YAAY;AACjE,MAAI,UAAU,gBAAgB,cAAc,cAAc;AAC1D,MAAI,SAAS,UAAU,MAAM,sBAAsB;AAEnD,MAAI,CAAC,OAAQ,QAAO;AACpB,MAAI,SAAS,OAAO,IAAI,SAAU,WAAW;AAE3C,QAAI,cAAc,MAAM;AACtB,aAAO;AAAA,IACT;AAEA,QAAI,iBAAiB,UAAU,CAAC;AAEhC,QAAI,mBAAmB,KAAK;AAC1B,aAAO,mBAAmB,SAAS;AAAA,IACrC;AAEA,QAAI,YAAY,wBAAW,cAAc;AAEzC,QAAI,WAAW;AACb,aAAO,UAAU,SAAS,SAAS;AAAA,IACrC;AAEA,QAAI,eAAe,MAAM,6BAA6B,GAAG;AACvD,YAAM,IAAI,WAAW,mEAAmE,iBAAiB,GAAG;AAAA,IAC9G;AAEA,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,EAAE;AACV,SAAO;AACT;AAEA,SAAS,mBAAmB,OAAO;AACjC,MAAI,UAAU,MAAM,MAAM,mBAAmB;AAE7C,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ,CAAC,EAAE,QAAQ,mBAAmB,GAAG;AAClD;;;AC3HA,IAAIC,cAAa;AA+BF,SAAR,aAA8B,MAAM;AACzC,MAAI,QAAQ,KAAK,OACbC,UAAS,KAAK,QACd,QAAQ,KAAK,OACbC,QAAO,KAAK,MACZ,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,UAAU,KAAK;AACnB,eAAa,GAAG,SAAS;AACzB,MAAI,YAAY;AAChB,MAAI,MAAO,cAAa,QAAQF;AAChC,MAAIC,QAAQ,cAAaA,WAAUD,cAAa;AAChD,MAAI,MAAO,cAAa,QAAQ;AAChC,MAAIE,MAAM,cAAaA;AACvB,MAAI,eAAe,YAAY,KAAK,KAAK;AACzC,MAAI,MAAO,iBAAgB,QAAQ,KAAK;AACxC,MAAI,QAAS,iBAAgB,UAAU;AACvC,MAAI,QAAS,iBAAgB;AAC7B,SAAO,KAAK,MAAM,eAAe,GAAI;AACvC;;;AC3Be,SAAR,oBAAqCC,eAAc;AACxD,eAAa,GAAG,SAAS;AACzB,MAAI,QAAQA,gBAAe;AAC3B,SAAO,KAAK,MAAM,KAAK;AACzB;;;ACJe,SAAR,sBAAuCC,eAAc;AAC1D,eAAa,GAAG,SAAS;AACzB,MAAI,UAAUA,gBAAe;AAC7B,SAAO,KAAK,MAAM,OAAO;AAC3B;;;ACJe,SAAR,sBAAuCC,eAAc;AAC1D,eAAa,GAAG,SAAS;AACzB,MAAI,UAAUA,gBAAe;AAC7B,SAAO,KAAK,MAAM,OAAO;AAC3B;;;ACJe,SAAR,eAAgC,SAAS;AAC9C,eAAa,GAAG,SAAS;AACzB,MAAI,QAAQ,UAAU;AACtB,SAAO,KAAK,MAAM,KAAK;AACzB;;;ACTe,SAAR,sBAAuC,SAAS;AACrD,eAAa,GAAG,SAAS;AACzB,SAAO,KAAK,MAAM,UAAU,oBAAoB;AAClD;;;ACHe,SAAR,iBAAkC,SAAS;AAChD,eAAa,GAAG,SAAS;AACzB,SAAO,KAAK,MAAM,UAAU,eAAe;AAC7C;;;ACEe,SAAR,iBAAkCC,SAAQ;AAC/C,eAAa,GAAG,SAAS;AACzB,MAAI,WAAWA,UAAS;AACxB,SAAO,KAAK,MAAM,QAAQ;AAC5B;;;ACLe,SAAR,cAA+BC,SAAQ;AAC5C,eAAa,GAAG,SAAS;AACzB,MAAI,QAAQA,UAAS;AACrB,SAAO,KAAK,MAAM,KAAK;AACzB;;;ACFe,SAAR,QAAyB,MAAM,KAAK;AACzC,eAAa,GAAG,SAAS;AACzB,MAAI,QAAQ,MAAM,OAAO,IAAI;AAC7B,MAAI,SAAS,EAAG,UAAS;AACzB,SAAO,QAAQ,MAAM,KAAK;AAC5B;;;ACZe,SAAR,WAA4B,MAAM;AACvC,eAAa,GAAG,SAAS;AACzB,SAAO,QAAQ,MAAM,CAAC;AACxB;;;ACHe,SAAR,WAA4B,MAAM;AACvC,eAAa,GAAG,SAAS;AACzB,SAAO,QAAQ,MAAM,CAAC;AACxB;;;ACHe,SAAR,aAA8B,MAAM;AACzC,eAAa,GAAG,SAAS;AACzB,SAAO,QAAQ,MAAM,CAAC;AACxB;;;ACHe,SAAR,WAA4B,MAAM;AACvC,eAAa,GAAG,SAAS;AACzB,SAAO,QAAQ,MAAM,CAAC;AACxB;;;ACHe,SAAR,aAA8B,MAAM;AACzC,eAAa,GAAG,SAAS;AACzB,SAAO,QAAQ,MAAM,CAAC;AACxB;;;ACHe,SAAR,YAA6B,MAAM;AACxC,eAAa,GAAG,SAAS;AACzB,SAAO,QAAQ,MAAM,CAAC;AACxB;;;ACHe,SAAR,cAA+B,MAAM;AAC1C,eAAa,GAAG,SAAS;AACzB,SAAO,QAAQ,MAAM,CAAC;AACxB;;;ACce,SAAR,UAA2B,UAAU;AAC1C,eAAa,GAAG,SAAS;AAEzB,MAAI,OAAO,aAAa,UAAU;AAChC,QAAI,QAAQ,SAAS,MAAM,+FAA+F;AAE1H,QAAI,OAAO;AAET,aAAO,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,MAAM,MAAM,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,MAAM,MAAM,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,OAAO,MAAM,UAAU,GAAG,CAAC,CAAC,CAAC;AAAA,IAC9O;AAEA,WAAO,oBAAI,KAAK,GAAG;AAAA,EACrB;AAEA,SAAO,OAAO,QAAQ;AACxB;;;ACzBe,SAAR,YAA6B,MAAM,KAAK;AAC7C,eAAa,GAAG,SAAS;AACzB,MAAI,QAAQ,OAAO,IAAI,IAAI;AAC3B,MAAI,SAAS,EAAG,UAAS;AACzB,SAAO,QAAQ,MAAM,KAAK;AAC5B;;;ACZe,SAAR,eAAgC,MAAM;AAC3C,eAAa,GAAG,SAAS;AACzB,SAAO,YAAY,MAAM,CAAC;AAC5B;;;ACHe,SAAR,eAAgC,MAAM;AAC3C,eAAa,GAAG,SAAS;AACzB,SAAO,YAAY,MAAM,CAAC;AAC5B;;;ACHe,SAAR,iBAAkC,MAAM;AAC7C,eAAa,GAAG,SAAS;AACzB,SAAO,YAAY,MAAM,CAAC;AAC5B;;;ACHe,SAAR,eAAgC,MAAM;AAC3C,eAAa,GAAG,SAAS;AACzB,SAAO,YAAY,MAAM,CAAC;AAC5B;;;ACHe,SAAR,iBAAkC,MAAM;AAC7C,eAAa,GAAG,SAAS;AACzB,SAAO,YAAY,MAAM,CAAC;AAC5B;;;ACHe,SAAR,gBAAiC,MAAM;AAC5C,eAAa,GAAG,SAAS;AACzB,SAAO,YAAY,MAAM,CAAC;AAC5B;;;ACHe,SAAR,kBAAmC,MAAM;AAC9C,eAAa,GAAG,SAAS;AACzB,SAAO,YAAY,MAAM,CAAC;AAC5B;;;ACFe,SAAR,iBAAkC,UAAU;AACjD,eAAa,GAAG,SAAS;AACzB,SAAO,KAAK,MAAM,WAAW,eAAe;AAC9C;;;ACEe,SAAR,gBAAiC,UAAU;AAChD,eAAa,GAAG,SAAS;AACzB,MAAI,QAAQ,WAAW;AACvB,SAAO,KAAK,MAAM,KAAK;AACzB;;;ACEe,SAAR,sBAAuC,WAAW,SAAS;AAChE,MAAI;AAEJ,MAAI,UAAU,SAAS,GAAG;AACxB,UAAM,IAAI,UAAU,qDAAqD;AAAA,EAC3E;AAEA,MAAI,YAAY,WAAW,qBAAqB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,eAAe,QAAQ,uBAAuB,SAAS,qBAAqB,CAAC;AAEvL,MAAI,YAAY,KAAK,YAAY,IAAI;AACnC,UAAM,IAAI,WAAW,8CAA8C;AAAA,EACrE;AAEA,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,UAAU,KAAK,WAAW;AAE9B,MAAI,UAAU,KAAK,WAAW,IAAI,UAAU;AAC5C,MAAI,iBAAiB,kBAAkB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc;AAC/G,MAAI,iBAAiB,eAAe,UAAU,SAAS,IAAI;AAC3D,MAAI,mBAAmB,UAAU;AACjC,MAAI,eAAe,KAAK,MAAM,mBAAmB,SAAS,IAAI;AAC9D,SAAO,IAAI,KAAK,KAAK,YAAY,GAAG,KAAK,SAAS,GAAG,KAAK,QAAQ,GAAG,KAAK,SAAS,GAAG,iBAAiB,YAAY;AACrH;;;AC5Be,SAAR,eAAgC,SAAS;AAC9C,eAAa,GAAG,SAAS;AACzB,MAAI,QAAQ,UAAU;AACtB,SAAO,KAAK,MAAM,KAAK;AACzB;;;ACTe,SAAR,sBAAuC,SAAS;AACrD,eAAa,GAAG,SAAS;AACzB,SAAO,UAAU;AACnB;;;ACEe,SAAR,iBAAkC,SAAS;AAChD,eAAa,GAAG,SAAS;AACzB,MAAI,UAAU,UAAU;AACxB,SAAO,KAAK,MAAM,OAAO;AAC3B;;;AC9BA,SAASC,SAAQ,KAAK;AAAE;AAA2B,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AAAE,IAAAA,WAAU,SAASA,SAAQC,MAAK;AAAE,aAAO,OAAOA;AAAA,IAAK;AAAA,EAAG,OAAO;AAAE,IAAAD,WAAU,SAASA,SAAQC,MAAK;AAAE,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAAK;AAAA,EAAG;AAAE,SAAOD,SAAQ,GAAG;AAAG;AA6C1W,SAAR,IAAqB,WAAW,QAAQ;AAC7C,eAAa,GAAG,SAAS;AAEzB,MAAIA,SAAQ,MAAM,MAAM,YAAY,WAAW,MAAM;AACnD,UAAM,IAAI,WAAW,oCAAoC;AAAA,EAC3D;AAEA,MAAI,OAAO,OAAO,SAAS;AAE3B,MAAI,MAAM,KAAK,QAAQ,CAAC,GAAG;AACzB,WAAO,oBAAI,KAAK,GAAG;AAAA,EACrB;AAEA,MAAI,OAAO,QAAQ,MAAM;AACvB,SAAK,YAAY,OAAO,IAAI;AAAA,EAC9B;AAEA,MAAI,OAAO,SAAS,MAAM;AACxB,WAAO,SAAS,MAAM,OAAO,KAAK;AAAA,EACpC;AAEA,MAAI,OAAO,QAAQ,MAAM;AACvB,SAAK,QAAQ,UAAU,OAAO,IAAI,CAAC;AAAA,EACrC;AAEA,MAAI,OAAO,SAAS,MAAM;AACxB,SAAK,SAAS,UAAU,OAAO,KAAK,CAAC;AAAA,EACvC;AAEA,MAAI,OAAO,WAAW,MAAM;AAC1B,SAAK,WAAW,UAAU,OAAO,OAAO,CAAC;AAAA,EAC3C;AAEA,MAAI,OAAO,WAAW,MAAM;AAC1B,SAAK,WAAW,UAAU,OAAO,OAAO,CAAC;AAAA,EAC3C;AAEA,MAAI,OAAO,gBAAgB,MAAM;AAC/B,SAAK,gBAAgB,UAAU,OAAO,YAAY,CAAC;AAAA,EACrD;AAEA,SAAO;AACT;;;ACtDe,SAAR,OAAwB,WAAW,UAAU,SAAS;AAC3D,MAAI,MAAM,OAAO,OAAO,uBAAuB,iBAAiB,uBAAuB,uBAAuB;AAE9G,eAAa,GAAG,SAAS;AACzB,MAAI,iBAAiB,kBAAkB;AACvC,MAAI,eAAe,WAAW,QAAQ,SAAS,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,kBAAkB,QAAQ,0BAA0B,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,UAAU,kBAAkB,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,UAAU,wBAAwB,gBAAgB,aAAa,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,kBAAkB,QAAQ,UAAU,SAAS,QAAQ,eAAe,kBAAkB,QAAQ,UAAU,SAAS,SAAS,wBAAwB,eAAe,YAAY,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,aAAa,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,kBAAkB,QAAQ,SAAS,SAAS,OAAO,CAAC;AAEp4B,MAAI,EAAE,gBAAgB,KAAK,gBAAgB,IAAI;AAC7C,UAAM,IAAI,WAAW,kDAAkD;AAAA,EACzE;AAEA,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,MAAM,UAAU,QAAQ;AAC5B,MAAI,aAAa,KAAK,OAAO;AAC7B,MAAI,YAAY,MAAM;AACtB,MAAI,YAAY,YAAY,KAAK;AACjC,MAAI,QAAQ,IAAI;AAChB,MAAI,OAAO,MAAM,KAAK,MAAM,IAAI,OAAO,aAAa,SAAS,KAAK,WAAW,SAAS,KAAK,aAAa,SAAS;AACjH,SAAO,QAAQ,MAAM,IAAI;AAC3B;;;AC9Be,SAAR,aAA8B,WAAW,gBAAgB;AAC9D,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,YAAY,UAAU,cAAc;AACxC,OAAK,SAAS,CAAC;AACf,OAAK,QAAQ,SAAS;AACtB,SAAO;AACT;;;ACuBe,SAARE,mBAAmC,YAAY;AACpD,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,CAAC;AACd,MAAI,iBAAiB,kBAAkB;AAEvC,WAAS,YAAY,gBAAgB;AACnC,QAAI,OAAO,UAAU,eAAe,KAAK,gBAAgB,QAAQ,GAAG;AAClE;AACA,aAAO,QAAQ,IAAI,eAAe,QAAQ;AAAA,IAC5C;AAAA,EACF;AAEA,WAAS,aAAa,YAAY;AAChC,QAAI,OAAO,UAAU,eAAe,KAAK,YAAY,SAAS,GAAG;AAC/D,UAAI,WAAW,SAAS,MAAM,QAAW;AACvC,eAAO,OAAO,SAAS;AAAA,MACzB,OAAO;AACL;AACA,eAAO,SAAS,IAAI,WAAW,SAAS;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AAEA,oBAA0B,MAAM;AAClC;;;AClDe,SAAR,UAA2B,WAAW,UAAU;AACrD,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,MAAM,UAAU,QAAQ;AAC5B,MAAI,aAAa,UAAU,IAAI;AAC/B,MAAI,OAAO,MAAM;AACjB,SAAO,QAAQ,MAAM,IAAI;AAC3B;;;ACRe,SAAR,WAA4B,WAAW,cAAc;AAC1D,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,UAAU,UAAU,YAAY;AACpC,MAAI,OAAO,WAAW,IAAI,IAAI;AAC9B,OAAK,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC;AACtC,SAAO;AACT;;;ACVe,SAAR,gBAAiC,WAAW,mBAAmB;AACpE,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAIC,gBAAe,UAAU,iBAAiB;AAC9C,OAAK,gBAAgBA,aAAY;AACjC,SAAO;AACT;;;ACLe,SAAR,WAA4B,WAAW,cAAc;AAC1D,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,UAAU,UAAU,YAAY;AACpC,MAAI,aAAa,KAAK,MAAM,KAAK,SAAS,IAAI,CAAC,IAAI;AACnD,MAAI,OAAO,UAAU;AACrB,SAAO,SAAS,MAAM,KAAK,SAAS,IAAI,OAAO,CAAC;AAClD;;;ACee,SAAR,QAAyB,WAAW,WAAW,SAAS;AAC7D,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,UAAU,SAAS;AAC9B,MAAI,OAAO,QAAQ,MAAM,OAAO,IAAI;AACpC,OAAK,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC;AACtC,SAAO;AACT;;;ACJe,SAAR,YAA6B,WAAW,eAAe,SAAS;AACrE,MAAI,MAAM,OAAO,OAAO,uBAAuB,iBAAiB,uBAAuB,uBAAuB;AAE9G,eAAa,GAAG,SAAS;AACzB,MAAI,iBAAiB,kBAAkB;AACvC,MAAI,wBAAwB,WAAW,QAAQ,SAAS,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,2BAA2B,QAAQ,0BAA0B,SAAS,wBAAwB,YAAY,QAAQ,YAAY,SAAS,UAAU,kBAAkB,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,UAAU,wBAAwB,gBAAgB,aAAa,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,2BAA2B,QAAQ,UAAU,SAAS,QAAQ,eAAe,2BAA2B,QAAQ,UAAU,SAAS,SAAS,wBAAwB,eAAe,YAAY,QAAQ,0BAA0B,SAAS,UAAU,yBAAyB,sBAAsB,aAAa,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,2BAA2B,QAAQ,SAAS,SAAS,OAAO,CAAC;AACj7B,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,WAAW,UAAU,aAAa;AACtC,MAAI,OAAO,yBAAyB,MAAM,gBAAgB,MAAM,OAAO,CAAC;AACxE,MAAI,YAAY,oBAAI,KAAK,CAAC;AAC1B,YAAU,YAAY,UAAU,GAAG,qBAAqB;AACxD,YAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AAC7B,SAAO,gBAAgB,WAAW,OAAO;AACzC,OAAK,QAAQ,KAAK,QAAQ,IAAI,IAAI;AAClC,SAAO;AACT;;;AC3Ce,SAAR,cAA+B,WAAW;AAC/C,eAAa,GAAG,SAAS;AACzB,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,OAAO,KAAK,YAAY;AAC5B,MAAI,SAAS,KAAK,MAAM,OAAO,EAAE,IAAI;AACrC,OAAK,YAAY,QAAQ,GAAG,CAAC;AAC7B,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;;;ACPe,SAAR,eAAgC;AACrC,SAAO,WAAW,KAAK,IAAI,CAAC;AAC9B;;;ACJe,SAAR,kBAAmC;AACxC,MAAI,MAAM,oBAAI,KAAK;AACnB,MAAI,OAAO,IAAI,YAAY;AAC3B,MAAI,QAAQ,IAAI,SAAS;AACzB,MAAI,MAAM,IAAI,QAAQ;AACtB,MAAI,OAAO,oBAAI,KAAK,CAAC;AACrB,OAAK,YAAY,MAAM,OAAO,MAAM,CAAC;AACrC,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;;;ACTe,SAAR,mBAAoC;AACzC,MAAI,MAAM,oBAAI,KAAK;AACnB,MAAI,OAAO,IAAI,YAAY;AAC3B,MAAI,QAAQ,IAAI,SAAS;AACzB,MAAI,MAAM,IAAI,QAAQ;AACtB,MAAI,OAAO,oBAAI,KAAK,CAAC;AACrB,OAAK,YAAY,MAAM,OAAO,MAAM,CAAC;AACrC,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,SAAO;AACT;;;ACNe,SAAR,UAA2B,WAAW,aAAa;AACxD,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,UAAU,WAAW,CAAC,MAAM;AACrC;;;AC1BA,SAASC,SAAQ,KAAK;AAAE;AAA2B,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AAAE,IAAAA,WAAU,SAASA,SAAQC,MAAK;AAAE,aAAO,OAAOA;AAAA,IAAK;AAAA,EAAG,OAAO;AAAE,IAAAD,WAAU,SAASA,SAAQC,MAAK;AAAE,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAAK;AAAA,EAAG;AAAE,SAAOD,SAAQ,GAAG;AAAG;AA8C1W,SAAR,IAAqB,MAAM,UAAU;AAC1C,eAAa,GAAG,SAAS;AACzB,MAAI,CAAC,YAAYA,SAAQ,QAAQ,MAAM,SAAU,QAAO,oBAAI,KAAK,GAAG;AACpE,MAAI,QAAQ,SAAS,QAAQ,UAAU,SAAS,KAAK,IAAI;AACzD,MAAIE,UAAS,SAAS,SAAS,UAAU,SAAS,MAAM,IAAI;AAC5D,MAAI,QAAQ,SAAS,QAAQ,UAAU,SAAS,KAAK,IAAI;AACzD,MAAIC,QAAO,SAAS,OAAO,UAAU,SAAS,IAAI,IAAI;AACtD,MAAI,QAAQ,SAAS,QAAQ,UAAU,SAAS,KAAK,IAAI;AACzD,MAAI,UAAU,SAAS,UAAU,UAAU,SAAS,OAAO,IAAI;AAC/D,MAAI,UAAU,SAAS,UAAU,UAAU,SAAS,OAAO,IAAI;AAE/D,MAAI,oBAAoB,UAAU,MAAMD,UAAS,QAAQ,EAAE;AAE3D,MAAI,kBAAkB,QAAQ,mBAAmBC,QAAO,QAAQ,CAAC;AAEjE,MAAI,eAAe,UAAU,QAAQ;AACrC,MAAI,eAAe,UAAU,eAAe;AAC5C,MAAI,UAAU,eAAe;AAC7B,MAAI,YAAY,IAAI,KAAK,gBAAgB,QAAQ,IAAI,OAAO;AAC5D,SAAO;AACT;;;AC5Ce,SAAR,gBAAiC,WAAW,aAAa;AAC9D,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,gBAAgB,WAAW,CAAC,MAAM;AAC3C;;;ACJe,SAAR,SAA0B,WAAW,aAAa;AACvD,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,SAAS,WAAW,CAAC,MAAM;AACpC;;;ACJe,SAAR,WAA4B,WAAW,aAAa;AACzD,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,WAAW,WAAW,CAAC,MAAM;AACtC;;;ACJe,SAAR,YAA6B,WAAW,aAAa;AAC1D,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,YAAY,WAAW,CAAC,MAAM;AACvC;;;ACJe,SAAR,WAA4B,WAAW,aAAa;AACzD,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,WAAW,WAAW,CAAC,MAAM;AACtC;;;ACJe,SAAR,SAA0B,WAAW,aAAa;AACvD,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,SAAS,WAAW,CAAC,MAAM;AACpC;;;ACJe,SAAR,SAA0B,WAAW,aAAa;AACvD,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,UAAU,WAAW;AAClC,SAAO,SAAS,WAAW,CAAC,MAAM;AACpC;;;ACLe,SAAR,YAA6B,OAAO;AACzC,eAAa,GAAG,SAAS;AACzB,SAAO,KAAK,MAAM,QAAQ,UAAU;AACtC;;;ACHe,SAAR,cAA+B,OAAO;AAC3C,eAAa,GAAG,SAAS;AACzB,SAAO,KAAK,MAAM,QAAQ,YAAY;AACxC;;;ACHe,SAAR,gBAAiC,OAAO;AAC7C,eAAa,GAAG,SAAS;AACzB,SAAO,KAAK,MAAM,QAAQ,cAAc;AAC1C;", "names": ["_typeof", "obj", "months", "days", "months", "_typeof", "obj", "_typeof", "obj", "days", "MILLISECONDS_IN_WEEK", "months", "days", "MINUTES_IN_DAY", "MINUTES_IN_MONTH", "milliseconds", "days", "months", "format", "format", "_typeof", "obj", "months", "days", "milliseconds", "getDefaultOptions", "MILLISECONDS_IN_WEEK", "MILLISECONDS_IN_WEEK", "milliseconds", "MILLISECONDS_IN_WEEK", "daysInYear", "months", "days", "milliseconds", "milliseconds", "milliseconds", "months", "months", "_typeof", "obj", "setDefaultOptions", "milliseconds", "_typeof", "obj", "months", "days"]}