import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Box, Alert, CircularProgress, Typography, Fab, Tooltip } from '@mui/material';
import { MyLocation, Refresh, Fullscreen, FullscreenExit } from '@mui/icons-material';
import { Loader } from '@googlemaps/js-api-loader';
import {
  GOOGLE_MAPS_API_KEY,
  GOOGLE_MAPS_LIBRARIES,
  createMapOptions,
  createAdvancedMarker,
  clearMarkers,
  DEFAULT_CENTER
} from '../../utils/googleMaps';

/**
 * Enhanced Map Component with real-time location tracking
 * Supports multiple markers, real-time updates, and performance optimization
 */
const EnhancedMap = ({
  center = DEFAULT_CENTER,
  zoom = 12,
  markers = [],
  onMapLoad,
  onMarkerClick,
  showUserLocation = false,
  enableFullscreen = true,
  enableRefresh = true,
  enableMyLocation = true,
  height = 400,
  style = {},
  mapOptions = {},
  children,
  ...props
}) => {
  const [mapInstance, setMapInstance] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userLocation, setUserLocation] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [advancedMarkers, setAdvancedMarkers] = useState([]);
  const [googleMapsLoaded, setGoogleMapsLoaded] = useState(false);

  const mapRef = useRef(null);
  const markersRef = useRef([]);
  const userLocationMarkerRef = useRef(null);
  const loaderRef = useRef(null);

  // Initialize Google Maps API
  useEffect(() => {
    if (!GOOGLE_MAPS_API_KEY) {
      setError('Google Maps API key is not configured');
      setLoading(false);
      return;
    }

    const loadGoogleMaps = async () => {
      try {
        // Check if Google Maps is already loaded
        if (window.google && window.google.maps && window.google.maps.Map) {
          console.log('Google Maps already loaded');
          setGoogleMapsLoaded(true);
          return;
        }

        // Create loader instance
        const loader = new Loader({
          apiKey: GOOGLE_MAPS_API_KEY,
          version: 'weekly',
          libraries: GOOGLE_MAPS_LIBRARIES,
          region: 'IN',
          language: 'en'
        });

        // Load Google Maps API
        await loader.load();
        console.log('Google Maps API loaded successfully');

        // Verify that the Map constructor is available
        if (window.google && window.google.maps && window.google.maps.Map) {
          setGoogleMapsLoaded(true);
        } else {
          throw new Error('Google Maps API loaded but Map constructor not available');
        }
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
        setError(`Failed to load Google Maps: ${error.message}`);
        setLoading(false);
      }
    };

    loadGoogleMaps();

    return () => {
      // Cleanup if needed
      if (mapInstance) {
        clearMarkers(advancedMarkers);
      }
    };
  }, []);

  // Initialize map instance when Google Maps is loaded
  useEffect(() => {
    if (!googleMapsLoaded || !mapRef.current) {
      return;
    }

    const initializeMap = () => {
      try {
        console.log('Initializing map...');

        const mapOptions = createMapOptions({
          useAdvancedMarkers: true,
          enableCustomStyling: false
        });

        const map = new window.google.maps.Map(mapRef.current, {
          center,
          zoom,
          ...mapOptions
        });

        setMapInstance(map);
        setLoading(false);
        setError(null);

        if (onMapLoad) {
          onMapLoad({ map, maps: window.google.maps });
        }

        console.log('Map initialized successfully');
      } catch (error) {
        console.error('Error initializing map:', error);
        setError(`Failed to initialize map: ${error.message}`);
        setLoading(false);
      }
    };

    // Small delay to ensure DOM is ready
    const timeoutId = setTimeout(initializeMap, 100);

    return () => clearTimeout(timeoutId);
  }, [googleMapsLoaded, center, zoom, onMapLoad]);

  // Update markers when markers prop changes
  useEffect(() => {
    if (!mapInstance || !markers) return;

    // Clear existing markers
    clearMarkers(advancedMarkers);
    
    // Create new markers
    const newMarkers = markers.map((markerData, index) => {
      const marker = createAdvancedMarker({
        position: markerData.position,
        map: mapInstance,
        type: markerData.type || 'default',
        title: markerData.title || `Marker ${index + 1}`,
        status: markerData.status || 'active',
        data: markerData.data || {},
        onClick: () => {
          if (onMarkerClick) {
            onMarkerClick(markerData, index);
          }
        }
      });
      
      return marker;
    }).filter(Boolean);

    setAdvancedMarkers(newMarkers);
    markersRef.current = newMarkers;

    // Fit bounds if multiple markers
    if (newMarkers.length > 1) {
      const bounds = new window.google.maps.LatLngBounds();
      markers.forEach(marker => {
        if (marker.position) {
          bounds.extend(new window.google.maps.LatLng(marker.position.lat, marker.position.lng));
        }
      });
      mapInstance.fitBounds(bounds, { padding: 50 });
    }
  }, [mapInstance, markers, onMarkerClick]);

  // Handle user location
  const handleGetUserLocation = useCallback(() => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by this browser');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const location = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          accuracy: position.coords.accuracy
        };
        
        setUserLocation(location);
        
        if (mapInstance) {
          // Center map on user location
          mapInstance.setCenter(location);
          mapInstance.setZoom(16);
          
          // Add/update user location marker
          if (userLocationMarkerRef.current) {
            userLocationMarkerRef.current.setMap(null);
          }
          
          if (showUserLocation) {
            const userMarker = createAdvancedMarker({
              position: location,
              map: mapInstance,
              type: 'user',
              title: `Your Location (±${Math.round(location.accuracy)}m)`,
              status: 'active'
            });
            
            userLocationMarkerRef.current = userMarker;
          }
        }
      },
      (error) => {
        console.error('Geolocation error:', error);
        setError('Failed to get your location. Please check location permissions.');
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    );
  }, [mapInstance, showUserLocation]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    if (mapInstance) {
      // Refresh markers
      clearMarkers(advancedMarkers);
      setAdvancedMarkers([]);
      
      // Get user location if enabled
      if (showUserLocation) {
        handleGetUserLocation();
      }
      
      // Trigger parent refresh if provided
      if (props.onRefresh) {
        props.onRefresh();
      }
    }
  }, [mapInstance, advancedMarkers, showUserLocation, handleGetUserLocation, props]);

  // Handle fullscreen toggle
  const handleFullscreenToggle = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearMarkers(advancedMarkers);
      if (userLocationMarkerRef.current) {
        userLocationMarkerRef.current.setMap(null);
      }
    };
  }, [advancedMarkers]);

  // Get user location on mount if enabled
  useEffect(() => {
    if (showUserLocation && mapInstance) {
      handleGetUserLocation();
    }
  }, [showUserLocation, mapInstance, handleGetUserLocation]);

  const mapContainerStyle = {
    height: isFullscreen ? '100vh' : height,
    width: '100%',
    position: isFullscreen ? 'fixed' : 'relative',
    top: isFullscreen ? 0 : 'auto',
    left: isFullscreen ? 0 : 'auto',
    zIndex: isFullscreen ? 9999 : 'auto',
    ...style
  };

  if (!GOOGLE_MAPS_API_KEY) {
    return (
      <Box sx={mapContainerStyle} display="flex" alignItems="center" justifyContent="center">
        <Alert severity="error">
          Google Maps API key is not configured
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={mapContainerStyle} ref={mapRef}>
      {loading && (
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 1000
          }}
        >
          <CircularProgress />
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
            Loading map...
          </Typography>
        </Box>
      )}

      {error && (
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 1000,
            width: '80%',
            maxWidth: 400
          }}
        >
          <Alert severity="error">{error}</Alert>
        </Box>
      )}

      {/* Map Container */}
      <div
        ref={mapRef}
        style={{
          width: '100%',
          height: '100%',
          borderRadius: '8px'
        }}
      />

      {children}

      {/* Control Buttons */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 16,
          right: 16,
          display: 'flex',
          flexDirection: 'column',
          gap: 1
        }}
      >
        {enableMyLocation && (
          <Tooltip title="My Location">
            <Fab
              size="small"
              color="primary"
              onClick={handleGetUserLocation}
              sx={{ backgroundColor: 'white', color: 'primary.main' }}
            >
              <MyLocation />
            </Fab>
          </Tooltip>
        )}

        {enableRefresh && (
          <Tooltip title="Refresh">
            <Fab
              size="small"
              onClick={handleRefresh}
              sx={{ backgroundColor: 'white', color: 'text.primary' }}
            >
              <Refresh />
            </Fab>
          </Tooltip>
        )}

        {enableFullscreen && (
          <Tooltip title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}>
            <Fab
              size="small"
              onClick={handleFullscreenToggle}
              sx={{ backgroundColor: 'white', color: 'text.primary' }}
            >
              {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
            </Fab>
          </Tooltip>
        )}
      </Box>

      {/* User Location Indicator */}
      {userLocation && (
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            left: 16,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            padding: 1,
            borderRadius: 1,
            boxShadow: 1
          }}
        >
          <Typography variant="caption" color="text.secondary">
            Your Location: {userLocation.lat.toFixed(6)}, {userLocation.lng.toFixed(6)}
          </Typography>
          <br />
          <Typography variant="caption" color="text.secondary">
            Accuracy: ±{Math.round(userLocation.accuracy)}m
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default EnhancedMap;
