{"version": 3, "sources": ["../../react-to-print/lib/index.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=t(require(\"react\")):\"function\"==typeof define&&define.amd?define(\"lib\",[\"react\"],t):\"object\"==typeof exports?exports.lib=t(require(\"react\")):e.lib=t(e.react)}(\"undefined\"!=typeof self?self:this,(function(e){return function(){\"use strict\";var t={155:function(t){t.exports=e}},o={};function n(e){var r=o[e];if(void 0!==r)return r.exports;var s=o[e]={exports:{}};return t[e](s,s.exports,n),s.exports}n.d=function(e,t){for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})};var r={};n.r(r),n.d(r,{useReactToPrint:function(){return f}});var s=n(155);function i({level:e=\"error\",messages:t,suppressErrors:o=!1}){o||(\"error\"===e?console.error(t):\"warning\"===e?console.warn(t):console.debug(t))}function l(e,t){if(t||!e){const e=document.getElementById(\"printWindow\");e&&document.body.removeChild(e)}}function a(e){return e instanceof Error?e:new Error(\"Unknown Error\")}function c(e,t){const{documentTitle:o,onAfterPrint:n,onPrintError:r,preserveAfterPrint:s,print:c,suppressErrors:d}=t;setTimeout((()=>{var t,u;if(e.contentWindow){function p(){null==n||n(),l(s)}if(e.contentWindow.focus(),c)c(e).then(p).catch((e=>{r?r(\"print\",a(e)):i({messages:[\"An error was thrown by the specified `print` function\"],suppressErrors:d})}));else{if(e.contentWindow.print){const h=null!==(u=null===(t=e.contentDocument)||void 0===t?void 0:t.title)&&void 0!==u?u:\"\",f=e.ownerDocument.title;o&&(e.ownerDocument.title=o,e.contentDocument&&(e.contentDocument.title=o)),e.contentWindow.print(),o&&(e.ownerDocument.title=f,e.contentDocument&&(e.contentDocument.title=h))}else i({messages:[\"Printing for this browser is not currently possible: the browser does not have a `print` method available for iframes.\"],suppressErrors:d});[/Android/i,/webOS/i,/iPhone/i,/iPad/i,/iPod/i,/BlackBerry/i,/Windows Phone/i].some((e=>{var t,o;return(null!==(o=null!==(t=navigator.userAgent)&&void 0!==t?t:navigator.vendor)&&void 0!==o?o:\"opera\"in window&&window.opera).match(e)}))?setTimeout(p,500):p()}}else i({messages:[\"Printing failed because the `contentWindow` of the print iframe did not load. This is possibly an error with `react-to-print`. Please file an issue: https://github.com/MatthewHerbst/react-to-print/issues/\"],suppressErrors:d})}),500)}function d(e){const t=[],o=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,null);let n=o.nextNode();for(;n;)t.push(n),n=o.nextNode();return t}function u(e,t,o){const n=d(e),r=d(t);if(n.length===r.length)for(let e=0;e<n.length;e++){const t=n[e],s=r[e],i=t.shadowRoot;if(null!==i){const e=s.attachShadow({mode:i.mode});e.innerHTML=i.innerHTML,u(i,e,o)}}else i({messages:[\"When cloning shadow root content, source and target elements have different size. `onBeforePrint` likely resolved too early.\",e,t],suppressErrors:o})}const p='\\n    @page {\\n        /* Remove browser default header (title) and footer (url) */\\n        margin: 0;\\n    }\\n    @media print {\\n        body {\\n            /* Tell browsers to print background colors */\\n            color-adjust: exact; /* Firefox. This is an older version of \"print-color-adjust\" */\\n            print-color-adjust: exact; /* Firefox/Safari */\\n            -webkit-print-color-adjust: exact; /* Chrome/Safari/Edge/Opera */\\n        }\\n    }\\n';function h(e,t,o,n){var r,s,l,d,h;const{contentNode:f,clonedContentNode:g,clonedImgNodes:m,clonedVideoNodes:b,numResourcesToLoad:y,originalCanvasNodes:v}=o,{bodyClass:w,fonts:E,ignoreGlobalStyles:A,pageStyle:T,nonce:S,suppressErrors:P,copyShadowRoots:k}=n;e.onload=null;const x=null!==(r=e.contentDocument)&&void 0!==r?r:null===(s=e.contentWindow)||void 0===s?void 0:s.document;if(x){const o=x.body.appendChild(g);k&&u(f,o,!!P),E&&((null===(l=e.contentDocument)||void 0===l?void 0:l.fonts)&&(null===(d=e.contentWindow)||void 0===d?void 0:d.FontFace)?E.forEach((o=>{const n=new FontFace(o.family,o.source,{weight:o.weight,style:o.style});e.contentDocument.fonts.add(n),n.loaded.then((()=>{t(n)})).catch((e=>{t(n,[\"Failed loading the font:\",n,\"Load error:\",a(e)])}))})):(E.forEach((e=>{t(e)})),i({messages:['\"react-to-print\" is not able to load custom fonts because the browser does not support the FontFace API but will continue attempting to print the page'],suppressErrors:P})));const n=null!=T?T:p,r=x.createElement(\"style\");S&&(r.setAttribute(\"nonce\",S),x.head.setAttribute(\"nonce\",S)),r.appendChild(x.createTextNode(n)),x.head.appendChild(r),w&&x.body.classList.add(...w.split(\" \"));const s=x.querySelectorAll(\"canvas\");for(let e=0;e<v.length;++e){const t=v[e],o=s[e];if(void 0===o){i({messages:[\"A canvas element could not be copied for printing, has it loaded? `onBeforePrint` likely resolved too early.\",t],suppressErrors:P});continue}const n=o.getContext(\"2d\");n&&n.drawImage(t,0,0)}for(let e=0;e<m.length;e++){const o=m[e],n=o.getAttribute(\"src\");if(n){const e=new Image;e.onload=()=>{t(o)},e.onerror=(e,n,r,s,i)=>{t(o,[\"Error loading <img>\",o,\"Error\",i])},e.src=n}else t(o,['Found an <img> tag with an empty \"src\" attribute. This prevents pre-loading it.',o])}for(let e=0;e<b.length;e++){const o=b[e];o.preload=\"auto\";const n=o.getAttribute(\"poster\");if(n){const e=new Image;e.onload=()=>{t(o)},e.onerror=(e,r,s,i,l)=>{t(o,[\"Error loading video poster\",n,\"for video\",o,\"Error:\",l])},e.src=n}else o.readyState>=2?t(o):o.src?(o.onloadeddata=()=>{t(o)},o.onerror=(e,n,r,s,i)=>{t(o,[\"Error loading video\",o,\"Error\",i])},o.onstalled=()=>{t(o,[\"Loading video stalled, skipping\",o])}):t(o,[\"Error loading video, `src` is empty\",o])}const c=\"select\",y=f.querySelectorAll(c),C=x.querySelectorAll(c);for(let e=0;e<y.length;e++)C[e].value=y[e].value;if(!A){const e=document.querySelectorAll(\"style, link[rel~='stylesheet'], link[as='style']\");for(let o=0,n=e.length;o<n;++o){const n=e[o];if(\"style\"===n.tagName.toLowerCase()){const e=x.createElement(n.tagName),t=n.sheet;if(t){let r=\"\";try{const e=t.cssRules.length;for(let o=0;o<e;++o)\"string\"==typeof t.cssRules[o].cssText&&(r+=`${t.cssRules[o].cssText}\\r\\n`)}catch(e){i({messages:[\"A stylesheet could not be accessed. This is likely due to the stylesheet having cross-origin imports, and many browsers block script access to cross-origin stylesheets. See https://github.com/MatthewHerbst/react-to-print/issues/429 for details. You may be able to load the sheet by both marking the stylesheet with the cross `crossorigin` attribute, and setting the `Access-Control-Allow-Origin` header on the server serving the stylesheet. Alternatively, host the stylesheet on your domain to avoid this issue entirely.\",n,`Original error: ${a(e).message}`],level:\"warning\"})}e.setAttribute(\"id\",`react-to-print-${o}`),S&&e.setAttribute(\"nonce\",S),e.appendChild(x.createTextNode(r)),x.head.appendChild(e)}}else if(n.getAttribute(\"href\"))if(n.hasAttribute(\"disabled\"))i({messages:[\"`react-to-print` encountered a <link> tag with a `disabled` attribute and will ignore it. Note that the `disabled` attribute is deprecated, and some browsers ignore it. You should stop using it. https://developer.mozilla.org/en-US/docs/Web/HTML/Element/link#attr-disabled. The <link> is:\",n],level:\"warning\"}),t(n);else{const e=x.createElement(n.tagName);for(let t=0,o=n.attributes.length;t<o;++t){const o=n.attributes[t];o&&e.setAttribute(o.nodeName,null!==(h=o.nodeValue)&&void 0!==h?h:\"\")}e.onload=()=>{t(e)},e.onerror=(o,n,r,s,i)=>{t(e,[\"Failed to load\",e,\"Error:\",i])},S&&e.setAttribute(\"nonce\",S),x.head.appendChild(e)}else i({messages:[\"`react-to-print` encountered a <link> tag with an empty `href` attribute. In addition to being invalid HTML, this can cause problems in many browsers, and so the <link> was not loaded. The <link> is:\",n],level:\"warning\"}),t(n)}}}0===y&&c(e,n)}function f({bodyClass:e,contentRef:t,copyShadowRoots:o,documentTitle:n,fonts:r,ignoreGlobalStyles:d,nonce:u,onAfterPrint:p,onBeforePrint:f,onPrintError:g,pageStyle:m,preserveAfterPrint:b,print:y,suppressErrors:v}){return(0,s.useCallback)((s=>{function w(){const l={bodyClass:e,contentRef:t,copyShadowRoots:o,documentTitle:n,fonts:r,ignoreGlobalStyles:d,nonce:u,onAfterPrint:p,onBeforePrint:f,onPrintError:g,pageStyle:m,preserveAfterPrint:b,print:y,suppressErrors:v},a=function(){const e=document.createElement(\"iframe\");return e.width=`${document.documentElement.clientWidth}px`,e.height=`${document.documentElement.clientHeight}px`,e.style.position=\"absolute\",e.style.top=`-${document.documentElement.clientHeight+100}px`,e.style.left=`-${document.documentElement.clientWidth+100}px`,e.id=\"printWindow\",e.srcdoc=\"<!DOCTYPE html>\",e}(),w=function(e,t){const{contentRef:o,fonts:n,ignoreGlobalStyles:r,suppressErrors:s}=t,l=function({contentRef:e,optionalContent:t,suppressErrors:o}){return!t||t instanceof Event||(e&&i({level:\"warning\",messages:['\"react-to-print\" received a `contentRef` option and a optional-content param passed to its callback. The `contentRef` option will be ignored.']}),\"function\"!=typeof t)?e?e.current:void i({messages:['\"react-to-print\" did not receive a `contentRef` option or a optional-content param pass to its callback.'],suppressErrors:o}):t()}({contentRef:o,optionalContent:e,suppressErrors:s});if(!l)return;const a=l.cloneNode(!0),c=document.querySelectorAll(\"link[rel~='stylesheet'], link[as='style']\"),d=a.querySelectorAll(\"img\"),u=a.querySelectorAll(\"video\"),p=n?n.length:0;return{contentNode:l,clonedContentNode:a,clonedImgNodes:d,clonedVideoNodes:u,numResourcesToLoad:(r?0:c.length)+d.length+u.length+p,originalCanvasNodes:l.querySelectorAll(\"canvas\")}}(s,l);if(!w)return void i({messages:[\"There is nothing to print\"],suppressErrors:v});const E=function(e,t,o){const{suppressErrors:n}=e,r=[],s=[];return function(l,a){r.includes(l)?i({level:\"debug\",messages:[\"Tried to mark a resource that has already been handled\",l],suppressErrors:n}):(a?(i({messages:['\"react-to-print\" was unable to load a resource but will continue attempting to print the page',...a],suppressErrors:n}),s.push(l)):r.push(l),r.length+s.length===t&&c(o,e))}}(l,w.numResourcesToLoad,a);!function(e,t,o,n){e.onload=()=>{h(e,t,o,n)},document.body.appendChild(e)}(a,E,w,l)}l(b,!0),f?f().then((()=>{w()})).catch((e=>{null==g||g(\"onBeforePrint\",a(e))})):w()}),[e,t,o,n,r,d,u,p,f,g,m,b,y,v])}return r}()}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,YAAU,OAAO,SAAO,OAAO,UAAQ,EAAE,eAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,OAAM,CAAC,OAAO,GAAE,CAAC,IAAE,YAAU,OAAO,UAAQ,QAAQ,MAAI,EAAE,eAAgB,IAAE,EAAE,MAAI,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,eAAa,OAAO,OAAK,OAAK,SAAM,SAAS,GAAE;AAAC,aAAO,WAAU;AAAC;AAAa,YAAI,IAAE,EAAC,KAAI,SAASA,IAAE;AAAC,UAAAA,GAAE,UAAQ;AAAA,QAAC,EAAC,GAAE,IAAE,CAAC;AAAE,iBAAS,EAAEC,IAAE;AAAC,cAAIC,KAAE,EAAED,EAAC;AAAE,cAAG,WAASC,GAAE,QAAOA,GAAE;AAAQ,cAAIC,KAAE,EAAEF,EAAC,IAAE,EAAC,SAAQ,CAAC,EAAC;AAAE,iBAAO,EAAEA,EAAC,EAAEE,IAAEA,GAAE,SAAQ,CAAC,GAAEA,GAAE;AAAA,QAAO;AAAC,UAAE,IAAE,SAASF,IAAED,IAAE;AAAC,mBAAQI,MAAKJ,GAAE,GAAE,EAAEA,IAAEI,EAAC,KAAG,CAAC,EAAE,EAAEH,IAAEG,EAAC,KAAG,OAAO,eAAeH,IAAEG,IAAE,EAAC,YAAW,MAAG,KAAIJ,GAAEI,EAAC,EAAC,CAAC;AAAA,QAAC,GAAE,EAAE,IAAE,SAASH,IAAED,IAAE;AAAC,iBAAO,OAAO,UAAU,eAAe,KAAKC,IAAED,EAAC;AAAA,QAAC,GAAE,EAAE,IAAE,SAASC,IAAE;AAAC,yBAAa,OAAO,UAAQ,OAAO,eAAa,OAAO,eAAeA,IAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,GAAE,OAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC;AAAA,QAAC;AAAE,YAAI,IAAE,CAAC;AAAE,UAAE,EAAE,CAAC,GAAE,EAAE,EAAE,GAAE,EAAC,iBAAgB,WAAU;AAAC,iBAAO;AAAA,QAAC,EAAC,CAAC;AAAE,YAAI,IAAE,EAAE,GAAG;AAAE,iBAAS,EAAE,EAAC,OAAMA,KAAE,SAAQ,UAASD,IAAE,gBAAeI,KAAE,MAAE,GAAE;AAAC,UAAAA,OAAI,YAAUH,KAAE,QAAQ,MAAMD,EAAC,IAAE,cAAYC,KAAE,QAAQ,KAAKD,EAAC,IAAE,QAAQ,MAAMA,EAAC;AAAA,QAAE;AAAC,iBAAS,EAAEC,IAAED,IAAE;AAAC,cAAGA,MAAG,CAACC,IAAE;AAAC,kBAAMA,KAAE,SAAS,eAAe,aAAa;AAAE,YAAAA,MAAG,SAAS,KAAK,YAAYA,EAAC;AAAA,UAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAE;AAAC,iBAAOA,cAAa,QAAMA,KAAE,IAAI,MAAM,eAAe;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAED,IAAE;AAAC,gBAAK,EAAC,eAAcI,IAAE,cAAaC,IAAE,cAAaH,IAAE,oBAAmBC,IAAE,OAAMG,IAAE,gBAAeC,GAAC,IAAEP;AAAE,qBAAY,MAAI;AAAC,gBAAIA,IAAEQ;AAAE,gBAAGP,GAAE,eAAc;AAAC,kBAASQ,KAAT,WAAY;AAAC,wBAAMJ,MAAGA,GAAE,GAAE,EAAEF,EAAC;AAAA,cAAC;AAAC,kBAAGF,GAAE,cAAc,MAAM,GAAEK,GAAE,CAAAA,GAAEL,EAAC,EAAE,KAAKQ,EAAC,EAAE,MAAO,CAAAR,OAAG;AAAC,gBAAAC,KAAEA,GAAE,SAAQ,EAAED,EAAC,CAAC,IAAE,EAAE,EAAC,UAAS,CAAC,uDAAuD,GAAE,gBAAeM,GAAC,CAAC;AAAA,cAAC,CAAE;AAAA,mBAAM;AAAC,oBAAGN,GAAE,cAAc,OAAM;AAAC,wBAAMS,KAAE,UAAQF,KAAE,UAAQR,KAAEC,GAAE,oBAAkB,WAASD,KAAE,SAAOA,GAAE,UAAQ,WAASQ,KAAEA,KAAE,IAAGG,KAAEV,GAAE,cAAc;AAAM,kBAAAG,OAAIH,GAAE,cAAc,QAAMG,IAAEH,GAAE,oBAAkBA,GAAE,gBAAgB,QAAMG,MAAIH,GAAE,cAAc,MAAM,GAAEG,OAAIH,GAAE,cAAc,QAAMU,IAAEV,GAAE,oBAAkBA,GAAE,gBAAgB,QAAMS;AAAA,gBAAG,MAAM,GAAE,EAAC,UAAS,CAAC,wHAAwH,GAAE,gBAAeH,GAAC,CAAC;AAAE,iBAAC,YAAW,UAAS,WAAU,SAAQ,SAAQ,eAAc,gBAAgB,EAAE,KAAM,CAAAN,OAAG;AAAC,sBAAID,IAAEI;AAAE,0BAAO,UAAQA,KAAE,UAAQJ,KAAE,UAAU,cAAY,WAASA,KAAEA,KAAE,UAAU,WAAS,WAASI,KAAEA,KAAE,WAAU,UAAQ,OAAO,OAAO,MAAMH,EAAC;AAAA,gBAAC,CAAE,IAAE,WAAWQ,IAAE,GAAG,IAAEA,GAAE;AAAA,cAAC;AAAA,YAAC,MAAM,GAAE,EAAC,UAAS,CAAC,8MAA8M,GAAE,gBAAeF,GAAC,CAAC;AAAA,UAAC,GAAG,GAAG;AAAA,QAAC;AAAC,iBAAS,EAAEN,IAAE;AAAC,gBAAMD,KAAE,CAAC,GAAEI,KAAE,SAAS,iBAAiBH,IAAE,WAAW,cAAa,IAAI;AAAE,cAAII,KAAED,GAAE,SAAS;AAAE,iBAAKC,KAAG,CAAAL,GAAE,KAAKK,EAAC,GAAEA,KAAED,GAAE,SAAS;AAAE,iBAAOJ;AAAA,QAAC;AAAC,iBAAS,EAAEC,IAAED,IAAEI,IAAE;AAAC,gBAAMC,KAAE,EAAEJ,EAAC,GAAEC,KAAE,EAAEF,EAAC;AAAE,cAAGK,GAAE,WAASH,GAAE,OAAO,UAAQD,KAAE,GAAEA,KAAEI,GAAE,QAAOJ,MAAI;AAAC,kBAAMD,KAAEK,GAAEJ,EAAC,GAAEE,KAAED,GAAED,EAAC,GAAEW,KAAEZ,GAAE;AAAW,gBAAG,SAAOY,IAAE;AAAC,oBAAMX,KAAEE,GAAE,aAAa,EAAC,MAAKS,GAAE,KAAI,CAAC;AAAE,cAAAX,GAAE,YAAUW,GAAE,WAAU,EAAEA,IAAEX,IAAEG,EAAC;AAAA,YAAC;AAAA,UAAC;AAAA,cAAM,GAAE,EAAC,UAAS,CAAC,gIAA+HH,IAAED,EAAC,GAAE,gBAAeI,GAAC,CAAC;AAAA,QAAC;AAAC,cAAM,IAAE;AAAmd,iBAAS,EAAEH,IAAED,IAAEI,IAAEC,IAAE;AAAC,cAAIH,IAAEC,IAAEU,IAAEN,IAAEG;AAAE,gBAAK,EAAC,aAAYC,IAAE,mBAAkB,GAAE,gBAAe,GAAE,kBAAiB,GAAE,oBAAmB,GAAE,qBAAoB,EAAC,IAAEP,IAAE,EAAC,WAAU,GAAE,OAAM,GAAE,oBAAmB,GAAE,WAAU,GAAE,OAAM,GAAE,gBAAe,GAAE,iBAAgB,EAAC,IAAEC;AAAE,UAAAJ,GAAE,SAAO;AAAK,gBAAM,IAAE,UAAQC,KAAED,GAAE,oBAAkB,WAASC,KAAEA,KAAE,UAAQC,KAAEF,GAAE,kBAAgB,WAASE,KAAE,SAAOA,GAAE;AAAS,cAAG,GAAE;AAAC,kBAAMC,KAAE,EAAE,KAAK,YAAY,CAAC;AAAE,iBAAG,EAAEO,IAAEP,IAAE,CAAC,CAAC,CAAC,GAAE,OAAK,UAAQS,KAAEZ,GAAE,oBAAkB,WAASY,KAAE,SAAOA,GAAE,WAAS,UAAQN,KAAEN,GAAE,kBAAgB,WAASM,KAAE,SAAOA,GAAE,YAAU,EAAE,QAAS,CAAAH,OAAG;AAAC,oBAAMC,KAAE,IAAI,SAASD,GAAE,QAAOA,GAAE,QAAO,EAAC,QAAOA,GAAE,QAAO,OAAMA,GAAE,MAAK,CAAC;AAAE,cAAAH,GAAE,gBAAgB,MAAM,IAAII,EAAC,GAAEA,GAAE,OAAO,KAAM,MAAI;AAAC,gBAAAL,GAAEK,EAAC;AAAA,cAAC,CAAE,EAAE,MAAO,CAAAJ,OAAG;AAAC,gBAAAD,GAAEK,IAAE,CAAC,4BAA2BA,IAAE,eAAc,EAAEJ,EAAC,CAAC,CAAC;AAAA,cAAC,CAAE;AAAA,YAAC,CAAE,KAAG,EAAE,QAAS,CAAAA,OAAG;AAAC,cAAAD,GAAEC,EAAC;AAAA,YAAC,CAAE,GAAE,EAAE,EAAC,UAAS,CAAC,wJAAwJ,GAAE,gBAAe,EAAC,CAAC;AAAI,kBAAMI,KAAE,QAAM,IAAE,IAAE,GAAEH,KAAE,EAAE,cAAc,OAAO;AAAE,kBAAIA,GAAE,aAAa,SAAQ,CAAC,GAAE,EAAE,KAAK,aAAa,SAAQ,CAAC,IAAGA,GAAE,YAAY,EAAE,eAAeG,EAAC,CAAC,GAAE,EAAE,KAAK,YAAYH,EAAC,GAAE,KAAG,EAAE,KAAK,UAAU,IAAI,GAAG,EAAE,MAAM,GAAG,CAAC;AAAE,kBAAMC,KAAE,EAAE,iBAAiB,QAAQ;AAAE,qBAAQF,KAAE,GAAEA,KAAE,EAAE,QAAO,EAAEA,IAAE;AAAC,oBAAMD,KAAE,EAAEC,EAAC,GAAEG,KAAED,GAAEF,EAAC;AAAE,kBAAG,WAASG,IAAE;AAAC,kBAAE,EAAC,UAAS,CAAC,gHAA+GJ,EAAC,GAAE,gBAAe,EAAC,CAAC;AAAE;AAAA,cAAQ;AAAC,oBAAMK,KAAED,GAAE,WAAW,IAAI;AAAE,cAAAC,MAAGA,GAAE,UAAUL,IAAE,GAAE,CAAC;AAAA,YAAC;AAAC,qBAAQC,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,oBAAMG,KAAE,EAAEH,EAAC,GAAEI,KAAED,GAAE,aAAa,KAAK;AAAE,kBAAGC,IAAE;AAAC,sBAAMJ,KAAE,IAAI;AAAM,gBAAAA,GAAE,SAAO,MAAI;AAAC,kBAAAD,GAAEI,EAAC;AAAA,gBAAC,GAAEH,GAAE,UAAQ,CAACA,IAAEI,IAAEH,IAAEC,IAAES,OAAI;AAAC,kBAAAZ,GAAEI,IAAE,CAAC,uBAAsBA,IAAE,SAAQQ,EAAC,CAAC;AAAA,gBAAC,GAAEX,GAAE,MAAII;AAAA,cAAC,MAAM,CAAAL,GAAEI,IAAE,CAAC,mFAAkFA,EAAC,CAAC;AAAA,YAAC;AAAC,qBAAQH,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,oBAAMG,KAAE,EAAEH,EAAC;AAAE,cAAAG,GAAE,UAAQ;AAAO,oBAAMC,KAAED,GAAE,aAAa,QAAQ;AAAE,kBAAGC,IAAE;AAAC,sBAAMJ,KAAE,IAAI;AAAM,gBAAAA,GAAE,SAAO,MAAI;AAAC,kBAAAD,GAAEI,EAAC;AAAA,gBAAC,GAAEH,GAAE,UAAQ,CAACA,IAAEC,IAAEC,IAAES,IAAEC,OAAI;AAAC,kBAAAb,GAAEI,IAAE,CAAC,8BAA6BC,IAAE,aAAYD,IAAE,UAASS,EAAC,CAAC;AAAA,gBAAC,GAAEZ,GAAE,MAAII;AAAA,cAAC,MAAM,CAAAD,GAAE,cAAY,IAAEJ,GAAEI,EAAC,IAAEA,GAAE,OAAKA,GAAE,eAAa,MAAI;AAAC,gBAAAJ,GAAEI,EAAC;AAAA,cAAC,GAAEA,GAAE,UAAQ,CAACH,IAAEI,IAAEH,IAAEC,IAAES,OAAI;AAAC,gBAAAZ,GAAEI,IAAE,CAAC,uBAAsBA,IAAE,SAAQQ,EAAC,CAAC;AAAA,cAAC,GAAER,GAAE,YAAU,MAAI;AAAC,gBAAAJ,GAAEI,IAAE,CAAC,mCAAkCA,EAAC,CAAC;AAAA,cAAC,KAAGJ,GAAEI,IAAE,CAAC,uCAAsCA,EAAC,CAAC;AAAA,YAAC;AAAC,kBAAME,KAAE,UAASQ,KAAEH,GAAE,iBAAiBL,EAAC,GAAE,IAAE,EAAE,iBAAiBA,EAAC;AAAE,qBAAQL,KAAE,GAAEA,KAAEa,GAAE,QAAOb,KAAI,GAAEA,EAAC,EAAE,QAAMa,GAAEb,EAAC,EAAE;AAAM,gBAAG,CAAC,GAAE;AAAC,oBAAMA,KAAE,SAAS,iBAAiB,kDAAkD;AAAE,uBAAQG,KAAE,GAAEC,KAAEJ,GAAE,QAAOG,KAAEC,IAAE,EAAED,IAAE;AAAC,sBAAMC,KAAEJ,GAAEG,EAAC;AAAE,oBAAG,YAAUC,GAAE,QAAQ,YAAY,GAAE;AAAC,wBAAMJ,KAAE,EAAE,cAAcI,GAAE,OAAO,GAAEL,KAAEK,GAAE;AAAM,sBAAGL,IAAE;AAAC,wBAAIE,KAAE;AAAG,wBAAG;AAAC,4BAAMD,KAAED,GAAE,SAAS;AAAO,+BAAQI,KAAE,GAAEA,KAAEH,IAAE,EAAEG,GAAE,aAAU,OAAOJ,GAAE,SAASI,EAAC,EAAE,YAAUF,MAAG,GAAGF,GAAE,SAASI,EAAC,EAAE,OAAO;AAAA;AAAA,oBAAO,SAAOH,IAAE;AAAC,wBAAE,EAAC,UAAS,CAAC,4gBAA2gBI,IAAE,mBAAmB,EAAEJ,EAAC,EAAE,OAAO,EAAE,GAAE,OAAM,UAAS,CAAC;AAAA,oBAAC;AAAC,oBAAAA,GAAE,aAAa,MAAK,kBAAkBG,EAAC,EAAE,GAAE,KAAGH,GAAE,aAAa,SAAQ,CAAC,GAAEA,GAAE,YAAY,EAAE,eAAeC,EAAC,CAAC,GAAE,EAAE,KAAK,YAAYD,EAAC;AAAA,kBAAC;AAAA,gBAAC,WAASI,GAAE,aAAa,MAAM,EAAE,KAAGA,GAAE,aAAa,UAAU,EAAE,GAAE,EAAC,UAAS,CAAC,mSAAkSA,EAAC,GAAE,OAAM,UAAS,CAAC,GAAEL,GAAEK,EAAC;AAAA,qBAAM;AAAC,wBAAMJ,KAAE,EAAE,cAAcI,GAAE,OAAO;AAAE,2BAAQL,KAAE,GAAEI,KAAEC,GAAE,WAAW,QAAOL,KAAEI,IAAE,EAAEJ,IAAE;AAAC,0BAAMI,KAAEC,GAAE,WAAWL,EAAC;AAAE,oBAAAI,MAAGH,GAAE,aAAaG,GAAE,UAAS,UAAQM,KAAEN,GAAE,cAAY,WAASM,KAAEA,KAAE,EAAE;AAAA,kBAAC;AAAC,kBAAAT,GAAE,SAAO,MAAI;AAAC,oBAAAD,GAAEC,EAAC;AAAA,kBAAC,GAAEA,GAAE,UAAQ,CAACG,IAAEC,IAAEH,IAAEC,IAAES,OAAI;AAAC,oBAAAZ,GAAEC,IAAE,CAAC,kBAAiBA,IAAE,UAASW,EAAC,CAAC;AAAA,kBAAC,GAAE,KAAGX,GAAE,aAAa,SAAQ,CAAC,GAAE,EAAE,KAAK,YAAYA,EAAC;AAAA,gBAAC;AAAA,oBAAM,GAAE,EAAC,UAAS,CAAC,2MAA0MI,EAAC,GAAE,OAAM,UAAS,CAAC,GAAEL,GAAEK,EAAC;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC;AAAC,gBAAI,KAAG,EAAEJ,IAAEI,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAE,EAAC,WAAUJ,IAAE,YAAWD,IAAE,iBAAgBI,IAAE,eAAcC,IAAE,OAAMH,IAAE,oBAAmBK,IAAE,OAAMC,IAAE,cAAaC,IAAE,eAAcE,IAAE,cAAa,GAAE,WAAU,GAAE,oBAAmB,GAAE,OAAM,GAAE,gBAAe,EAAC,GAAE;AAAC,kBAAO,GAAE,EAAE,aAAc,CAAAR,OAAG;AAAC,qBAAS,IAAG;AAAC,oBAAMU,KAAE,EAAC,WAAUZ,IAAE,YAAWD,IAAE,iBAAgBI,IAAE,eAAcC,IAAE,OAAMH,IAAE,oBAAmBK,IAAE,OAAMC,IAAE,cAAaC,IAAE,eAAcE,IAAE,cAAa,GAAE,WAAU,GAAE,oBAAmB,GAAE,OAAM,GAAE,gBAAe,EAAC,GAAEI,KAAE,WAAU;AAAC,sBAAMd,KAAE,SAAS,cAAc,QAAQ;AAAE,uBAAOA,GAAE,QAAM,GAAG,SAAS,gBAAgB,WAAW,MAAKA,GAAE,SAAO,GAAG,SAAS,gBAAgB,YAAY,MAAKA,GAAE,MAAM,WAAS,YAAWA,GAAE,MAAM,MAAI,IAAI,SAAS,gBAAgB,eAAa,GAAG,MAAKA,GAAE,MAAM,OAAK,IAAI,SAAS,gBAAgB,cAAY,GAAG,MAAKA,GAAE,KAAG,eAAcA,GAAE,SAAO,mBAAkBA;AAAA,cAAC,EAAE,GAAEe,KAAE,SAASf,IAAED,IAAE;AAAC,sBAAK,EAAC,YAAWI,IAAE,OAAMC,IAAE,oBAAmBH,IAAE,gBAAeC,GAAC,IAAEH,IAAEa,KAAE,SAAS,EAAC,YAAWZ,IAAE,iBAAgBD,IAAE,gBAAeI,GAAC,GAAE;AAAC,yBAAM,CAACJ,MAAGA,cAAa,UAAQC,MAAG,EAAE,EAAC,OAAM,WAAU,UAAS,CAAC,+IAA+I,EAAC,CAAC,GAAE,cAAY,OAAOD,MAAGC,KAAEA,GAAE,UAAQ,KAAK,EAAE,EAAC,UAAS,CAAC,0GAA0G,GAAE,gBAAeG,GAAC,CAAC,IAAEJ,GAAE;AAAA,gBAAC,EAAE,EAAC,YAAWI,IAAE,iBAAgBH,IAAE,gBAAeE,GAAC,CAAC;AAAE,oBAAG,CAACU,GAAE;AAAO,sBAAME,KAAEF,GAAE,UAAU,IAAE,GAAEP,KAAE,SAAS,iBAAiB,2CAA2C,GAAEC,KAAEQ,GAAE,iBAAiB,KAAK,GAAEP,KAAEO,GAAE,iBAAiB,OAAO,GAAEN,KAAEJ,KAAEA,GAAE,SAAO;AAAE,uBAAM,EAAC,aAAYQ,IAAE,mBAAkBE,IAAE,gBAAeR,IAAE,kBAAiBC,IAAE,qBAAoBN,KAAE,IAAEI,GAAE,UAAQC,GAAE,SAAOC,GAAE,SAAOC,IAAE,qBAAoBI,GAAE,iBAAiB,QAAQ,EAAC;AAAA,cAAC,EAAEV,IAAEU,EAAC;AAAE,kBAAG,CAACG,GAAE,QAAO,KAAK,EAAE,EAAC,UAAS,CAAC,2BAA2B,GAAE,gBAAe,EAAC,CAAC;AAAE,oBAAM,IAAE,SAASf,IAAED,IAAEI,IAAE;AAAC,sBAAK,EAAC,gBAAeC,GAAC,IAAEJ,IAAEC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,uBAAO,SAASU,IAAEE,IAAE;AAAC,kBAAAb,GAAE,SAASW,EAAC,IAAE,EAAE,EAAC,OAAM,SAAQ,UAAS,CAAC,0DAAyDA,EAAC,GAAE,gBAAeR,GAAC,CAAC,KAAGU,MAAG,EAAE,EAAC,UAAS,CAAC,iGAAgG,GAAGA,EAAC,GAAE,gBAAeV,GAAC,CAAC,GAAEF,GAAE,KAAKU,EAAC,KAAGX,GAAE,KAAKW,EAAC,GAAEX,GAAE,SAAOC,GAAE,WAASH,MAAG,EAAEI,IAAEH,EAAC;AAAA,gBAAE;AAAA,cAAC,EAAEY,IAAEG,GAAE,oBAAmBD,EAAC;AAAE,eAAC,SAASd,IAAED,IAAEI,IAAEC,IAAE;AAAC,gBAAAJ,GAAE,SAAO,MAAI;AAAC,oBAAEA,IAAED,IAAEI,IAAEC,EAAC;AAAA,gBAAC,GAAE,SAAS,KAAK,YAAYJ,EAAC;AAAA,cAAC,EAAEc,IAAE,GAAEC,IAAEH,EAAC;AAAA,YAAC;AAAC,cAAE,GAAE,IAAE,GAAEF,KAAEA,GAAE,EAAE,KAAM,MAAI;AAAC,gBAAE;AAAA,YAAC,CAAE,EAAE,MAAO,CAAAV,OAAG;AAAC,sBAAM,KAAG,EAAE,iBAAgB,EAAEA,EAAC,CAAC;AAAA,YAAC,CAAE,IAAE,EAAE;AAAA,UAAC,GAAG,CAACA,IAAED,IAAEI,IAAEC,IAAEH,IAAEK,IAAEC,IAAEC,IAAEE,IAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAC,EAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["t", "e", "r", "s", "o", "n", "c", "d", "u", "p", "h", "f", "i", "l", "y", "a", "w"]}