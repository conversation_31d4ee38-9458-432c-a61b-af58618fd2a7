import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Container,
  IconButton,
  useMediaQuery,
  useTheme,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Skeleton,
  Chip,
  alpha,
  Divider,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  TablePagination,
  FormControlLabel,
  Switch,
  Toolbar,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import {
  LocalShipping,
  People,
  AttachMoney,
  Assignment,
  Add,
  MoreVert,
  CheckCircle,
  Cancel,
  DirectionsCar,
  Inventory,
  Paid,
  TrendingUp,
  AccessTime,
  DateRange,
  Refresh,
  FilterList
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { collection, query, where, getDocs, onSnapshot, orderBy, limit, startAfter, endBefore, Timestamp, doc, deleteDoc } from 'firebase/firestore';
import { db } from '../firebase';
import { useAuth } from '../context/AuthContext';
import AdminLayout from '../components/layout/AdminLayout';
import OrdersTable from '../components/shared/OrdersTable';
import StatsCard from '../components/shared/StatsCard';
import ActivityChart from '../components/shared/ActivityChart';
import GoogleMapComponent from '../components/maps/GoogleMapComponent';
import { useMultiUserTracking } from '../hooks/useLocationTracking';
import { formatDistanceToNow, subDays, startOfDay, endOfDay, format } from 'date-fns';
import { motion } from 'framer-motion';
import emptyStateImage from '../assets/empty-state.svg';
import emptyStateImageDark from '../assets/empty-state-dark.svg';
// Theme is already imported above, no need to import it again

const AdminDashboard = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const emptyStateSrc = theme.palette.mode === 'dark' ? emptyStateImageDark : emptyStateImage;

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [mobileOpen, setMobileOpen] = useState(false);
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    stats: null,
    recentOrders: [],
    allOrders: [],
    activities: []
  });

  // Courier tracking state
  const [couriers, setCouriers] = useState([]);
  const [showCourierMap, setShowCourierMap] = useState(false);

  // Delete order confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [orderToDelete, setOrderToDelete] = useState(null);

  // Pagination for recent orders
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  // Date range filter
  const [dateRange, setDateRange] = useState('week');
  const [customDateRange, setCustomDateRange] = useState({
    startDate: subDays(new Date(), 7),
    endDate: new Date()
  });

  // Status filter
  const [statusFilter, setStatusFilter] = useState('all');

  // Get courier IDs for multi-user tracking
  const courierIds = couriers.map(courier => courier.id);

  // Use multi-user tracking hook
  const {
    userLocations,
    loading: trackingLoading,
    getUserLocation,
    getActiveUsers,
    activeUsers: activeUserCount
  } = useMultiUserTracking(courierIds);

  const statCards = useMemo(() => [
    {
      title: 'Total Orders',
      value: dashboardData.stats?.totalOrders || 0,
      icon: <Assignment fontSize="large" />,
      color: theme.palette.primary.main,
      trend: dashboardData.stats?.ordersTrend || 0
    },
    {
      title: 'Pending Orders',
      value: dashboardData.stats?.pendingOrders || 0,
      icon: <AccessTime fontSize="large" />,
      color: theme.palette.warning.main,
      trend: dashboardData.stats?.pendingTrend || 0
    },
    {
      title: 'Revenue',
      value: `₹${(dashboardData.stats?.totalRevenue || 0).toFixed(2)}`,
      icon: <Paid fontSize="large" />,
      color: theme.palette.success.main,
      trend: dashboardData.stats?.revenueTrend || 0
    },
    {
      title: 'Active Customers',
      value: dashboardData.stats?.totalCustomers || 0,
      icon: <People fontSize="large" />,
      color: theme.palette.info.main,
      trend: dashboardData.stats?.customerTrend || 0
    }
  ], [dashboardData.stats, theme]);

  // Get date range based on selected option
  const getDateRange = () => {
    const today = new Date();
    let startDate;

    switch (dateRange) {
      case 'today':
        startDate = startOfDay(today);
        break;
      case 'week':
        startDate = subDays(today, 7);
        break;
      case 'month':
        startDate = subDays(today, 30);
        break;
      case 'custom':
        return {
          start: startOfDay(customDateRange.startDate),
          end: endOfDay(customDateRange.endDate)
        };
      default:
        startDate = subDays(today, 7);
    }

    return {
      start: startOfDay(startDate),
      end: endOfDay(today)
    };
  };

  // Fetch couriers for tracking
  const fetchCouriers = async () => {
    try {
      const couriersRef = collection(db, 'users');
      const couriersQuery = query(couriersRef, where('role', '==', 'courier'));
      const couriersSnapshot = await getDocs(couriersQuery);

      const couriersData = couriersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      setCouriers(couriersData);
    } catch (error) {
      console.error('Error fetching couriers:', error);
    }
  };

  // Set up real-time listeners when component mounts or filters change
  useEffect(() => {
    if (!user?.uid) return;

    // Fetch couriers for tracking
    fetchCouriers();

    // Only set up real-time listeners if enabled
    if (isRealTimeEnabled) {
      setUpRealTimeListeners();
    } else {
      fetchDashboardData();
    }

    return () => {
      // Clean up listeners when component unmounts
      if (window.dashboardUnsubscribe) {
        window.dashboardUnsubscribe();
      }
    };
  }, [user?.uid, dateRange, customDateRange, statusFilter, isRealTimeEnabled]);

  const handleDrawerToggle = () => setMobileOpen(!mobileOpen);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleDateRangeChange = (event) => {
    setDateRange(event.target.value);
  };

  const handleStatusFilterChange = (event) => {
    setStatusFilter(event.target.value);
  };

  const handleToggleRealTime = () => {
    setIsRealTimeEnabled(!isRealTimeEnabled);
  };

  // Handle delete order click
  const handleDeleteOrderClick = (orderId) => {
    const orderToDelete = dashboardData.allOrders.find(order => order.id === orderId);
    if (orderToDelete) {
      setOrderToDelete(orderToDelete);
      setDeleteDialogOpen(true);
    }
  };

  // Confirm delete order
  const confirmDeleteOrder = async () => {
    if (!orderToDelete) return;

    try {
      // Delete from Firestore
      await deleteDoc(doc(db, "orders", orderToDelete.id));

      // If not using real-time updates, update the local state
      if (!isRealTimeEnabled) {
        const updatedOrders = dashboardData.allOrders.filter(order => order.id !== orderToDelete.id);
        const updatedRecentOrders = dashboardData.recentOrders.filter(order => order.id !== orderToDelete.id);

        // Recalculate stats
        const updatedStats = processOrderData(updatedOrders, dashboardData.stats?.totalCustomers || 0);
        const updatedActivities = generateActivities(updatedOrders);

        setDashboardData({
          stats: updatedStats,
          allOrders: updatedOrders,
          recentOrders: updatedRecentOrders,
          activities: updatedActivities
        });
      }

      // Show success message
      alert("Order deleted successfully");
    } catch (error) {
      console.error("Error deleting order:", error);
      alert("Failed to delete order. Please try again.");
    } finally {
      setDeleteDialogOpen(false);
      setOrderToDelete(null);
    }
  };

  const setUpRealTimeListeners = () => {
    setLoading(true);
    setError('');

    try {
      // Get date range
      const { start, end } = getDateRange();

      // Create base query for orders
      let ordersQuery = query(
        collection(db, 'orders'),
        orderBy('createdAt', 'desc')
      );

      // Apply date range filter
      ordersQuery = query(
        collection(db, 'orders'),
        where('createdAt', '>=', Timestamp.fromDate(start)),
        where('createdAt', '<=', Timestamp.fromDate(end)),
        orderBy('createdAt', 'desc')
      );

      // Apply status filter if not 'all'
      if (statusFilter !== 'all') {
        ordersQuery = query(
          collection(db, 'orders'),
          where('status', '==', statusFilter),
          where('createdAt', '>=', Timestamp.fromDate(start)),
          where('createdAt', '<=', Timestamp.fromDate(end)),
          orderBy('createdAt', 'desc')
        );
      }

      // Set up real-time listener for orders
      const unsubscribeOrders = onSnapshot(
        ordersQuery,
        async (ordersSnapshot) => {
          try {
            const ordersData = ordersSnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data(),
              createdAt: doc.data().createdAt?.toDate() || new Date()
            }));

            // Fetch customers (these don't need real-time updates)
            let customerCount = 0;
            try {
              const customersSnapshot = await getDocs(
                query(collection(db, 'users'), where('role', '==', 'customer'))
              );
              customerCount = customersSnapshot.size;
            } catch (customerErr) {
              console.error('Error fetching customers:', customerErr);
              // Continue with default customer count if there's an error
              if (customerErr.code === 'permission-denied') {
                console.log('Permission denied for users collection, using estimated count');
              }
            }

            const stats = processOrderData(ordersData, customerCount);
            const activities = generateActivities(ordersData);

            // Calculate trends
            let previousPeriodOrders = [];
            try {
              // Calculate the date range duration in milliseconds
              const rangeDuration = end.getTime() - start.getTime();

              // Safely calculate previous period dates
              const previousEnd = new Date(start.getTime() - 1); // 1ms before current start
              const previousStart = new Date(previousEnd.getTime() - rangeDuration);

              // Ensure dates are valid
              if (isNaN(previousStart.getTime()) || isNaN(previousEnd.getTime())) {
                throw new Error('Invalid date calculation');
              }

              const previousPeriodQuery = query(
                collection(db, 'orders'),
                where('createdAt', '>=', Timestamp.fromDate(previousStart)),
                where('createdAt', '<=', Timestamp.fromDate(previousEnd))
              );

              const previousPeriodSnapshot = await getDocs(previousPeriodQuery);
              previousPeriodOrders = previousPeriodSnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data(),
                createdAt: doc.data().createdAt?.toDate() || new Date()
              }));
            } catch (trendErr) {
              console.error('Error fetching previous period data:', trendErr);
              // Continue with empty previous period if there's an error
            }

            const previousStats = processOrderData(previousPeriodOrders, customerCount);

            // Calculate trends
            if (previousStats.totalOrders > 0) {
              stats.ordersTrend = ((stats.totalOrders - previousStats.totalOrders) / previousStats.totalOrders) * 100;
              stats.revenueTrend = ((stats.totalRevenue - previousStats.totalRevenue) / previousStats.totalRevenue) * 100;
            } else {
              stats.ordersTrend = 0;
              stats.revenueTrend = 0;
            }

            setDashboardData({
              stats,
              allOrders: ordersData,
              recentOrders: ordersData.slice(0, rowsPerPage),
              activities
            });

            setLoading(false);
          } catch (err) {
            console.error('Error processing dashboard data:', err);

            // Handle permission errors gracefully
            if (err.code === 'permission-denied') {
              setError('You do not have permission to access some data. Some features may be limited.');
            } else {
              setError('Error processing dashboard data. Please try again.');
            }

            // Initialize with empty data
            setDashboardData({
              stats: {
                totalOrders: 0,
                totalCustomers: 0,
                pendingOrders: 0,
                delivered: 0,
                inTransit: 0,
                cancelled: 0,
                onField: 0,
                monthlyDeliveries: 0,
                totalRevenue: 0,
                ordersTrend: 0,
                revenueTrend: 0
              },
              allOrders: [],
              recentOrders: [],
              activities: []
            });

            setLoading(false);
          }
        },
        (err) => {
          console.error('Firestore listener error:', err);

          // Handle permission errors gracefully
          if (err.code === 'permission-denied') {
            setError('You do not have permission to access this data. Please contact your administrator.');
          } else {
            setError('Failed to connect to the database. Please check your connection.');
          }

          // Initialize with empty data
          setDashboardData({
            stats: {
              totalOrders: 0,
              totalCustomers: 0,
              pendingOrders: 0,
              delivered: 0,
              inTransit: 0,
              cancelled: 0,
              onField: 0,
              monthlyDeliveries: 0,
              totalRevenue: 0,
              ordersTrend: 0,
              revenueTrend: 0
            },
            allOrders: [],
            recentOrders: [],
            activities: []
          });

          setLoading(false);
        }
      );

      // Store unsubscribe function
      window.dashboardUnsubscribe = unsubscribeOrders;

    } catch (err) {
      console.error('Error setting up real-time listeners:', err);

      // Handle permission errors gracefully
      if (err.code === 'permission-denied') {
        setError('You do not have permission to access this data. Please contact your administrator.');
      } else {
        setError('Failed to set up real-time data. Falling back to manual refresh.');
      }

      setLoading(false);

      // Fall back to manual fetch
      fetchDashboardData();
    }
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError('');

      // Get date range
      const { start, end } = getDateRange();

      // Create base query for orders
      let ordersQuery = query(
        collection(db, 'orders'),
        where('createdAt', '>=', Timestamp.fromDate(start)),
        where('createdAt', '<=', Timestamp.fromDate(end)),
        orderBy('createdAt', 'desc')
      );

      // Apply status filter if not 'all'
      if (statusFilter !== 'all') {
        ordersQuery = query(
          collection(db, 'orders'),
          where('status', '==', statusFilter),
          where('createdAt', '>=', Timestamp.fromDate(start)),
          where('createdAt', '<=', Timestamp.fromDate(end)),
          orderBy('createdAt', 'desc')
        );
      }

      // Fetch orders and customers with error handling
      let ordersData = [];
      let customerCount = 0;

      try {
        const ordersSnapshot = await getDocs(ordersQuery);
        ordersData = ordersSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date()
        }));
      } catch (ordersErr) {
        console.error('Error fetching orders:', ordersErr);
        if (ordersErr.code === 'permission-denied') {
          setError('You do not have permission to access orders data. Some features may be limited.');
        } else {
          throw ordersErr; // Re-throw if it's not a permission error
        }
      }

      try {
        const customersSnapshot = await getDocs(
          query(collection(db, 'users'), where('role', '==', 'customer'))
        );
        customerCount = customersSnapshot.size;
      } catch (customersErr) {
        console.error('Error fetching customers:', customersErr);
        // Continue with default customer count
        if (customersErr.code === 'permission-denied') {
          console.log('Permission denied for users collection, using estimated count');
        }
      }

      const stats = processOrderData(ordersData, customerCount);
      const activities = generateActivities(ordersData);

      // Calculate trends with error handling
      let previousPeriodOrders = [];
      try {
        // Calculate the date range duration in milliseconds
        const rangeDuration = end.getTime() - start.getTime();

        // Safely calculate previous period dates
        const previousEnd = new Date(start.getTime() - 1); // 1ms before current start
        const previousStart = new Date(previousEnd.getTime() - rangeDuration);

        // Ensure dates are valid
        if (isNaN(previousStart.getTime()) || isNaN(previousEnd.getTime())) {
          throw new Error('Invalid date calculation');
        }

        const previousPeriodQuery = query(
          collection(db, 'orders'),
          where('createdAt', '>=', Timestamp.fromDate(previousStart)),
          where('createdAt', '<=', Timestamp.fromDate(previousEnd))
        );

        const previousPeriodSnapshot = await getDocs(previousPeriodQuery);
        previousPeriodOrders = previousPeriodSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date()
        }));
      } catch (trendErr) {
        console.error('Error fetching previous period data:', trendErr);
        // Continue with empty previous period
      }

      const previousStats = processOrderData(previousPeriodOrders, customerCount);

      // Calculate trends
      if (previousStats.totalOrders > 0) {
        stats.ordersTrend = ((stats.totalOrders - previousStats.totalOrders) / previousStats.totalOrders) * 100;
        stats.revenueTrend = ((stats.totalRevenue - previousStats.totalRevenue) / previousStats.totalRevenue) * 100;
      } else {
        stats.ordersTrend = 0;
        stats.revenueTrend = 0;
      }

      setDashboardData({
        stats,
        allOrders: ordersData,
        recentOrders: ordersData.slice(0, rowsPerPage),
        activities
      });

    } catch (err) {
      console.error('Dashboard error:', err);

      // Handle permission errors gracefully
      if (err.code === 'permission-denied') {
        setError('You do not have permission to access this data. Please contact your administrator.');
      } else {
        setError('Failed to load dashboard data. Please try again.');
      }

      // Initialize with empty data
      setDashboardData({
        stats: {
          totalOrders: 0,
          totalCustomers: 0,
          pendingOrders: 0,
          delivered: 0,
          inTransit: 0,
          cancelled: 0,
          onField: 0,
          monthlyDeliveries: 0,
          totalRevenue: 0,
          ordersTrend: 0,
          revenueTrend: 0
        },
        allOrders: [],
        recentOrders: [],
        activities: []
      });
    } finally {
      setLoading(false);
    }
  };

  const processOrderData = (orders, customerCount) => {
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    return orders.reduce((acc, order) => {
      const status = order.status?.toLowerCase();
      const amount = order.discountedAmount || order.amount || 0;

      acc.totalRevenue += parseFloat(amount);
      acc[status] = (acc[status] || 0) + 1;

      if (status === 'delivered' &&
          order.createdAt.getMonth() === currentMonth &&
          order.createdAt.getFullYear() === currentYear) {
        acc.monthlyDeliveries++;
      }

      return acc;
    }, {
      totalOrders: orders.length,
      totalCustomers: customerCount,
      pendingOrders: 0,
      delivered: 0,
      inTransit: 0,
      cancelled: 0,
      onField: 0,
      monthlyDeliveries: 0,
      totalRevenue: 0
    });
  };

  const generateActivities = (orders) => {
    return orders
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice(0, 5)
      .map(order => ({
        id: order.id,
        user: order.customerName || 'Customer',
        action: `Order ${order.status}`,
        time: formatDistanceToNow(order.createdAt, { addSuffix: true }),
        amount: order.amount || 0,
        status: order.status
      }));
  };

  const handleAddOrder = () => navigate('/orders/create');

  const handleViewOrder = (orderId) => navigate(`/orders/${orderId}`);

  const renderLoadingSkeleton = () => (
    <Grid container spacing={3}>
      {[1, 2, 3, 4].map((item) => (
        <Grid item xs={12} sm={6} md={3} key={item}>
          <Skeleton variant="rounded" height={150} />
        </Grid>
      ))}
      <Grid item xs={12} md={8}>
        <Skeleton variant="rounded" height={400} />
      </Grid>
      <Grid item xs={12} md={4}>
        <Skeleton variant="rounded" height={400} />
      </Grid>
    </Grid>
  );

  const renderEmptyState = () => (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      style={{ textAlign: 'center', marginTop: theme.spacing(8) }}
    >
      <img
        src={emptyStateSrc}
        alt="No data"
        style={{ width: '60%', maxWidth: 400, opacity: 0.8 }}
      />
      <Typography variant="h6" color="textSecondary" gutterBottom>
        No orders found
      </Typography>
      <Button
        variant="contained"
        color="primary"
        startIcon={<Add />}
        onClick={handleAddOrder}
      >
        Create First Order
      </Button>
    </motion.div>
  );

  return (
    <AdminLayout>
      <Container maxWidth="xl" sx={{ py: 4, px: isMobile ? 2 : 4 }}>
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
            flexDirection: isMobile ? 'column' : 'row',
            gap: 2
          }}>
            <Typography variant="h4" fontWeight="800" color="primary">
              Dashboard Overview
            </Typography>
            <Button
              variant="gradient"
              startIcon={<Add />}
              onClick={handleAddOrder}
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                color: 'white',
                borderRadius: 50,
                px: 4,
                py: 1
              }}
            >
              New Order
            </Button>
          </Box>

          {/* Filter Controls */}
          <Paper
            sx={{
              p: 2,
              mb: 4,
              borderRadius: 2,
              display: 'flex',
              flexDirection: isMobile ? 'column' : 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: 2
            }}
          >
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: 2,
              width: isMobile ? '100%' : 'auto'
            }}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel id="date-range-label">Date Range</InputLabel>
                <Select
                  labelId="date-range-label"
                  id="date-range-select"
                  value={dateRange}
                  label="Date Range"
                  onChange={handleDateRangeChange}
                >
                  <MenuItem value="today">Today</MenuItem>
                  <MenuItem value="week">Last 7 Days</MenuItem>
                  <MenuItem value="month">Last 30 Days</MenuItem>
                  <MenuItem value="custom">Custom Range</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel id="status-filter-label">Status</InputLabel>
                <Select
                  labelId="status-filter-label"
                  id="status-filter-select"
                  value={statusFilter}
                  label="Status"
                  onChange={handleStatusFilterChange}
                >
                  <MenuItem value="all">All Orders</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="processing">Processing</MenuItem>
                  <MenuItem value="ready_to_cargo">Ready to Cargo</MenuItem>
                  <MenuItem value="in_transit">In Transit</MenuItem>
                  <MenuItem value="hold">On Hold</MenuItem>
                  <MenuItem value="delivered">Delivered</MenuItem>
                  <MenuItem value="return">Returned</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={isRealTimeEnabled}
                    onChange={handleToggleRealTime}
                    color="primary"
                  />
                }
                label="Real-time Updates"
              />

              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={fetchDashboardData}
                size="small"
              >
                Refresh
              </Button>
            </Box>
          </Paper>

          {error && (
            <Alert
              severity="error"
              sx={{ mb: 4 }}
              action={
                <Button color="inherit" size="small" onClick={fetchDashboardData}>
                  Retry
                </Button>
              }
            >
              {error}
            </Alert>
          )}

          {loading ? renderLoadingSkeleton() : (
            <>
              {!dashboardData.stats || dashboardData.stats.totalOrders === 0 ? renderEmptyState() : (
                <>
                  <Grid container spacing={{ xs: 2, sm: 3 }}>
                    {statCards.map((item, idx) => (
                      <Grid item key={idx} xs={6} sm={6} md={3}>
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: idx * 0.1 }}
                        >
                          <StatsCard {...item} />
                        </motion.div>
                      </Grid>
                    ))}
                  </Grid>

                  <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mt: 1 }}>
                    <Grid item xs={12} md={8}>
                      <Paper sx={{
                        p: { xs: 2, sm: 3 },
                        borderRadius: 4,
                        boxShadow: theme.shadows[4],
                        background: alpha(theme.palette.background.paper, 0.8),
                        backdropFilter: 'blur(10px)'
                      }}>
                        <Box sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          mb: { xs: 2, sm: 3 },
                          flexDirection: isMobile ? 'column' : 'row',
                          gap: isMobile ? 1 : 0
                        }}>
                          <Typography variant="h6" fontWeight="600" component="div" sx={{
                            fontSize: { xs: '1rem', sm: '1.25rem' }
                          }}>
                            Delivery Analytics
                          </Typography>
                          {isRealTimeEnabled && (
                            <Chip
                              label="Real-time Data"
                              color="success"
                              size="small"
                              icon={<TrendingUp fontSize="small" />}
                            />
                          )}
                        </Box>
                        <Box sx={{ height: { xs: 250, sm: 300, md: 350 } }}>
                          <ActivityChart orders={dashboardData.allOrders} />
                        </Box>
                      </Paper>
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Paper sx={{
                        p: { xs: 2, sm: 3 },
                        borderRadius: 4,
                        boxShadow: theme.shadows[4]
                      }}>
                        <Box sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          mb: { xs: 2, sm: 3 }
                        }}>
                          <Typography variant="h6" fontWeight="600" component="div" sx={{
                            fontSize: { xs: '1rem', sm: '1.25rem' }
                          }}>
                            Recent Activities
                          </Typography>
                          <IconButton size="small">
                            <MoreVert />
                          </IconButton>
                        </Box>
                        <List disablePadding sx={{
                          maxHeight: { xs: 300, sm: 350, md: 400 },
                          overflow: 'auto',
                          '&::-webkit-scrollbar': {
                            width: '4px',
                          },
                          '&::-webkit-scrollbar-track': {
                            background: 'transparent',
                          },
                          '&::-webkit-scrollbar-thumb': {
                            background: theme.palette.divider,
                            borderRadius: '4px',
                          },
                        }}>
                          {dashboardData.activities.map((activity) => (
                            <motion.div
                              key={activity.id}
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                            >
                              <ListItem
                                sx={{
                                  py: 1.5,
                                  borderRadius: 2,
                                  '&:hover': {
                                    backgroundColor: theme.palette.action.hover
                                  }
                                }}
                              >
                                <ListItemAvatar>
                                  <Avatar sx={{
                                    bgcolor: theme.palette.background.default,
                                    color: theme.palette.text.primary,
                                    width: { xs: 32, sm: 40 },
                                    height: { xs: 32, sm: 40 }
                                  }}>
                                    {activity.status === 'delivered' ? (
                                      <CheckCircle color="success" fontSize={isMobile ? "small" : "medium"} />
                                    ) : activity.status === 'cancelled' ? (
                                      <Cancel color="error" fontSize={isMobile ? "small" : "medium"} />
                                    ) : (
                                      <LocalShipping color="info" fontSize={isMobile ? "small" : "medium"} />
                                    )}
                                  </Avatar>
                                </ListItemAvatar>
                                <ListItemText
                                  primary={activity.user}
                                  primaryTypographyProps={{
                                    fontWeight: "500",
                                    fontSize: { xs: '0.875rem', sm: '1rem' }
                                  }}
                                  secondaryTypographyProps={{
                                    component: 'div' // Change the secondary component to div instead of p
                                  }}
                                  secondary={
                                    <>
                                      <Typography variant="body2" component="div" sx={{
                                        fontSize: { xs: '0.75rem', sm: '0.875rem' }
                                      }}>
                                        {activity.action}
                                      </Typography>
                                      <Box
                                        sx={{
                                          display: 'flex',
                                          justifyContent: 'space-between',
                                          mt: 0.5
                                        }}
                                      >
                                        <Chip
                                          label={`₹${activity.amount}`}
                                          size="small"
                                          color={activity.status === 'delivered' ? 'success' : activity.status === 'cancelled' ? 'error' : 'primary'}
                                          variant="outlined"
                                          sx={{
                                            height: { xs: 20, sm: 24 },
                                            fontSize: { xs: '0.625rem', sm: '0.75rem' }
                                          }}
                                        />
                                        <Typography variant="caption" color="textSecondary">
                                          {activity.time}
                                        </Typography>
                                      </Box>
                                    </>
                                  }
                                />
                              </ListItem>
                              <Divider sx={{ my: 1 }} />
                            </motion.div>
                          ))}
                        </List>
                      </Paper>
                    </Grid>
                  </Grid>

                  {/* Courier Tracking Map */}
                  <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mt: 1 }}>
                    <Grid item xs={12}>
                      <Paper sx={{
                        p: { xs: 2, sm: 3 },
                        borderRadius: 4,
                        boxShadow: theme.shadows[4],
                        background: alpha(theme.palette.background.paper, 0.8),
                        backdropFilter: 'blur(10px)'
                      }}>
                        <Box sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          mb: { xs: 2, sm: 3 },
                          flexDirection: isMobile ? 'column' : 'row',
                          gap: isMobile ? 1 : 0
                        }}>
                          <Typography variant="h6" fontWeight="600" component="div" sx={{
                            fontSize: { xs: '1rem', sm: '1.25rem' }
                          }}>
                            Live Courier Tracking
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                            <Chip
                              label={`${activeUserCount} Active`}
                              color="success"
                              size="small"
                              icon={<DirectionsCar fontSize="small" />}
                            />
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => setShowCourierMap(!showCourierMap)}
                              sx={{ textTransform: 'none' }}
                            >
                              {showCourierMap ? 'Hide Map' : 'Show Map'}
                            </Button>
                          </Box>
                        </Box>

                        {showCourierMap && (
                          <Box sx={{ height: 400, borderRadius: 2, overflow: 'hidden' }}>
                            <GoogleMapComponent
                              center={{ lat: 19.0760, lng: 72.8777 }}
                              zoom={11}
                              markers={Object.entries(userLocations).map(([userId, locationData]) => {
                                const courier = couriers.find(c => c.id === userId);
                                return {
                                  position: {
                                    lat: locationData.location?.lat || locationData.location?.latitude,
                                    lng: locationData.location?.lng || locationData.location?.longitude
                                  },
                                  type: 'courier',
                                  title: `${courier?.name || courier?.fullName || 'Courier'} - ${locationData.isOnline ? 'Online' : 'Offline'}`,
                                  status: locationData.trackingActive ? 'active' : 'inactive',
                                  data: {
                                    userId,
                                    courier,
                                    locationData,
                                    lastUpdate: locationData.lastUpdate
                                  }
                                };
                              }).filter(marker => marker.position.lat && marker.position.lng)}
                              showUserLocation={false}
                              enableFullscreen={true}
                              enableRefresh={true}
                              enableMyLocation={false}
                              height={400}
                              onMarkerClick={(markerData) => {
                                console.log('Courier clicked:', markerData);
                                // Navigate to courier details or show info
                              }}
                            />
                          </Box>
                        )}

                        {!showCourierMap && (
                          <Box sx={{
                            textAlign: 'center',
                            py: 4,
                            color: 'text.secondary'
                          }}>
                            <DirectionsCar sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
                            <Typography variant="body1">
                              Click "Show Map" to view live courier locations
                            </Typography>
                            <Typography variant="body2" sx={{ mt: 1 }}>
                              {couriers.length} couriers registered, {activeUserCount} currently active
                            </Typography>
                          </Box>
                        )}
                      </Paper>
                    </Grid>
                  </Grid>

                  <Paper sx={{
                    mt: 4,
                    borderRadius: 4,
                    boxShadow: theme.shadows[4]
                  }}>
                    <Box sx={{
                      p: 3,
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      borderBottom: `1px solid ${theme.palette.divider}`
                    }}>
                      <Typography variant="h6" fontWeight="600" component="div">
                        Recent Orders
                      </Typography>
                      <Button
                        onClick={() => navigate('/orders/list')}
                        endIcon={<TrendingUp />}
                        sx={{ textTransform: 'none' }}
                      >
                        View Full Report
                      </Button>
                    </Box>
                    <OrdersTable
                      orders={dashboardData.recentOrders.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)}
                      onViewOrder={handleViewOrder}
                      onDeleteOrder={handleDeleteOrderClick}
                      sx={{
                        '& .MuiDataGrid-columnHeaders': {
                          backgroundColor: theme.palette.background.paper,
                          borderRadius: 4
                        }
                      }}
                    />
                    <TablePagination
                      component="div"
                      count={dashboardData.recentOrders.length}
                      page={page}
                      onPageChange={handleChangePage}
                      rowsPerPage={rowsPerPage}
                      onRowsPerPageChange={handleChangeRowsPerPage}
                      rowsPerPageOptions={[5, 10, 25]}
                    />
                  </Paper>
                </>
              )}
            </>
          )}
        </Container>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
        >
          <DialogTitle>Confirm Delete</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete this order? This action cannot be undone.
            </DialogContentText>
            {orderToDelete && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Order ID:</strong> {orderToDelete.id.substring(0, 8)}
                </Typography>
                <Typography variant="body2">
                  <strong>Customer:</strong> {orderToDelete.customerName || 'Unknown Customer'}
                </Typography>
                <Typography variant="body2">
                  <strong>Amount:</strong> ₹{parseFloat(orderToDelete.amount || 0).toFixed(2)}
                </Typography>
                <Typography variant="body2">
                  <strong>Status:</strong> {orderToDelete.status || 'Pending'}
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
            <Button onClick={confirmDeleteOrder} color="error" variant="contained">
              Delete
            </Button>
          </DialogActions>
        </Dialog>
    </AdminLayout>
  );
};

export default AdminDashboard;