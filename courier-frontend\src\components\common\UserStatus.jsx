import React, { useState, useEffect } from 'react';
import { Box, Typography, Tooltip, Badge } from '@mui/material';
import { styled } from '@mui/material/styles';
import { listenToUserStatus, getLastActiveTime } from '../../utils/userStatusUtils';

const StyledBadge = styled(Badge)(({ theme, status }) => ({
  '& .MuiBadge-badge': {
    backgroundColor: status === 'online' ? '#44b700' : '#bdbdbd',
    color: status === 'online' ? '#44b700' : '#bdbdbd',
    boxShadow: `0 0 0 2px ${theme.palette.background.paper}`,
    '&::after': {
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      borderRadius: '50%',
      animation: status === 'online' ? 'ripple 1.2s infinite ease-in-out' : 'none',
      border: '1px solid currentColor',
      content: '""',
    },
  },
  '@keyframes ripple': {
    '0%': {
      transform: 'scale(.8)',
      opacity: 1,
    },
    '100%': {
      transform: 'scale(2.4)',
      opacity: 0,
    },
  },
}));

/**
 * Component to display user online status
 *
 * @param {Object} props - Component props
 * @param {string} props.userId - User ID to track
 * @param {React.ReactNode} props.children - Child component (usually an Avatar)
 * @param {boolean} props.showText - Whether to show status text
 * @param {boolean} props.inline - Whether to render as inline elements (span instead of p)
 * @param {Object} props.badgeProps - Props to pass to the Badge component
 * @param {Object} props.sx - Additional styles
 */
const UserStatus = ({ userId, children, showText = false, inline = false, badgeProps = {}, sx = {} }) => {
  const [status, setStatus] = useState({ isOnline: false, lastChanged: null });

  useEffect(() => {
    if (!userId) return;

    const unsubscribe = listenToUserStatus(userId, (newStatus) => {
      setStatus(newStatus);
    });

    return () => unsubscribe();
  }, [userId]);

  const statusText = status.isOnline
    ? 'Online'
    : status.lastChanged
      ? `Last seen ${getLastActiveTime(status.lastChanged)}`
      : 'Offline';

  const tooltipTitle = (
    <Typography variant="body2">
      {statusText}
    </Typography>
  );

  // If no children are provided, just show the status text
  if (!children) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', ...sx }}>
        <Box
          sx={{
            width: 10,
            height: 10,
            borderRadius: '50%',
            bgcolor: status.isOnline ? '#44b700' : '#bdbdbd',
            mr: 1
          }}
        />
        <Typography
          variant="body2"
          color="text.secondary"
          component={inline ? "span" : "p"}
        >
          {statusText}
        </Typography>
      </Box>
    );
  }

  // If children are provided, wrap them in a badge
  return (
    <Tooltip title={tooltipTitle} arrow>
      <Box sx={sx}>
        <StyledBadge
          overlap="circular"
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          variant="dot"
          status={status.isOnline ? 'online' : 'offline'}
          {...badgeProps}
        >
          {children}
        </StyledBadge>

        {showText && (
          <Typography
            variant="caption"
            color="text.secondary"
            component={inline ? "span" : "p"}
            sx={{ display: inline ? 'inline' : 'block', mt: 0.5, textAlign: 'center' }}
          >
            {status.isOnline ? 'Online' : 'Offline'}
          </Typography>
        )}
      </Box>
    </Tooltip>
  );
};

export default UserStatus;
