import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  Chip,
  Grid,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  LocationOn,
  LocationOff,
  GpsFixed,
  GpsNotFixed,
  Speed,
  AccessTime,
  MyLocation,
  History,
  Settings,
  Info,
  Refresh,
  Warning
} from '@mui/icons-material';
import { useLocationTracking } from '../../hooks/useLocationTracking';
import { useAuth } from '../../context/AuthContext';

const LocationTrackingPanel = () => {
  const { user } = useAuth();
  const [showSettings, setShowSettings] = useState(false);
  const [showLocationHistory, setShowLocationHistory] = useState(false);
  const [autoStart, setAutoStart] = useState(false);

  const {
    isTracking,
    currentLocation,
    locationHistory,
    error,
    stats,
    permissionStatus,
    startTracking,
    stopTracking,
    getCurrentLocation,
    getLocationHistory,
    requestPermission,
    isSupported,
    isOnline
  } = useLocationTracking();

  // Auto-start tracking if enabled
  useEffect(() => {
    const savedAutoStart = localStorage.getItem('autoStartTracking') === 'true';
    setAutoStart(savedAutoStart);
    
    if (savedAutoStart && !isTracking && permissionStatus === 'granted') {
      handleStartTracking();
    }
  }, [permissionStatus]);

  const handleStartTracking = async () => {
    try {
      const success = await startTracking({
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 30000,
        distanceThreshold: 5,
        accuracyThreshold: 50
      });
      
      if (!success) {
        console.error('Failed to start tracking');
      }
    } catch (err) {
      console.error('Error starting tracking:', err);
    }
  };

  const handleStopTracking = async () => {
    try {
      await stopTracking();
    } catch (err) {
      console.error('Error stopping tracking:', err);
    }
  };

  const handleGetCurrentLocation = async () => {
    try {
      await getCurrentLocation();
    } catch (err) {
      console.error('Error getting current location:', err);
    }
  };

  const handleRequestPermission = async () => {
    try {
      await requestPermission();
    } catch (err) {
      console.error('Error requesting permission:', err);
    }
  };

  const handleAutoStartChange = (event) => {
    const enabled = event.target.checked;
    setAutoStart(enabled);
    localStorage.setItem('autoStartTracking', enabled.toString());
  };

  const handleShowHistory = async () => {
    await getLocationHistory(50);
    setShowLocationHistory(true);
  };

  const getPermissionStatusColor = () => {
    switch (permissionStatus) {
      case 'granted': return 'success';
      case 'denied': return 'error';
      default: return 'warning';
    }
  };

  const getPermissionStatusText = () => {
    switch (permissionStatus) {
      case 'granted': return 'Granted';
      case 'denied': return 'Denied';
      case 'prompt': return 'Not Requested';
      default: return 'Unknown';
    }
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'N/A';
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const formatAccuracy = (accuracy) => {
    if (!accuracy) return 'N/A';
    return `±${Math.round(accuracy)}m`;
  };

  const formatSpeed = (speed) => {
    if (!speed) return 'N/A';
    return `${(speed * 3.6).toFixed(1)} km/h`;
  };

  if (!isSupported) {
    return (
      <Card>
        <CardContent>
          <Alert severity="error">
            Geolocation is not supported by this browser
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box>
      {/* Main Control Panel */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6" component="div">
              Location Tracking
            </Typography>
            <Box>
              <Tooltip title="Settings">
                <IconButton onClick={() => setShowSettings(true)}>
                  <Settings />
                </IconButton>
              </Tooltip>
              <Tooltip title="Refresh">
                <IconButton onClick={handleGetCurrentLocation}>
                  <Refresh />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Status Indicators */}
          <Grid container spacing={2} mb={2}>
            <Grid item xs={6} sm={3}>
              <Box textAlign="center">
                <Chip
                  icon={isTracking ? <LocationOn /> : <LocationOff />}
                  label={isTracking ? 'Active' : 'Inactive'}
                  color={isTracking ? 'success' : 'default'}
                  variant="outlined"
                />
                <Typography variant="caption" display="block" mt={1}>
                  Tracking Status
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box textAlign="center">
                <Chip
                  icon={isOnline ? <GpsFixed /> : <GpsNotFixed />}
                  label={isOnline ? 'Online' : 'Offline'}
                  color={isOnline ? 'primary' : 'default'}
                  variant="outlined"
                />
                <Typography variant="caption" display="block" mt={1}>
                  Connection
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box textAlign="center">
                <Chip
                  label={getPermissionStatusText()}
                  color={getPermissionStatusColor()}
                  variant="outlined"
                />
                <Typography variant="caption" display="block" mt={1}>
                  Permission
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box textAlign="center">
                <Chip
                  label={stats?.successRate || '0%'}
                  color="info"
                  variant="outlined"
                />
                <Typography variant="caption" display="block" mt={1}>
                  Success Rate
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Error Display */}
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error.message}
            </Alert>
          )}

          {/* Control Buttons */}
          <Box display="flex" gap={1} mb={2}>
            {!isTracking ? (
              <Button
                variant="contained"
                startIcon={<LocationOn />}
                onClick={handleStartTracking}
                disabled={permissionStatus === 'denied'}
                fullWidth
              >
                Start Tracking
              </Button>
            ) : (
              <Button
                variant="outlined"
                startIcon={<LocationOff />}
                onClick={handleStopTracking}
                fullWidth
              >
                Stop Tracking
              </Button>
            )}
            
            {permissionStatus !== 'granted' && (
              <Button
                variant="outlined"
                onClick={handleRequestPermission}
                fullWidth
              >
                Request Permission
              </Button>
            )}
          </Box>

          {/* Current Location Info */}
          {currentLocation && (
            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle2" gutterBottom>
                  Current Location
                </Typography>
                <Grid container spacing={1}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Coordinates: {currentLocation.lat?.toFixed(6)}, {currentLocation.lng?.toFixed(6)}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Accuracy: {formatAccuracy(currentLocation.accuracy)}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Speed: {formatSpeed(currentLocation.speed)}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Updated: {formatTimestamp(currentLocation.timestamp)}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Statistics Card */}
      {stats && (
        <Card sx={{ mb: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Tracking Statistics
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6} sm={3}>
                <Box textAlign="center">
                  <Typography variant="h4" color="primary">
                    {stats.successfulUpdates || 0}
                  </Typography>
                  <Typography variant="caption">
                    Successful Updates
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box textAlign="center">
                  <Typography variant="h4" color="secondary">
                    {stats.failedUpdates || 0}
                  </Typography>
                  <Typography variant="caption">
                    Failed Updates
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box textAlign="center">
                  <Typography variant="h4" color="info.main">
                    {stats.averageAccuracy?.toFixed(1) || 0}m
                  </Typography>
                  <Typography variant="caption">
                    Avg Accuracy
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box textAlign="center">
                  <Typography variant="h4" color="success.main">
                    {stats.historySize || 0}
                  </Typography>
                  <Typography variant="caption">
                    History Points
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Quick Actions
          </Typography>
          <List dense>
            <ListItem button onClick={handleShowHistory}>
              <ListItemIcon>
                <History />
              </ListItemIcon>
              <ListItemText 
                primary="View Location History" 
                secondary={`${locationHistory.length} locations stored`}
              />
            </ListItem>
          </List>
        </CardContent>
      </Card>

      {/* Settings Dialog */}
      <Dialog open={showSettings} onClose={() => setShowSettings(false)}>
        <DialogTitle>Location Tracking Settings</DialogTitle>
        <DialogContent>
          <FormControlLabel
            control={
              <Switch
                checked={autoStart}
                onChange={handleAutoStartChange}
              />
            }
            label="Auto-start tracking when app opens"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSettings(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Location History Dialog */}
      <Dialog 
        open={showLocationHistory} 
        onClose={() => setShowLocationHistory(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Location History</DialogTitle>
        <DialogContent>
          <List>
            {locationHistory.map((location, index) => (
              <React.Fragment key={index}>
                <ListItem>
                  <ListItemIcon>
                    <LocationOn />
                  </ListItemIcon>
                  <ListItemText
                    primary={`${location.lat?.toFixed(6)}, ${location.lng?.toFixed(6)}`}
                    secondary={`${formatTimestamp(location.timestamp)} - Accuracy: ${formatAccuracy(location.accuracy)}`}
                  />
                </ListItem>
                {index < locationHistory.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowLocationHistory(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default LocationTrackingPanel;
