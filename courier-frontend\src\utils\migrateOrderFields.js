// Utility to migrate existing orders to use consistent courier assignment field names
import { collection, getDocs, updateDoc, doc, writeBatch } from 'firebase/firestore';
import { db } from '../firebase';

/**
 * Migrate existing orders to ensure consistent courier assignment field names
 * This function will add missing fields (assignedCourierId, courierId) to orders
 * that only have one of the courier assignment fields
 */
export const migrateOrderCourierFields = async () => {
  try {
    console.log('Starting order field migration...');
    
    // Get all orders
    const ordersRef = collection(db, 'orders');
    const snapshot = await getDocs(ordersRef);
    
    if (snapshot.empty) {
      console.log('No orders found to migrate');
      return { success: true, message: 'No orders found' };
    }

    const batch = writeBatch(db);
    let migratedCount = 0;
    let totalOrders = snapshot.docs.length;

    snapshot.docs.forEach((orderDoc) => {
      const orderData = orderDoc.data();
      const orderId = orderDoc.id;
      let needsUpdate = false;
      const updates = {};

      // Check if order has any courier assignment
      const assignedCourier = orderData.assignedCourier;
      const assignedCourierId = orderData.assignedCourierId;
      const courierId = orderData.courierId;

      // If order has a courier assigned but missing some fields, add them
      if (assignedCourier && (!assignedCourierId || !courierId)) {
        if (!assignedCourierId) {
          updates.assignedCourierId = assignedCourier;
          needsUpdate = true;
        }
        if (!courierId) {
          updates.courierId = assignedCourier;
          needsUpdate = true;
        }
      } else if (assignedCourierId && (!assignedCourier || !courierId)) {
        if (!assignedCourier) {
          updates.assignedCourier = assignedCourierId;
          needsUpdate = true;
        }
        if (!courierId) {
          updates.courierId = assignedCourierId;
          needsUpdate = true;
        }
      } else if (courierId && (!assignedCourier || !assignedCourierId)) {
        if (!assignedCourier) {
          updates.assignedCourier = courierId;
          needsUpdate = true;
        }
        if (!assignedCourierId) {
          updates.assignedCourierId = courierId;
          needsUpdate = true;
        }
      }

      if (needsUpdate) {
        const orderRef = doc(db, 'orders', orderId);
        batch.update(orderRef, updates);
        migratedCount++;
        console.log(`Migrating order ${orderId.substring(0, 8)}:`, updates);
      }
    });

    if (migratedCount > 0) {
      await batch.commit();
      console.log(`Migration completed: ${migratedCount} out of ${totalOrders} orders updated`);
      return { 
        success: true, 
        message: `Successfully migrated ${migratedCount} orders`,
        migratedCount,
        totalOrders
      };
    } else {
      console.log('No orders needed migration');
      return { 
        success: true, 
        message: 'All orders already have consistent field names',
        migratedCount: 0,
        totalOrders
      };
    }

  } catch (error) {
    console.error('Error during migration:', error);
    return { 
      success: false, 
      message: `Migration failed: ${error.message}`,
      error
    };
  }
};

/**
 * Check how many orders need migration without actually migrating them
 */
export const checkOrdersMigrationStatus = async () => {
  try {
    console.log('Checking orders migration status...');
    
    const ordersRef = collection(db, 'orders');
    const snapshot = await getDocs(ordersRef);
    
    if (snapshot.empty) {
      return { 
        totalOrders: 0, 
        needsMigration: 0, 
        hasAssignedCourier: 0,
        hasAssignedCourierId: 0,
        hasCourierId: 0,
        unassigned: 0
      };
    }

    let needsMigration = 0;
    let hasAssignedCourier = 0;
    let hasAssignedCourierId = 0;
    let hasCourierId = 0;
    let unassigned = 0;

    snapshot.docs.forEach((orderDoc) => {
      const orderData = orderDoc.data();
      
      const assignedCourier = orderData.assignedCourier;
      const assignedCourierId = orderData.assignedCourierId;
      const courierId = orderData.courierId;

      // Count field presence
      if (assignedCourier) hasAssignedCourier++;
      if (assignedCourierId) hasAssignedCourierId++;
      if (courierId) hasCourierId++;

      // Check if order has any courier assignment
      const hasAnyCourierField = assignedCourier || assignedCourierId || courierId;
      
      if (!hasAnyCourierField) {
        unassigned++;
      } else {
        // Check if order needs migration (has some but not all fields)
        const hasAllFields = assignedCourier && assignedCourierId && courierId;
        if (!hasAllFields) {
          needsMigration++;
        }
      }
    });

    const status = {
      totalOrders: snapshot.docs.length,
      needsMigration,
      hasAssignedCourier,
      hasAssignedCourierId,
      hasCourierId,
      unassigned,
      fullyMigrated: snapshot.docs.length - needsMigration - unassigned
    };

    console.log('Migration status:', status);
    return status;

  } catch (error) {
    console.error('Error checking migration status:', error);
    throw error;
  }
};

export default {
  migrateOrderCourierFields,
  checkOrdersMigrationStatus
};
