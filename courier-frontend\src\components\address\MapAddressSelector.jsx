// Map Address Selector Component for precise location selection
import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Alert,
  CircularProgress,
  Paper,
  IconButton,
  Tooltip,
  Chip
} from '@mui/material';
import {
  MyLocation,
  CenterFocusStrong,
  Check,
  Refresh
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { GOOGLE_MAPS_API_KEY, DEFAULT_CENTER } from '../../utils/googleMaps';

const MapAddressSelector = ({
  onLocationSelect,
  initialCoordinates = null,
  height = 400,
  disabled = false,
  showCurrentLocationButton = true,
  showConfirmButton = true,
  zoom = 15
}) => {
  const [map, setMap] = useState(null);
  const [marker, setMarker] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(initialCoordinates);
  const [loading, setLoading] = useState(true);
  const [geocoding, setGeocoding] = useState(false);
  const [addressInfo, setAddressInfo] = useState(null);
  const [error, setError] = useState(null);
  
  const mapRef = useRef(null);
  const geocoder = useRef(null);

  useEffect(() => {
    initializeMap();
    return () => {
      if (marker) {
        marker.setMap(null);
      }
    };
  }, []);

  useEffect(() => {
    if (map && selectedLocation) {
      updateMarkerPosition(selectedLocation);
      reverseGeocode(selectedLocation);
    }
  }, [selectedLocation, map]);

  const initializeMap = async () => {
    try {
      if (!window.google || !window.google.maps) {
        throw new Error('Google Maps not loaded');
      }

      geocoder.current = new window.google.maps.Geocoder();

      const center = initialCoordinates || DEFAULT_CENTER;
      
      const mapInstance = new window.google.maps.Map(mapRef.current, {
        center: center,
        zoom: zoom,
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
        gestureHandling: 'cooperative',
        clickableIcons: false
      });

      // Add click listener
      mapInstance.addListener('click', (event) => {
        if (!disabled) {
          const clickedLocation = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng()
          };
          setSelectedLocation(clickedLocation);
        }
      });

      setMap(mapInstance);
      
      // Set initial marker if coordinates provided
      if (initialCoordinates) {
        setSelectedLocation(initialCoordinates);
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Error initializing map:', error);
      setError('Failed to load map. Please check your internet connection.');
      setLoading(false);
    }
  };

  const updateMarkerPosition = (location) => {
    if (!map) return;

    // Remove existing marker
    if (marker) {
      marker.setMap(null);
    }

    // Create new marker
    const newMarker = new window.google.maps.Marker({
      position: location,
      map: map,
      draggable: !disabled,
      animation: window.google.maps.Animation.DROP,
      title: 'Selected Location'
    });

    // Add drag listener
    if (!disabled) {
      newMarker.addListener('dragend', (event) => {
        const draggedLocation = {
          lat: event.latLng.lat(),
          lng: event.latLng.lng()
        };
        setSelectedLocation(draggedLocation);
      });
    }

    setMarker(newMarker);

    // Center map on marker
    map.panTo(location);
  };

  const reverseGeocode = async (location) => {
    if (!geocoder.current) return;

    setGeocoding(true);
    setAddressInfo(null);

    try {
      const results = await new Promise((resolve, reject) => {
        geocoder.current.geocode(
          { location: location },
          (results, status) => {
            if (status === 'OK' && results && results[0]) {
              resolve(results);
            } else {
              reject(new Error(`Geocoding failed: ${status}`));
            }
          }
        );
      });

      const result = results[0];
      const addressData = {
        place_id: result.place_id,
        formatted_address: result.formatted_address,
        coordinates: location,
        address_components: result.address_components,
        types: result.types,
        source: 'map_selection'
      };

      setAddressInfo(addressData);
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      setAddressInfo({
        formatted_address: `${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}`,
        coordinates: location,
        source: 'map_selection',
        error: 'Could not determine address'
      });
    } finally {
      setGeocoding(false);
    }
  };

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      toast.error('Geolocation is not supported by this browser');
      return;
    }

    setLoading(true);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const currentLocation = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
        
        setSelectedLocation(currentLocation);
        
        if (map) {
          map.setCenter(currentLocation);
          map.setZoom(18);
        }
        
        setLoading(false);
        toast.success('Current location detected');
      },
      (error) => {
        console.error('Geolocation error:', error);
        setLoading(false);
        
        let errorMessage = 'Failed to get current location';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out';
            break;
        }
        
        toast.error(errorMessage);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000
      }
    );
  };

  const confirmSelection = () => {
    if (addressInfo && onLocationSelect) {
      onLocationSelect(addressInfo);
      toast.success('Location selected successfully');
    }
  };

  const centerOnMarker = () => {
    if (map && selectedLocation) {
      map.panTo(selectedLocation);
      map.setZoom(18);
    }
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ height }}>
        {error}
        <Button onClick={initializeMap} sx={{ ml: 2 }}>
          Retry
        </Button>
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="body2" color="text.secondary">
          Click on the map to select a precise location
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          {showCurrentLocationButton && (
            <Tooltip title="Use current location">
              <IconButton
                size="small"
                onClick={getCurrentLocation}
                disabled={loading || disabled}
              >
                <MyLocation />
              </IconButton>
            </Tooltip>
          )}
          
          {selectedLocation && (
            <Tooltip title="Center on selected location">
              <IconButton
                size="small"
                onClick={centerOnMarker}
                disabled={disabled}
              >
                <CenterFocusStrong />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      <Paper 
        sx={{ 
          height, 
          position: 'relative',
          overflow: 'hidden',
          borderRadius: 2
        }}
      >
        <div
          ref={mapRef}
          style={{
            width: '100%',
            height: '100%',
            opacity: disabled ? 0.6 : 1
          }}
        />
        
        {loading && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'rgba(255, 255, 255, 0.8)',
              zIndex: 1
            }}
          >
            <CircularProgress />
          </Box>
        )}
      </Paper>

      {/* Address Information */}
      {selectedLocation && (
        <Box sx={{ mt: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <Typography variant="subtitle2">
              Selected Location
            </Typography>
            {geocoding && <CircularProgress size={16} />}
          </Box>
          
          <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
            {addressInfo ? (
              <>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  {addressInfo.formatted_address}
                </Typography>
                
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip
                    label={`${selectedLocation.lat.toFixed(6)}, ${selectedLocation.lng.toFixed(6)}`}
                    size="small"
                    variant="outlined"
                  />
                  {addressInfo.error && (
                    <Chip
                      label="Address unavailable"
                      size="small"
                      color="warning"
                      variant="outlined"
                    />
                  )}
                </Box>
              </>
            ) : (
              <Typography variant="body2" color="text.secondary">
                {geocoding ? 'Getting address...' : 'Click on map to select location'}
              </Typography>
            )}
          </Paper>
          
          {showConfirmButton && addressInfo && (
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                startIcon={<Check />}
                onClick={confirmSelection}
                disabled={disabled || !addressInfo}
              >
                Confirm Location
              </Button>
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};

export default MapAddressSelector;
