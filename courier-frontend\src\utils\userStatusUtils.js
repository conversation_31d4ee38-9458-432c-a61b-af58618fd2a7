import { doc, updateDoc, onSnapshot, serverTimestamp, setDoc, getDoc } from 'firebase/firestore';
import { db } from '../firebase';

/**
 * Updates the user's online status in Firestore
 * @param {string} userId - The user's ID
 * @param {boolean} isOnline - Whether the user is online
 * @returns {Promise<void>}
 */
export const updateUserStatus = async (userId, isOnline) => {
  if (!userId) return;

  try {
    const userStatusRef = doc(db, 'userStatus', userId);
    const userStatusDoc = await getDoc(userStatusRef);

    const statusData = {
      isOnline,
      lastChanged: serverTimestamp(),
    };

    if (!userStatusDoc.exists()) {
      await setDoc(userStatusRef, statusData);
    } else {
      await updateDoc(userStatusRef, statusData);
    }

    console.log(`User status updated: ${userId} is ${isOnline ? 'online' : 'offline'}`);
  } catch (error) {
    console.error('Error updating user status:', error);
  }
};

/**
 * Sets up a listener for the user's online status
 * @param {string} userId - The user's ID
 * @param {Function} onStatusChange - Callback function when status changes
 * @returns {Function} - Unsubscribe function
 */
export const listenToUserStatus = (userId, onStatusChange) => {
  if (!userId) return () => {};

  const userStatusRef = doc(db, 'userStatus', userId);

  return onSnapshot(userStatusRef, (doc) => {
    if (doc.exists()) {
      const data = doc.data();
      onStatusChange({
        isOnline: data.isOnline,
        lastChanged: data.lastChanged?.toDate() || new Date()
      });
    } else {
      onStatusChange({ isOnline: false, lastChanged: new Date() });
    }
  }, (error) => {
    console.error('Error listening to user status:', error);
    onStatusChange({ isOnline: false, lastChanged: new Date() });
  });
};

/**
 * Sets up presence tracking for the current user
 * @param {string} userId - The user's ID
 * @returns {Function} - Cleanup function
 */
export const setupPresenceTracking = (userId) => {
  if (!userId) return () => {};

  console.log(`Setting up presence tracking for user: ${userId}`);

  // Update status to online immediately
  updateUserStatus(userId, true);

  // Set up event listeners for when the user goes offline
  const handleOffline = () => {
    console.log(`User ${userId} went offline`);
    updateUserStatus(userId, false);
  };

  const handleOnline = () => {
    console.log(`User ${userId} came online`);
    updateUserStatus(userId, true);
  };

  // Set up visibility change event to handle tab switching
  const handleVisibilityChange = () => {
    if (document.hidden) {
      console.log(`User ${userId} tab became hidden`);
      updateUserStatus(userId, false);
    } else {
      console.log(`User ${userId} tab became visible`);
      updateUserStatus(userId, true);
    }
  };

  // Set up focus/blur events for better presence detection
  const handleFocus = () => {
    console.log(`User ${userId} focused window`);
    updateUserStatus(userId, true);
  };

  const handleBlur = () => {
    console.log(`User ${userId} blurred window`);
    // Don't immediately set to offline on blur, wait a bit
    setTimeout(() => {
      if (document.hidden) {
        updateUserStatus(userId, false);
      }
    }, 5000); // 5 second delay
  };

  window.addEventListener('offline', handleOffline);
  window.addEventListener('online', handleOnline);
  document.addEventListener('visibilitychange', handleVisibilityChange);
  window.addEventListener('focus', handleFocus);
  window.addEventListener('blur', handleBlur);

  // Set up beforeunload event to mark user as offline when leaving
  const handleBeforeUnload = () => {
    console.log(`User ${userId} is leaving the page`);
    updateUserStatus(userId, false);
  };
  window.addEventListener('beforeunload', handleBeforeUnload);

  // Set up periodic heartbeat to maintain online status
  const heartbeatInterval = setInterval(() => {
    if (!document.hidden) {
      updateUserStatus(userId, true);
    }
  }, 30000); // Update every 30 seconds

  // Return cleanup function
  return () => {
    console.log(`Cleaning up presence tracking for user: ${userId}`);
    updateUserStatus(userId, false);
    window.removeEventListener('offline', handleOffline);
    window.removeEventListener('online', handleOnline);
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    window.removeEventListener('focus', handleFocus);
    window.removeEventListener('blur', handleBlur);
    window.removeEventListener('beforeunload', handleBeforeUnload);
    clearInterval(heartbeatInterval);
  };
};

/**
 * Hook to get the formatted time since the user was last active
 * @param {Date} lastActive - The timestamp when the user was last active
 * @returns {string} - Formatted time string
 */
export const getLastActiveTime = (lastActive) => {
  if (!lastActive) return 'Unknown';

  const now = new Date();
  const diffMs = now - lastActive;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffSec < 60) return 'Just now';
  if (diffMin < 60) return `${diffMin} min ago`;
  if (diffHour < 24) return `${diffHour} hr ago`;
  if (diffDay === 1) return 'Yesterday';
  if (diffDay < 7) return `${diffDay} days ago`;

  return lastActive.toLocaleDateString();
};
