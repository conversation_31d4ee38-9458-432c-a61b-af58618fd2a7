// Address Service for managing address history, favorites, and validation
import { collection, addDoc, getDocs, query, where, orderBy, limit, deleteDoc, doc } from 'firebase/firestore';
import { db } from '../firebase';

class AddressService {
  constructor() {
    this.localStoragePrefix = 'courier_address_';
  }

  // Address History Management
  async saveAddressToHistory(userId, address, addressType = 'delivery') {
    try {
      // Save to Firestore
      const historyRef = collection(db, 'addressHistory');
      await addDoc(historyRef, {
        userId,
        address: address.formatted_address,
        coordinates: address.coordinates,
        place_id: address.place_id,
        addressType,
        timestamp: new Date(),
        source: address.source || 'manual'
      });

      // Also save to localStorage for offline access
      this.saveToLocalStorage(`${addressType}_history`, address, 10);
      
      return true;
    } catch (error) {
      console.error('Error saving address to history:', error);
      // Fallback to localStorage only
      this.saveToLocalStorage(`${addressType}_history`, address, 10);
      return false;
    }
  }

  async getAddressHistory(userId, addressType = 'delivery', limitCount = 10) {
    try {
      // Try to get from Firestore first
      const historyRef = collection(db, 'addressHistory');
      const q = query(
        historyRef,
        where('userId', '==', userId),
        where('addressType', '==', addressType),
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      const history = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return history;
    } catch (error) {
      console.error('Error fetching address history:', error);
      // Fallback to localStorage
      return this.getFromLocalStorage(`${addressType}_history`) || [];
    }
  }

  // Address Favorites Management
  async saveAddressToFavorites(userId, address, name, addressType = 'delivery') {
    try {
      const favoritesRef = collection(db, 'addressFavorites');
      const docRef = await addDoc(favoritesRef, {
        userId,
        name,
        address: address.formatted_address,
        coordinates: address.coordinates,
        place_id: address.place_id,
        addressType,
        timestamp: new Date(),
        source: address.source || 'manual'
      });

      // Also save to localStorage
      const favorite = {
        id: docRef.id,
        name,
        ...address,
        timestamp: new Date().toISOString()
      };
      this.saveToLocalStorage(`${addressType}_favorites`, favorite);
      
      return docRef.id;
    } catch (error) {
      console.error('Error saving address to favorites:', error);
      // Fallback to localStorage only
      const favorite = {
        id: Date.now().toString(),
        name,
        ...address,
        timestamp: new Date().toISOString()
      };
      this.saveToLocalStorage(`${addressType}_favorites`, favorite);
      return favorite.id;
    }
  }

  async getAddressFavorites(userId, addressType = 'delivery') {
    try {
      const favoritesRef = collection(db, 'addressFavorites');
      const q = query(
        favoritesRef,
        where('userId', '==', userId),
        where('addressType', '==', addressType),
        orderBy('timestamp', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const favorites = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return favorites;
    } catch (error) {
      console.error('Error fetching address favorites:', error);
      // Fallback to localStorage
      return this.getFromLocalStorage(`${addressType}_favorites`) || [];
    }
  }

  async removeAddressFromFavorites(favoriteId) {
    try {
      await deleteDoc(doc(db, 'addressFavorites', favoriteId));
      
      // Also remove from localStorage
      ['pickup_favorites', 'delivery_favorites'].forEach(key => {
        const favorites = this.getFromLocalStorage(key) || [];
        const updated = favorites.filter(fav => fav.id !== favoriteId);
        localStorage.setItem(this.localStoragePrefix + key, JSON.stringify(updated));
      });
      
      return true;
    } catch (error) {
      console.error('Error removing address from favorites:', error);
      return false;
    }
  }

  // Address Validation
  async validateAddress(address) {
    try {
      // Basic validation checks
      const validation = {
        isValid: false,
        hasCoordinates: false,
        hasDetailedComponents: false,
        isDeliverable: false,
        confidence: 0,
        issues: []
      };

      // Check if address has coordinates
      if (address.coordinates && address.coordinates.lat && address.coordinates.lng) {
        validation.hasCoordinates = true;
        validation.confidence += 30;
      } else {
        validation.issues.push('No GPS coordinates available');
      }

      // Check if address has detailed components
      if (address.address_components && address.address_components.length > 0) {
        validation.hasDetailedComponents = true;
        validation.confidence += 25;
      } else {
        validation.issues.push('Limited address details');
      }

      // Check if address has place_id (indicates Google Places verification)
      if (address.place_id) {
        validation.confidence += 25;
      } else {
        validation.issues.push('Address not verified by Google Places');
      }

      // Check address completeness
      if (address.formatted_address && address.formatted_address.length > 10) {
        validation.confidence += 20;
      } else {
        validation.issues.push('Address appears incomplete');
      }

      // Determine overall validity
      validation.isValid = validation.confidence >= 50;
      validation.isDeliverable = validation.confidence >= 70;

      return validation;
    } catch (error) {
      console.error('Error validating address:', error);
      return {
        isValid: false,
        hasCoordinates: false,
        hasDetailedComponents: false,
        isDeliverable: false,
        confidence: 0,
        issues: ['Validation service unavailable']
      };
    }
  }

  // Geocoding and Address Enhancement
  async enhanceAddress(address) {
    try {
      if (!window.google || !window.google.maps) {
        throw new Error('Google Maps not available');
      }

      const geocoder = new window.google.maps.Geocoder();
      
      return new Promise((resolve, reject) => {
        const request = address.coordinates 
          ? { location: address.coordinates }
          : { address: address.formatted_address };

        geocoder.geocode(request, (results, status) => {
          if (status === 'OK' && results && results[0]) {
            const result = results[0];
            const enhanced = {
              ...address,
              formatted_address: result.formatted_address,
              place_id: result.place_id,
              address_components: result.address_components,
              types: result.types,
              coordinates: address.coordinates || {
                lat: result.geometry.location.lat(),
                lng: result.geometry.location.lng()
              },
              enhanced: true
            };
            resolve(enhanced);
          } else {
            reject(new Error(`Geocoding failed: ${status}`));
          }
        });
      });
    } catch (error) {
      console.error('Error enhancing address:', error);
      return address; // Return original address if enhancement fails
    }
  }

  // Cost Estimation
  calculateDeliveryCost(distance, packageWeight, priority = 'standard', packageType = 'standard') {
    const basePrices = {
      document: 10,
      parcel: 15,
      fragile: 20,
      food: 18,
      electronics: 25,
      other: 15
    };

    const priorityMultipliers = {
      standard: 1,
      express: 1.5,
      urgent: 2
    };

    const basePrice = basePrices[packageType] || basePrices.other;
    const weightFactor = Math.max(1, packageWeight * 2);
    const distanceFactor = distance * 0.5;
    const priorityMultiplier = priorityMultipliers[priority] || 1;

    const totalCost = (basePrice + weightFactor + distanceFactor) * priorityMultiplier;

    return {
      basePrice,
      weightFactor,
      distanceFactor,
      priorityMultiplier,
      totalCost: Math.round(totalCost * 100) / 100,
      currency: 'INR'
    };
  }

  // Local Storage Helpers
  saveToLocalStorage(key, data, maxItems = 10) {
    try {
      const storageKey = this.localStoragePrefix + key;
      let items = JSON.parse(localStorage.getItem(storageKey) || '[]');
      
      // Remove duplicates based on place_id or formatted_address
      items = items.filter(item => 
        item.place_id !== data.place_id && 
        item.formatted_address !== data.formatted_address
      );
      
      // Add new item to the beginning
      items.unshift(data);
      
      // Limit the number of items
      if (items.length > maxItems) {
        items = items.slice(0, maxItems);
      }
      
      localStorage.setItem(storageKey, JSON.stringify(items));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }

  getFromLocalStorage(key) {
    try {
      const storageKey = this.localStoragePrefix + key;
      return JSON.parse(localStorage.getItem(storageKey) || '[]');
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return [];
    }
  }

  clearLocalStorage(key) {
    try {
      const storageKey = this.localStoragePrefix + key;
      localStorage.removeItem(storageKey);
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }

  // Bulk Address Processing
  async processBulkAddresses(addresses) {
    const results = [];
    
    for (let i = 0; i < addresses.length; i++) {
      try {
        const enhanced = await this.enhanceAddress(addresses[i]);
        const validation = await this.validateAddress(enhanced);
        
        results.push({
          original: addresses[i],
          enhanced,
          validation,
          index: i
        });
        
        // Add small delay to avoid rate limiting
        if (i < addresses.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (error) {
        results.push({
          original: addresses[i],
          enhanced: addresses[i],
          validation: { isValid: false, issues: [error.message] },
          index: i,
          error: error.message
        });
      }
    }
    
    return results;
  }
}

// Export singleton instance
export default new AddressService();
