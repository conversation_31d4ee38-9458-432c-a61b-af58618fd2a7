{"version": 3, "sources": ["../../trim-canvas/build/index.js", "../../@babel/runtime/helpers/esm/objectWithoutProperties.js", "../../@babel/runtime/helpers/esm/createClass.js", "../../@babel/runtime/helpers/esm/classCallCheck.js", "../../@babel/runtime/helpers/esm/inherits.js", "../../@babel/runtime/helpers/esm/getPrototypeOf.js", "../../@babel/runtime/helpers/esm/isNativeReflectConstruct.js", "../../@babel/runtime/helpers/esm/possibleConstructorReturn.js", "../../@babel/runtime/helpers/esm/createSuper.js", "../../signature_pad/dist/signature_pad.mjs", "../../react-signature-canvas/src/index.tsx"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define([],t):\"object\"==typeof exports?exports.trimCanvas=t():e.trimCanvas=t()}(this,function(){return function(e){function t(n){if(r[n])return r[n].exports;var o=r[n]={exports:{},id:n,loaded:!1};return e[n].call(o.exports,o,o.exports,t),o.loaded=!0,o.exports}var r={};return t.m=e,t.c=r,t.p=\"\",t(0)}([function(e,t){\"use strict\";function r(e){var t=e.getContext(\"2d\"),r=e.width,n=e.height,o=t.getImageData(0,0,r,n).data,f=a(!0,r,n,o),i=a(!1,r,n,o),c=u(!0,r,n,o),d=u(!1,r,n,o),p=d-c+1,l=i-f+1,s=t.getImageData(c,f,p,l);return e.width=p,e.height=l,t.clearRect(0,0,p,l),t.putImageData(s,0,0),e}function n(e,t,r,n){return{red:n[4*(r*t+e)],green:n[4*(r*t+e)+1],blue:n[4*(r*t+e)+2],alpha:n[4*(r*t+e)+3]}}function o(e,t,r,o){return n(e,t,r,o).alpha}function a(e,t,r,n){for(var a=e?1:-1,u=e?0:r-1,f=u;e?f<r:f>-1;f+=a)for(var i=0;i<t;i++)if(o(i,f,t,n))return f;return null}function u(e,t,r,n){for(var a=e?1:-1,u=e?0:t-1,f=u;e?f<t:f>-1;f+=a)for(var i=0;i<r;i++)if(o(f,i,t,n))return f;return null}Object.defineProperty(t,\"__esModule\",{value:!0}),t.default=r}])});", "import objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose.js\";\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nexport { _objectWithoutProperties as default };", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nexport { _createClass as default };", "function _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nexport { _classCallCheck as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && setPrototypeOf(t, e);\n}\nexport { _inherits as default };", "function _getPrototypeOf(t) {\n  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, _getPrototypeOf(t);\n}\nexport { _getPrototypeOf as default };", "function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nexport { _isNativeReflectConstruct as default };", "import _typeof from \"./typeof.js\";\nimport assertThisInitialized from \"./assertThisInitialized.js\";\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nexport { _possibleConstructorReturn as default };", "import getPrototypeOf from \"./getPrototypeOf.js\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport possibleConstructorReturn from \"./possibleConstructorReturn.js\";\nfunction _createSuper(t) {\n  var r = isNativeReflectConstruct();\n  return function () {\n    var e,\n      o = getPrototypeOf(t);\n    if (r) {\n      var s = getPrototypeOf(this).constructor;\n      e = Reflect.construct(o, arguments, s);\n    } else e = o.apply(this, arguments);\n    return possibleConstructorReturn(this, e);\n  };\n}\nexport { _createSuper as default };", "/*!\n * Signature Pad v2.3.2\n * https://github.com/szimek/signature_pad\n *\n * Copyright 2017 <PERSON><PERSON><PERSON>\n * Released under the MIT license\n *\n * The main idea and some parts of the code (e.g. drawing variable width B<PERSON><PERSON> curve) are taken from:\n * http://corner.squareup.com/2012/07/smoother-signatures.html\n *\n * Implementation of interpolation using cubic Bézier curves is taken from:\n * http://benknowscode.wordpress.com/2012/09/14/path-interpolation-using-cubic-bezier-and-control-point-estimation-in-javascript\n *\n * Algorithm for approximated length of a Bézier curve is taken from:\n * http://www.lemoda.net/maths/bezier-length/index.html\n *\n */\n\nfunction Point(x, y, time) {\n  this.x = x;\n  this.y = y;\n  this.time = time || new Date().getTime();\n}\n\nPoint.prototype.velocityFrom = function (start) {\n  return this.time !== start.time ? this.distanceTo(start) / (this.time - start.time) : 1;\n};\n\nPoint.prototype.distanceTo = function (start) {\n  return Math.sqrt(Math.pow(this.x - start.x, 2) + Math.pow(this.y - start.y, 2));\n};\n\nPoint.prototype.equals = function (other) {\n  return this.x === other.x && this.y === other.y && this.time === other.time;\n};\n\nfunction Bezier(startPoint, control1, control2, endPoint) {\n  this.startPoint = startPoint;\n  this.control1 = control1;\n  this.control2 = control2;\n  this.endPoint = endPoint;\n}\n\n// Returns approximated length.\nBezier.prototype.length = function () {\n  var steps = 10;\n  var length = 0;\n  var px = void 0;\n  var py = void 0;\n\n  for (var i = 0; i <= steps; i += 1) {\n    var t = i / steps;\n    var cx = this._point(t, this.startPoint.x, this.control1.x, this.control2.x, this.endPoint.x);\n    var cy = this._point(t, this.startPoint.y, this.control1.y, this.control2.y, this.endPoint.y);\n    if (i > 0) {\n      var xdiff = cx - px;\n      var ydiff = cy - py;\n      length += Math.sqrt(xdiff * xdiff + ydiff * ydiff);\n    }\n    px = cx;\n    py = cy;\n  }\n\n  return length;\n};\n\n/* eslint-disable no-multi-spaces, space-in-parens */\nBezier.prototype._point = function (t, start, c1, c2, end) {\n  return start * (1.0 - t) * (1.0 - t) * (1.0 - t) + 3.0 * c1 * (1.0 - t) * (1.0 - t) * t + 3.0 * c2 * (1.0 - t) * t * t + end * t * t * t;\n};\n\n/* eslint-disable */\n\n// http://stackoverflow.com/a/27078401/815507\nfunction throttle(func, wait, options) {\n  var context, args, result;\n  var timeout = null;\n  var previous = 0;\n  if (!options) options = {};\n  var later = function later() {\n    previous = options.leading === false ? 0 : Date.now();\n    timeout = null;\n    result = func.apply(context, args);\n    if (!timeout) context = args = null;\n  };\n  return function () {\n    var now = Date.now();\n    if (!previous && options.leading === false) previous = now;\n    var remaining = wait - (now - previous);\n    context = this;\n    args = arguments;\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n      previous = now;\n      result = func.apply(context, args);\n      if (!timeout) context = args = null;\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(later, remaining);\n    }\n    return result;\n  };\n}\n\nfunction SignaturePad(canvas, options) {\n  var self = this;\n  var opts = options || {};\n\n  this.velocityFilterWeight = opts.velocityFilterWeight || 0.7;\n  this.minWidth = opts.minWidth || 0.5;\n  this.maxWidth = opts.maxWidth || 2.5;\n  this.throttle = 'throttle' in opts ? opts.throttle : 16; // in miliseconds\n  this.minDistance = 'minDistance' in opts ? opts.minDistance : 5;\n\n  if (this.throttle) {\n    this._strokeMoveUpdate = throttle(SignaturePad.prototype._strokeUpdate, this.throttle);\n  } else {\n    this._strokeMoveUpdate = SignaturePad.prototype._strokeUpdate;\n  }\n\n  this.dotSize = opts.dotSize || function () {\n    return (this.minWidth + this.maxWidth) / 2;\n  };\n  this.penColor = opts.penColor || 'black';\n  this.backgroundColor = opts.backgroundColor || 'rgba(0,0,0,0)';\n  this.onBegin = opts.onBegin;\n  this.onEnd = opts.onEnd;\n\n  this._canvas = canvas;\n  this._ctx = canvas.getContext('2d');\n  this.clear();\n\n  // We need add these inline so they are available to unbind while still having\n  // access to 'self' we could use _.bind but it's not worth adding a dependency.\n  this._handleMouseDown = function (event) {\n    if (event.which === 1) {\n      self._mouseButtonDown = true;\n      self._strokeBegin(event);\n    }\n  };\n\n  this._handleMouseMove = function (event) {\n    if (self._mouseButtonDown) {\n      self._strokeMoveUpdate(event);\n    }\n  };\n\n  this._handleMouseUp = function (event) {\n    if (event.which === 1 && self._mouseButtonDown) {\n      self._mouseButtonDown = false;\n      self._strokeEnd(event);\n    }\n  };\n\n  this._handleTouchStart = function (event) {\n    if (event.targetTouches.length === 1) {\n      var touch = event.changedTouches[0];\n      self._strokeBegin(touch);\n    }\n  };\n\n  this._handleTouchMove = function (event) {\n    // Prevent scrolling.\n    event.preventDefault();\n\n    var touch = event.targetTouches[0];\n    self._strokeMoveUpdate(touch);\n  };\n\n  this._handleTouchEnd = function (event) {\n    var wasCanvasTouched = event.target === self._canvas;\n    if (wasCanvasTouched) {\n      event.preventDefault();\n      self._strokeEnd(event);\n    }\n  };\n\n  // Enable mouse and touch event handlers\n  this.on();\n}\n\n// Public methods\nSignaturePad.prototype.clear = function () {\n  var ctx = this._ctx;\n  var canvas = this._canvas;\n\n  ctx.fillStyle = this.backgroundColor;\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n  this._data = [];\n  this._reset();\n  this._isEmpty = true;\n};\n\nSignaturePad.prototype.fromDataURL = function (dataUrl) {\n  var _this = this;\n\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  var image = new Image();\n  var ratio = options.ratio || window.devicePixelRatio || 1;\n  var width = options.width || this._canvas.width / ratio;\n  var height = options.height || this._canvas.height / ratio;\n\n  this._reset();\n  image.src = dataUrl;\n  image.onload = function () {\n    _this._ctx.drawImage(image, 0, 0, width, height);\n  };\n  this._isEmpty = false;\n};\n\nSignaturePad.prototype.toDataURL = function (type) {\n  var _canvas;\n\n  switch (type) {\n    case 'image/svg+xml':\n      return this._toSVG();\n    default:\n      for (var _len = arguments.length, options = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        options[_key - 1] = arguments[_key];\n      }\n\n      return (_canvas = this._canvas).toDataURL.apply(_canvas, [type].concat(options));\n  }\n};\n\nSignaturePad.prototype.on = function () {\n  this._handleMouseEvents();\n  this._handleTouchEvents();\n};\n\nSignaturePad.prototype.off = function () {\n  this._canvas.removeEventListener('mousedown', this._handleMouseDown);\n  this._canvas.removeEventListener('mousemove', this._handleMouseMove);\n  document.removeEventListener('mouseup', this._handleMouseUp);\n\n  this._canvas.removeEventListener('touchstart', this._handleTouchStart);\n  this._canvas.removeEventListener('touchmove', this._handleTouchMove);\n  this._canvas.removeEventListener('touchend', this._handleTouchEnd);\n};\n\nSignaturePad.prototype.isEmpty = function () {\n  return this._isEmpty;\n};\n\n// Private methods\nSignaturePad.prototype._strokeBegin = function (event) {\n  this._data.push([]);\n  this._reset();\n  this._strokeUpdate(event);\n\n  if (typeof this.onBegin === 'function') {\n    this.onBegin(event);\n  }\n};\n\nSignaturePad.prototype._strokeUpdate = function (event) {\n  var x = event.clientX;\n  var y = event.clientY;\n\n  var point = this._createPoint(x, y);\n  var lastPointGroup = this._data[this._data.length - 1];\n  var lastPoint = lastPointGroup && lastPointGroup[lastPointGroup.length - 1];\n  var isLastPointTooClose = lastPoint && point.distanceTo(lastPoint) < this.minDistance;\n\n  // Skip this point if it's too close to the previous one\n  if (!(lastPoint && isLastPointTooClose)) {\n    var _addPoint = this._addPoint(point),\n        curve = _addPoint.curve,\n        widths = _addPoint.widths;\n\n    if (curve && widths) {\n      this._drawCurve(curve, widths.start, widths.end);\n    }\n\n    this._data[this._data.length - 1].push({\n      x: point.x,\n      y: point.y,\n      time: point.time,\n      color: this.penColor\n    });\n  }\n};\n\nSignaturePad.prototype._strokeEnd = function (event) {\n  var canDrawCurve = this.points.length > 2;\n  var point = this.points[0]; // Point instance\n\n  if (!canDrawCurve && point) {\n    this._drawDot(point);\n  }\n\n  if (point) {\n    var lastPointGroup = this._data[this._data.length - 1];\n    var lastPoint = lastPointGroup[lastPointGroup.length - 1]; // plain object\n\n    // When drawing a dot, there's only one point in a group, so without this check\n    // such group would end up with exactly the same 2 points.\n    if (!point.equals(lastPoint)) {\n      lastPointGroup.push({\n        x: point.x,\n        y: point.y,\n        time: point.time,\n        color: this.penColor\n      });\n    }\n  }\n\n  if (typeof this.onEnd === 'function') {\n    this.onEnd(event);\n  }\n};\n\nSignaturePad.prototype._handleMouseEvents = function () {\n  this._mouseButtonDown = false;\n\n  this._canvas.addEventListener('mousedown', this._handleMouseDown);\n  this._canvas.addEventListener('mousemove', this._handleMouseMove);\n  document.addEventListener('mouseup', this._handleMouseUp);\n};\n\nSignaturePad.prototype._handleTouchEvents = function () {\n  // Pass touch events to canvas element on mobile IE11 and Edge.\n  this._canvas.style.msTouchAction = 'none';\n  this._canvas.style.touchAction = 'none';\n\n  this._canvas.addEventListener('touchstart', this._handleTouchStart);\n  this._canvas.addEventListener('touchmove', this._handleTouchMove);\n  this._canvas.addEventListener('touchend', this._handleTouchEnd);\n};\n\nSignaturePad.prototype._reset = function () {\n  this.points = [];\n  this._lastVelocity = 0;\n  this._lastWidth = (this.minWidth + this.maxWidth) / 2;\n  this._ctx.fillStyle = this.penColor;\n};\n\nSignaturePad.prototype._createPoint = function (x, y, time) {\n  var rect = this._canvas.getBoundingClientRect();\n\n  return new Point(x - rect.left, y - rect.top, time || new Date().getTime());\n};\n\nSignaturePad.prototype._addPoint = function (point) {\n  var points = this.points;\n  var tmp = void 0;\n\n  points.push(point);\n\n  if (points.length > 2) {\n    // To reduce the initial lag make it work with 3 points\n    // by copying the first point to the beginning.\n    if (points.length === 3) points.unshift(points[0]);\n\n    tmp = this._calculateCurveControlPoints(points[0], points[1], points[2]);\n    var c2 = tmp.c2;\n    tmp = this._calculateCurveControlPoints(points[1], points[2], points[3]);\n    var c3 = tmp.c1;\n    var curve = new Bezier(points[1], c2, c3, points[2]);\n    var widths = this._calculateCurveWidths(curve);\n\n    // Remove the first element from the list,\n    // so that we always have no more than 4 points in points array.\n    points.shift();\n\n    return { curve: curve, widths: widths };\n  }\n\n  return {};\n};\n\nSignaturePad.prototype._calculateCurveControlPoints = function (s1, s2, s3) {\n  var dx1 = s1.x - s2.x;\n  var dy1 = s1.y - s2.y;\n  var dx2 = s2.x - s3.x;\n  var dy2 = s2.y - s3.y;\n\n  var m1 = { x: (s1.x + s2.x) / 2.0, y: (s1.y + s2.y) / 2.0 };\n  var m2 = { x: (s2.x + s3.x) / 2.0, y: (s2.y + s3.y) / 2.0 };\n\n  var l1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n  var l2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);\n\n  var dxm = m1.x - m2.x;\n  var dym = m1.y - m2.y;\n\n  var k = l2 / (l1 + l2);\n  var cm = { x: m2.x + dxm * k, y: m2.y + dym * k };\n\n  var tx = s2.x - cm.x;\n  var ty = s2.y - cm.y;\n\n  return {\n    c1: new Point(m1.x + tx, m1.y + ty),\n    c2: new Point(m2.x + tx, m2.y + ty)\n  };\n};\n\nSignaturePad.prototype._calculateCurveWidths = function (curve) {\n  var startPoint = curve.startPoint;\n  var endPoint = curve.endPoint;\n  var widths = { start: null, end: null };\n\n  var velocity = this.velocityFilterWeight * endPoint.velocityFrom(startPoint) + (1 - this.velocityFilterWeight) * this._lastVelocity;\n\n  var newWidth = this._strokeWidth(velocity);\n\n  widths.start = this._lastWidth;\n  widths.end = newWidth;\n\n  this._lastVelocity = velocity;\n  this._lastWidth = newWidth;\n\n  return widths;\n};\n\nSignaturePad.prototype._strokeWidth = function (velocity) {\n  return Math.max(this.maxWidth / (velocity + 1), this.minWidth);\n};\n\nSignaturePad.prototype._drawPoint = function (x, y, size) {\n  var ctx = this._ctx;\n\n  ctx.moveTo(x, y);\n  ctx.arc(x, y, size, 0, 2 * Math.PI, false);\n  this._isEmpty = false;\n};\n\nSignaturePad.prototype._drawCurve = function (curve, startWidth, endWidth) {\n  var ctx = this._ctx;\n  var widthDelta = endWidth - startWidth;\n  var drawSteps = Math.floor(curve.length());\n\n  ctx.beginPath();\n\n  for (var i = 0; i < drawSteps; i += 1) {\n    // Calculate the Bezier (x, y) coordinate for this step.\n    var t = i / drawSteps;\n    var tt = t * t;\n    var ttt = tt * t;\n    var u = 1 - t;\n    var uu = u * u;\n    var uuu = uu * u;\n\n    var x = uuu * curve.startPoint.x;\n    x += 3 * uu * t * curve.control1.x;\n    x += 3 * u * tt * curve.control2.x;\n    x += ttt * curve.endPoint.x;\n\n    var y = uuu * curve.startPoint.y;\n    y += 3 * uu * t * curve.control1.y;\n    y += 3 * u * tt * curve.control2.y;\n    y += ttt * curve.endPoint.y;\n\n    var width = startWidth + ttt * widthDelta;\n    this._drawPoint(x, y, width);\n  }\n\n  ctx.closePath();\n  ctx.fill();\n};\n\nSignaturePad.prototype._drawDot = function (point) {\n  var ctx = this._ctx;\n  var width = typeof this.dotSize === 'function' ? this.dotSize() : this.dotSize;\n\n  ctx.beginPath();\n  this._drawPoint(point.x, point.y, width);\n  ctx.closePath();\n  ctx.fill();\n};\n\nSignaturePad.prototype._fromData = function (pointGroups, drawCurve, drawDot) {\n  for (var i = 0; i < pointGroups.length; i += 1) {\n    var group = pointGroups[i];\n\n    if (group.length > 1) {\n      for (var j = 0; j < group.length; j += 1) {\n        var rawPoint = group[j];\n        var point = new Point(rawPoint.x, rawPoint.y, rawPoint.time);\n        var color = rawPoint.color;\n\n        if (j === 0) {\n          // First point in a group. Nothing to draw yet.\n\n          // All points in the group have the same color, so it's enough to set\n          // penColor just at the beginning.\n          this.penColor = color;\n          this._reset();\n\n          this._addPoint(point);\n        } else if (j !== group.length - 1) {\n          // Middle point in a group.\n          var _addPoint2 = this._addPoint(point),\n              curve = _addPoint2.curve,\n              widths = _addPoint2.widths;\n\n          if (curve && widths) {\n            drawCurve(curve, widths, color);\n          }\n        } else {\n          // Last point in a group. Do nothing.\n        }\n      }\n    } else {\n      this._reset();\n      var _rawPoint = group[0];\n      drawDot(_rawPoint);\n    }\n  }\n};\n\nSignaturePad.prototype._toSVG = function () {\n  var _this2 = this;\n\n  var pointGroups = this._data;\n  var canvas = this._canvas;\n  var ratio = Math.max(window.devicePixelRatio || 1, 1);\n  var minX = 0;\n  var minY = 0;\n  var maxX = canvas.width / ratio;\n  var maxY = canvas.height / ratio;\n  var svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n\n  svg.setAttributeNS(null, 'width', canvas.width);\n  svg.setAttributeNS(null, 'height', canvas.height);\n\n  this._fromData(pointGroups, function (curve, widths, color) {\n    var path = document.createElement('path');\n\n    // Need to check curve for NaN values, these pop up when drawing\n    // lines on the canvas that are not continuous. E.g. Sharp corners\n    // or stopping mid-stroke and than continuing without lifting mouse.\n    if (!isNaN(curve.control1.x) && !isNaN(curve.control1.y) && !isNaN(curve.control2.x) && !isNaN(curve.control2.y)) {\n      var attr = 'M ' + curve.startPoint.x.toFixed(3) + ',' + curve.startPoint.y.toFixed(3) + ' ' + ('C ' + curve.control1.x.toFixed(3) + ',' + curve.control1.y.toFixed(3) + ' ') + (curve.control2.x.toFixed(3) + ',' + curve.control2.y.toFixed(3) + ' ') + (curve.endPoint.x.toFixed(3) + ',' + curve.endPoint.y.toFixed(3));\n\n      path.setAttribute('d', attr);\n      path.setAttribute('stroke-width', (widths.end * 2.25).toFixed(3));\n      path.setAttribute('stroke', color);\n      path.setAttribute('fill', 'none');\n      path.setAttribute('stroke-linecap', 'round');\n\n      svg.appendChild(path);\n    }\n  }, function (rawPoint) {\n    var circle = document.createElement('circle');\n    var dotSize = typeof _this2.dotSize === 'function' ? _this2.dotSize() : _this2.dotSize;\n    circle.setAttribute('r', dotSize);\n    circle.setAttribute('cx', rawPoint.x);\n    circle.setAttribute('cy', rawPoint.y);\n    circle.setAttribute('fill', rawPoint.color);\n\n    svg.appendChild(circle);\n  });\n\n  var prefix = 'data:image/svg+xml;base64,';\n  var header = '<svg' + ' xmlns=\"http://www.w3.org/2000/svg\"' + ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"' + (' viewBox=\"' + minX + ' ' + minY + ' ' + maxX + ' ' + maxY + '\"') + (' width=\"' + maxX + '\"') + (' height=\"' + maxY + '\"') + '>';\n  var body = svg.innerHTML;\n\n  // IE hack for missing innerHTML property on SVGElement\n  if (body === undefined) {\n    var dummy = document.createElement('dummy');\n    var nodes = svg.childNodes;\n    dummy.innerHTML = '';\n\n    for (var i = 0; i < nodes.length; i += 1) {\n      dummy.appendChild(nodes[i].cloneNode(true));\n    }\n\n    body = dummy.innerHTML;\n  }\n\n  var footer = '</svg>';\n  var data = header + body + footer;\n\n  return prefix + btoa(data);\n};\n\nSignaturePad.prototype.fromData = function (pointGroups) {\n  var _this3 = this;\n\n  this.clear();\n\n  this._fromData(pointGroups, function (curve, widths) {\n    return _this3._drawCurve(curve, widths.start, widths.end);\n  }, function (rawPoint) {\n    return _this3._drawDot(rawPoint);\n  });\n\n  this._data = pointGroups;\n};\n\nSignaturePad.prototype.toData = function () {\n  return this._data;\n};\n\nexport default SignaturePad;\n", "import PropTypes from 'prop-types'\nimport React, { Component } from 'react'\nimport SignaturePad from 'signature_pad'\nimport trimCanvas from 'trim-canvas'\n\nexport interface SignatureCanvasProps extends SignaturePad.SignaturePadOptions {\n  canvasProps?: React.CanvasHTMLAttributes<HTMLCanvasElement>\n  clearOnResize?: boolean\n}\n\nexport class SignatureCanvas extends Component<SignatureCanvasProps> {\n  static override propTypes = {\n    // signature_pad's props\n    velocityFilterWeight: PropTypes.number,\n    minWidth: PropTypes.number,\n    maxWidth: PropTypes.number,\n    minDistance: PropTypes.number,\n    dotSize: PropTypes.oneOfType([PropTypes.number, PropTypes.func]),\n    penColor: PropTypes.string,\n    throttle: PropTypes.number,\n    onEnd: PropTypes.func,\n    onBegin: PropTypes.func,\n    // props specific to the React wrapper\n    canvasProps: PropTypes.object,\n    clearOnResize: PropTypes.bool\n  }\n\n  static defaultProps: Pick<SignatureCanvasProps, 'clearOnResize'> = {\n    clearOnResize: true\n  }\n\n  static refNullError = new Error('react-signature-canvas is currently ' +\n    'mounting or unmounting: React refs are null during this phase.')\n\n  // shortcut reference (https://stackoverflow.com/a/29244254/3431180)\n  private readonly staticThis = this.constructor as typeof SignatureCanvas\n\n  _sigPad: SignaturePad | null = null\n  _canvas: HTMLCanvasElement | null = null\n\n  private readonly setRef = (ref: HTMLCanvasElement | null): void => {\n    this._canvas = ref\n    // if component is unmounted, set internal references to null\n    if (this._canvas === null) {\n      this._sigPad = null\n    }\n  }\n\n  _excludeOurProps = (): SignaturePad.SignaturePadOptions => {\n    const { canvasProps, clearOnResize, ...sigPadProps } = this.props\n    return sigPadProps\n  }\n\n  override componentDidMount: Component['componentDidMount'] = () => {\n    const canvas = this.getCanvas()\n    this._sigPad = new SignaturePad(canvas, this._excludeOurProps())\n    this._resizeCanvas()\n    this.on()\n  }\n\n  override componentWillUnmount: Component['componentWillUnmount'] = () => {\n    this.off()\n  }\n\n  // propagate prop updates to SignaturePad\n  override componentDidUpdate: Component['componentDidUpdate'] = () => {\n    Object.assign(this._sigPad, this._excludeOurProps())\n  }\n\n  // return the canvas ref for operations like toDataURL\n  getCanvas = (): HTMLCanvasElement => {\n    if (this._canvas === null) {\n      throw this.staticThis.refNullError\n    }\n    return this._canvas\n  }\n\n  // return a trimmed copy of the canvas\n  getTrimmedCanvas = (): HTMLCanvasElement => {\n    // copy the canvas\n    const canvas = this.getCanvas()\n    const copy = document.createElement('canvas')\n    copy.width = canvas.width\n    copy.height = canvas.height\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    copy.getContext('2d')!.drawImage(canvas, 0, 0)\n    // then trim it\n    return trimCanvas(copy)\n  }\n\n  // return the internal SignaturePad reference\n  getSignaturePad = (): SignaturePad => {\n    if (this._sigPad === null) {\n      throw this.staticThis.refNullError\n    }\n    return this._sigPad\n  }\n\n  _checkClearOnResize = (): void => {\n    if (!this.props.clearOnResize) { // eslint-disable-line @typescript-eslint/strict-boolean-expressions -- this is backward compatible with the previous behavior, where null was treated as falsey\n      return\n    }\n    this._resizeCanvas()\n  }\n\n  _resizeCanvas = (): void => {\n    const canvasProps = this.props.canvasProps ?? {}\n    const { width, height } = canvasProps\n    // don't resize if the canvas has fixed width and height\n    if (typeof width !== 'undefined' && typeof height !== 'undefined') {\n      return\n    }\n\n    const canvas = this.getCanvas()\n    /* When zoomed out to less than 100%, for some very strange reason,\n      some browsers report devicePixelRatio as less than 1\n      and only part of the canvas is cleared then. */\n    const ratio = Math.max(window.devicePixelRatio ?? 1, 1)\n\n    if (typeof width === 'undefined') {\n      canvas.width = canvas.offsetWidth * ratio\n    }\n    if (typeof height === 'undefined') {\n      canvas.height = canvas.offsetHeight * ratio\n    }\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    canvas.getContext('2d')!.scale(ratio, ratio)\n    this.clear()\n  }\n\n  override render: Component['render'] = () => {\n    const { canvasProps } = this.props\n    return <canvas ref={this.setRef} {...canvasProps} />\n  }\n\n  // all wrapper functions below render\n  //\n  on: SignaturePad['on'] = () => {\n    window.addEventListener('resize', this._checkClearOnResize)\n    return this.getSignaturePad().on()\n  }\n\n  off: SignaturePad['off'] = () => {\n    window.removeEventListener('resize', this._checkClearOnResize)\n    return this.getSignaturePad().off()\n  }\n\n  clear: SignaturePad['clear'] = () => {\n    return this.getSignaturePad().clear()\n  }\n\n  isEmpty: SignaturePad['isEmpty'] = () => {\n    return this.getSignaturePad().isEmpty()\n  }\n\n  fromDataURL: SignaturePad['fromDataURL'] = (dataURL, options) => {\n    return this.getSignaturePad().fromDataURL(dataURL, options)\n  }\n\n  toDataURL: SignaturePad['toDataURL'] = (type, encoderOptions) => {\n    return this.getSignaturePad().toDataURL(type, encoderOptions)\n  }\n\n  fromData: SignaturePad['fromData'] = (pointGroups) => {\n    return this.getSignaturePad().fromData(pointGroups)\n  }\n\n  toData: SignaturePad['toData'] = () => {\n    return this.getSignaturePad().toData()\n  }\n}\n\nexport default SignatureCanvas\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,YAAU,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,GAAE,CAAC,IAAE,YAAU,OAAO,UAAQ,QAAQ,aAAW,EAAE,IAAE,EAAE,aAAW,EAAE;AAAA,IAAC,EAAE,SAAK,WAAU;AAAC,aAAO,SAAS,GAAE;AAAC,iBAAS,EAAE,GAAE;AAAC,cAAG,EAAE,CAAC,EAAE,QAAO,EAAE,CAAC,EAAE;AAAQ,cAAI,IAAE,EAAE,CAAC,IAAE,EAAC,SAAQ,CAAC,GAAE,IAAG,GAAE,QAAO,MAAE;AAAE,iBAAO,EAAE,CAAC,EAAE,KAAK,EAAE,SAAQ,GAAE,EAAE,SAAQ,CAAC,GAAE,EAAE,SAAO,MAAG,EAAE;AAAA,QAAO;AAAC,YAAI,IAAE,CAAC;AAAE,eAAO,EAAE,IAAE,GAAE,EAAE,IAAE,GAAE,EAAE,IAAE,IAAG,EAAE,CAAC;AAAA,MAAC,EAAE,CAAC,SAAS,GAAE,GAAE;AAAC;AAAa,iBAAS,EAAEA,IAAE;AAAC,cAAIC,KAAED,GAAE,WAAW,IAAI,GAAEE,KAAEF,GAAE,OAAMG,KAAEH,GAAE,QAAOI,KAAEH,GAAE,aAAa,GAAE,GAAEC,IAAEC,EAAC,EAAE,MAAK,IAAE,EAAE,MAAGD,IAAEC,IAAEC,EAAC,GAAE,IAAE,EAAE,OAAGF,IAAEC,IAAEC,EAAC,GAAE,IAAE,EAAE,MAAGF,IAAEC,IAAEC,EAAC,GAAE,IAAE,EAAE,OAAGF,IAAEC,IAAEC,EAAC,GAAE,IAAE,IAAE,IAAE,GAAE,IAAE,IAAE,IAAE,GAAE,IAAEH,GAAE,aAAa,GAAE,GAAE,GAAE,CAAC;AAAE,iBAAOD,GAAE,QAAM,GAAEA,GAAE,SAAO,GAAEC,GAAE,UAAU,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,aAAa,GAAE,GAAE,CAAC,GAAED;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,iBAAM,EAAC,KAAIA,GAAE,KAAGD,KAAED,KAAED,GAAE,GAAE,OAAMG,GAAE,KAAGD,KAAED,KAAED,MAAG,CAAC,GAAE,MAAKG,GAAE,KAAGD,KAAED,KAAED,MAAG,CAAC,GAAE,OAAMG,GAAE,KAAGD,KAAED,KAAED,MAAG,CAAC,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAEC,IAAEE,IAAE;AAAC,iBAAO,EAAEJ,IAAEC,IAAEC,IAAEE,EAAC,EAAE;AAAA,QAAK;AAAC,iBAAS,EAAEJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAQE,KAAEL,KAAE,IAAE,IAAGM,KAAEN,KAAE,IAAEE,KAAE,GAAE,IAAEI,IAAEN,KAAE,IAAEE,KAAE,IAAE,IAAG,KAAGG,GAAE,UAAQ,IAAE,GAAE,IAAEJ,IAAE,IAAI,KAAG,EAAE,GAAE,GAAEA,IAAEE,EAAC,EAAE,QAAO;AAAE,iBAAO;AAAA,QAAI;AAAC,iBAAS,EAAEH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAQE,KAAEL,KAAE,IAAE,IAAGM,KAAEN,KAAE,IAAEC,KAAE,GAAE,IAAEK,IAAEN,KAAE,IAAEC,KAAE,IAAE,IAAG,KAAGI,GAAE,UAAQ,IAAE,GAAE,IAAEH,IAAE,IAAI,KAAG,EAAE,GAAE,GAAED,IAAEE,EAAC,EAAE,QAAO;AAAE,iBAAO;AAAA,QAAI;AAAC,eAAO,eAAe,GAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAE,EAAE,UAAQ;AAAA,MAAC,CAAC,CAAC;AAAA,IAAC,CAAC;AAAA;AAAA;;;;;;ACAnpC;AACA,SAAS,yBAAyB,GAAG,GAAG;AACtC,MAAI,QAAQ,EAAG,QAAO,CAAC;AACvB,MAAI,GACF,GACA,IAAI,8BAA6B,GAAG,CAAC;AACvC,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,SAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,KAAI,EAAE,CAAC,GAAG,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,qBAAqB,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EACnH;AACA,SAAO;AACT;;;ACXA;AACA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,IAAI,EAAE,CAAC;AACX,MAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,cAAc,EAAE,GAAG,GAAG,CAAC;AAAA,EAC7I;AACF;AACA,SAAS,aAAa,GAAG,GAAG,GAAG;AAC7B,SAAO,KAAK,kBAAkB,EAAE,WAAW,CAAC,GAAG,KAAK,kBAAkB,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa;AAAA,IACjH,UAAU;AAAA,EACZ,CAAC,GAAG;AACN;;;ACXA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,MAAI,EAAE,aAAa,GAAI,OAAM,IAAI,UAAU,mCAAmC;AAChF;;;ACDA,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,cAAc,OAAO,KAAK,SAAS,EAAG,OAAM,IAAI,UAAU,oDAAoD;AAClH,IAAE,YAAY,OAAO,OAAO,KAAK,EAAE,WAAW;AAAA,IAC5C,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa;AAAA,IACxC,UAAU;AAAA,EACZ,CAAC,GAAG,KAAK,gBAAe,GAAG,CAAC;AAC9B;;;ACZA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUI,IAAG;AAC3F,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAC/C,GAAG,gBAAgB,CAAC;AACtB;;;ACJA,SAAS,4BAA4B;AACnC,MAAI;AACF,QAAI,IAAI,CAAC,QAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAY;AAAA,IAAC,CAAC,CAAC;AAAA,EACxF,SAASC,IAAG;AAAA,EAAC;AACb,UAAQ,4BAA4B,SAASC,6BAA4B;AACvE,WAAO,CAAC,CAAC;AAAA,EACX,GAAG;AACL;;;ACPA;AAEA,SAAS,2BAA2B,GAAG,GAAG;AACxC,MAAI,MAAM,YAAY,QAAQ,CAAC,KAAK,cAAc,OAAO,GAAI,QAAO;AACpE,MAAI,WAAW,EAAG,OAAM,IAAI,UAAU,0DAA0D;AAChG,SAAO,uBAAsB,CAAC;AAChC;;;ACHA,SAAS,aAAa,GAAG;AACvB,MAAI,IAAI,0BAAyB;AACjC,SAAO,WAAY;AACjB,QAAI,GACF,IAAI,gBAAe,CAAC;AACtB,QAAI,GAAG;AACL,UAAI,IAAI,gBAAe,IAAI,EAAE;AAC7B,UAAI,QAAQ,UAAU,GAAG,WAAW,CAAC;AAAA,IACvC,MAAO,KAAI,EAAE,MAAM,MAAM,SAAS;AAClC,WAAO,2BAA0B,MAAM,CAAC;AAAA,EAC1C;AACF;;;;;;;ACIA,SAAS,MAAM,GAAG,GAAG,MAAM;AACzB,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,OAAO,SAAQ,oBAAI,KAAK,GAAE,QAAQ;AACzC;AAEA,MAAM,UAAU,eAAe,SAAU,OAAO;AAC9C,SAAO,KAAK,SAAS,MAAM,OAAO,KAAK,WAAW,KAAK,KAAK,KAAK,OAAO,MAAM,QAAQ;AACxF;AAEA,MAAM,UAAU,aAAa,SAAU,OAAO;AAC5C,SAAO,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,CAAC;AAChF;AAEA,MAAM,UAAU,SAAS,SAAU,OAAO;AACxC,SAAO,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,SAAS,MAAM;AACzE;AAEA,SAAS,OAAO,YAAY,UAAU,UAAU,UAAU;AACxD,OAAK,aAAa;AAClB,OAAK,WAAW;AAChB,OAAK,WAAW;AAChB,OAAK,WAAW;AAClB;AAGA,OAAO,UAAU,SAAS,WAAY;AACpC,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,MAAI,KAAK;AACT,MAAI,KAAK;AAET,WAAS,IAAI,GAAG,KAAK,OAAO,KAAK,GAAG;AAClC,QAAI,IAAI,IAAI;AACZ,QAAI,KAAK,KAAK,OAAO,GAAG,KAAK,WAAW,GAAG,KAAK,SAAS,GAAG,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC;AAC5F,QAAI,KAAK,KAAK,OAAO,GAAG,KAAK,WAAW,GAAG,KAAK,SAAS,GAAG,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC;AAC5F,QAAI,IAAI,GAAG;AACT,UAAI,QAAQ,KAAK;AACjB,UAAI,QAAQ,KAAK;AACjB,gBAAU,KAAK,KAAK,QAAQ,QAAQ,QAAQ,KAAK;AAAA,IACnD;AACA,SAAK;AACL,SAAK;AAAA,EACP;AAEA,SAAO;AACT;AAGA,OAAO,UAAU,SAAS,SAAU,GAAG,OAAO,IAAI,IAAI,KAAK;AACzD,SAAO,SAAS,IAAM,MAAM,IAAM,MAAM,IAAM,KAAK,IAAM,MAAM,IAAM,MAAM,IAAM,KAAK,IAAI,IAAM,MAAM,IAAM,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI;AACzI;AAKA,SAAS,SAAS,MAAM,MAAM,SAAS;AACrC,MAAI,SAAS,MAAM;AACnB,MAAI,UAAU;AACd,MAAI,WAAW;AACf,MAAI,CAAC,QAAS,WAAU,CAAC;AACzB,MAAI,QAAQ,SAASC,SAAQ;AAC3B,eAAW,QAAQ,YAAY,QAAQ,IAAI,KAAK,IAAI;AACpD,cAAU;AACV,aAAS,KAAK,MAAM,SAAS,IAAI;AACjC,QAAI,CAAC,QAAS,WAAU,OAAO;AAAA,EACjC;AACA,SAAO,WAAY;AACjB,QAAI,MAAM,KAAK,IAAI;AACnB,QAAI,CAAC,YAAY,QAAQ,YAAY,MAAO,YAAW;AACvD,QAAI,YAAY,QAAQ,MAAM;AAC9B,cAAU;AACV,WAAO;AACP,QAAI,aAAa,KAAK,YAAY,MAAM;AACtC,UAAI,SAAS;AACX,qBAAa,OAAO;AACpB,kBAAU;AAAA,MACZ;AACA,iBAAW;AACX,eAAS,KAAK,MAAM,SAAS,IAAI;AACjC,UAAI,CAAC,QAAS,WAAU,OAAO;AAAA,IACjC,WAAW,CAAC,WAAW,QAAQ,aAAa,OAAO;AACjD,gBAAU,WAAW,OAAO,SAAS;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,QAAQ,SAAS;AACrC,MAAI,OAAO;AACX,MAAI,OAAO,WAAW,CAAC;AAEvB,OAAK,uBAAuB,KAAK,wBAAwB;AACzD,OAAK,WAAW,KAAK,YAAY;AACjC,OAAK,WAAW,KAAK,YAAY;AACjC,OAAK,WAAW,cAAc,OAAO,KAAK,WAAW;AACrD,OAAK,cAAc,iBAAiB,OAAO,KAAK,cAAc;AAE9D,MAAI,KAAK,UAAU;AACjB,SAAK,oBAAoB,SAAS,aAAa,UAAU,eAAe,KAAK,QAAQ;AAAA,EACvF,OAAO;AACL,SAAK,oBAAoB,aAAa,UAAU;AAAA,EAClD;AAEA,OAAK,UAAU,KAAK,WAAW,WAAY;AACzC,YAAQ,KAAK,WAAW,KAAK,YAAY;AAAA,EAC3C;AACA,OAAK,WAAW,KAAK,YAAY;AACjC,OAAK,kBAAkB,KAAK,mBAAmB;AAC/C,OAAK,UAAU,KAAK;AACpB,OAAK,QAAQ,KAAK;AAElB,OAAK,UAAU;AACf,OAAK,OAAO,OAAO,WAAW,IAAI;AAClC,OAAK,MAAM;AAIX,OAAK,mBAAmB,SAAU,OAAO;AACvC,QAAI,MAAM,UAAU,GAAG;AACrB,WAAK,mBAAmB;AACxB,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAEA,OAAK,mBAAmB,SAAU,OAAO;AACvC,QAAI,KAAK,kBAAkB;AACzB,WAAK,kBAAkB,KAAK;AAAA,IAC9B;AAAA,EACF;AAEA,OAAK,iBAAiB,SAAU,OAAO;AACrC,QAAI,MAAM,UAAU,KAAK,KAAK,kBAAkB;AAC9C,WAAK,mBAAmB;AACxB,WAAK,WAAW,KAAK;AAAA,IACvB;AAAA,EACF;AAEA,OAAK,oBAAoB,SAAU,OAAO;AACxC,QAAI,MAAM,cAAc,WAAW,GAAG;AACpC,UAAI,QAAQ,MAAM,eAAe,CAAC;AAClC,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAEA,OAAK,mBAAmB,SAAU,OAAO;AAEvC,UAAM,eAAe;AAErB,QAAI,QAAQ,MAAM,cAAc,CAAC;AACjC,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAEA,OAAK,kBAAkB,SAAU,OAAO;AACtC,QAAI,mBAAmB,MAAM,WAAW,KAAK;AAC7C,QAAI,kBAAkB;AACpB,YAAM,eAAe;AACrB,WAAK,WAAW,KAAK;AAAA,IACvB;AAAA,EACF;AAGA,OAAK,GAAG;AACV;AAGA,aAAa,UAAU,QAAQ,WAAY;AACzC,MAAI,MAAM,KAAK;AACf,MAAI,SAAS,KAAK;AAElB,MAAI,YAAY,KAAK;AACrB,MAAI,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC/C,MAAI,SAAS,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAE9C,OAAK,QAAQ,CAAC;AACd,OAAK,OAAO;AACZ,OAAK,WAAW;AAClB;AAEA,aAAa,UAAU,cAAc,SAAU,SAAS;AACtD,MAAI,QAAQ;AAEZ,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,MAAI,QAAQ,IAAI,MAAM;AACtB,MAAI,QAAQ,QAAQ,SAAS,OAAO,oBAAoB;AACxD,MAAI,QAAQ,QAAQ,SAAS,KAAK,QAAQ,QAAQ;AAClD,MAAI,SAAS,QAAQ,UAAU,KAAK,QAAQ,SAAS;AAErD,OAAK,OAAO;AACZ,QAAM,MAAM;AACZ,QAAM,SAAS,WAAY;AACzB,UAAM,KAAK,UAAU,OAAO,GAAG,GAAG,OAAO,MAAM;AAAA,EACjD;AACA,OAAK,WAAW;AAClB;AAEA,aAAa,UAAU,YAAY,SAAU,MAAM;AACjD,MAAI;AAEJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,KAAK,OAAO;AAAA,IACrB;AACE,eAAS,OAAO,UAAU,QAAQ,UAAU,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACzG,gBAAQ,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,MACpC;AAEA,cAAQ,UAAU,KAAK,SAAS,UAAU,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,OAAO,CAAC;AAAA,EACnF;AACF;AAEA,aAAa,UAAU,KAAK,WAAY;AACtC,OAAK,mBAAmB;AACxB,OAAK,mBAAmB;AAC1B;AAEA,aAAa,UAAU,MAAM,WAAY;AACvC,OAAK,QAAQ,oBAAoB,aAAa,KAAK,gBAAgB;AACnE,OAAK,QAAQ,oBAAoB,aAAa,KAAK,gBAAgB;AACnE,WAAS,oBAAoB,WAAW,KAAK,cAAc;AAE3D,OAAK,QAAQ,oBAAoB,cAAc,KAAK,iBAAiB;AACrE,OAAK,QAAQ,oBAAoB,aAAa,KAAK,gBAAgB;AACnE,OAAK,QAAQ,oBAAoB,YAAY,KAAK,eAAe;AACnE;AAEA,aAAa,UAAU,UAAU,WAAY;AAC3C,SAAO,KAAK;AACd;AAGA,aAAa,UAAU,eAAe,SAAU,OAAO;AACrD,OAAK,MAAM,KAAK,CAAC,CAAC;AAClB,OAAK,OAAO;AACZ,OAAK,cAAc,KAAK;AAExB,MAAI,OAAO,KAAK,YAAY,YAAY;AACtC,SAAK,QAAQ,KAAK;AAAA,EACpB;AACF;AAEA,aAAa,UAAU,gBAAgB,SAAU,OAAO;AACtD,MAAI,IAAI,MAAM;AACd,MAAI,IAAI,MAAM;AAEd,MAAI,QAAQ,KAAK,aAAa,GAAG,CAAC;AAClC,MAAI,iBAAiB,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACrD,MAAI,YAAY,kBAAkB,eAAe,eAAe,SAAS,CAAC;AAC1E,MAAI,sBAAsB,aAAa,MAAM,WAAW,SAAS,IAAI,KAAK;AAG1E,MAAI,EAAE,aAAa,sBAAsB;AACvC,QAAI,YAAY,KAAK,UAAU,KAAK,GAChC,QAAQ,UAAU,OAClB,SAAS,UAAU;AAEvB,QAAI,SAAS,QAAQ;AACnB,WAAK,WAAW,OAAO,OAAO,OAAO,OAAO,GAAG;AAAA,IACjD;AAEA,SAAK,MAAM,KAAK,MAAM,SAAS,CAAC,EAAE,KAAK;AAAA,MACrC,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,MACT,MAAM,MAAM;AAAA,MACZ,OAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAEA,aAAa,UAAU,aAAa,SAAU,OAAO;AACnD,MAAI,eAAe,KAAK,OAAO,SAAS;AACxC,MAAI,QAAQ,KAAK,OAAO,CAAC;AAEzB,MAAI,CAAC,gBAAgB,OAAO;AAC1B,SAAK,SAAS,KAAK;AAAA,EACrB;AAEA,MAAI,OAAO;AACT,QAAI,iBAAiB,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACrD,QAAI,YAAY,eAAe,eAAe,SAAS,CAAC;AAIxD,QAAI,CAAC,MAAM,OAAO,SAAS,GAAG;AAC5B,qBAAe,KAAK;AAAA,QAClB,GAAG,MAAM;AAAA,QACT,GAAG,MAAM;AAAA,QACT,MAAM,MAAM;AAAA,QACZ,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,OAAO,KAAK,UAAU,YAAY;AACpC,SAAK,MAAM,KAAK;AAAA,EAClB;AACF;AAEA,aAAa,UAAU,qBAAqB,WAAY;AACtD,OAAK,mBAAmB;AAExB,OAAK,QAAQ,iBAAiB,aAAa,KAAK,gBAAgB;AAChE,OAAK,QAAQ,iBAAiB,aAAa,KAAK,gBAAgB;AAChE,WAAS,iBAAiB,WAAW,KAAK,cAAc;AAC1D;AAEA,aAAa,UAAU,qBAAqB,WAAY;AAEtD,OAAK,QAAQ,MAAM,gBAAgB;AACnC,OAAK,QAAQ,MAAM,cAAc;AAEjC,OAAK,QAAQ,iBAAiB,cAAc,KAAK,iBAAiB;AAClE,OAAK,QAAQ,iBAAiB,aAAa,KAAK,gBAAgB;AAChE,OAAK,QAAQ,iBAAiB,YAAY,KAAK,eAAe;AAChE;AAEA,aAAa,UAAU,SAAS,WAAY;AAC1C,OAAK,SAAS,CAAC;AACf,OAAK,gBAAgB;AACrB,OAAK,cAAc,KAAK,WAAW,KAAK,YAAY;AACpD,OAAK,KAAK,YAAY,KAAK;AAC7B;AAEA,aAAa,UAAU,eAAe,SAAU,GAAG,GAAG,MAAM;AAC1D,MAAI,OAAO,KAAK,QAAQ,sBAAsB;AAE9C,SAAO,IAAI,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK,SAAQ,oBAAI,KAAK,GAAE,QAAQ,CAAC;AAC5E;AAEA,aAAa,UAAU,YAAY,SAAU,OAAO;AAClD,MAAI,SAAS,KAAK;AAClB,MAAI,MAAM;AAEV,SAAO,KAAK,KAAK;AAEjB,MAAI,OAAO,SAAS,GAAG;AAGrB,QAAI,OAAO,WAAW,EAAG,QAAO,QAAQ,OAAO,CAAC,CAAC;AAEjD,UAAM,KAAK,6BAA6B,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACvE,QAAI,KAAK,IAAI;AACb,UAAM,KAAK,6BAA6B,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACvE,QAAI,KAAK,IAAI;AACb,QAAI,QAAQ,IAAI,OAAO,OAAO,CAAC,GAAG,IAAI,IAAI,OAAO,CAAC,CAAC;AACnD,QAAI,SAAS,KAAK,sBAAsB,KAAK;AAI7C,WAAO,MAAM;AAEb,WAAO,EAAE,OAAc,OAAe;AAAA,EACxC;AAEA,SAAO,CAAC;AACV;AAEA,aAAa,UAAU,+BAA+B,SAAU,IAAI,IAAI,IAAI;AAC1E,MAAI,MAAM,GAAG,IAAI,GAAG;AACpB,MAAI,MAAM,GAAG,IAAI,GAAG;AACpB,MAAI,MAAM,GAAG,IAAI,GAAG;AACpB,MAAI,MAAM,GAAG,IAAI,GAAG;AAEpB,MAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,KAAK,GAAK,IAAI,GAAG,IAAI,GAAG,KAAK,EAAI;AAC1D,MAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,KAAK,GAAK,IAAI,GAAG,IAAI,GAAG,KAAK,EAAI;AAE1D,MAAI,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AACxC,MAAI,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAExC,MAAI,MAAM,GAAG,IAAI,GAAG;AACpB,MAAI,MAAM,GAAG,IAAI,GAAG;AAEpB,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,KAAK,EAAE,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,GAAG,IAAI,MAAM,EAAE;AAEhD,MAAI,KAAK,GAAG,IAAI,GAAG;AACnB,MAAI,KAAK,GAAG,IAAI,GAAG;AAEnB,SAAO;AAAA,IACL,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,IAClC,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;AAAA,EACpC;AACF;AAEA,aAAa,UAAU,wBAAwB,SAAU,OAAO;AAC9D,MAAI,aAAa,MAAM;AACvB,MAAI,WAAW,MAAM;AACrB,MAAI,SAAS,EAAE,OAAO,MAAM,KAAK,KAAK;AAEtC,MAAI,WAAW,KAAK,uBAAuB,SAAS,aAAa,UAAU,KAAK,IAAI,KAAK,wBAAwB,KAAK;AAEtH,MAAI,WAAW,KAAK,aAAa,QAAQ;AAEzC,SAAO,QAAQ,KAAK;AACpB,SAAO,MAAM;AAEb,OAAK,gBAAgB;AACrB,OAAK,aAAa;AAElB,SAAO;AACT;AAEA,aAAa,UAAU,eAAe,SAAU,UAAU;AACxD,SAAO,KAAK,IAAI,KAAK,YAAY,WAAW,IAAI,KAAK,QAAQ;AAC/D;AAEA,aAAa,UAAU,aAAa,SAAU,GAAG,GAAG,MAAM;AACxD,MAAI,MAAM,KAAK;AAEf,MAAI,OAAO,GAAG,CAAC;AACf,MAAI,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI,KAAK,IAAI,KAAK;AACzC,OAAK,WAAW;AAClB;AAEA,aAAa,UAAU,aAAa,SAAU,OAAO,YAAY,UAAU;AACzE,MAAI,MAAM,KAAK;AACf,MAAI,aAAa,WAAW;AAC5B,MAAI,YAAY,KAAK,MAAM,MAAM,OAAO,CAAC;AAEzC,MAAI,UAAU;AAEd,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK,GAAG;AAErC,QAAI,IAAI,IAAI;AACZ,QAAI,KAAK,IAAI;AACb,QAAI,MAAM,KAAK;AACf,QAAI,IAAI,IAAI;AACZ,QAAI,KAAK,IAAI;AACb,QAAI,MAAM,KAAK;AAEf,QAAI,IAAI,MAAM,MAAM,WAAW;AAC/B,SAAK,IAAI,KAAK,IAAI,MAAM,SAAS;AACjC,SAAK,IAAI,IAAI,KAAK,MAAM,SAAS;AACjC,SAAK,MAAM,MAAM,SAAS;AAE1B,QAAI,IAAI,MAAM,MAAM,WAAW;AAC/B,SAAK,IAAI,KAAK,IAAI,MAAM,SAAS;AACjC,SAAK,IAAI,IAAI,KAAK,MAAM,SAAS;AACjC,SAAK,MAAM,MAAM,SAAS;AAE1B,QAAI,QAAQ,aAAa,MAAM;AAC/B,SAAK,WAAW,GAAG,GAAG,KAAK;AAAA,EAC7B;AAEA,MAAI,UAAU;AACd,MAAI,KAAK;AACX;AAEA,aAAa,UAAU,WAAW,SAAU,OAAO;AACjD,MAAI,MAAM,KAAK;AACf,MAAI,QAAQ,OAAO,KAAK,YAAY,aAAa,KAAK,QAAQ,IAAI,KAAK;AAEvE,MAAI,UAAU;AACd,OAAK,WAAW,MAAM,GAAG,MAAM,GAAG,KAAK;AACvC,MAAI,UAAU;AACd,MAAI,KAAK;AACX;AAEA,aAAa,UAAU,YAAY,SAAU,aAAa,WAAW,SAAS;AAC5E,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK,GAAG;AAC9C,QAAI,QAAQ,YAAY,CAAC;AAEzB,QAAI,MAAM,SAAS,GAAG;AACpB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,YAAI,WAAW,MAAM,CAAC;AACtB,YAAI,QAAQ,IAAI,MAAM,SAAS,GAAG,SAAS,GAAG,SAAS,IAAI;AAC3D,YAAI,QAAQ,SAAS;AAErB,YAAI,MAAM,GAAG;AAKX,eAAK,WAAW;AAChB,eAAK,OAAO;AAEZ,eAAK,UAAU,KAAK;AAAA,QACtB,WAAW,MAAM,MAAM,SAAS,GAAG;AAEjC,cAAI,aAAa,KAAK,UAAU,KAAK,GACjC,QAAQ,WAAW,OACnB,SAAS,WAAW;AAExB,cAAI,SAAS,QAAQ;AACnB,sBAAU,OAAO,QAAQ,KAAK;AAAA,UAChC;AAAA,QACF,OAAO;AAAA,QAEP;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,OAAO;AACZ,UAAI,YAAY,MAAM,CAAC;AACvB,cAAQ,SAAS;AAAA,IACnB;AAAA,EACF;AACF;AAEA,aAAa,UAAU,SAAS,WAAY;AAC1C,MAAI,SAAS;AAEb,MAAI,cAAc,KAAK;AACvB,MAAI,SAAS,KAAK;AAClB,MAAI,QAAQ,KAAK,IAAI,OAAO,oBAAoB,GAAG,CAAC;AACpD,MAAI,OAAO;AACX,MAAI,OAAO;AACX,MAAI,OAAO,OAAO,QAAQ;AAC1B,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,MAAM,SAAS,gBAAgB,8BAA8B,KAAK;AAEtE,MAAI,eAAe,MAAM,SAAS,OAAO,KAAK;AAC9C,MAAI,eAAe,MAAM,UAAU,OAAO,MAAM;AAEhD,OAAK,UAAU,aAAa,SAAU,OAAO,QAAQ,OAAO;AAC1D,QAAI,OAAO,SAAS,cAAc,MAAM;AAKxC,QAAI,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,MAAM,MAAM,SAAS,CAAC,GAAG;AAChH,UAAI,OAAO,OAAO,MAAM,WAAW,EAAE,QAAQ,CAAC,IAAI,MAAM,MAAM,WAAW,EAAE,QAAQ,CAAC,IAAI,OAAO,OAAO,MAAM,SAAS,EAAE,QAAQ,CAAC,IAAI,MAAM,MAAM,SAAS,EAAE,QAAQ,CAAC,IAAI,QAAQ,MAAM,SAAS,EAAE,QAAQ,CAAC,IAAI,MAAM,MAAM,SAAS,EAAE,QAAQ,CAAC,IAAI,QAAQ,MAAM,SAAS,EAAE,QAAQ,CAAC,IAAI,MAAM,MAAM,SAAS,EAAE,QAAQ,CAAC;AAExT,WAAK,aAAa,KAAK,IAAI;AAC3B,WAAK,aAAa,iBAAiB,OAAO,MAAM,MAAM,QAAQ,CAAC,CAAC;AAChE,WAAK,aAAa,UAAU,KAAK;AACjC,WAAK,aAAa,QAAQ,MAAM;AAChC,WAAK,aAAa,kBAAkB,OAAO;AAE3C,UAAI,YAAY,IAAI;AAAA,IACtB;AAAA,EACF,GAAG,SAAU,UAAU;AACrB,QAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,QAAI,UAAU,OAAO,OAAO,YAAY,aAAa,OAAO,QAAQ,IAAI,OAAO;AAC/E,WAAO,aAAa,KAAK,OAAO;AAChC,WAAO,aAAa,MAAM,SAAS,CAAC;AACpC,WAAO,aAAa,MAAM,SAAS,CAAC;AACpC,WAAO,aAAa,QAAQ,SAAS,KAAK;AAE1C,QAAI,YAAY,MAAM;AAAA,EACxB,CAAC;AAED,MAAI,SAAS;AACb,MAAI,SAAS,wFAAkG,eAAe,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,QAAQ,aAAa,OAAO,QAAQ,cAAc,OAAO,OAAO;AAC5O,MAAI,OAAO,IAAI;AAGf,MAAI,SAAS,QAAW;AACtB,QAAI,QAAQ,SAAS,cAAc,OAAO;AAC1C,QAAI,QAAQ,IAAI;AAChB,UAAM,YAAY;AAElB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,YAAM,YAAY,MAAM,CAAC,EAAE,UAAU,IAAI,CAAC;AAAA,IAC5C;AAEA,WAAO,MAAM;AAAA,EACf;AAEA,MAAI,SAAS;AACb,MAAI,OAAO,SAAS,OAAO;AAE3B,SAAO,SAAS,KAAK,IAAI;AAC3B;AAEA,aAAa,UAAU,WAAW,SAAU,aAAa;AACvD,MAAI,SAAS;AAEb,OAAK,MAAM;AAEX,OAAK,UAAU,aAAa,SAAU,OAAO,QAAQ;AACnD,WAAO,OAAO,WAAW,OAAO,OAAO,OAAO,OAAO,GAAG;AAAA,EAC1D,GAAG,SAAU,UAAU;AACrB,WAAO,OAAO,SAAS,QAAQ;AAAA,EACjC,CAAC;AAED,OAAK,QAAQ;AACf;AAEA,aAAa,UAAU,SAAS,WAAY;AAC1C,SAAO,KAAK;AACd;AAEA,IAAO,wBAAQ;;;;;AC/kBFC,IAAAA,kBAAe,SAAAC,YAAA;AAAAC,YAAAF,kBAAAC,UAAA;AAAA,MAAAE,SAAAC,aAAAJ,gBAAA;AAAA,WAAAA,mBAAA;AAAA,QAAAK;AAAAC,oBAAA,MAAAN,gBAAA;AAAA,aAAAO,OAAAC,UAAAC,QAAAC,OAAAC,IAAAA,MAAAJ,IAAA,GAAAK,OAAA,GAAAA,OAAAL,MAAAK,QAAA;AAAAF,WAAAE,IAAA,IAAAJ,UAAAI,IAAA;IAAA;AAAAP,YAAAF,OAAAU,KAAAC,MAAAX,QAAA,CAAA,IAAA,EAAAY,OAAAL,IAAA,CAAA;AAAAL,UAyBTW,aAAaX,MAAKY;AAAWZ,UAE9Ca,UAA+B;AAAIb,UACnCc,UAAoC;AAAId,UAEvBe,SAAS,SAACC,KAAwC;AACjEhB,YAAKc,UAAUE;AAEf,UAAIhB,MAAKc,YAAY,MAAM;AACzBd,cAAKa,UAAU;MACjB;;AACDb,UAEDiB,mBAAmB,WAAwC;AACzD,UAAAC,cAAuDlB,MAAKmB;AAAzCD,kBAAXE;AAA0BF,kBAAbG;AAAkBC,UAAAA,cAAWC,yBAAAL,aAAAM,SAAA;AAClD,aAAOF;;AACRtB,UAEQyB,oBAAoD,WAAM;AACjE,UAAMC,SAAS1B,MAAK2B,UAAS;AAC7B3B,YAAKa,UAAU,IAAIe,sBAAaF,QAAQ1B,MAAKiB,iBAAgB,CAAE;AAC/DjB,YAAK6B,cAAa;AAClB7B,YAAK8B,GAAE;;AACR9B,UAEQ+B,uBAA0D,WAAM;AACvE/B,YAAKgC,IAAG;;AACThC,UAGQiC,qBAAsD,WAAM;AACnEC,aAAOC,OAAOnC,MAAKa,SAASb,MAAKiB,iBAAgB,CAAE;;AACpDjB,UAGD2B,YAAY,WAAyB;AACnC,UAAI3B,MAAKc,YAAY,MAAM;AACzB,cAAMd,MAAKW,WAAWyB;MACxB;AACA,aAAOpC,MAAKc;;AACbd,UAGDqC,mBAAmB,WAAyB;AAE1C,UAAMX,SAAS1B,MAAK2B,UAAS;AAC7B,UAAMW,OAAOC,SAASC,cAAc,QAAQ;AAC5CF,WAAKG,QAAQf,OAAOe;AACpBH,WAAKI,SAAShB,OAAOgB;AAErBJ,WAAKK,WAAW,IAAI,EAAGC,UAAUlB,QAAQ,GAAG,CAAC;AAE7C,iBAAOmB,mBAAAA,SAAWP,IAAI;;AACvBtC,UAGD8C,kBAAkB,WAAoB;AACpC,UAAI9C,MAAKa,YAAY,MAAM;AACzB,cAAMb,MAAKW,WAAWyB;MACxB;AACA,aAAOpC,MAAKa;;AACbb,UAED+C,sBAAsB,WAAY;AAChC,UAAI,CAAC/C,MAAKmB,MAAME,eAAe;AAC7B;MACF;AACArB,YAAK6B,cAAa;;AACnB7B,UAED6B,gBAAgB,WAAY;AAAA,UAAAmB,uBAAAC;AAC1B,UAAM7B,eAAW4B,wBAAGhD,MAAKmB,MAAMC,iBAAW,QAAA4B,0BAAA,SAAAA,wBAAI,CAAA;AAC9C,UAAQP,QAAkBrB,YAAlBqB,OAAOC,SAAWtB,YAAXsB;AAEf,UAAI,OAAOD,UAAU,eAAe,OAAOC,WAAW,aAAa;AACjE;MACF;AAEA,UAAMhB,SAAS1B,MAAK2B,UAAS;AAI7B,UAAMuB,QAAQC,KAAKC,KAAGH,wBAACI,OAAOC,sBAAgB,QAAAL,0BAAAA,SAAAA,wBAAI,GAAG,CAAC;AAEtD,UAAI,OAAOR,UAAU,aAAa;AAChCf,eAAOe,QAAQf,OAAO6B,cAAcL;MACtC;AACA,UAAI,OAAOR,WAAW,aAAa;AACjChB,eAAOgB,SAAShB,OAAO8B,eAAeN;MACxC;AAEAxB,aAAOiB,WAAW,IAAI,EAAGc,MAAMP,OAAOA,KAAK;AAC3ClD,YAAK0D,MAAK;;AACX1D,UAEQ2D,SAA8B,WAAM;AAC3C,UAAQvC,cAAgBpB,MAAKmB,MAArBC;AACR,aAAOwC,aAAAA,QAAApB,cAAA,UAAAqB,SAAA;QAAQ7C,KAAKhB,MAAKe;SAAYK,WAAW,CAAG;;AACpDpB,UAID8B,KAAyB,WAAM;AAC7BuB,aAAOS,iBAAiB,UAAU9D,MAAK+C,mBAAmB;AAC1D,aAAO/C,MAAK8C,gBAAe,EAAGhB,GAAE;;AACjC9B,UAEDgC,MAA2B,WAAM;AAC/BqB,aAAOU,oBAAoB,UAAU/D,MAAK+C,mBAAmB;AAC7D,aAAO/C,MAAK8C,gBAAe,EAAGd,IAAG;;AAClChC,UAED0D,QAA+B,WAAM;AACnC,aAAO1D,MAAK8C,gBAAe,EAAGY,MAAK;;AACpC1D,UAEDgE,UAAmC,WAAM;AACvC,aAAOhE,MAAK8C,gBAAe,EAAGkB,QAAO;;AACtChE,UAEDiE,cAA2C,SAACC,SAASC,SAAY;AAC/D,aAAOnE,MAAK8C,gBAAe,EAAGmB,YAAYC,SAASC,OAAO;;AAC3DnE,UAEDoE,YAAuC,SAACC,MAAMC,gBAAmB;AAC/D,aAAOtE,MAAK8C,gBAAe,EAAGsB,UAAUC,MAAMC,cAAc;;AAC7DtE,UAEDuE,WAAqC,SAACC,aAAgB;AACpD,aAAOxE,MAAK8C,gBAAe,EAAGyB,SAASC,WAAW;;AACnDxE,UAEDyE,SAAiC,WAAM;AACrC,aAAOzE,MAAK8C,gBAAe,EAAG2B,OAAM;;AACrC,WAAAzE;;AAjCD,SAAA0E,aAAA/E,gBAAA;AAAA,EA9HmCgF,sBAAS;AAAjChF,gBACKiF,YAAY;;EAE1BC,sBAAsBC,kBAAAA,QAAUC;EAChCC,UAAUF,kBAAAA,QAAUC;EACpBE,UAAUH,kBAAAA,QAAUC;EACpBG,aAAaJ,kBAAAA,QAAUC;EACvBI,SAASL,kBAAAA,QAAUM,UAAU,CAACN,kBAAAA,QAAUC,QAAQD,kBAAAA,QAAUO,IAAI,CAAC;EAC/DC,UAAUR,kBAAAA,QAAUS;EACpBC,UAAUV,kBAAAA,QAAUC;EACpBU,OAAOX,kBAAAA,QAAUO;EACjBK,SAASZ,kBAAAA,QAAUO;;EAEnBjE,aAAa0D,kBAAAA,QAAUa;EACvBtE,eAAeyD,kBAAAA,QAAUc;AAC3B;AAfWjG,gBAiBJkG,eAA4D;EACjExE,eAAe;AACjB;AAnBW1B,gBAqBJyC,eAAe,IAAI0D,MAAM,oGACkC;", "names": ["e", "t", "r", "n", "o", "a", "u", "t", "t", "_isNativeReflectConstruct", "later", "SignatureCanvas", "_Component", "_inherits", "_super", "_createSuper", "_this", "_classCallCheck", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "staticThis", "constructor", "_sigPad", "_canvas", "setRef", "ref", "_excludeOurProps", "_this$props", "props", "canvasProps", "clearOnResize", "sigPadProps", "_objectWithoutProperties", "_excluded", "componentDidMount", "canvas", "get<PERSON>anvas", "SignaturePad", "_resizeCanvas", "on", "componentWillUnmount", "off", "componentDidUpdate", "Object", "assign", "refNull<PERSON><PERSON>r", "getTrimmedCanvas", "copy", "document", "createElement", "width", "height", "getContext", "drawImage", "trimCanvas", "getSignaturePad", "_checkClearOnResize", "_this$props$canvasPro", "_window$devicePixelRa", "ratio", "Math", "max", "window", "devicePixelRatio", "offsetWidth", "offsetHeight", "scale", "clear", "render", "React", "_extends", "addEventListener", "removeEventListener", "isEmpty", "fromDataURL", "dataURL", "options", "toDataURL", "type", "encoderOptions", "fromData", "pointGroups", "toData", "_createClass", "Component", "propTypes", "velocityFilterWeight", "PropTypes", "number", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minDistance", "dotSize", "oneOfType", "func", "penColor", "string", "throttle", "onEnd", "onBegin", "object", "bool", "defaultProps", "Error"]}