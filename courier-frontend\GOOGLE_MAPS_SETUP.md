# Google Maps API Setup Guide

This guide will help you set up the required Google Maps APIs for the courier management system.

## Required APIs

The application uses the following Google Cloud APIs:

1. **Maps JavaScript API** - For map display and basic functionality
2. **Places API (New)** - For address autocomplete and geocoding
3. **Routes API** - For modern distance/duration calculations (replaces deprecated Distance Matrix API)
4. **Directions API** - Fallback for route calculations
5. **Geocoding API** - For address to coordinates conversion

## Setup Instructions

### 1. Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select an existing one
3. Make sure billing is enabled for your project

### 2. Enable Required APIs

1. Navigate to **APIs & Services** > **Library**
2. Search for and enable each of the following APIs:
   - Maps JavaScript API
   - Places API (New)
   - Routes API
   - Directions API
   - Geocoding API

### 3. Create API Credentials

1. Go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **API Key**
3. Copy the generated API key
4. (Optional but recommended) Restrict the API key:
   - Click on the API key to edit it
   - Under **Application restrictions**, select **HTTP referrers**
   - Add your domain (e.g., `localhost:5173` for development)
   - Under **API restrictions**, select **Restrict key** and choose the APIs listed above

### 4. Configure Environment Variables

1. Create a `.env` file in the `courier-frontend` directory if it doesn't exist
2. Add your API key:
   ```
   VITE_GOOGLE_MAPS_API_KEY=your_api_key_here
   ```

### 5. Test the Setup

You can test if your APIs are working correctly by opening the browser console and running:

```javascript
import { logAPIStatus } from './src/utils/googleMaps.js';
logAPIStatus();
```

This will show you which APIs are working and which ones need attention.

## Common Issues

### Distance Matrix API Error

If you see an error like "You're calling a legacy API, which is not enabled for your project", it means:

1. The old Distance Matrix API is deprecated
2. Our application now uses the modern Routes API instead
3. Make sure the **Routes API** is enabled in your Google Cloud Console

### API Key Issues

- Make sure your API key is correctly set in the `.env` file
- Ensure the API key has the correct restrictions (if any)
- Check that billing is enabled for your Google Cloud project

### CORS Issues

If you encounter CORS errors:

1. Make sure your domain is added to the API key restrictions
2. For development, add `localhost:5173` (or your dev server port)
3. For production, add your actual domain

## Cost Optimization

To minimize costs:

1. Set up API key restrictions to prevent unauthorized usage
2. Enable billing alerts in Google Cloud Console
3. Monitor your API usage in the Google Cloud Console
4. The application includes fallback to Haversine formula when APIs are unavailable

## Support

If you encounter issues:

1. Check the browser console for error messages
2. Use the `logAPIStatus()` function to diagnose API availability
3. Verify all required APIs are enabled in Google Cloud Console
4. Check your API key configuration and restrictions
