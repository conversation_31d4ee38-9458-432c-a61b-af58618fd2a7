import { useState, useEffect } from 'react';
import { Box, Chip, Tooltip, Typography } from '@mui/material';
import { 
  Wifi as WifiIcon, 
  WifiOff as WifiOffIcon,
  Circle as CircleIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { listenToUserStatus } from '../../utils/userStatusUtils';
import { useOffline } from '../../context/OfflineContext';

/**
 * Component to display both network connectivity and user presence status
 * 
 * @param {Object} props - Component props
 * @param {string} props.userId - User ID to track presence for (optional)
 * @param {boolean} props.showNetworkStatus - Whether to show network connectivity status
 * @param {boolean} props.showUserStatus - Whether to show user presence status
 * @param {string} props.variant - Display variant: 'chip', 'icon', 'text'
 * @param {string} props.size - Size: 'small', 'medium', 'large'
 * @param {Object} props.sx - Additional styles
 */
const UserPresence = ({ 
  userId, 
  showNetworkStatus = true, 
  showUserStatus = true,
  variant = 'chip',
  size = 'small',
  sx = {} 
}) => {
  const [userStatus, setUserStatus] = useState({ isOnline: false, lastChanged: null });
  const { isOnline: networkOnline } = useOffline();

  // Listen to user presence status if userId is provided
  useEffect(() => {
    if (!userId || !showUserStatus) return;
    
    const unsubscribe = listenToUserStatus(userId, (newStatus) => {
      setUserStatus(newStatus);
    });
    
    return () => unsubscribe();
  }, [userId, showUserStatus]);

  const getStatusColor = (isOnline) => isOnline ? 'success' : 'error';
  const getStatusText = (isOnline) => isOnline ? 'Online' : 'Offline';

  // Render based on variant
  if (variant === 'icon') {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ...sx }}>
        {showNetworkStatus && (
          <Tooltip title={`Network: ${getStatusText(networkOnline)}`}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {networkOnline ? (
                <WifiIcon color="success" fontSize={size} />
              ) : (
                <WifiOffIcon color="error" fontSize={size} />
              )}
            </Box>
          </Tooltip>
        )}
        
        {showUserStatus && userId && (
          <Tooltip title={`User: ${getStatusText(userStatus.isOnline)}`}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CircleIcon 
                sx={{ 
                  color: userStatus.isOnline ? '#44b700' : '#bdbdbd',
                  fontSize: size === 'small' ? 12 : size === 'medium' ? 16 : 20
                }} 
              />
            </Box>
          </Tooltip>
        )}
      </Box>
    );
  }

  if (variant === 'text') {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, ...sx }}>
        {showNetworkStatus && (
          <Typography variant="caption" color={getStatusColor(networkOnline)}>
            Network: {getStatusText(networkOnline)}
          </Typography>
        )}
        
        {showUserStatus && userId && (
          <Typography variant="caption" color={getStatusColor(userStatus.isOnline)}>
            User: {getStatusText(userStatus.isOnline)}
          </Typography>
        )}
      </Box>
    );
  }

  // Default: chip variant
  return (
    <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', ...sx }}>
      {showNetworkStatus && (
        <Tooltip title={`Network connectivity: ${getStatusText(networkOnline)}`}>
          <Chip
            icon={networkOnline ? <WifiIcon /> : <WifiOffIcon />}
            label={`Network ${getStatusText(networkOnline)}`}
            color={getStatusColor(networkOnline)}
            size={size}
            variant="outlined"
          />
        </Tooltip>
      )}
      
      {showUserStatus && userId && (
        <Tooltip title={`User presence: ${getStatusText(userStatus.isOnline)}`}>
          <Chip
            icon={<PersonIcon />}
            label={`User ${getStatusText(userStatus.isOnline)}`}
            color={getStatusColor(userStatus.isOnline)}
            size={size}
            variant="outlined"
          />
        </Tooltip>
      )}
    </Box>
  );
};

/**
 * Hook to get both network and user status
 * @param {string} userId - User ID to track
 * @returns {Object} Object with networkOnline, userOnline, and combined status
 */
export const useUserPresence = (userId) => {
  const [userStatus, setUserStatus] = useState({ isOnline: false, lastChanged: null });
  const { isOnline: networkOnline } = useOffline();

  useEffect(() => {
    if (!userId) return;
    
    const unsubscribe = listenToUserStatus(userId, (newStatus) => {
      setUserStatus(newStatus);
    });
    
    return () => unsubscribe();
  }, [userId]);

  return {
    networkOnline,
    userOnline: userStatus.isOnline,
    userLastSeen: userStatus.lastChanged,
    // User is considered fully online if both network and user presence are online
    fullyOnline: networkOnline && userStatus.isOnline,
    // User is considered available if either network is online OR user was recently active
    available: networkOnline || (userStatus.lastChanged && 
      Date.now() - userStatus.lastChanged.getTime() < 5 * 60 * 1000) // 5 minutes
  };
};

export default UserPresence;
