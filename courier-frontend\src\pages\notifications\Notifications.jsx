import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  IconButton,
  Chip,
  Button,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  useTheme,
  Tabs,
  Tab,
  Badge
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Search as SearchIcon,
  CheckCircle as CheckCircleIcon,
  NotificationsActive as NotificationsActiveIcon,
  NotificationsOff as NotificationsOffIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { collection, query, orderBy, onSnapshot, updateDoc, doc, where, limit, Timestamp } from 'firebase/firestore';
import { db } from '../../firebase';
import { useAuth } from '../../context/AuthContext';
import AdminSidebar from '../../components/layout/AdminSidebar';
import CourierSidebar from '../../components/layout/CourierSidebar';
import TopNavbar from '../../components/layout/TopNavbar';

const Notifications = () => {
  const { user, role } = useAuth();
  const theme = useTheme();

  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [mobileOpen, setMobileOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [unreadCount, setUnreadCount] = useState(0);

  const handleDrawerToggle = () => setMobileOpen(!mobileOpen);

  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
  };

  // Fetch notifications
  useEffect(() => {
    if (!user?.uid) return;

    setLoading(true);

    let notificationsQuery;

    if (role === 'admin') {
      // Admin can see all notifications
      notificationsQuery = query(
        collection(db, 'notifications'),
        orderBy('createdAt', 'desc'),
        limit(100)
      );
    } else {
      // Courier only sees their notifications
      notificationsQuery = query(
        collection(db, 'notifications'),
        where('recipientId', '==', user.uid),
        orderBy('createdAt', 'desc'),
        limit(100)
      );
    }

    const unsubscribe = onSnapshot(notificationsQuery, (snapshot) => {
      try {
        const notificationsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          read: doc.data().read || false,
          createdAt: doc.data().createdAt?.toDate() || new Date()
        }));

        setNotifications(notificationsData);
        setUnreadCount(notificationsData.filter(notif => !notif.read).length);
        setLoading(false);
      } catch (err) {
        console.error('Error processing notifications:', err);
        setError('Failed to load notifications. Please try again.');
        setLoading(false);
      }
    }, (err) => {
      console.error('Error in notifications listener:', err);
      setError('Failed to load notifications. Please try again.');
      setLoading(false);
    });

    return () => unsubscribe();
  }, [user, role]);

  // Mark notification as read
  const handleMarkAsRead = async (notificationId) => {
    try {
      const notificationRef = doc(db, 'notifications', notificationId);
      await updateDoc(notificationRef, {
        read: true,
        readAt: Timestamp.now()
      });
    } catch (err) {
      console.error('Error marking notification as read:', err);
    }
  };

  // Mark all notifications as read
  const handleMarkAllAsRead = async () => {
    try {
      const unreadNotifications = notifications.filter(notif => !notif.read);

      for (const notification of unreadNotifications) {
        const notificationRef = doc(db, 'notifications', notification.id);
        await updateDoc(notificationRef, {
          read: true,
          readAt: Timestamp.now()
        });
      }
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
    }
  };

  // Filter notifications based on tab and search query
  const filteredNotifications = notifications.filter(notification => {
    // Filter by tab
    if (tabValue === 1 && notification.read) return true;
    if (tabValue === 2 && !notification.read) return true;
    if (tabValue === 0) return true;

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        notification.title?.toLowerCase().includes(query) ||
        notification.message?.toLowerCase().includes(query) ||
        notification.body?.toLowerCase().includes(query)
      );
    }

    return false;
  });

  const Sidebar = role === 'admin' ? AdminSidebar : CourierSidebar;

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <Sidebar
        mobileOpen={mobileOpen}
        handleDrawerToggle={handleDrawerToggle}
      />
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <TopNavbar handleDrawerToggle={handleDrawerToggle} />
        <Container maxWidth="lg" sx={{ mt: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h4" fontWeight="bold" color="primary">
              Notifications
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<CheckCircleIcon />}
                onClick={handleMarkAllAsRead}
                disabled={unreadCount === 0}
              >
                Mark All as Read
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => setLoading(true)}
                disabled={loading}
              >
                Refresh
              </Button>
            </Box>
          </Box>

          <Paper sx={{ mb: 3 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                variant="fullWidth"
              >
                <Tab
                  label="All"
                  icon={<NotificationsIcon />}
                />
                <Tab
                  label="Read"
                  icon={<CheckCircleIcon />}
                />
                <Tab
                  icon={
                    <Badge badgeContent={unreadCount} color="error">
                      <NotificationsActiveIcon />
                    </Badge>
                  }
                  label="Unread"
                />
              </Tabs>
            </Box>
          </Paper>

          <Box sx={{ mb: 3 }}>
            <TextField
              fullWidth
              placeholder="Search notifications..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                )
              }}
            />
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Paper>
              {filteredNotifications.length === 0 ? (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <NotificationsOffIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary">
                    No notifications found
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {searchQuery ? 'Try adjusting your search query' : 'You have no notifications at this time'}
                  </Typography>
                </Box>
              ) : (
                <List>
                  {filteredNotifications.map((notification, index) => (
                    <React.Fragment key={notification.id}>
                      {index > 0 && <Divider />}
                      <ListItem
                        alignItems="flex-start"
                        sx={{
                          bgcolor: !notification.read ?
                            theme.palette.mode === 'dark' ? 'rgba(144, 202, 249, 0.08)' : 'rgba(33, 150, 243, 0.04)'
                            : 'transparent',
                          transition: 'background-color 0.3s'
                        }}
                        secondaryAction={
                          !notification.read && (
                            <IconButton
                              edge="end"
                              aria-label="mark as read"
                              onClick={() => handleMarkAsRead(notification.id)}
                            >
                              <CheckCircleIcon />
                            </IconButton>
                          )
                        }
                      >
                        <ListItemIcon>
                          <NotificationsIcon color={notification.read ? 'disabled' : 'primary'} />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle1" fontWeight={notification.read ? 'normal' : 'bold'}>
                                {notification.title || 'Notification'}
                              </Typography>
                              {!notification.read && (
                                <Chip
                                  label="New"
                                  color="primary"
                                  size="small"
                                  sx={{ height: 20, fontSize: '0.7rem' }}
                                />
                              )}
                            </Box>
                          }
                          secondary={
                            <>
                              <Typography
                                variant="body2"
                                color="text.primary"
                                sx={{ mt: 0.5, mb: 1 }}
                              >
                                {notification.message || notification.body || 'No message content'}
                              </Typography>
                              <Typography
                                variant="caption"
                                color="text.secondary"
                              >
                                {notification.createdAt.toLocaleString()}
                              </Typography>
                            </>
                          }
                        />
                      </ListItem>
                    </React.Fragment>
                  ))}
                </List>
              )}
            </Paper>
          )}
        </Container>
      </Box>
    </Box>
  );
};

export default Notifications;
