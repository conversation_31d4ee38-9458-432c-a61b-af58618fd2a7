// Google Places Autocomplete Address Form Component (Based on Google's Official Template)
import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  TextField,
  Grid,
  Typography,
  Button,
  Paper,
  IconButton,
  Tooltip,
  Alert,
  Chip
} from '@mui/material';
import {
  LocationOn,
  MyLocation,
  Clear,
  StarBorder
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { GOOGLE_MAPS_API_KEY } from '../../utils/googleMaps';

const GoogleAddressForm = ({
  title = "Address Information",
  onAddressChange,
  onLocationSelect,
  initialData = {},
  required = false,
  disabled = false,
  showFavorites = true,
  addressType = "delivery", // 'pickup' or 'delivery'
  countryRestriction = ["in"], // Default to India
  ...props
}) => {
  // Form state based on Google's template
  const [formData, setFormData] = useState({
    shipAddress: initialData.shipAddress || '',
    address2: initialData.address2 || '',
    locality: initialData.locality || '',
    state: initialData.state || '',
    postcode: initialData.postcode || '',
    country: initialData.country || '',
    ...initialData
  });

  const [autocomplete, setAutocomplete] = useState(null);
  const [favorites, setFavorites] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [validationStatus, setValidationStatus] = useState(null);

  // Refs for form fields
  const address1Field = useRef(null);
  const address2Field = useRef(null);
  const localityField = useRef(null);
  const stateField = useRef(null);
  const postcodeField = useRef(null);
  const countryField = useRef(null);

  // Initialize Google Places Autocomplete (based on Google's official template)
  useEffect(() => {
    if (window.google && window.google.maps && address1Field.current) {
      initAutocomplete();
      loadFavorites();
    }
  }, []);

  // Update form data when initial data changes
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      ...initialData
    }));
  }, [initialData]);

  const initAutocomplete = () => {
    try {
      // Create the autocomplete object, restricting the search predictions
      const autocompleteInstance = new window.google.maps.places.Autocomplete(
        address1Field.current,
        {
          componentRestrictions: { country: countryRestriction },
          fields: ["address_components", "geometry", "place_id", "formatted_address"],
          types: ["address"],
        }
      );

      setAutocomplete(autocompleteInstance);

      // When the user selects an address from the drop-down, populate the address fields
      autocompleteInstance.addListener("place_changed", fillInAddress);

      // Focus on the address field
      address1Field.current.focus();
    } catch (error) {
      console.error('Error initializing autocomplete:', error);
      toast.error('Failed to initialize address autocomplete');
    }
  };

  // Core function based on Google's official template
  const fillInAddress = () => {
    try {
      // Get the place details from the autocomplete object
      const place = autocomplete.getPlace();

      if (!place || !place.address_components) {
        toast.warning('Please select a valid address from the dropdown');
        return;
      }

      let address1 = "";
      let postcode = "";
      const newFormData = { ...formData };

      // Get each component of the address from the place details
      // Based on Google's official template structure
      for (const component of place.address_components) {
        const componentType = component.types[0];

        switch (componentType) {
          case "street_number": {
            address1 = `${component.long_name} ${address1}`;
            break;
          }
          case "route": {
            address1 += component.short_name;
            break;
          }
          case "postal_code": {
            postcode = `${component.long_name}${postcode}`;
            break;
          }
          case "postal_code_suffix": {
            postcode = `${postcode}-${component.long_name}`;
            break;
          }
          case "locality":
            newFormData.locality = component.long_name;
            break;
          case "administrative_area_level_1": {
            newFormData.state = component.short_name;
            break;
          }
          case "country":
            newFormData.country = component.long_name;
            break;
          case "sublocality_level_1":
            // For places like Brooklyn, use sublocality if locality is not available
            if (!newFormData.locality) {
              newFormData.locality = component.long_name;
            }
            break;
          case "postal_town":
            // For UK and Sweden, use postal_town as city
            if (!newFormData.locality) {
              newFormData.locality = component.long_name;
            }
            break;
        }
      }

      // Update form data
      newFormData.shipAddress = address1;
      newFormData.postcode = postcode;

      setFormData(newFormData);

      // Create location data for parent component
      const locationData = {
        formatted_address: place.formatted_address,
        place_id: place.place_id,
        coordinates: place.geometry ? {
          lat: place.geometry.location.lat(),
          lng: place.geometry.location.lng()
        } : null,
        address_components: place.address_components,
        components: newFormData
      };

      // Notify parent components
      if (onAddressChange) {
        onAddressChange(place.formatted_address);
      }

      if (onLocationSelect) {
        onLocationSelect(locationData);
      }

      // Save to favorites/history
      saveToHistory(locationData);

      // Focus on address2 field for additional details
      if (address2Field.current) {
        address2Field.current.focus();
      }

      setValidationStatus('valid');
      toast.success('Address selected successfully');

    } catch (error) {
      console.error('Error filling address:', error);
      toast.error('Failed to process selected address');
      setValidationStatus('error');
    }
  };

  const loadFavorites = () => {
    try {
      const saved = localStorage.getItem(`address_favorites_${addressType}`);
      if (saved) {
        setFavorites(JSON.parse(saved));
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
    }
  };

  const saveToHistory = (address) => {
    try {
      const history = JSON.parse(localStorage.getItem(`address_history_${addressType}`) || '[]');
      const newHistory = [address, ...history.filter(h => h.place_id !== address.place_id)].slice(0, 10);
      localStorage.setItem(`address_history_${addressType}`, JSON.stringify(newHistory));
    } catch (error) {
      console.error('Error saving to history:', error);
    }
  };

  const saveFavorite = () => {
    if (!formData.shipAddress) {
      toast.warning('Please select an address first');
      return;
    }

    try {
      const favorite = {
        id: Date.now(),
        name: `${formData.locality || 'Address'} - ${formData.shipAddress}`,
        ...formData,
        timestamp: new Date().toISOString()
      };

      const newFavorites = [...favorites, favorite];
      setFavorites(newFavorites);
      localStorage.setItem(`address_favorites_${addressType}`, JSON.stringify(newFavorites));
      toast.success('Address saved to favorites');
    } catch (error) {
      console.error('Error saving favorite:', error);
      toast.error('Failed to save favorite');
    }
  };

  const removeFavorite = (id) => {
    try {
      const newFavorites = favorites.filter(f => f.id !== id);
      setFavorites(newFavorites);
      localStorage.setItem(`address_favorites_${addressType}`, JSON.stringify(newFavorites));
      toast.success('Removed from favorites');
    } catch (error) {
      console.error('Error removing favorite:', error);
    }
  };

  const handleInputChange = (field, value) => {
    const newFormData = { ...formData, [field]: value };
    setFormData(newFormData);

    if (onAddressChange) {
      const fullAddress = `${newFormData.shipAddress}, ${newFormData.address2}, ${newFormData.locality}, ${newFormData.state}, ${newFormData.postcode}, ${newFormData.country}`.replace(/,\s*,/g, ',').replace(/^,\s*|,\s*$/g, '');
      onAddressChange(fullAddress);
    }
  };

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      toast.error('Geolocation is not supported by this browser');
      return;
    }

    setIsLoading(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;

        if (window.google && window.google.maps) {
          const geocoder = new window.google.maps.Geocoder();
          geocoder.geocode(
            { location: { lat: latitude, lng: longitude } },
            (results, status) => {
              setIsLoading(false);

              if (status === 'OK' && results[0]) {
                // Simulate autocomplete selection with current location
                const place = results[0];
                if (autocomplete) {
                  autocomplete.set('place', place);
                  fillInAddress();
                }
                toast.success('Current location detected');
              } else {
                toast.error('Failed to get address for current location');
              }
            }
          );
        }
      },
      (error) => {
        setIsLoading(false);
        console.error('Geolocation error:', error);
        toast.error('Failed to get current location');
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000
      }
    );
  };

  // Render the address form based on Google's official template
  return (
    <Paper sx={{ p: 3, maxWidth: 600 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LocationOn color="primary" />
          {title}
          {required && <span style={{ color: 'red' }}> *</span>}
        </Typography>

        <Typography variant="caption" color="text.secondary">
          * = required field
        </Typography>
      </Box>

      {/* Validation Status */}
      {validationStatus && (
        <Alert
          severity={validationStatus === 'valid' ? 'success' : 'error'}
          sx={{ mb: 2 }}
        >
          {validationStatus === 'valid' ? 'Address verified successfully' : 'Please check the address details'}
        </Alert>
      )}

      <Grid container spacing={2}>
        {/* Main Address Field with Autocomplete */}
        <Grid item xs={12}>
          <TextField
            fullWidth
            label={`${addressType === 'pickup' ? 'Pickup' : 'Delivery'} Address *`}
            placeholder="Start typing an address..."
            value={formData.shipAddress}
            onChange={(e) => handleInputChange('shipAddress', e.target.value)}
            inputRef={address1Field}
            required={required}
            disabled={disabled}
            InputProps={{
              endAdornment: (
                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  <Tooltip title="Use current location">
                    <IconButton
                      size="small"
                      onClick={getCurrentLocation}
                      disabled={isLoading || disabled}
                    >
                      <MyLocation fontSize="small" />
                    </IconButton>
                  </Tooltip>

                  {showFavorites && formData.shipAddress && (
                    <Tooltip title="Save to favorites">
                      <IconButton
                        size="small"
                        onClick={saveFavorite}
                        disabled={disabled}
                      >
                        <StarBorder fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              )
            }}
            helperText="Avoid using 'address' in field names to prevent browser autofill conflicts"
          />
        </Grid>

        {/* Address Line 2 */}
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Apartment, unit, suite, or floor #"
            value={formData.address2}
            onChange={(e) => handleInputChange('address2', e.target.value)}
            inputRef={address2Field}
            disabled={disabled}
          />
        </Grid>

        {/* City */}
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="City *"
            value={formData.locality}
            onChange={(e) => handleInputChange('locality', e.target.value)}
            inputRef={localityField}
            required={required}
            disabled={disabled}
          />
        </Grid>

        {/* State */}
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="State/Province *"
            value={formData.state}
            onChange={(e) => handleInputChange('state', e.target.value)}
            inputRef={stateField}
            required={required}
            disabled={disabled}
          />
        </Grid>

        {/* Postal Code */}
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Postal Code *"
            value={formData.postcode}
            onChange={(e) => handleInputChange('postcode', e.target.value)}
            inputRef={postcodeField}
            required={required}
            disabled={disabled}
          />
        </Grid>

        {/* Country */}
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Country/Region *"
            value={formData.country}
            onChange={(e) => handleInputChange('country', e.target.value)}
            inputRef={countryField}
            required={required}
            disabled={disabled}
          />
        </Grid>
      </Grid>

      {/* Favorites Section */}
      {showFavorites && favorites.length > 0 && (
        <Box sx={{ mt: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Saved Addresses
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {favorites.slice(0, 3).map((favorite) => (
              <Chip
                key={favorite.id}
                label={favorite.name}
                onClick={() => {
                  setFormData(favorite);
                  if (onAddressChange) {
                    onAddressChange(favorite.shipAddress);
                  }
                }}
                onDelete={() => removeFavorite(favorite.id)}
                deleteIcon={<Clear />}
                variant="outlined"
                size="small"
              />
            ))}
          </Box>
        </Box>
      )}

      {/* Form Actions */}
      <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
        <Button
          variant="contained"
          onClick={() => {
            const fullAddress = `${formData.shipAddress}, ${formData.address2}, ${formData.locality}, ${formData.state}, ${formData.postcode}, ${formData.country}`.replace(/,\s*,/g, ',').replace(/^,\s*|,\s*$/g, '');
            if (onAddressChange) onAddressChange(fullAddress);
            toast.success('Address saved');
          }}
          disabled={!formData.shipAddress || disabled}
        >
          Save Address
        </Button>

        <Button
          variant="outlined"
          onClick={() => {
            setFormData({
              shipAddress: '',
              address2: '',
              locality: '',
              state: '',
              postcode: '',
              country: ''
            });
            setValidationStatus(null);
          }}
          disabled={disabled}
        >
          Clear Form
        </Button>
      </Box>
    </Paper>
  );
};

export default GoogleAddressForm;
