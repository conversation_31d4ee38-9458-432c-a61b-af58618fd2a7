// Google Places Autocomplete Address Form Component (Based on Google's Official Template)
import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  TextField,
  Grid,
  Typography,
  Button,
  Paper,
  IconButton,
  Tooltip,
  Alert,
  Chip
} from '@mui/material';
import {
  LocationOn,
  MyLocation,
  Clear,
  Star,
  StarBorder
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { GOOGLE_MAPS_API_KEY } from '../../utils/googleMaps';

const GoogleAddressForm = ({
  title = "Address Information",
  onAddressChange,
  onLocationSelect,
  initialData = {},
  required = false,
  disabled = false,
  showFavorites = true,
  addressType = "delivery", // 'pickup' or 'delivery'
  countryRestriction = ["in"], // Default to India
  ...props
}) => {
  // Form state based on Google's template
  const [formData, setFormData] = useState({
    shipAddress: initialData.shipAddress || '',
    address2: initialData.address2 || '',
    locality: initialData.locality || '',
    state: initialData.state || '',
    postcode: initialData.postcode || '',
    country: initialData.country || '',
    ...initialData
  });

  const [autocomplete, setAutocomplete] = useState(null);
  const [favorites, setFavorites] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [validationStatus, setValidationStatus] = useState(null);

  // Refs for form fields
  const address1Field = useRef(null);
  const address2Field = useRef(null);
  const localityField = useRef(null);
  const stateField = useRef(null);
  const postcodeField = useRef(null);
  const countryField = useRef(null);

  // Initialize Google Places services
  useEffect(() => {
    if (window.google && window.google.maps && GOOGLE_MAPS_API_KEY) {
      autocompleteService.current = new window.google.maps.places.AutocompleteService();
      placesService.current = new window.google.maps.places.PlacesService(
        document.createElement('div')
      );
      geocoder.current = new window.google.maps.Geocoder();
      
      // Load favorites and history from localStorage
      loadFavorites();
      loadHistory();
    }
  }, []);

  // Update input value when prop changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  const loadFavorites = () => {
    try {
      const saved = localStorage.getItem(`address_favorites_${addressType}`);
      if (saved) {
        setFavorites(JSON.parse(saved));
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
    }
  };

  const loadHistory = () => {
    try {
      const saved = localStorage.getItem(`address_history_${addressType}`);
      if (saved) {
        setHistory(JSON.parse(saved).slice(0, 10)); // Keep only last 10
      }
    } catch (error) {
      console.error('Error loading history:', error);
    }
  };

  const saveFavorite = (address) => {
    try {
      const newFavorites = [...favorites, { ...address, id: Date.now() }];
      setFavorites(newFavorites);
      localStorage.setItem(`address_favorites_${addressType}`, JSON.stringify(newFavorites));
      toast.success('Address saved to favorites');
    } catch (error) {
      console.error('Error saving favorite:', error);
      toast.error('Failed to save favorite');
    }
  };

  const saveToHistory = (address) => {
    try {
      const newHistory = [address, ...history.filter(h => h.place_id !== address.place_id)].slice(0, 10);
      setHistory(newHistory);
      localStorage.setItem(`address_history_${addressType}`, JSON.stringify(newHistory));
    } catch (error) {
      console.error('Error saving to history:', error);
    }
  };

  const removeFavorite = (id) => {
    try {
      const newFavorites = favorites.filter(f => f.id !== id);
      setFavorites(newFavorites);
      localStorage.setItem(`address_favorites_${addressType}`, JSON.stringify(newFavorites));
      toast.success('Removed from favorites');
    } catch (error) {
      console.error('Error removing favorite:', error);
    }
  };

  const fetchPredictions = useCallback((input) => {
    if (!autocompleteService.current || !input || input.length < 3) {
      setPredictions([]);
      return;
    }

    setLoading(true);
    
    const request = {
      input: input,
      componentRestrictions: { country: 'IN' },
      types: ['address'],
      fields: ['place_id', 'description', 'types']
    };

    autocompleteService.current.getPlacePredictions(request, (predictions, status) => {
      setLoading(false);
      
      if (status === window.google.maps.places.PlacesServiceStatus.OK && predictions) {
        setPredictions(predictions.map(prediction => ({
          place_id: prediction.place_id,
          description: prediction.description,
          types: prediction.types,
          structured_formatting: prediction.structured_formatting
        })));
      } else {
        setPredictions([]);
      }
    });
  }, []);

  const handleInputChange = (event, newValue) => {
    setInputValue(newValue);
    setShowSuggestions(true);
    
    if (onChange) {
      onChange(newValue);
    }

    // Debounce API calls
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }
    
    debounceTimer.current = setTimeout(() => {
      fetchPredictions(newValue);
    }, 300);
  };

  const getPlaceDetails = (placeId) => {
    if (!placesService.current) return;

    const request = {
      placeId: placeId,
      fields: [
        'place_id',
        'formatted_address',
        'geometry',
        'address_components',
        'name',
        'types',
        'plus_code'
      ]
    };

    placesService.current.getDetails(request, (place, status) => {
      if (status === window.google.maps.places.PlacesServiceStatus.OK && place) {
        const addressData = {
          place_id: place.place_id,
          formatted_address: place.formatted_address,
          name: place.name,
          coordinates: {
            lat: place.geometry.location.lat(),
            lng: place.geometry.location.lng()
          },
          address_components: place.address_components,
          types: place.types,
          plus_code: place.plus_code
        };

        setSelectedPlace(addressData);
        setInputValue(place.formatted_address);
        setShowSuggestions(false);
        
        // Save to history
        saveToHistory(addressData);
        
        // Notify parent components
        if (onChange) onChange(place.formatted_address);
        if (onLocationSelect) onLocationSelect(addressData);
        if (onCoordinatesChange) onCoordinatesChange(addressData.coordinates);
        
        toast.success('Address selected successfully');
      } else {
        toast.error('Failed to get place details');
      }
    });
  };

  const handlePlaceSelect = (place) => {
    if (place.place_id) {
      getPlaceDetails(place.place_id);
    } else {
      // Handle favorites/history selection
      setSelectedPlace(place);
      setInputValue(place.formatted_address);
      setShowSuggestions(false);
      
      if (onChange) onChange(place.formatted_address);
      if (onLocationSelect) onLocationSelect(place);
      if (onCoordinatesChange && place.coordinates) {
        onCoordinatesChange(place.coordinates);
      }
    }
  };

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      toast.error('Geolocation is not supported by this browser');
      return;
    }

    setLoading(true);
    
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        
        if (geocoder.current) {
          geocoder.current.geocode(
            { location: { lat: latitude, lng: longitude } },
            (results, status) => {
              setLoading(false);
              
              if (status === 'OK' && results[0]) {
                const addressData = {
                  place_id: results[0].place_id,
                  formatted_address: results[0].formatted_address,
                  coordinates: { lat: latitude, lng: longitude },
                  address_components: results[0].address_components,
                  types: results[0].types,
                  isCurrentLocation: true
                };
                
                handlePlaceSelect(addressData);
                toast.success('Current location detected');
              } else {
                toast.error('Failed to get address for current location');
              }
            }
          );
        }
      },
      (error) => {
        setLoading(false);
        console.error('Geolocation error:', error);
        toast.error('Failed to get current location');
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000
      }
    );
  };

  const renderSuggestionItem = (option, index) => (
    <ListItem
      key={option.place_id || option.id || index}
      button
      onClick={() => handlePlaceSelect(option)}
      sx={{ py: 1 }}
    >
      <ListItemIcon>
        {option.isCurrentLocation ? (
          <MyLocation color="primary" />
        ) : option.isFavorite ? (
          <Star color="warning" />
        ) : option.isHistory ? (
          <History color="action" />
        ) : option.types?.includes('establishment') ? (
          <Business color="action" />
        ) : (
          <LocationOn color="action" />
        )}
      </ListItemIcon>
      <ListItemText
        primary={
          option.structured_formatting?.main_text || 
          option.name || 
          option.formatted_address?.split(',')[0]
        }
        secondary={
          option.structured_formatting?.secondary_text || 
          option.formatted_address
        }
        primaryTypographyProps={{ variant: 'body2' }}
        secondaryTypographyProps={{ variant: 'caption', color: 'text.secondary' }}
      />
      {option.isFavorite && (
        <IconButton
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            removeFavorite(option.id);
          }}
        >
          <Clear fontSize="small" />
        </IconButton>
      )}
    </ListItem>
  );

  const getSuggestions = () => {
    const suggestions = [];
    
    // Add favorites
    if (showFavorites && favorites.length > 0) {
      suggestions.push(
        <Typography key="favorites-header" variant="caption" sx={{ px: 2, py: 1, color: 'text.secondary' }}>
          Favorites
        </Typography>
      );
      favorites.forEach((fav, index) => {
        suggestions.push(renderSuggestionItem({ ...fav, isFavorite: true }, `fav-${index}`));
      });
      if (history.length > 0 || predictions.length > 0) {
        suggestions.push(<Divider key="fav-divider" />);
      }
    }
    
    // Add recent history
    if (showHistory && history.length > 0) {
      suggestions.push(
        <Typography key="history-header" variant="caption" sx={{ px: 2, py: 1, color: 'text.secondary' }}>
          Recent
        </Typography>
      );
      history.slice(0, 3).forEach((hist, index) => {
        suggestions.push(renderSuggestionItem({ ...hist, isHistory: true }, `hist-${index}`));
      });
      if (predictions.length > 0) {
        suggestions.push(<Divider key="hist-divider" />);
      }
    }
    
    // Add predictions
    if (predictions.length > 0) {
      suggestions.push(
        <Typography key="predictions-header" variant="caption" sx={{ px: 2, py: 1, color: 'text.secondary' }}>
          Suggestions
        </Typography>
      );
      predictions.forEach((prediction, index) => {
        suggestions.push(renderSuggestionItem(prediction, `pred-${index}`));
      });
    }
    
    return suggestions;
  };

  return (
    <Box sx={{ position: 'relative' }}>
      <TextField
        fullWidth
        label={label}
        value={inputValue}
        onChange={(e) => handleInputChange(e, e.target.value)}
        onFocus={() => setShowSuggestions(true)}
        error={error}
        helperText={helperText}
        required={required}
        placeholder={placeholder}
        disabled={disabled}
        InputProps={{
          endAdornment: (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              {loading && <CircularProgress size={20} />}
              
              {showGPSButton && (
                <Tooltip title="Use current location">
                  <IconButton
                    size="small"
                    onClick={getCurrentLocation}
                    disabled={loading || disabled}
                  >
                    <MyLocation fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              
              {selectedPlace && !selectedPlace.isFavorite && (
                <Tooltip title="Save to favorites">
                  <IconButton
                    size="small"
                    onClick={() => saveFavorite(selectedPlace)}
                    disabled={disabled}
                  >
                    <StarBorder fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              
              {showMapButton && (
                <Tooltip title="Select on map">
                  <IconButton
                    size="small"
                    disabled={disabled}
                  >
                    <MapIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          )
        }}
        {...props}
      />
      
      {showSuggestions && (getSuggestions().length > 0) && (
        <Paper
          sx={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            zIndex: 1300,
            maxHeight: 400,
            overflow: 'auto',
            mt: 0.5
          }}
          elevation={8}
        >
          <List dense>
            {getSuggestions()}
          </List>
        </Paper>
      )}
    </Box>
  );
};

export default AddressAutocomplete;
