import { doc, updateDoc, collection, addDoc, serverTimestamp, onSnapshot, query, where, orderBy, limit, getDocs } from 'firebase/firestore';
import { db } from '../firebase';
import { LocationTracker } from '../utils/googleMaps';

/**
 * Enhanced Firebase Location Service for real-time location tracking
 * Handles location updates, history, and real-time synchronization
 */
export class LocationService {
  constructor(userId, userRole = 'courier') {
    this.userId = userId;
    this.userRole = userRole;
    this.locationTracker = null;
    this.isTracking = false;
    this.unsubscribeCallbacks = [];
    this.locationUpdateInterval = null;
    this.lastLocationUpdate = null;
    
    // Configuration
    this.config = {
      updateInterval: 15000, // 15 seconds
      batchSize: 10, // Number of locations to batch before sending
      maxRetries: 3,
      retryDelay: 2000,
      offlineQueueSize: 100
    };
    
    // Offline queue for when network is unavailable
    this.offlineQueue = [];
    this.isOnline = navigator.onLine;
    
    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processOfflineQueue();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  /**
   * Start real-time location tracking with Firebase integration
   * @param {Object} options - Tracking options
   * @returns {Promise<boolean>} Success status
   */
  async startTracking(options = {}) {
    if (this.isTracking) {
      console.warn('Location tracking is already active');
      return true;
    }

    try {
      // Initialize location tracker
      this.locationTracker = new LocationTracker({
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 30000,
        distanceThreshold: 5, // 5 meters minimum movement
        accuracyThreshold: 50, // 50 meters maximum accuracy
        ...options
      });

      // Start tracking with callbacks
      const success = await this.locationTracker.startTracking(
        (location) => this.handleLocationUpdate(location),
        (error) => this.handleLocationError(error)
      );

      if (success) {
        this.isTracking = true;
        
        // Set up periodic Firebase updates
        this.locationUpdateInterval = setInterval(() => {
          this.syncLocationToFirebase();
        }, this.config.updateInterval);

        // Update user status to indicate tracking is active
        await this.updateUserTrackingStatus(true);
        
        console.log('🚀 Real-time location tracking started for user:', this.userId);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Failed to start location tracking:', error);
      return false;
    }
  }

  /**
   * Stop location tracking
   */
  async stopTracking() {
    if (!this.isTracking) return;

    try {
      // Stop location tracker
      if (this.locationTracker) {
        this.locationTracker.stopTracking();
        this.locationTracker = null;
      }

      // Clear interval
      if (this.locationUpdateInterval) {
        clearInterval(this.locationUpdateInterval);
        this.locationUpdateInterval = null;
      }

      // Clean up subscriptions
      this.unsubscribeCallbacks.forEach(unsubscribe => unsubscribe());
      this.unsubscribeCallbacks = [];

      // Update user status
      await this.updateUserTrackingStatus(false);

      this.isTracking = false;
      console.log('📍 Location tracking stopped for user:', this.userId);
    } catch (error) {
      console.error('Error stopping location tracking:', error);
    }
  }

  /**
   * Handle location updates from the tracker
   * @param {Object} location - Location data
   */
  handleLocationUpdate(location) {
    this.lastLocationUpdate = {
      ...location,
      userId: this.userId,
      userRole: this.userRole,
      timestamp: new Date(),
      serverTimestamp: serverTimestamp()
    };

    // If online, update immediately; otherwise queue for later
    if (this.isOnline) {
      this.syncLocationToFirebase();
    } else {
      this.queueLocationUpdate(this.lastLocationUpdate);
    }
  }

  /**
   * Handle location errors
   * @param {Error} error - Location error
   */
  handleLocationError(error) {
    console.error('Location tracking error:', error);
    
    // Attempt to restart tracking after a delay for certain errors
    if (error.code === 'TIMEOUT' || error.code === 'POSITION_UNAVAILABLE') {
      setTimeout(() => {
        if (this.isTracking) {
          console.log('Attempting to restart location tracking...');
          this.restartTracking();
        }
      }, 5000);
    }
  }

  /**
   * Sync current location to Firebase
   */
  async syncLocationToFirebase() {
    if (!this.lastLocationUpdate || !this.isOnline) return;

    try {
      // Update user document with current location
      const userRef = doc(db, 'users', this.userId);
      await updateDoc(userRef, {
        currentLocation: {
          latitude: this.lastLocationUpdate.latitude,
          longitude: this.lastLocationUpdate.longitude,
          lat: this.lastLocationUpdate.lat,
          lng: this.lastLocationUpdate.lng,
          accuracy: this.lastLocationUpdate.accuracy,
          altitude: this.lastLocationUpdate.altitude,
          heading: this.lastLocationUpdate.heading,
          speed: this.lastLocationUpdate.speed,
          clientTimestamp: this.lastLocationUpdate.clientTimestamp
        },
        lastLocationUpdate: serverTimestamp(),
        isOnline: true,
        trackingActive: true
      });

      // Add to location history
      await this.addLocationToHistory(this.lastLocationUpdate);

      console.log('📍 Location synced to Firebase successfully');
    } catch (error) {
      console.error('Failed to sync location to Firebase:', error);
      
      // Queue for retry if it's a network error
      if (error.code === 'unavailable' || error.code === 'deadline-exceeded') {
        this.queueLocationUpdate(this.lastLocationUpdate);
      }
    }
  }

  /**
   * Add location to history collection
   * @param {Object} location - Location data
   */
  async addLocationToHistory(location) {
    try {
      const historyRef = collection(db, 'locationHistory');
      await addDoc(historyRef, {
        courierId: this.userId,
        userId: this.userId,
        userRole: this.userRole,
        latitude: location.latitude,
        longitude: location.longitude,
        lat: location.lat,
        lng: location.lng,
        accuracy: location.accuracy,
        altitude: location.altitude,
        heading: location.heading,
        speed: location.speed,
        timestamp: serverTimestamp(),
        clientTimestamp: location.clientTimestamp,
        deviceInfo: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          language: navigator.language
        }
      });
    } catch (error) {
      console.error('Failed to add location to history:', error);
    }
  }

  /**
   * Update user tracking status
   * @param {boolean} isActive - Whether tracking is active
   */
  async updateUserTrackingStatus(isActive) {
    try {
      const userRef = doc(db, 'users', this.userId);
      await updateDoc(userRef, {
        trackingActive: isActive,
        lastTrackingUpdate: serverTimestamp(),
        isOnline: isActive
      });
    } catch (error) {
      console.error('Failed to update tracking status:', error);
    }
  }

  /**
   * Queue location update for offline processing
   * @param {Object} location - Location data
   */
  queueLocationUpdate(location) {
    this.offlineQueue.push(location);
    
    // Limit queue size
    if (this.offlineQueue.length > this.config.offlineQueueSize) {
      this.offlineQueue = this.offlineQueue.slice(-this.config.offlineQueueSize);
    }
    
    console.log(`📱 Location queued for offline sync (${this.offlineQueue.length} pending)`);
  }

  /**
   * Process offline queue when connection is restored
   */
  async processOfflineQueue() {
    if (this.offlineQueue.length === 0) return;

    console.log(`🔄 Processing ${this.offlineQueue.length} offline location updates...`);

    const queue = [...this.offlineQueue];
    this.offlineQueue = [];

    for (const location of queue) {
      try {
        await this.addLocationToHistory(location);
      } catch (error) {
        console.error('Failed to process offline location:', error);
        // Re-queue failed items
        this.offlineQueue.push(location);
      }
    }

    // Update current location with the latest one
    if (queue.length > 0) {
      const latestLocation = queue[queue.length - 1];
      this.lastLocationUpdate = latestLocation;
      await this.syncLocationToFirebase();
    }

    console.log('✅ Offline queue processed');
  }

  /**
   * Restart tracking after an error
   */
  async restartTracking() {
    await this.stopTracking();
    setTimeout(async () => {
      await this.startTracking();
    }, 2000);
  }

  /**
   * Get current location once
   * @returns {Promise<Object>} Current location
   */
  async getCurrentLocation() {
    if (!this.locationTracker) {
      this.locationTracker = new LocationTracker();
    }
    
    return await this.locationTracker.getCurrentLocation();
  }

  /**
   * Get tracking statistics
   * @returns {Object} Tracking stats
   */
  getTrackingStats() {
    if (!this.locationTracker) {
      return {
        isTracking: false,
        stats: null
      };
    }

    return {
      isTracking: this.isTracking,
      stats: this.locationTracker.getStats(),
      offlineQueueSize: this.offlineQueue.length,
      isOnline: this.isOnline
    };
  }

  /**
   * Subscribe to real-time location updates for a user
   * @param {string} userId - User ID to track
   * @param {Function} callback - Callback for location updates
   * @returns {Function} Unsubscribe function
   */
  subscribeToUserLocation(userId, callback) {
    const userRef = doc(db, 'users', userId);
    
    const unsubscribe = onSnapshot(userRef, (doc) => {
      if (doc.exists()) {
        const data = doc.data();
        if (data.currentLocation) {
          callback({
            userId: userId,
            location: data.currentLocation,
            lastUpdate: data.lastLocationUpdate?.toDate(),
            isOnline: data.isOnline,
            trackingActive: data.trackingActive
          });
        }
      }
    });

    this.unsubscribeCallbacks.push(unsubscribe);
    return unsubscribe;
  }

  /**
   * Get location history for a user
   * @param {string} userId - User ID
   * @param {number} limitCount - Number of records to fetch
   * @returns {Promise<Array>} Location history
   */
  async getLocationHistory(userId, limitCount = 50) {
    try {
      const historyRef = collection(db, 'locationHistory');
      const q = query(
        historyRef,
        where('userId', '==', userId),
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate()
      }));
    } catch (error) {
      console.error('Failed to get location history:', error);
      return [];
    }
  }
}

// Export singleton instance
export const createLocationService = (userId, userRole) => {
  return new LocationService(userId, userRole);
};
