import { doc, updateDoc, serverTimestamp, collection, addDoc } from 'firebase/firestore';
import { db } from '../firebase';

/**
 * Get the current position using the Geolocation API
 * @param {Object} options - Geolocation options
 * @returns {Promise<Object>} Position object with lat, lng, accuracy, timestamp
 */
export const getCurrentPosition = (options = {}) => {
  const defaultOptions = {
    enableHighAccuracy: true,
    timeout: 10000,
    maximumAge: 60000, // 1 minute
    ...options
  };

  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        resolve({
          lat: latitude,
          lng: longitude,
          latitude,
          longitude,
          accuracy,
          timestamp: new Date().toISOString(),
          clientTimestamp: Date.now()
        });
      },
      (error) => {
        let errorMessage = 'Failed to get location';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out';
            break;
          default:
            errorMessage = 'An unknown error occurred while retrieving location';
            break;
        }
        reject(new Error(errorMessage));
      },
      defaultOptions
    );
  });
};

/**
 * Watch the user's position and call a callback with updates
 * @param {Function} callback - Function to call with position updates
 * @param {Object} options - Geolocation options
 * @returns {number} Watch ID that can be used to clear the watch
 */
export const watchPosition = (callback, options = {}) => {
  const defaultOptions = {
    enableHighAccuracy: true,
    timeout: 10000,
    maximumAge: 30000, // 30 seconds
    ...options
  };

  if (!navigator.geolocation) {
    throw new Error('Geolocation is not supported by this browser');
  }

  return navigator.geolocation.watchPosition(
    (position) => {
      const { latitude, longitude, accuracy } = position.coords;
      const locationData = {
        lat: latitude,
        lng: longitude,
        latitude,
        longitude,
        accuracy,
        timestamp: new Date().toISOString(),
        clientTimestamp: Date.now()
      };
      callback(locationData);
    },
    (error) => {
      console.error('Location watch error:', error);
      callback(null, error);
    },
    defaultOptions
  );
};

/**
 * Clear a position watch
 * @param {number} watchId - The watch ID returned by watchPosition
 */
export const clearPositionWatch = (watchId) => {
  if (navigator.geolocation && watchId) {
    navigator.geolocation.clearWatch(watchId);
  }
};

/**
 * Update courier location in Firebase
 * @param {string} courierId - The courier's user ID
 * @param {Object} location - Location object with lat, lng, accuracy, timestamp
 * @returns {Promise<void>}
 */
export const updateCourierLocation = async (courierId, location) => {
  if (!courierId || !location) {
    throw new Error('Courier ID and location are required');
  }

  try {
    const userRef = doc(db, 'users', courierId);
    
    await updateDoc(userRef, {
      currentLocation: {
        latitude: location.lat || location.latitude,
        longitude: location.lng || location.longitude,
        lat: location.lat || location.latitude,
        lng: location.lng || location.longitude,
        accuracy: location.accuracy || 0,
        timestamp: location.timestamp || new Date().toISOString(),
        clientTimestamp: location.clientTimestamp || Date.now()
      },
      lastLocationUpdate: serverTimestamp(),
      isOnline: true
    });

    console.log(`Location updated for courier ${courierId}`);
  } catch (error) {
    console.error('Error updating courier location:', error);
    throw error;
  }
};

/**
 * Save location to history for tracking purposes
 * @param {string} courierId - The courier's user ID
 * @param {Object} location - Location object
 * @returns {Promise<void>}
 */
export const saveLocationHistory = async (courierId, location) => {
  if (!courierId || !location) {
    return;
  }

  try {
    const historyRef = collection(db, 'locationHistory');
    
    await addDoc(historyRef, {
      courierId,
      latitude: location.lat || location.latitude,
      longitude: location.lng || location.longitude,
      accuracy: location.accuracy || 0,
      timestamp: serverTimestamp(),
      clientTimestamp: location.clientTimestamp || Date.now()
    });

    console.log(`Location history saved for courier ${courierId}`);
  } catch (error) {
    console.error('Error saving location history:', error);
    // Don't throw error for history saving as it's not critical
  }
};

/**
 * Start location tracking for a courier
 * @param {string} courierId - The courier's user ID
 * @param {Object} options - Tracking options
 * @returns {Object} Object with stop function and current watch ID
 */
export const startLocationTracking = (courierId, options = {}) => {
  const trackingOptions = {
    updateInterval: 30000, // 30 seconds
    saveHistory: true,
    ...options
  };

  let watchId = null;
  let lastUpdate = 0;

  const handleLocationUpdate = async (location, error) => {
    if (error) {
      console.error('Location tracking error:', error);
      return;
    }

    if (!location) return;

    const now = Date.now();
    
    // Throttle updates based on interval
    if (now - lastUpdate < trackingOptions.updateInterval) {
      return;
    }

    lastUpdate = now;

    try {
      // Update current location
      await updateCourierLocation(courierId, location);

      // Save to history if enabled
      if (trackingOptions.saveHistory) {
        await saveLocationHistory(courierId, location);
      }
    } catch (error) {
      console.error('Error in location tracking:', error);
    }
  };

  // Start watching position
  try {
    watchId = watchPosition(handleLocationUpdate, {
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 30000
    });

    console.log(`Started location tracking for courier ${courierId}`);
  } catch (error) {
    console.error('Failed to start location tracking:', error);
  }

  // Return control object
  return {
    stop: () => {
      if (watchId) {
        clearPositionWatch(watchId);
        console.log(`Stopped location tracking for courier ${courierId}`);
      }
    },
    watchId
  };
};

/**
 * Check if location services are available and permissions are granted
 * @returns {Promise<boolean>} True if location is available
 */
export const checkLocationAvailability = async () => {
  if (!navigator.geolocation) {
    return false;
  }

  try {
    await getCurrentPosition({ timeout: 5000 });
    return true;
  } catch (error) {
    console.warn('Location not available:', error.message);
    return false;
  }
};
