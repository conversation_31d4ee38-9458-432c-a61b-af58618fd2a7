import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  Divider,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  useTheme,
  Alert,
  Chip,
} from '@mui/material';
import { useParams, useNavigate } from 'react-router-dom';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '../../firebase';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';

const OrderConfirmForm = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const theme = useTheme();

  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [dispatchDate, setDispatchDate] = useState(null);

  // Fetch order details
  useEffect(() => {
    const fetchOrder = async () => {
      try {
        setLoading(true);
        const orderDoc = await getDoc(doc(db, 'orders', orderId));

        if (orderDoc.exists()) {
          const orderData = {
            id: orderDoc.id,
            ...orderDoc.data(),
            createdAt: orderDoc.data().createdAt?.toDate() || new Date(),
            dispatchDate: orderDoc.data().dispatchDate?.toDate() || null,
          };
          setOrder(orderData);
          setDispatchDate(orderData.dispatchDate);
        } else {
          setError('Order not found');
        }
      } catch (err) {
        console.error('Error fetching order:', err);
        setError('Failed to load order details');
      } finally {
        setLoading(false);
      }
    };

    if (orderId) {
      fetchOrder();
    }
  }, [orderId]);

  const handleUpdateOrder = async () => {
    try {
      setUpdating(true);
      setError('');
      setSuccess('');

      const orderRef = doc(db, 'orders', orderId);
      await updateDoc(orderRef, {
        dispatchDate: dispatchDate,
        // Add other fields that need to be updated
      });

      setSuccess('Order updated successfully');
    } catch (err) {
      console.error('Error updating order:', err);
      setError('Failed to update order');
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !order) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="error" gutterBottom>
          {error}
        </Typography>
        <Button variant="outlined" onClick={() => navigate('/orders/list')}>
          Back to Orders
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Order Confirm Form
        </Typography>

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Order Info */}
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Order No."
              value={order?.orderNumber || ''}
              InputProps={{ readOnly: true }}
              variant="outlined"
              margin="normal"
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Order Date"
              value={order?.createdAt?.toLocaleString() || ''}
              InputProps={{ readOnly: true }}
              variant="outlined"
              margin="normal"
              size="small"
            />
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 2 }}>
              <Typography variant="subtitle1">Customer Detail</Typography>
            </Divider>
          </Grid>

          {/* Customer Details */}
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Customer Name"
              value={order?.customerName || ''}
              InputProps={{ readOnly: true }}
              variant="outlined"
              margin="normal"
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Phone"
              value={order?.customerPhone || ''}
              InputProps={{ readOnly: true }}
              variant="outlined"
              margin="normal"
              size="small"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Address"
              value={order?.deliveryAddress || ''}
              InputProps={{ readOnly: true }}
              variant="outlined"
              margin="normal"
              size="small"
              multiline
              rows={2}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="City"
              value={order?.city || ''}
              InputProps={{ readOnly: true }}
              variant="outlined"
              margin="normal"
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="District"
              value={order?.district || ''}
              InputProps={{ readOnly: true }}
              variant="outlined"
              margin="normal"
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="State"
              value={order?.state || ''}
              InputProps={{ readOnly: true }}
              variant="outlined"
              margin="normal"
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Pin Code"
              value={order?.pinCode || ''}
              InputProps={{ readOnly: true }}
              variant="outlined"
              margin="normal"
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              fullWidth
              label="Email ID"
              value={order?.email || ''}
              InputProps={{ readOnly: true }}
              variant="outlined"
              margin="normal"
              size="small"
            />
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 2 }}>
              <Typography variant="subtitle1">Product Details</Typography>
            </Divider>
          </Grid>

          {/* Product Details */}
          <Grid item xs={12}>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow sx={{ backgroundColor: theme.palette.action.hover }}>
                    <TableCell>Sr. No.</TableCell>
                    <TableCell>Product</TableCell>
                    <TableCell>Code</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell align="right">Price</TableCell>
                    <TableCell align="center">Delivered</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell>1</TableCell>
                    <TableCell>{order?.productName || 'Product'}</TableCell>
                    <TableCell>{order?.productCode || 'N/A'}</TableCell>
                    <TableCell align="right">{order?.quantity || 1}</TableCell>
                    <TableCell align="right">₹{order?.amount || order?.totalPrice || '0.00'}</TableCell>
                    <TableCell align="center">
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={order?.status === 'delivered'}
                            disabled={true}
                          />
                        }
                        label=""
                      />
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Delivery Charge"
              value={order?.deliveryCharge || '0'}
              InputProps={{ readOnly: true }}
              variant="outlined"
              margin="normal"
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Dispatch Date"
                value={dispatchDate}
                onChange={(newValue) => setDispatchDate(newValue)}
                renderInput={(props) => (
                  <TextField
                    {...props}
                    fullWidth
                    variant="outlined"
                    margin="normal"
                    size="small"
                  />
                )}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Net Amount"
              value={order?.amount || order?.totalPrice || '0.00'}
              InputProps={{ readOnly: true }}
              variant="outlined"
              margin="normal"
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
              <Typography variant="body1" sx={{ mr: 2 }}>
                Current Status:
              </Typography>
              <Chip
                label={order?.status || 'Pending'}
                color={getStatusColor(order?.status)}
                size="small"
              />
            </Box>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Remarks"
              value={order?.remarks || ''}
              multiline
              rows={2}
              variant="outlined"
              margin="normal"
              size="small"
            />
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleUpdateOrder}
                disabled={updating}
                sx={{ mr: 2 }}
              >
                {updating ? <CircularProgress size={24} /> : 'Update'}
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate('/orders/list')}
              >
                Cancel
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

// Helper function to get status color
const getStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case "delivered":
      return "success";
    case "in transit":
    case "in_transit":
    case "shipped":
    case "ready_to_cargo":
      return "info";
    case "pending":
    case "processing":
      return "warning";
    case "cancelled":
    case "failed":
    case "deleted":
      return "error";
    default:
      return "default";
  }
};

export default OrderConfirmForm;
