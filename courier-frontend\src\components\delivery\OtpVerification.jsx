import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogA<PERSON>,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress,
  IconButton,
  InputAdornment,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Chip,
  Divider
} from '@mui/material';
import {
  Phone as PhoneIcon,
  Sms as SmsIcon,
  Refresh as RefreshIcon,
  Security as SecurityIcon,
  Timer as TimerIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { sendOTPForOrder, verifyOTPForOrder } from '../../utils/otpService';

/**
 * Enhanced OTP Verification Component for Delivery Confirmation
 * Supports both SMS and Voice OTP delivery methods
 */
const OtpVerification = ({
  open,
  onClose,
  orderId,
  customerPhone,
  customerName = 'Customer',
  onVerificationSuccess,
  onVerificationError
}) => {
  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [sessionId, setSessionId] = useState(null);
  const [formattedPhone, setFormattedPhone] = useState('');
  const [deliveryMethod, setDeliveryMethod] = useState('sms');
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [canResend, setCanResend] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const maxAttempts = 3;

  // Timer effect for resend cooldown
  useEffect(() => {
    let interval;
    if (timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [timeRemaining]);

  // Reset state when dialog opens
  useEffect(() => {
    if (open) {
      setOtp('');
      setError('');
      setSuccess('');
      setAttempts(0);
      setCanResend(false);
      setTimeRemaining(0);
      // Auto-send OTP when dialog opens
      if (orderId && customerPhone) {
        handleSendOtp();
      }
    }
  }, [open, orderId, customerPhone]);

  /**
   * Send OTP to customer
   */
  const handleSendOtp = async () => {
    if (!orderId || !customerPhone) {
      setError('Order ID and customer phone are required');
      return;
    }

    setSending(true);
    setError('');
    setSuccess('');

    try {
      const result = await sendOTPForOrder(orderId, customerPhone, customerName, deliveryMethod);

      if (result.success) {
        setSessionId(result.sessionId);
        setFormattedPhone(result.formattedPhone);
        setSuccess(`OTP sent successfully via ${deliveryMethod.toUpperCase()} to ${result.formattedPhone}`);
        setTimeRemaining(30); // 30 seconds cooldown
        setCanResend(false);

        if (result.reused) {
          setSuccess(prev => prev + ' (Reusing recent OTP)');
        }
      } else {
        setError(result.error || 'Failed to send OTP');
      }
    } catch (error) {
      console.error('Error sending OTP:', error);
      setError('Failed to send OTP. Please try again.');
    } finally {
      setSending(false);
    }
  };

  /**
   * Verify OTP
   */
  const handleVerifyOtp = async () => {
    if (!otp.trim()) {
      setError('Please enter the OTP');
      return;
    }

    if (otp.length !== 6) {
      setError('OTP must be 6 digits');
      return;
    }

    if (attempts >= maxAttempts) {
      setError('Maximum verification attempts exceeded');
      return;
    }

    if (!orderId) {
      setError('Order ID is missing');
      return;
    }

    setLoading(true);
    setError('');

    try {
      console.log(`Verifying OTP ${otp} for order ${orderId}`);
      const result = await verifyOTPForOrder(orderId, otp);

      console.log('OTP verification result:', result);

      if (result.success) {
        setSuccess('OTP verified successfully!');
        setTimeout(() => {
          onVerificationSuccess && onVerificationSuccess(orderId);
          onClose();
        }, 1000);
      } else {
        setAttempts(prev => prev + 1);
        const errorMessage = result.error || 'Invalid OTP';
        console.error('OTP verification failed:', errorMessage);
        setError(errorMessage);
        setOtp('');

        if (attempts + 1 >= maxAttempts) {
          setError('Maximum verification attempts exceeded. Please try again later.');
          setTimeout(() => {
            onVerificationError && onVerificationError(orderId, 'Max attempts exceeded');
            onClose();
          }, 2000);
        }
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      setError('Failed to verify OTP. Please try again.');
      setAttempts(prev => prev + 1);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle delivery method change
   */
  const handleDeliveryMethodChange = (event) => {
    setDeliveryMethod(event.target.value);
  };

  /**
   * Handle resend OTP
   */
  const handleResendOtp = () => {
    if (canResend) {
      handleSendOtp();
    }
  };

  /**
   * Handle close dialog
   */
  const handleClose = () => {
    setOtp('');
    setError('');
    setSuccess('');
    setAttempts(0);
    onClose();
  };

  /**
   * Handle OTP input change
   */
  const handleOtpChange = (event) => {
    const value = event.target.value.replace(/\D/g, '').slice(0, 6);
    setOtp(value);
  };

  /**
   * Handle key press for OTP input
   */
  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && otp.length === 6) {
      handleVerifyOtp();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 1
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        pb: 1
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SecurityIcon color="primary" />
          <Typography variant="h6" fontWeight="600">
            OTP Verification
          </Typography>
        </Box>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 2 }}>
        {/* Order Info */}
        <Box sx={{ mb: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Order ID: <strong>{orderId}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Customer: <strong>{customerName}</strong>
          </Typography>
          {formattedPhone && (
            <Typography variant="body2" color="text.secondary">
              Phone: <strong>{formattedPhone}</strong>
            </Typography>
          )}
        </Box>

        {/* Delivery Method Selection */}
        <FormControl component="fieldset" sx={{ mb: 3 }}>
          <FormLabel component="legend" sx={{ mb: 1 }}>
            Delivery Method
          </FormLabel>
          <RadioGroup
            row
            value={deliveryMethod}
            onChange={handleDeliveryMethodChange}
          >
            <FormControlLabel
              value="sms"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SmsIcon fontSize="small" />
                  SMS
                </Box>
              }
            />
            <FormControlLabel
              value="call"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PhoneIcon fontSize="small" />
                  Voice Call
                </Box>
              }
            />
          </RadioGroup>
        </FormControl>

        <Divider sx={{ my: 2 }} />

        {/* Status Messages */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {/* OTP Input */}
        <TextField
          fullWidth
          label="Enter 6-digit OTP"
          value={otp}
          onChange={handleOtpChange}
          onKeyPress={handleKeyPress}
          placeholder="000000"
          inputProps={{
            maxLength: 6,
            style: { textAlign: 'center', fontSize: '1.5rem', letterSpacing: '0.5rem' }
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SecurityIcon color="action" />
              </InputAdornment>
            )
          }}
          sx={{ mb: 2 }}
          disabled={loading || sending}
        />

        {/* Attempts Counter */}
        {attempts > 0 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
            <Chip
              label={`${attempts}/${maxAttempts} attempts used`}
              color={attempts >= maxAttempts ? 'error' : 'warning'}
              size="small"
            />
          </Box>
        )}

        {/* Resend Timer */}
        {timeRemaining > 0 && (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 2 }}>
            <TimerIcon fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary">
              Resend available in {timeRemaining} seconds
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button
          onClick={handleResendOtp}
          disabled={!canResend || sending || timeRemaining > 0}
          startIcon={sending ? <CircularProgress size={16} /> : <RefreshIcon />}
          variant="outlined"
        >
          {sending ? 'Sending...' : 'Resend OTP'}
        </Button>

        <Button
          onClick={handleVerifyOtp}
          disabled={otp.length !== 6 || loading || attempts >= maxAttempts}
          startIcon={loading ? <CircularProgress size={16} /> : <SecurityIcon />}
          variant="contained"
          color="primary"
        >
          {loading ? 'Verifying...' : 'Verify OTP'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default OtpVerification;