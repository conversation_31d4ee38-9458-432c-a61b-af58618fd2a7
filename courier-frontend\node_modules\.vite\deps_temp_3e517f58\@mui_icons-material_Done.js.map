{"version": 3, "sources": ["../../@mui/icons-material/Done.js"], "sourcesContent": ["\"use strict\";\n\"use client\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _createSvgIcon = _interopRequireDefault(require(\"./utils/createSvgIcon\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nvar _default = exports.default = (0, _createSvgIcon.default)( /*#__PURE__*/(0, _jsxRuntime.jsx)(\"path\", {\n  d: \"M9 16.2 4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4z\"\n}), 'Done');"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAGA,QAAI,yBAAyB;AAC7B,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB,uBAAuB,uBAAgC;AAC5E,QAAI,cAAc;AAClB,QAAI,WAAW,QAAQ,WAAW,GAAG,eAAe,UAAwB,GAAG,YAAY,KAAK,QAAQ;AAAA,MACtG,GAAG;AAAA,IACL,CAAC,GAAG,MAAM;AAAA;AAAA;", "names": []}