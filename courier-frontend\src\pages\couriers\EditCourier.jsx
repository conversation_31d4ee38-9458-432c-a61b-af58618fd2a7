import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Divider,
  useTheme,
  useMediaQuery,
  Snackbar
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  ArrowBack as ArrowBackIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  DirectionsCar as CarIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { doc, getDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../../firebase';
import { useAuth } from '../../context/AuthContext';
import AdminSidebar from '../../components/layout/AdminSidebar';
import TopNavbar from '../../components/layout/TopNavbar';

const vehicleTypes = [
  'Motorcycle',
  'Car',
  'Van',
  'Bicycle',
  'Truck',
  'Scooter',
  'Other'
];

const EditCourier = () => {
  const { courierId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [mobileOpen, setMobileOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    vehicleType: '',
    vehicleModel: '',
    licensePlate: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
    status: 'active',
    notes: ''
  });

  const [originalData, setOriginalData] = useState({});

  const handleDrawerToggle = () => setMobileOpen(!mobileOpen);

  // Fetch courier data
  useEffect(() => {
    const fetchCourierData = async () => {
      setLoading(true);
      setError('');

      try {
        // First try to fetch from users collection
        let courierData = null;

        try {
          // Try by direct ID first
          const userDocRef = doc(db, 'users', courierId);
          const userDoc = await getDoc(userDocRef);

          if (userDoc.exists()) {
            courierData = {
              id: userDoc.id,
              ...userDoc.data()
            };
          } else {
            // If not found by direct ID, try querying by UID
            const usersRef = collection(db, 'users');
            const q = query(usersRef, where('uid', '==', courierId));
            const querySnapshot = await getDocs(q);

            if (!querySnapshot.empty) {
              const doc = querySnapshot.docs[0];
              courierData = {
                id: doc.id,
                ...doc.data()
              };
            }
          }
        } catch (err) {
          console.error('Error fetching from users collection:', err);
        }

        // If not found in users collection, try couriers collection
        if (!courierData) {
          try {
            const courierDocRef = doc(db, 'couriers', courierId);
            const courierDoc = await getDoc(courierDocRef);

            if (courierDoc.exists()) {
              courierData = {
                id: courierDoc.id,
                ...courierDoc.data()
              };
            }
          } catch (err) {
            console.error('Error fetching from couriers collection:', err);
          }
        }

        if (courierData) {
          // Normalize field names
          const normalizedData = {
            fullName: courierData.fullName || courierData.name || '',
            email: courierData.email || '',
            phone: courierData.phone || '',
            vehicleType: courierData.vehicleType || courierData.vehicle || '',
            vehicleModel: courierData.vehicleModel || '',
            licensePlate: courierData.licensePlate || '',
            address: courierData.address || '',
            city: courierData.city || '',
            state: courierData.state || '',
            zipCode: courierData.zipCode || '',
            country: courierData.country || '',
            status: courierData.status || 'active',
            notes: courierData.notes || ''
          };

          setFormData(normalizedData);
          setOriginalData(courierData);
        } else {
          setError('Courier not found');
        }
      } catch (err) {
        console.error('Error fetching courier data:', err);
        setError('Failed to load courier data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    if (courierId) {
      fetchCourierData();
    }
  }, [courierId]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError('');

    try {
      // Determine which collection to update
      let docRef;

      if (originalData.id) {
        if ('uid' in originalData) {
          // It's from users collection
          docRef = doc(db, 'users', originalData.id);
        } else {
          // It's from couriers collection
          docRef = doc(db, 'couriers', originalData.id);
        }

        // Prepare update data
        const updateData = {
          // Use appropriate field name based on original data
          [originalData.fullName ? 'fullName' : 'name']: formData.fullName,
          email: formData.email,
          phone: formData.phone,
          [originalData.vehicleType ? 'vehicleType' : 'vehicle']: formData.vehicleType,
          vehicleModel: formData.vehicleModel,
          licensePlate: formData.licensePlate,
          address: formData.address,
          city: formData.city,
          state: formData.state,
          zipCode: formData.zipCode,
          country: formData.country,
          status: formData.status,
          notes: formData.notes,
          updatedAt: new Date()
        };

        await updateDoc(docRef, updateData);

        setSnackbar({
          open: true,
          message: 'Courier updated successfully',
          severity: 'success'
        });

        // Navigate back after successful update
        setTimeout(() => {
          navigate('/couriers/list');
        }, 2000);
      } else {
        setError('Cannot update courier: Original data not found');
      }
    } catch (err) {
      console.error('Error updating courier:', err);
      setError(`Failed to update courier: ${err.message}`);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate('/couriers/list');
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <AdminSidebar
        mobileOpen={mobileOpen}
        handleDrawerToggle={handleDrawerToggle}
      />
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <TopNavbar handleDrawerToggle={handleDrawerToggle} />
        <Container maxWidth="lg" sx={{ mt: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h4" fontWeight="bold" color="primary">
              Edit Courier
            </Typography>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={handleCancel}
            >
              Back to List
            </Button>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {success}
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Paper sx={{ p: 3 }}>
              <form onSubmit={handleSubmit}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      <PersonIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Personal Information
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleInputChange}
                      required
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Phone Number"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      required
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select
                        name="status"
                        value={formData.status}
                        onChange={handleInputChange}
                        label="Status"
                      >
                        <MenuItem value="active">Active</MenuItem>
                        <MenuItem value="inactive">Inactive</MenuItem>
                        <MenuItem value="on_leave">On Leave</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                      <CarIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Vehicle Information
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Vehicle Type</InputLabel>
                      <Select
                        name="vehicleType"
                        value={formData.vehicleType}
                        onChange={handleInputChange}
                        label="Vehicle Type"
                      >
                        {vehicleTypes.map(type => (
                          <MenuItem key={type} value={type}>{type}</MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Vehicle Model"
                      name="vehicleModel"
                      value={formData.vehicleModel}
                      onChange={handleInputChange}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="License Plate"
                      name="licensePlate"
                      value={formData.licensePlate}
                      onChange={handleInputChange}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                      <LocationIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Address
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Address"
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                    />
                  </Grid>

                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="City"
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                    />
                  </Grid>

                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="State/Province"
                      name="state"
                      value={formData.state}
                      onChange={handleInputChange}
                    />
                  </Grid>

                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Zip/Postal Code"
                      name="zipCode"
                      value={formData.zipCode}
                      onChange={handleInputChange}
                    />
                  </Grid>

                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Country"
                      name="country"
                      value={formData.country}
                      onChange={handleInputChange}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Notes"
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      multiline
                      rows={4}
                    />
                  </Grid>

                  <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<CancelIcon />}
                      onClick={handleCancel}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      startIcon={<SaveIcon />}
                      disabled={saving}
                    >
                      {saving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </Grid>
                </Grid>
              </form>
            </Paper>
          )}
        </Container>
      </Box>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        message={snackbar.message}
      />
    </Box>
  );
};

export default EditCourier;
