import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Alert,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemText,
  Divider,
  LinearProgress
} from '@mui/material';
import {
  LocationOn,
  LocationOff,
  GpsFixed,
  GpsNotFixed,
  CheckCircle,
  Error,
  Warning,
  Info
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { useLocationTracking, useMultiUserTracking } from '../../hooks/useLocationTracking';
import { LocationService } from '../../services/locationService';

/**
 * Location Test Panel - For testing and debugging location tracking functionality
 */
const LocationTestPanel = () => {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [locationService, setLocationService] = useState(null);

  const {
    isTracking,
    currentLocation,
    error,
    stats,
    permissionStatus,
    startTracking,
    stopTracking,
    getCurrentLocation,
    requestPermission,
    isSupported
  } = useLocationTracking();

  const {
    userLocations,
    loading: multiUserLoading,
    activeUsers
  } = useMultiUserTracking([user?.uid].filter(Boolean));

  // Initialize location service
  useEffect(() => {
    if (user?.uid) {
      const service = new LocationService(user.uid, user.role || 'courier');
      setLocationService(service);
    }
  }, [user]);

  const addTestResult = (test, status, message, details = null) => {
    setTestResults(prev => [...prev, {
      id: Date.now(),
      test,
      status, // 'success', 'error', 'warning', 'info'
      message,
      details,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const clearTestResults = () => {
    setTestResults([]);
  };

  const runComprehensiveTest = async () => {
    setIsRunningTests(true);
    clearTestResults();

    try {
      // Test 1: Browser Support
      addTestResult(
        'Browser Support',
        isSupported ? 'success' : 'error',
        isSupported ? 'Geolocation API is supported' : 'Geolocation API is not supported',
        { isSupported }
      );

      if (!isSupported) {
        setIsRunningTests(false);
        return;
      }

      // Test 2: Permission Status
      addTestResult(
        'Permission Status',
        permissionStatus === 'granted' ? 'success' : permissionStatus === 'denied' ? 'error' : 'warning',
        `Permission status: ${permissionStatus}`,
        { permissionStatus }
      );

      // Test 3: Request Permission (if needed)
      if (permissionStatus === 'prompt') {
        try {
          await requestPermission();
          addTestResult(
            'Permission Request',
            'success',
            'Permission requested successfully'
          );
        } catch (err) {
          addTestResult(
            'Permission Request',
            'error',
            'Failed to request permission',
            { error: err.message }
          );
        }
      }

      // Test 4: Get Current Location
      try {
        await getCurrentLocation();
        addTestResult(
          'Get Current Location',
          'success',
          'Successfully retrieved current location',
          { location: currentLocation }
        );
      } catch (err) {
        addTestResult(
          'Get Current Location',
          'error',
          'Failed to get current location',
          { error: err.message }
        );
      }

      // Test 5: Start Tracking
      if (permissionStatus === 'granted') {
        try {
          const success = await startTracking({
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 30000
          });
          
          addTestResult(
            'Start Tracking',
            success ? 'success' : 'error',
            success ? 'Location tracking started' : 'Failed to start tracking'
          );

          // Wait a bit for location updates
          await new Promise(resolve => setTimeout(resolve, 3000));

          // Test 6: Check for Location Updates
          if (currentLocation) {
            addTestResult(
              'Location Updates',
              'success',
              'Receiving location updates',
              {
                accuracy: currentLocation.accuracy,
                timestamp: currentLocation.timestamp
              }
            );
          } else {
            addTestResult(
              'Location Updates',
              'warning',
              'No location updates received yet'
            );
          }

          // Test 7: Firebase Integration
          if (locationService) {
            try {
              await locationService.updateLocation({
                lat: currentLocation?.lat || 19.0760,
                lng: currentLocation?.lng || 72.8777,
                accuracy: currentLocation?.accuracy || 100,
                timestamp: Date.now()
              });
              
              addTestResult(
                'Firebase Integration',
                'success',
                'Successfully updated location in Firebase'
              );
            } catch (err) {
              addTestResult(
                'Firebase Integration',
                'error',
                'Failed to update location in Firebase',
                { error: err.message }
              );
            }
          }

        } catch (err) {
          addTestResult(
            'Start Tracking',
            'error',
            'Failed to start tracking',
            { error: err.message }
          );
        }
      }

      // Test 8: Multi-User Tracking
      if (userLocations && Object.keys(userLocations).length > 0) {
        addTestResult(
          'Multi-User Tracking',
          'success',
          `Tracking ${Object.keys(userLocations).length} users`,
          { userCount: Object.keys(userLocations).length }
        );
      } else {
        addTestResult(
          'Multi-User Tracking',
          'info',
          'No other users being tracked'
        );
      }

      // Test 9: Statistics
      if (stats) {
        addTestResult(
          'Statistics',
          'success',
          'Location statistics available',
          stats
        );
      } else {
        addTestResult(
          'Statistics',
          'info',
          'No statistics available yet'
        );
      }

    } catch (err) {
      addTestResult(
        'Test Suite',
        'error',
        'Test suite encountered an error',
        { error: err.message }
      );
    }

    setIsRunningTests(false);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return <CheckCircle color="success" />;
      case 'error': return <Error color="error" />;
      case 'warning': return <Warning color="warning" />;
      default: return <Info color="info" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'success';
      case 'error': return 'error';
      case 'warning': return 'warning';
      default: return 'info';
    }
  };

  return (
    <Box>
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Location Tracking Test Panel
          </Typography>
          
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={6} sm={3}>
              <Chip
                icon={isSupported ? <GpsFixed /> : <GpsNotFixed />}
                label={isSupported ? 'Supported' : 'Not Supported'}
                color={isSupported ? 'success' : 'error'}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={6} sm={3}>
              <Chip
                icon={isTracking ? <LocationOn /> : <LocationOff />}
                label={isTracking ? 'Tracking' : 'Not Tracking'}
                color={isTracking ? 'success' : 'default'}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={6} sm={3}>
              <Chip
                label={`Permission: ${permissionStatus}`}
                color={permissionStatus === 'granted' ? 'success' : permissionStatus === 'denied' ? 'error' : 'warning'}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={6} sm={3}>
              <Chip
                label={`Active Users: ${activeUsers}`}
                color="info"
                variant="outlined"
              />
            </Grid>
          </Grid>

          <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
            <Button
              variant="contained"
              onClick={runComprehensiveTest}
              disabled={isRunningTests}
            >
              Run Comprehensive Test
            </Button>
            <Button
              variant="outlined"
              onClick={clearTestResults}
              disabled={isRunningTests}
            >
              Clear Results
            </Button>
          </Box>

          {isRunningTests && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" gutterBottom>
                Running tests...
              </Typography>
              <LinearProgress />
            </Box>
          )}
        </CardContent>
      </Card>

      {testResults.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Test Results
            </Typography>
            <List>
              {testResults.map((result, index) => (
                <React.Fragment key={result.id}>
                  <ListItem>
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                      {getStatusIcon(result.status)}
                      <Box sx={{ ml: 2, flex: 1 }}>
                        <Typography variant="subtitle2">
                          {result.test}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {result.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {result.timestamp}
                        </Typography>
                        {result.details && (
                          <Box sx={{ mt: 1 }}>
                            <Typography variant="caption" component="pre" sx={{ 
                              backgroundColor: 'grey.100', 
                              p: 1, 
                              borderRadius: 1,
                              fontSize: '0.7rem',
                              overflow: 'auto'
                            }}>
                              {JSON.stringify(result.details, null, 2)}
                            </Typography>
                          </Box>
                        )}
                      </Box>
                      <Chip
                        size="small"
                        label={result.status}
                        color={getStatusColor(result.status)}
                      />
                    </Box>
                  </ListItem>
                  {index < testResults.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default LocationTestPanel;
