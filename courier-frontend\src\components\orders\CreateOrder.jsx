// courier-frontend/src/pages/orders/CreateOrder.jsx
import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Paper,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Stepper,
  Step,
  StepLabel,
  CircularProgress,
  Divider,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  useMediaQuery,
  Snackbar,
  IconButton,
  Tooltip,
  Chip, // Added missing Chip import
  Card,
  CardContent,
  InputAdornment
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  LocationOn,
  Person,
  Description,
  Payment,
  Check,
  Help,
  ArrowBack,
  Save,
  Info,
  ShoppingCart,
  Phone,
  Email,
  Home,
  Business,
  Calculate,
  Add,
  Remove,
  Send,
  ArrowForward,
  CloudUpload
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import {
  collection,
  addDoc,
  getDocs,
  query,
  where,
  serverTimestamp,
} from 'firebase/firestore';
import { db } from '../../firebase';
import { useAuth } from '../../context/AuthContext';
import { generateNumericOrderId } from '../../utils/orderUtils';
import { toast } from 'react-toastify';
import AdminLayout from "../layout/AdminLayout";
import BulkOrderImport from './BulkOrderImport';
import GoogleMapComponent from '../maps/GoogleMapComponent';
import {
  GOOGLE_MAPS_API_KEY,
  DEFAULT_CENTER,
  createMapOptions,
  calculateDistanceWithAPI,
  createAdvancedMarker,
  clearMarkers,
  GOOGLE_MAPS_LIBRARIES
} from '../../utils/googleMaps';

const validateEmail = (email) => {
  const re = /\S+@\S+\.\S+/;
  return re.test(email);
};

const validatePhone = (phone) => {
  const re = /^\d{10}$/; // basic 10-digit number check
  return re.test(phone);
};

const steps = ['Customer & Address', 'Product Details', 'Payment & Confirm'];

const packageTypes = [
  { value: 'document', label: '📄 Document', basePrice: 50, description: 'Letters, papers, certificates' },
  { value: 'small', label: '📦 Small Package', basePrice: 80, description: 'Books, accessories, small items' },
  { value: 'medium', label: '📦 Medium Package', basePrice: 120, description: 'Clothes, shoes, electronics' },
  { value: 'large', label: '📦 Large Package', basePrice: 200, description: 'Appliances, furniture parts' },
  { value: 'fragile', label: '🔸 Fragile Items', basePrice: 150, description: 'Glass, ceramics, delicate items' },
  { value: 'food', label: '🍕 Food Delivery', basePrice: 100, description: 'Restaurant orders, groceries' }
];

const priorityOptions = [
  { value: 'standard', label: '🚚 Standard (1-2 days)', multiplier: 1, color: 'default' },
  { value: 'express', label: '⚡ Express (Same day)', multiplier: 1.5, color: 'warning' },
  { value: 'urgent', label: '🚀 Urgent (2-4 hours)', multiplier: 2.5, color: 'error' }
];

const paymentMethods = [
  { value: 'cash', label: 'Cash on Delivery' },
  { value: 'card', label: 'Credit/Debit Card' },
  { value: 'bank', label: 'Bank Transfer' },
  { value: 'wallet', label: 'Digital Wallet' },
];

const CreateOrder = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [loadingMap, setLoadingMap] = useState(false);
  const [couriers, setCouriers] = useState([]);

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [saveDraftDialogOpen, setSaveDraftDialogOpen] = useState(false);
  const [mapReady, setMapReady] = useState(false);
  const [geocodeResults, setGeocodeResults] = useState({ pickup: null, delivery: null });
  const [calculatePriceClicked, setCalculatePriceClicked] = useState(false);
  const [helpDialogOpen, setHelpDialogOpen] = useState(false);
  const [helpContent, setHelpContent] = useState({
    title: '',
    content: '',
  });

  const [orderData, setOrderData] = useState({
    // Customer & Address (Combined in Step 1)
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    pickupAddress: '',
    deliveryAddress: '',
    deliveryInstructions: '',

    // Enhanced Product Details (Step 2)
    productName: '',
    quantity: 1,
    unitPrice: '',
    totalAmount: '',
    packageType: 'small',
    packageWeight: '',
    packageDimensions: '',
    priority: 'standard',
    specialInstructions: '',

    // Payment & System (Step 3)
    paymentMethod: 'cash',
    estimatedDistance: '',
    estimatedCost: 0,
    finalAmount: 0,

    // System Fields
    status: 'pending',
    createdBy: user?.uid || '',
    createdAt: null,
    updatedAt: null,
  });

  const [errors, setErrors] = useState({});
  const [mapCenter, setMapCenter] = useState(DEFAULT_CENTER);
  const [mapInstance, setMapInstance] = useState(null);
  const [advancedMarkers, setAdvancedMarkers] = useState([]);
  const [directionsRenderer, setDirectionsRenderer] = useState(null);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'info',
  });

  // Enhanced address input states
  const [bulkImportOpen, setBulkImportOpen] = useState(false);
  const [pickupCoordinates, setPickupCoordinates] = useState(null);
  const [deliveryCoordinates, setDeliveryCoordinates] = useState(null);
  const [addressHistory, setAddressHistory] = useState({
    pickup: [],
    delivery: []
  });

  useEffect(() => {
    fetchCouriers();

    // Check for draft
    const savedDraft = localStorage.getItem('orderDraft');
    if (savedDraft) {
      const draftData = JSON.parse(savedDraft);
      setSaveDraftDialogOpen(true);
    }

    // Clean up function
    return () => {
      if (activeStep < steps.length - 1) {
        saveDraft();
      }
    };
  }, []);

  // Auto-calculate total amount when quantity or unit price changes
  useEffect(() => {
    if (orderData.quantity && orderData.unitPrice) {
      const total = parseFloat(orderData.quantity) * parseFloat(orderData.unitPrice);
      setOrderData(prev => ({
        ...prev,
        totalAmount: total.toFixed(2)
      }));
    }
  }, [orderData.quantity, orderData.unitPrice]);

  // Auto-calculate delivery cost when required fields are filled
  useEffect(() => {
    if (orderData.packageType &&
        orderData.packageWeight &&
        orderData.priority &&
        orderData.totalAmount) {
      calculateEnhancedPrice();
    }
  }, [orderData.packageType, orderData.packageWeight, orderData.priority, orderData.totalAmount, orderData.estimatedDistance]);

  // Update map when addresses change
  useEffect(() => {
    if (mapReady && orderData.pickupAddress && orderData.deliveryAddress) {
      updateMapWithAddresses();
    }
  }, [mapReady, orderData.pickupAddress, orderData.deliveryAddress]);

  const fetchCouriers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/orders/available-couriers', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user?.accessToken}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setCouriers(data.couriers || []);
          if (data.couriers.length === 0) {
            toast.warning('No couriers available at the moment.');
          }
        } else {
          throw new Error(data.error || 'Failed to fetch couriers');
        }
      } else {
        await fetchCouriersFromFirestore();
      }
    } catch (error) {
      console.error('Error fetching couriers from API:', error);
      await fetchCouriersFromFirestore();
    } finally {
      setLoading(false);
    }
  };

  const fetchCouriersFromFirestore = async () => {
    try {
      const couriersRef = collection(db, 'users');
      const q = query(couriersRef, where('role', '==', 'courier'));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const couriersList = querySnapshot.docs.map(doc => ({
          id: doc.id,
          name: doc.data().name || doc.data().displayName || doc.data().email || `Courier ${doc.id.substring(0, 5)}`,
          email: doc.data().email,
          phone: doc.data().phone,
          status: doc.data().status || 'available',
          assignedOrdersCount: (doc.data().assignedOrders || []).length,
          isOnline: doc.data().isOnline || false,
          ...doc.data()
        }));

        couriersList.sort((a, b) => {
          if (a.status === 'available' && b.status !== 'available') return -1;
          if (a.status !== 'available' && b.status === 'available') return 1;
          return a.assignedOrdersCount - b.assignedOrdersCount;
        });

        setCouriers(couriersList);

        if (couriersList.length === 0) {
          toast.warning('No couriers found. Please add couriers first.');
        }
      } else {
        setCouriers([]);
        toast.warning('No couriers available. Please add couriers first.');
      }
    } catch (error) {
      console.error('Error fetching couriers from Firestore:', error);
      setCouriers([]);
      toast.error('Failed to load couriers');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setOrderData(prev => ({
      ...prev,
      [name]: value
    }));

    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const showHelp = (title, content) => {
    setHelpContent({ title, content });
    setHelpDialogOpen(true);
  };

  // Enhanced address handlers
  const handlePickupAddressChange = (address) => {
    setOrderData(prev => ({
      ...prev,
      pickupAddress: address
    }));
  };

  const handleDeliveryAddressChange = (address) => {
    setOrderData(prev => ({
      ...prev,
      deliveryAddress: address
    }));
  };

  const handlePickupLocationSelect = (locationData) => {
    setPickupCoordinates(locationData.coordinates);
    setOrderData(prev => ({
      ...prev,
      pickupAddress: locationData.formatted_address,
      pickupCoordinates: locationData.coordinates
    }));
  };

  const handleDeliveryLocationSelect = (locationData) => {
    setDeliveryCoordinates(locationData.coordinates);
    setOrderData(prev => ({
      ...prev,
      deliveryAddress: locationData.formatted_address,
      deliveryCoordinates: locationData.coordinates
    }));
  };

  const handleBulkOrdersImported = (orders) => {
    toast.success(`Successfully imported ${orders.length} orders`);
    setBulkImportOpen(false);
    // Optionally navigate to orders list or refresh
    navigate('/orders');
  };

  // Enhanced price calculation function
  const calculateEnhancedPrice = () => {
    const packageType = packageTypes.find(p => p.value === orderData.packageType);
    const priority = priorityOptions.find(p => p.value === orderData.priority);

    if (!packageType || !priority) return;

    // Enhanced cost calculation
    const basePrice = packageType.basePrice;
    const productValue = parseFloat(orderData.totalAmount || 0);
    const weightFactor = parseFloat(orderData.packageWeight || 0) * 3;
    const distanceFactor = parseFloat(orderData.estimatedDistance || 0) * 2;
    const priorityMultiplier = priority.multiplier;

    // Insurance factor for valuable items
    const insuranceFactor = productValue > 1000 ? productValue * 0.01 : 0;

    const deliveryCost = (basePrice + weightFactor + distanceFactor + insuranceFactor) * priorityMultiplier;
    const finalCost = Math.round(deliveryCost);

    setOrderData(prev => ({
      ...prev,
      estimatedCost: finalCost,
      finalAmount: productValue + finalCost
    }));
  };

  const validateStep = () => {
    const newErrors = {};

    if (activeStep === 0) {
      // Customer & Address validation
      if (!orderData.customerName) newErrors.customerName = 'Customer name is required';
      if (orderData.customerEmail && !validateEmail(orderData.customerEmail)) {
        newErrors.customerEmail = 'Please enter a valid email address';
      }
      if (!orderData.customerPhone) {
        newErrors.customerPhone = 'Customer phone is required';
      } else if (!validatePhone(orderData.customerPhone)) {
        newErrors.customerPhone = 'Please enter a valid phone number';
      }
      if (!orderData.pickupAddress) newErrors.pickupAddress = 'Pickup address is required';
      if (!orderData.deliveryAddress) newErrors.deliveryAddress = 'Delivery address is required';
      if (orderData.pickupAddress === orderData.deliveryAddress) {
        newErrors.deliveryAddress = 'Delivery address must be different from pickup address';
      }
    } else if (activeStep === 1) {
      // Product Details validation
      if (!orderData.productName) newErrors.productName = 'Product name is required';
      if (!orderData.quantity || orderData.quantity < 1) newErrors.quantity = 'Quantity must be at least 1';
      if (!orderData.unitPrice || parseFloat(orderData.unitPrice) <= 0) newErrors.unitPrice = 'Unit price must be greater than 0';
      if (!orderData.packageWeight) {
        newErrors.packageWeight = 'Package weight is required';
      } else if (parseFloat(orderData.packageWeight) <= 0) {
        newErrors.packageWeight = 'Weight must be greater than 0';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      saveDraft();
      setActiveStep(prevStep => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prevStep => prevStep - 1);
  };

  const saveDraft = () => {
    localStorage.setItem('orderDraft', JSON.stringify(orderData));
  };

  const loadDraft = () => {
    const savedDraft = localStorage.getItem('orderDraft');
    if (savedDraft) {
      setOrderData(JSON.parse(savedDraft));
      setNotification({
        open: true,
        message: 'Draft loaded successfully',
        severity: 'success'
      });
    }
    setSaveDraftDialogOpen(false);
  };

  const clearDraft = () => {
    localStorage.removeItem('orderDraft');
    setSaveDraftDialogOpen(false);
  };

  const updateMapWithAddresses = async () => {
    try {
      setLoadingMap(true);

      // Check if Google Maps is available
      if (!window.google || !window.google.maps) {
        console.warn('Google Maps not loaded, skipping map update');
        setLoadingMap(false);
        return;
      }

      const geocoder = new window.google.maps.Geocoder();

      // Enhanced geocoding with better error handling
      const geocodeAddress = (address, addressType) => {
        return new Promise((resolve, reject) => {
          if (!address || address.trim() === '') {
            reject(new Error(`${addressType} address is empty`));
            return;
          }

          geocoder.geocode({
            address: address.trim(),
            region: 'IN', // Bias towards India
            componentRestrictions: { country: 'IN' }
          }, (results, status) => {
            if (status === 'OK' && results && results[0]) {
              const location = results[0].geometry.location;
              const coords = {
                lat: location.lat(),
                lng: location.lng(),
                formatted_address: results[0].formatted_address,
                place_id: results[0].place_id
              };
              console.log(`Successfully geocoded ${addressType}:`, coords);
              resolve(coords);
            } else {
              console.warn(`Geocoding failed for ${addressType} address:`, status);
              // Use fallback coordinates with slight randomization
              const fallbackCoords = {
                lat: 19.0760 + (Math.random() * 0.1 - 0.05),
                lng: 72.8777 + (Math.random() * 0.1 - 0.05),
                formatted_address: address,
                place_id: null,
                fallback: true
              };

              if (status !== 'ZERO_RESULTS') {
                toast.warning(`Could not find exact location for ${addressType} address. Using approximate location.`);
              }

              resolve(fallbackCoords);
            }
          });
        });
      };

      // Geocode both addresses concurrently for better performance
      const [pickupCoords, deliveryCoords] = await Promise.all([
        geocodeAddress(orderData.pickupAddress, 'pickup'),
        geocodeAddress(orderData.deliveryAddress, 'delivery')
      ]);

      // Enhanced distance calculation with route optimization
      const distanceResult = await calculateDistanceWithAPI(
        { lat: pickupCoords.lat, lng: pickupCoords.lng },
        { lat: deliveryCoords.lat, lng: deliveryCoords.lng }
      );

      const distance = distanceResult.distance.toFixed(2);
      const duration = distanceResult.duration ? Math.round(distanceResult.duration) : null;

      console.log(`Distance calculated: ${distance} km using ${distanceResult.method}`);
      if (duration) {
        console.log(`Estimated duration: ${duration} minutes`);
      }

      // Store geocoding results with additional metadata
      setGeocodeResults({
        pickup: pickupCoords,
        delivery: deliveryCoords,
        distance: distance,
        duration: duration,
        method: distanceResult.method
      });

      // Calculate optimal map center and zoom level
      const bounds = new window.google.maps.LatLngBounds();
      bounds.extend(new window.google.maps.LatLng(pickupCoords.lat, pickupCoords.lng));
      bounds.extend(new window.google.maps.LatLng(deliveryCoords.lat, deliveryCoords.lng));

      const center = bounds.getCenter();
      setMapCenter({
        lat: center.lat(),
        lng: center.lng()
      });

      // Update order data with enhanced information
      setOrderData(prev => ({
        ...prev,
        estimatedDistance: distance,
        estimatedDuration: duration,
        routeOptimized: distanceResult.method !== 'haversine'
      }));

      // Enhanced map rendering with better markers and route display
      if (window.google && window.google.maps && mapInstance) {
        // Clear existing markers
        clearMarkers(advancedMarkers);

        const newMarkers = [];

        // Create enhanced pickup marker
        const pickupMarker = createAdvancedMarker({
          position: pickupCoords,
          map: mapInstance,
          type: 'pickup',
          title: `Pickup: ${pickupCoords.formatted_address || orderData.pickupAddress}`,
          data: {
            address: orderData.pickupAddress,
            coordinates: pickupCoords,
            type: 'pickup'
          },
          status: 'pending'
        });
        if (pickupMarker) newMarkers.push(pickupMarker);

        // Create enhanced delivery marker
        const deliveryMarker = createAdvancedMarker({
          position: deliveryCoords,
          map: mapInstance,
          type: 'delivery',
          title: `Delivery: ${deliveryCoords.formatted_address || orderData.deliveryAddress}`,
          data: {
            address: orderData.deliveryAddress,
            coordinates: deliveryCoords,
            type: 'delivery'
          },
          status: 'pending'
        });
        if (deliveryMarker) newMarkers.push(deliveryMarker);

        setAdvancedMarkers(newMarkers);

        // Clear existing directions
        if (directionsRenderer) {
          if (directionsRenderer.setMap) {
            directionsRenderer.setMap(null);
          } else if (directionsRenderer.polyline) {
            directionsRenderer.polyline.setMap(null);
          }
          setDirectionsRenderer(null);
        }

        // Use modern approach without legacy DirectionsService
        // Since legacy Directions API is not enabled, we'll show route using polyline from Routes API
        try {
          // Try to get route polyline from Routes API
          const routeData = await calculateDistanceWithAPI(pickupCoords, deliveryCoords);

          if (routeData.polyline && window.google && window.google.maps && window.google.maps.geometry) {
            // Decode and display the polyline
            const decodedPath = window.google.maps.geometry.encoding.decodePath(routeData.polyline);

            const routePolyline = new window.google.maps.Polyline({
              path: decodedPath,
              geodesic: true,
              strokeColor: '#4285F4',
              strokeOpacity: 0.8,
              strokeWeight: 6,
              map: mapInstance
            });

            // Store polyline for cleanup
            setDirectionsRenderer({ polyline: routePolyline });

            // Update map bounds to show the entire route
            const bounds = new window.google.maps.LatLngBounds();
            decodedPath.forEach(point => bounds.extend(point));
            bounds.extend(new window.google.maps.LatLng(pickupCoords.lat, pickupCoords.lng));
            bounds.extend(new window.google.maps.LatLng(deliveryCoords.lat, deliveryCoords.lng));
            mapInstance.fitBounds(bounds, { padding: 50 });

            console.log('Route rendered successfully using Routes API polyline');
          } else {
            // Fallback: show straight line between points
            const routeLine = new window.google.maps.Polyline({
              path: [
                new window.google.maps.LatLng(pickupCoords.lat, pickupCoords.lng),
                new window.google.maps.LatLng(deliveryCoords.lat, deliveryCoords.lng)
              ],
              geodesic: true,
              strokeColor: '#FF6B6B',
              strokeOpacity: 0.6,
              strokeWeight: 4,
              strokeStyle: 'dashed',
              map: mapInstance
            });

            setDirectionsRenderer({ polyline: routeLine });

            // Update map bounds
            const bounds = new window.google.maps.LatLngBounds();
            bounds.extend(new window.google.maps.LatLng(pickupCoords.lat, pickupCoords.lng));
            bounds.extend(new window.google.maps.LatLng(deliveryCoords.lat, deliveryCoords.lng));
            mapInstance.fitBounds(bounds, { padding: 50 });

            console.log('Route rendered as straight line (fallback)');
            toast.info('Showing direct route - detailed routing unavailable');
          }
        } catch (error) {
          console.error('Error rendering route:', error);
          // Final fallback: just show markers without route
          updateMapMarkers();
          toast.warning('Could not display route on map');
        }
      }

    } catch (error) {
      console.error('Error updating map:', error);
      toast.error('Failed to load map data');
    } finally {
      setLoadingMap(false);
    }
  };

  const calculatePrice = () => {
    try {
      // Get base price from package type
      const packageTypeObj = packageTypes.find(type => type.value === orderData.packageType);
      const basePrice = packageTypeObj ? packageTypeObj.basePrice : 15;

      // Get priority multiplier
      const priorityObj = priorityOptions.find(option => option.value === orderData.priority);
      const priorityMultiplier = priorityObj ? priorityObj.multiplier : 1;

      // Enhanced weight factor calculation
      const weight = parseFloat(orderData.packageWeight) || 0;
      const weightFactor = Math.max(1, weight * 0.5 + (weight > 10 ? (weight - 10) * 0.3 : 0));

      // Enhanced distance factor with route optimization bonus
      const distance = parseFloat(orderData.estimatedDistance) || 0;
      const distanceFactor = Math.max(1, distance * 0.2 + (distance > 20 ? (distance - 20) * 0.1 : 0));

      // Route optimization bonus (discount for optimized routes)
      const routeOptimizationDiscount = orderData.routeOptimized ? 0.95 : 1;

      // Duration factor (if available)
      const duration = parseFloat(orderData.estimatedDuration) || 0;
      const durationFactor = duration > 0 ? Math.max(1, duration / 60 * 0.1) : 1; // Convert minutes to hours

      // Calculate final price with all factors
      let calculatedPrice = basePrice * weightFactor * distanceFactor * priorityMultiplier * durationFactor * routeOptimizationDiscount;

      // Apply minimum price threshold
      calculatedPrice = Math.max(calculatedPrice, 10);

      // Round to 2 decimal places
      calculatedPrice = parseFloat(calculatedPrice.toFixed(2));

      console.log('Price calculation breakdown:', {
        basePrice,
        weightFactor,
        distanceFactor,
        priorityMultiplier,
        durationFactor,
        routeOptimizationDiscount,
        finalPrice: calculatedPrice
      });

      setOrderData(prev => ({
        ...prev,
        amount: calculatedPrice.toString()
      }));

      setCalculatePriceClicked(true);

      // Show price breakdown to user
      toast.success(`Price calculated: ₹${calculatedPrice} (Base: ₹${basePrice}, Distance: ${distance}km, Weight: ${weight}kg)`);

    } catch (error) {
      console.error('Error calculating price:', error);
      toast.error('Failed to calculate price. Please try again.');
    }
  };

  // Get markers for the enhanced map
  const getOrderMapMarkers = () => {
    const markers = [];

    // Add pickup marker
    if (geocodeResults?.pickup) {
      markers.push({
        position: geocodeResults.pickup,
        type: 'pickup',
        title: `Pickup: ${orderData.pickupAddress}`,
        status: 'pending',
        data: {
          address: orderData.pickupAddress,
          type: 'pickup'
        }
      });
    }

    // Add delivery marker
    if (geocodeResults?.delivery) {
      markers.push({
        position: geocodeResults.delivery,
        type: 'delivery',
        title: `Delivery: ${orderData.deliveryAddress}`,
        status: 'pending',
        data: {
          address: orderData.deliveryAddress,
          type: 'delivery'
        }
      });
    }

    return markers;
  };

  const handleSubmit = async () => {
    if (!validateStep()) return;

    setConfirmDialogOpen(true);
  };

  const confirmSubmit = async () => {
    try {
      setLoading(true);
      setConfirmDialogOpen(false);

      const orderPayload = {
        customerName: orderData.customerName.trim(),
        customerEmail: orderData.customerEmail.trim().toLowerCase(),
        customerPhone: orderData.customerPhone.replace(/\D/g, ''),
        pickupAddress: orderData.pickupAddress.trim(),
        deliveryAddress: orderData.deliveryAddress.trim(),
        deliveryCoordinates: geocodeResults.delivery || null,
        productName: orderData.productName?.trim() || orderData.packageType,
        quantity: 1,
        packageDetails: orderData.packageDescription || '',
        amount: parseFloat(orderData.amount),
        courierId: orderData.courierId || null,
        priority: orderData.priority,
        notes: orderData.deliveryInstructions || '',
        userId: user.uid,
        packageType: orderData.packageType,
        packageWeight: orderData.packageWeight,
        packageDimensions: orderData.packageDimensions,
        paymentMethod: orderData.paymentMethod,
        estimatedDistance: orderData.estimatedDistance
      };

      const response = await fetch('/api/orders/place', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user?.accessToken}`
        },
        body: JSON.stringify(orderPayload)
      });

      const result = await response.json();

      if (response.ok && result.success) {
        localStorage.removeItem('orderDraft');
        toast.success(`Order created successfully! Order #${result.orderNumber}`);
        
        if (result.orderId) {
          navigate(`/orders/${result.orderId}`);
        } else {
          navigate('/orders');
        }
      } else {
        if (result.details && Array.isArray(result.details)) {
          const errorMessage = result.details.join(', ');
          toast.error(`Validation failed: ${errorMessage}`);
        } else {
          toast.error(result.error || 'Failed to create order');
        }
      }
    } catch (error) {
      console.error('Error creating order:', error);
      try {
        console.warn('API failed, falling back to direct Firestore creation');
        await createOrderDirectly();
      } catch (fallbackError) {
        console.error('Fallback order creation also failed:', fallbackError);
        toast.error(`Failed to create order: ${fallbackError.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const createOrderDirectly = async () => {
    const orderNumber = generateNumericOrderId(10);

    const orderToSubmit = {
      ...orderData,
      orderNumber,
      trackingId: `TRK-${Math.random().toString(36).substring(2, 11).toUpperCase()}`,
      estimatedDeliveryTime: calculateEstimatedDelivery(orderData.priority),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy: user.uid,
      status: orderData.courierId ? 'processing' : 'pending',
      ...(orderData.courierId && {
        assignedCourier: orderData.courierId,
        assignedCourierId: orderData.courierId,
        courierId: orderData.courierId
      })
    };

    const docRef = await addDoc(collection(db, 'orders'), orderToSubmit);

    await addDoc(collection(db, 'orderHistory'), {
      orderId: docRef.id,
      action: 'created',
      timestamp: serverTimestamp(),
      userId: user.uid,
      details: {
        status: orderToSubmit.status,
        note: 'Order created'
      }
    });

    if (orderData.courierId) {
      await addDoc(collection(db, 'notifications'), {
        userId: orderData.courierId,
        type: 'order_assigned',
        title: 'New Order Assigned',
        message: `You have been assigned order #${orderNumber}`,
        read: false,
        createdAt: serverTimestamp()
      });
    }

    localStorage.removeItem('orderDraft');
    toast.success('Order created successfully!');
    navigate('/orders');
  };

  const calculateEstimatedDelivery = (priority) => {
    const now = new Date();
    let estimatedDelivery = new Date(now);

    switch(priority) {
      case 'same-day':
        estimatedDelivery.setHours(now.getHours() + 4);
        break;
      case 'express':
        estimatedDelivery.setDate(now.getDate() + 1);
        break;
      case 'standard':
      default:
        estimatedDelivery.setDate(now.getDate() + 2);
        break;
    }

    return estimatedDelivery.toISOString();
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            {/* Customer Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Person color="primary" />
                Customer Information
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Customer Name"
                name="customerName"
                value={orderData.customerName}
                onChange={handleChange}
                error={!!errors.customerName}
                helperText={errors.customerName}
                required
                InputProps={{
                  startAdornment: <Person sx={{ color: 'action.active', mr: 1 }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Customer Phone"
                name="customerPhone"
                value={orderData.customerPhone}
                onChange={handleChange}
                error={!!errors.customerPhone}
                helperText={errors.customerPhone}
                required
                placeholder="e.g. +91 98765 43210"
                InputProps={{
                  startAdornment: <Phone sx={{ color: 'action.active', mr: 1 }} />,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Customer Email (Optional)"
                name="customerEmail"
                type="email"
                value={orderData.customerEmail}
                onChange={handleChange}
                error={!!errors.customerEmail}
                helperText={errors.customerEmail || 'Optional. Enter a valid email to receive notifications.'}
                InputProps={{
                  startAdornment: <Email sx={{ color: 'action.active', mr: 1 }} />,
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
            </Grid>

            {/* Address Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LocationOn color="primary" />
                Pickup & Delivery Addresses
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Pickup Address"
                name="pickupAddress"
                value={orderData.pickupAddress}
                onChange={handleChange}
                error={!!errors.pickupAddress}
                helperText={errors.pickupAddress}
                required
                multiline
                rows={2}
                placeholder="Enter complete pickup address with landmarks"
                InputProps={{
                  startAdornment: <Home sx={{ color: 'action.active', mr: 1 }} />,
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Delivery Address"
                name="deliveryAddress"
                value={orderData.deliveryAddress}
                onChange={handleChange}
                error={!!errors.deliveryAddress}
                helperText={errors.deliveryAddress}
                required
                multiline
                rows={2}
                placeholder="Enter complete delivery address with landmarks"
                InputProps={{
                  startAdornment: <Business sx={{ color: 'action.active', mr: 1 }} />,
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Delivery Instructions (Optional)"
                name="deliveryInstructions"
                value={orderData.deliveryInstructions}
                onChange={handleChange}
                multiline
                rows={2}
                placeholder="Special instructions for delivery (e.g., gate code, floor number, contact person)..."
              />
            </Grid>

            {orderData.estimatedDistance && (
              <Grid item xs={12}>
                <Alert severity="info" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Calculate />
                  Estimated Distance: {orderData.estimatedDistance} km
                </Alert>
              </Grid>
            )}
          </Grid>
        );
      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ShoppingCart color="primary" />
                Product Details
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Product Name"
                name="productName"
                value={orderData.productName}
                onChange={handleChange}
                error={!!errors.productName}
                helperText={errors.productName}
                required
                placeholder="e.g., iPhone 15, Samsung TV, Books..."
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Quantity"
                name="quantity"
                type="number"
                value={orderData.quantity}
                onChange={handleChange}
                error={!!errors.quantity}
                helperText={errors.quantity}
                required
                inputProps={{ min: 1, step: 1 }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                        <IconButton
                          size="small"
                          onClick={() => handleChange({ target: { name: 'quantity', value: (orderData.quantity || 1) + 1 } })}
                        >
                          <Add fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleChange({ target: { name: 'quantity', value: Math.max(1, (orderData.quantity || 1) - 1) } })}
                        >
                          <Remove fontSize="small" />
                        </IconButton>
                      </Box>
                    </InputAdornment>
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Unit Price (₹)"
                name="unitPrice"
                type="number"
                value={orderData.unitPrice}
                onChange={handleChange}
                error={!!errors.unitPrice}
                helperText={errors.unitPrice}
                required
                inputProps={{ min: 0.01, step: 0.01 }}
                InputProps={{
                  startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Total Amount"
                value={`₹${orderData.totalAmount || '0.00'}`}
                InputProps={{ readOnly: true }}
                variant="filled"
                sx={{
                  '& .MuiFilledInput-root': {
                    backgroundColor: 'primary.50',
                    fontWeight: 'bold'
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Package Weight (kg)"
                name="packageWeight"
                type="number"
                inputProps={{ step: "0.1", min: "0.1" }}
                value={orderData.packageWeight}
                onChange={handleChange}
                error={!!errors.packageWeight}
                helperText={errors.packageWeight}
                required
                InputProps={{
                  endAdornment: <InputAdornment position="end">kg</InputAdornment>,
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.packageType}>
                <InputLabel>Package Type</InputLabel>
                <Select
                  name="packageType"
                  value={orderData.packageType}
                  onChange={handleChange}
                  label="Package Type"
                  required
                >
                  {packageTypes.map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box>
                        <Typography variant="body1">{type.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {type.description} - Base: ₹{type.basePrice}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
                {errors.packageType && (
                  <Typography color="error" variant="caption">
                    {errors.packageType}
                  </Typography>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Delivery Priority</InputLabel>
                <Select
                  name="priority"
                  value={orderData.priority}
                  onChange={handleChange}
                  label="Delivery Priority"
                >
                  {priorityOptions.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={`${option.multiplier}x`}
                          size="small"
                          color={option.color}
                        />
                        <Typography>{option.label}</Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Special Instructions (Optional)"
                name="specialInstructions"
                value={orderData.specialInstructions}
                onChange={handleChange}
                multiline
                rows={2}
                placeholder="Any special handling instructions (fragile, keep upright, temperature sensitive, etc.)..."
              />
            </Grid>

            {orderData.estimatedCost > 0 && (
              <Grid item xs={12}>
                <Alert severity="success" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Calculate />
                  Estimated Delivery Cost: ₹{orderData.estimatedCost} |
                  Total Payable: ₹{parseFloat(orderData.totalAmount || 0) + orderData.estimatedCost}
                </Alert>
              </Grid>
            )}
          </Grid>
        );
      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Payment color="primary" />
                Payment & Confirmation
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Payment Method</InputLabel>
                <Select
                  name="paymentMethod"
                  value={orderData.paymentMethod}
                  onChange={handleChange}
                  label="Payment Method"
                >
                  {paymentMethods.map(method => (
                    <MenuItem key={method.value} value={method.value}>
                      {method.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Total Payable Amount"
                value={`₹${parseFloat(orderData.totalAmount || 0) + orderData.estimatedCost}`}
                InputProps={{ readOnly: true }}
                variant="filled"
                sx={{
                  '& .MuiFilledInput-root': {
                    backgroundColor: 'success.50',
                    fontWeight: 'bold',
                    fontSize: '1.1rem'
                  }
                }}
              />
            </Grid>

            {/* Enhanced Order Summary */}
            <Grid item xs={12}>
              <Card variant="outlined" sx={{ mt: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">
                    📋 Order Summary
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">Customer:</Typography>
                      <Typography variant="body1" fontWeight="bold">{orderData.customerName}</Typography>
                      <Typography variant="body2">{orderData.customerPhone}</Typography>
                      {orderData.customerEmail && (
                        <Typography variant="body2">{orderData.customerEmail}</Typography>
                      )}
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">Product:</Typography>
                      <Typography variant="body1" fontWeight="bold">{orderData.productName}</Typography>
                      <Typography variant="body2">
                        Qty: {orderData.quantity} × ₹{orderData.unitPrice} = ₹{orderData.totalAmount}
                      </Typography>
                      <Typography variant="body2">Weight: {orderData.packageWeight} kg</Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <Divider sx={{ my: 1 }} />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">Pickup Address:</Typography>
                      <Typography variant="body2">{orderData.pickupAddress}</Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">Delivery Address:</Typography>
                      <Typography variant="body2">{orderData.deliveryAddress}</Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <Divider sx={{ my: 1 }} />
                    </Grid>

                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body1">Product Value:</Typography>
                        <Typography variant="body1">₹{orderData.totalAmount}</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body1">Delivery Charges:</Typography>
                        <Typography variant="body1">₹{orderData.estimatedCost}</Typography>
                      </Box>
                      <Divider sx={{ my: 1 }} />
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="h6" color="primary">Total Payable:</Typography>
                        <Typography variant="h6" color="primary" fontWeight="bold">
                          ₹{parseFloat(orderData.totalAmount || 0) + orderData.estimatedCost}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );


      default:
        return null;
    }
  };

  return (
    <AdminLayout>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
          <Paper
            elevation={0}
            sx={{
              p: { xs: 2, sm: 4 },
              borderRadius: 4,
              boxShadow: '0 0 10px rgba(0,0,0,0.1)',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <IconButton
                  sx={{ mr: 2 }}
                  onClick={() => navigate('/orders')}
                  aria-label="back to orders"
                >
                  <ArrowBack />
                </IconButton>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  Create New Order
                </Typography>
              </Box>

              <Button
                variant="outlined"
                startIcon={<CloudUpload />}
                onClick={() => setBulkImportOpen(true)}
                sx={{ ml: 2 }}
              >
                Bulk Import
              </Button>
            </Box>

            <Stepper
              activeStep={activeStep}
              sx={{
                mb: 4,
                display: { xs: 'none', sm: 'flex' }
              }}
            >
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>

            {/* Mobile stepper alternative */}
            <Box sx={{ display: { xs: 'block', sm: 'none' }, mb: 3 }}>
              <Typography variant="subtitle1" textAlign="center">
                Step {activeStep + 1}: {steps[activeStep]}
              </Typography>
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mt: 1
              }}>
                {steps.map((_, index) => (
                  <Box
                    key={index}
                    sx={{
                      height: 6,
                      width: `calc(${100 / steps.length}% - 4px)`,
                      backgroundColor: index <= activeStep ? theme.palette.primary.main : theme.palette.grey[300],
                      borderRadius: 3
                    }}
                  />
                ))}
              </Box>
            </Box>

            {renderStepContent(activeStep)}

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
              <Button
                disabled={activeStep === 0}
                onClick={handleBack}
                startIcon={<ArrowBack />}
                sx={{ mr: 1 }}
              >
                Back
              </Button>
              <Box>
                {activeStep !== steps.length - 1 && (
                  <Button
                    variant="outlined"
                    onClick={saveDraft}
                    sx={{ mr: 2 }}
                  >
                    Save Draft
                  </Button>
                )}
                <Button
                  variant="contained"
                  color="primary"
                  onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}
                  disabled={loading}
                  sx={{ borderRadius: 2, px: 4 }}
                  endIcon={activeStep === steps.length - 1 ? <Check /> : null}
                >
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : activeStep === steps.length - 1 ? (
                    'Create Order'
                  ) : (
                    'Next'
                  )}
                </Button>
              </Box>
            </Box>
          </Paper>
        </Container>

        {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
      >
        <DialogTitle>Confirm Order Creation</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to create this order? This will initiate the delivery process and notify the assigned courier if selected.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)} color="primary">
            Cancel
          </Button>
          <Button onClick={confirmSubmit} color="primary" variant="contained" autoFocus>
            {loading ? <CircularProgress size={24} /> : 'Confirm'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Draft Dialog */}
      <Dialog
        open={saveDraftDialogOpen}
        onClose={() => setSaveDraftDialogOpen(false)}
      >
        <DialogTitle>Load Saved Draft?</DialogTitle>
        <DialogContent>
          <DialogContentText>
            We found a previously saved draft for an order. Would you like to continue with this draft or start a new order?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={clearDraft} color="error">
            Start New
          </Button>
          <Button onClick={loadDraft} color="primary" variant="contained" autoFocus>
            Load Draft
          </Button>
        </DialogActions>
      </Dialog>

      {/* Help Dialog */}
      <Dialog
        open={helpDialogOpen}
        onClose={() => setHelpDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{helpContent.title}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {helpContent.content}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setHelpDialogOpen(false)} color="primary">
            Got it
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, open: false })}
        message={notification.message}
      />

      {/* Bulk Import Dialog */}
      <Dialog
        open={bulkImportOpen}
        onClose={() => setBulkImportOpen(false)}
        maxWidth="lg"
        fullWidth
        fullScreen={isMobile}
      >
        <DialogTitle>
          Bulk Order Import
        </DialogTitle>
        <DialogContent>
          <BulkOrderImport
            onOrdersImported={handleBulkOrdersImported}
            onClose={() => setBulkImportOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default CreateOrder;