{"version": 3, "sources": ["../../@firebase/storage/src/implementation/constants.ts", "../../@firebase/storage/src/implementation/error.ts", "../../@firebase/storage/src/implementation/location.ts", "../../@firebase/storage/src/implementation/failrequest.ts", "../../@firebase/storage/src/implementation/backoff.ts", "../../@firebase/storage/src/implementation/type.ts", "../../@firebase/storage/src/implementation/url.ts", "../../@firebase/storage/src/implementation/connection.ts", "../../@firebase/storage/src/implementation/utils.ts", "../../@firebase/storage/src/implementation/request.ts", "../../@firebase/storage/src/implementation/fs.ts", "../../@firebase/storage/src/platform/browser/base64.ts", "../../@firebase/storage/src/implementation/string.ts", "../../@firebase/storage/src/implementation/blob.ts", "../../@firebase/storage/src/implementation/json.ts", "../../@firebase/storage/src/implementation/path.ts", "../../@firebase/storage/src/implementation/metadata.ts", "../../@firebase/storage/src/implementation/list.ts", "../../@firebase/storage/src/implementation/requestinfo.ts", "../../@firebase/storage/src/implementation/requests.ts", "../../@firebase/storage/src/implementation/taskenums.ts", "../../@firebase/storage/src/implementation/observer.ts", "../../@firebase/storage/src/implementation/async.ts", "../../@firebase/storage/src/platform/browser/connection.ts", "../../@firebase/storage/src/task.ts", "../../@firebase/storage/src/reference.ts", "../../@firebase/storage/src/service.ts", "../../@firebase/storage/src/constants.ts", "../../@firebase/storage/src/api.ts", "../../@firebase/storage/src/api.browser.ts", "../../@firebase/storage/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Constants used in the Firebase Storage library.\n */\n\n/**\n * Domain name for firebase storage.\n */\nexport const DEFAULT_HOST = 'firebasestorage.googleapis.com';\n\n/**\n * The key in Firebase config json for the storage bucket.\n */\nexport const CONFIG_STORAGE_BUCKET_KEY = 'storageBucket';\n\n/**\n * 2 minutes\n *\n * The timeout for all operations except upload.\n */\nexport const DEFAULT_MAX_OPERATION_RETRY_TIME = 2 * 60 * 1000;\n\n/**\n * 10 minutes\n *\n * The timeout for upload.\n */\nexport const DEFAULT_MAX_UPLOAD_RETRY_TIME = 10 * 60 * 1000;\n\n/**\n * 1 second\n */\nexport const DEFAULT_MIN_SLEEP_TIME_MILLIS = 1000;\n\n/**\n * This is the value of Number.MIN_SAFE_INTEGER, which is not well supported\n * enough for us to use it directly.\n */\nexport const MIN_SAFE_INTEGER = -9007199254740991;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nimport { CONFIG_STORAGE_BUCKET_KEY } from './constants';\n\n/**\n * An error returned by the Firebase Storage SDK.\n * @public\n */\nexport class StorageError extends FirebaseError {\n  private readonly _baseMessage: string;\n  /**\n   * Stores custom error data unique to the `StorageError`.\n   */\n  customData: { serverResponse: string | null } = { serverResponse: null };\n\n  /**\n   * @param code - A `StorageErrorCode` string to be prefixed with 'storage/' and\n   *  added to the end of the message.\n   * @param message  - Error message.\n   * @param status_ - Corresponding HTTP Status Code\n   */\n  constructor(code: StorageErrorCode, message: string, private status_ = 0) {\n    super(\n      prependCode(code),\n      `Firebase Storage: ${message} (${prependCode(code)})`\n    );\n    this._baseMessage = this.message;\n    // Without this, `instanceof StorageError`, in tests for example,\n    // returns false.\n    Object.setPrototypeOf(this, StorageError.prototype);\n  }\n\n  get status(): number {\n    return this.status_;\n  }\n\n  set status(status: number) {\n    this.status_ = status;\n  }\n\n  /**\n   * Compares a `StorageErrorCode` against this error's code, filtering out the prefix.\n   */\n  _codeEquals(code: StorageErrorCode): boolean {\n    return prependCode(code) === this.code;\n  }\n\n  /**\n   * Optional response message that was added by the server.\n   */\n  get serverResponse(): null | string {\n    return this.customData.serverResponse;\n  }\n\n  set serverResponse(serverResponse: string | null) {\n    this.customData.serverResponse = serverResponse;\n    if (this.customData.serverResponse) {\n      this.message = `${this._baseMessage}\\n${this.customData.serverResponse}`;\n    } else {\n      this.message = this._baseMessage;\n    }\n  }\n}\n\nexport const errors = {};\n\n/**\n * @public\n * Error codes that can be attached to `StorageError` objects.\n */\nexport enum StorageErrorCode {\n  // Shared between all platforms\n  UNKNOWN = 'unknown',\n  OBJECT_NOT_FOUND = 'object-not-found',\n  BUCKET_NOT_FOUND = 'bucket-not-found',\n  PROJECT_NOT_FOUND = 'project-not-found',\n  QUOTA_EXCEEDED = 'quota-exceeded',\n  UNAUTHENTICATED = 'unauthenticated',\n  UNAUTHORIZED = 'unauthorized',\n  UNAUTHORIZED_APP = 'unauthorized-app',\n  RETRY_LIMIT_EXCEEDED = 'retry-limit-exceeded',\n  INVALID_CHECKSUM = 'invalid-checksum',\n  CANCELED = 'canceled',\n  // JS specific\n  INVALID_EVENT_NAME = 'invalid-event-name',\n  INVALID_URL = 'invalid-url',\n  INVALID_DEFAULT_BUCKET = 'invalid-default-bucket',\n  NO_DEFAULT_BUCKET = 'no-default-bucket',\n  CANNOT_SLICE_BLOB = 'cannot-slice-blob',\n  SERVER_FILE_WRONG_SIZE = 'server-file-wrong-size',\n  NO_DOWNLOAD_URL = 'no-download-url',\n  INVALID_ARGUMENT = 'invalid-argument',\n  INVALID_ARGUMENT_COUNT = 'invalid-argument-count',\n  APP_DELETED = 'app-deleted',\n  INVALID_ROOT_OPERATION = 'invalid-root-operation',\n  INVALID_FORMAT = 'invalid-format',\n  INTERNAL_ERROR = 'internal-error',\n  UNSUPPORTED_ENVIRONMENT = 'unsupported-environment'\n}\n\nexport function prependCode(code: StorageErrorCode): string {\n  return 'storage/' + code;\n}\n\nexport function unknown(): StorageError {\n  const message =\n    'An unknown error occurred, please check the error payload for ' +\n    'server response.';\n  return new StorageError(StorageErrorCode.UNKNOWN, message);\n}\n\nexport function objectNotFound(path: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.OBJECT_NOT_FOUND,\n    \"Object '\" + path + \"' does not exist.\"\n  );\n}\n\nexport function bucketNotFound(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.BUCKET_NOT_FOUND,\n    \"Bucket '\" + bucket + \"' does not exist.\"\n  );\n}\n\nexport function projectNotFound(project: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.PROJECT_NOT_FOUND,\n    \"Project '\" + project + \"' does not exist.\"\n  );\n}\n\nexport function quotaExceeded(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.QUOTA_EXCEEDED,\n    \"Quota for bucket '\" +\n      bucket +\n      \"' exceeded, please view quota on \" +\n      'https://firebase.google.com/pricing/.'\n  );\n}\n\nexport function unauthenticated(): StorageError {\n  const message =\n    'User is not authenticated, please authenticate using Firebase ' +\n    'Authentication and try again.';\n  return new StorageError(StorageErrorCode.UNAUTHENTICATED, message);\n}\n\nexport function unauthorizedApp(): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNAUTHORIZED_APP,\n    'This app does not have permission to access Firebase Storage on this project.'\n  );\n}\n\nexport function unauthorized(path: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNAUTHORIZED,\n    \"User does not have permission to access '\" + path + \"'.\"\n  );\n}\n\nexport function retryLimitExceeded(): StorageError {\n  return new StorageError(\n    StorageErrorCode.RETRY_LIMIT_EXCEEDED,\n    'Max retry time for operation exceeded, please try again.'\n  );\n}\n\nexport function invalidChecksum(\n  path: string,\n  checksum: string,\n  calculated: string\n): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_CHECKSUM,\n    \"Uploaded/downloaded object '\" +\n      path +\n      \"' has checksum '\" +\n      checksum +\n      \"' which does not match '\" +\n      calculated +\n      \"'. Please retry the upload/download.\"\n  );\n}\n\nexport function canceled(): StorageError {\n  return new StorageError(\n    StorageErrorCode.CANCELED,\n    'User canceled the upload/download.'\n  );\n}\n\nexport function invalidEventName(name: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_EVENT_NAME,\n    \"Invalid event name '\" + name + \"'.\"\n  );\n}\n\nexport function invalidUrl(url: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_URL,\n    \"Invalid URL '\" + url + \"'.\"\n  );\n}\n\nexport function invalidDefaultBucket(bucket: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_DEFAULT_BUCKET,\n    \"Invalid default bucket '\" + bucket + \"'.\"\n  );\n}\n\nexport function noDefaultBucket(): StorageError {\n  return new StorageError(\n    StorageErrorCode.NO_DEFAULT_BUCKET,\n    'No default bucket ' +\n      \"found. Did you set the '\" +\n      CONFIG_STORAGE_BUCKET_KEY +\n      \"' property when initializing the app?\"\n  );\n}\n\nexport function cannotSliceBlob(): StorageError {\n  return new StorageError(\n    StorageErrorCode.CANNOT_SLICE_BLOB,\n    'Cannot slice blob for upload. Please retry the upload.'\n  );\n}\n\nexport function serverFileWrongSize(): StorageError {\n  return new StorageError(\n    StorageErrorCode.SERVER_FILE_WRONG_SIZE,\n    'Server recorded incorrect upload file size, please retry the upload.'\n  );\n}\n\nexport function noDownloadURL(): StorageError {\n  return new StorageError(\n    StorageErrorCode.NO_DOWNLOAD_URL,\n    'The given file does not have any download URLs.'\n  );\n}\n\nexport function missingPolyFill(polyFill: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.UNSUPPORTED_ENVIRONMENT,\n    `${polyFill} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`\n  );\n}\n\n/**\n * @internal\n */\nexport function invalidArgument(message: string): StorageError {\n  return new StorageError(StorageErrorCode.INVALID_ARGUMENT, message);\n}\n\nexport function invalidArgumentCount(\n  argMin: number,\n  argMax: number,\n  fnName: string,\n  real: number\n): StorageError {\n  let countPart;\n  let plural;\n  if (argMin === argMax) {\n    countPart = argMin;\n    plural = argMin === 1 ? 'argument' : 'arguments';\n  } else {\n    countPart = 'between ' + argMin + ' and ' + argMax;\n    plural = 'arguments';\n  }\n  return new StorageError(\n    StorageErrorCode.INVALID_ARGUMENT_COUNT,\n    'Invalid argument count in `' +\n      fnName +\n      '`: Expected ' +\n      countPart +\n      ' ' +\n      plural +\n      ', received ' +\n      real +\n      '.'\n  );\n}\n\nexport function appDeleted(): StorageError {\n  return new StorageError(\n    StorageErrorCode.APP_DELETED,\n    'The Firebase app was deleted.'\n  );\n}\n\n/**\n * @param name - The name of the operation that was invalid.\n *\n * @internal\n */\nexport function invalidRootOperation(name: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_ROOT_OPERATION,\n    \"The operation '\" +\n      name +\n      \"' cannot be performed on a root reference, create a non-root \" +\n      \"reference using child, such as .child('file.png').\"\n  );\n}\n\n/**\n * @param format - The format that was not valid.\n * @param message - A message describing the format violation.\n */\nexport function invalidFormat(format: string, message: string): StorageError {\n  return new StorageError(\n    StorageErrorCode.INVALID_FORMAT,\n    \"String does not match format '\" + format + \"': \" + message\n  );\n}\n\n/**\n * @param message - A message describing the internal error.\n */\nexport function unsupportedEnvironment(message: string): StorageError {\n  throw new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, message);\n}\n\n/**\n * @param message - A message describing the internal error.\n */\nexport function internalError(message: string): StorageError {\n  throw new StorageError(\n    StorageErrorCode.INTERNAL_ERROR,\n    'Internal error: ' + message\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Functionality related to the parsing/composition of bucket/\n * object location.\n */\n\nimport { invalidDefaultBucket, invalidUrl } from './error';\nimport { DEFAULT_HOST } from './constants';\n\n/**\n * Firebase Storage location data.\n *\n * @internal\n */\nexport class Location {\n  private path_: string;\n\n  constructor(public readonly bucket: string, path: string) {\n    this.path_ = path;\n  }\n\n  get path(): string {\n    return this.path_;\n  }\n\n  get isRoot(): boolean {\n    return this.path.length === 0;\n  }\n\n  fullServerUrl(): string {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o/' + encode(this.path);\n  }\n\n  bucketOnlyServerUrl(): string {\n    const encode = encodeURIComponent;\n    return '/b/' + encode(this.bucket) + '/o';\n  }\n\n  static makeFromBucketSpec(bucketString: string, host: string): Location {\n    let bucketLocation;\n    try {\n      bucketLocation = Location.makeFromUrl(bucketString, host);\n    } catch (e) {\n      // Not valid URL, use as-is. This lets you put bare bucket names in\n      // config.\n      return new Location(bucketString, '');\n    }\n    if (bucketLocation.path === '') {\n      return bucketLocation;\n    } else {\n      throw invalidDefaultBucket(bucketString);\n    }\n  }\n\n  static makeFromUrl(url: string, host: string): Location {\n    let location: Location | null = null;\n    const bucketDomain = '([A-Za-z0-9.\\\\-_]+)';\n\n    function gsModify(loc: Location): void {\n      if (loc.path.charAt(loc.path.length - 1) === '/') {\n        loc.path_ = loc.path_.slice(0, -1);\n      }\n    }\n    const gsPath = '(/(.*))?$';\n    const gsRegex = new RegExp('^gs://' + bucketDomain + gsPath, 'i');\n    const gsIndices = { bucket: 1, path: 3 };\n\n    function httpModify(loc: Location): void {\n      loc.path_ = decodeURIComponent(loc.path);\n    }\n    const version = 'v[A-Za-z0-9_]+';\n    const firebaseStorageHost = host.replace(/[.]/g, '\\\\.');\n    const firebaseStoragePath = '(/([^?#]*).*)?$';\n    const firebaseStorageRegExp = new RegExp(\n      `^https?://${firebaseStorageHost}/${version}/b/${bucketDomain}/o${firebaseStoragePath}`,\n      'i'\n    );\n    const firebaseStorageIndices = { bucket: 1, path: 3 };\n\n    const cloudStorageHost =\n      host === DEFAULT_HOST\n        ? '(?:storage.googleapis.com|storage.cloud.google.com)'\n        : host;\n    const cloudStoragePath = '([^?#]*)';\n    const cloudStorageRegExp = new RegExp(\n      `^https?://${cloudStorageHost}/${bucketDomain}/${cloudStoragePath}`,\n      'i'\n    );\n    const cloudStorageIndices = { bucket: 1, path: 2 };\n\n    const groups = [\n      { regex: gsRegex, indices: gsIndices, postModify: gsModify },\n      {\n        regex: firebaseStorageRegExp,\n        indices: firebaseStorageIndices,\n        postModify: httpModify\n      },\n      {\n        regex: cloudStorageRegExp,\n        indices: cloudStorageIndices,\n        postModify: httpModify\n      }\n    ];\n    for (let i = 0; i < groups.length; i++) {\n      const group = groups[i];\n      const captures = group.regex.exec(url);\n      if (captures) {\n        const bucketValue = captures[group.indices.bucket];\n        let pathValue = captures[group.indices.path];\n        if (!pathValue) {\n          pathValue = '';\n        }\n        location = new Location(bucketValue, pathValue);\n        group.postModify(location);\n        break;\n      }\n    }\n    if (location == null) {\n      throw invalidUrl(url);\n    }\n    return location;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { StorageError } from './error';\nimport { Request } from './request';\n\n/**\n * A request whose promise always fails.\n */\nexport class FailRequest<T> implements Request<T> {\n  promise_: Promise<T>;\n\n  constructor(error: StorageError) {\n    this.promise_ = Promise.reject<T>(error);\n  }\n\n  /** @inheritDoc */\n  getPromise(): Promise<T> {\n    return this.promise_;\n  }\n\n  /** @inheritDoc */\n  cancel(_appDelete = false): void {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Provides a method for running a function with exponential\n * backoff.\n */\ntype id = (p1: boolean) => void;\n\nexport { id };\n\n/**\n * Accepts a callback for an action to perform (`doRequest`),\n * and then a callback for when the backoff has completed (`backoffCompleteCb`).\n * The callback sent to start requires an argument to call (`onRequestComplete`).\n * When `start` calls `doRequest`, it passes a callback for when the request has\n * completed, `onRequestComplete`. Based on this, the backoff continues, with\n * another call to `doRequest` and the above loop continues until the timeout\n * is hit, or a successful response occurs.\n * @description\n * @param doRequest Callback to perform request\n * @param backoffCompleteCb Callback to call when backoff has been completed\n */\nexport function start(\n  doRequest: (\n    onRequestComplete: (success: boolean) => void,\n    canceled: boolean\n  ) => void,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  backoffCompleteCb: (...args: any[]) => unknown,\n  timeout: number\n): id {\n  // TODO(andysoto): make this code cleaner (probably refactor into an actual\n  // type instead of a bunch of functions with state shared in the closure)\n  let waitSeconds = 1;\n  // Would type this as \"number\" but that doesn't work for Node so ¯\\_(ツ)_/¯\n  // TODO: find a way to exclude Node type definition for storage because storage only works in browser\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let retryTimeoutId: any = null;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let globalTimeoutId: any = null;\n  let hitTimeout = false;\n  let cancelState = 0;\n\n  function canceled(): boolean {\n    return cancelState === 2;\n  }\n  let triggeredCallback = false;\n\n  function triggerCallback(...args: any[]): void {\n    if (!triggeredCallback) {\n      triggeredCallback = true;\n      backoffCompleteCb.apply(null, args);\n    }\n  }\n\n  function callWithDelay(millis: number): void {\n    retryTimeoutId = setTimeout(() => {\n      retryTimeoutId = null;\n      doRequest(responseHandler, canceled());\n    }, millis);\n  }\n\n  function clearGlobalTimeout(): void {\n    if (globalTimeoutId) {\n      clearTimeout(globalTimeoutId);\n    }\n  }\n\n  function responseHandler(success: boolean, ...args: any[]): void {\n    if (triggeredCallback) {\n      clearGlobalTimeout();\n      return;\n    }\n    if (success) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    const mustStop = canceled() || hitTimeout;\n    if (mustStop) {\n      clearGlobalTimeout();\n      triggerCallback.call(null, success, ...args);\n      return;\n    }\n    if (waitSeconds < 64) {\n      /* TODO(andysoto): don't back off so quickly if we know we're offline. */\n      waitSeconds *= 2;\n    }\n    let waitMillis;\n    if (cancelState === 1) {\n      cancelState = 2;\n      waitMillis = 0;\n    } else {\n      waitMillis = (waitSeconds + Math.random()) * 1000;\n    }\n    callWithDelay(waitMillis);\n  }\n  let stopped = false;\n\n  function stop(wasTimeout: boolean): void {\n    if (stopped) {\n      return;\n    }\n    stopped = true;\n    clearGlobalTimeout();\n    if (triggeredCallback) {\n      return;\n    }\n    if (retryTimeoutId !== null) {\n      if (!wasTimeout) {\n        cancelState = 2;\n      }\n      clearTimeout(retryTimeoutId);\n      callWithDelay(0);\n    } else {\n      if (!wasTimeout) {\n        cancelState = 1;\n      }\n    }\n  }\n  callWithDelay(0);\n  globalTimeoutId = setTimeout(() => {\n    hitTimeout = true;\n    stop(true);\n  }, timeout);\n  return stop;\n}\n\n/**\n * Stops the retry loop from repeating.\n * If the function is currently \"in between\" retries, it is invoked immediately\n * with the second parameter as \"true\". Otherwise, it will be invoked once more\n * after the current invocation finishes iff the current invocation would have\n * triggered another retry.\n */\nexport function stop(id: id): void {\n  id(false);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { invalidArgument } from './error';\n\nexport function isJustDef<T>(p: T | null | undefined): p is T | null {\n  return p !== void 0;\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function isFunction(p: unknown): p is Function {\n  return typeof p === 'function';\n}\n\nexport function isNonArrayObject(p: unknown): boolean {\n  return typeof p === 'object' && !Array.isArray(p);\n}\n\nexport function isString(p: unknown): p is string {\n  return typeof p === 'string' || p instanceof String;\n}\n\nexport function isNativeBlob(p: unknown): p is Blob {\n  return isNativeBlobDefined() && p instanceof Blob;\n}\n\nexport function isNativeBlobDefined(): boolean {\n  return typeof Blob !== 'undefined';\n}\n\nexport function validateNumber(\n  argument: string,\n  minValue: number,\n  maxValue: number,\n  value: number\n): void {\n  if (value < minValue) {\n    throw invalidArgument(\n      `Invalid value for '${argument}'. Expected ${minValue} or greater.`\n    );\n  }\n  if (value > maxValue) {\n    throw invalidArgument(\n      `Invalid value for '${argument}'. Expected ${maxValue} or less.`\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Functions to create and manipulate URLs for the server API.\n */\nimport { UrlParams } from './requestinfo';\n\nexport function makeUrl(\n  urlPart: string,\n  host: string,\n  protocol: string\n): string {\n  let origin = host;\n  if (protocol == null) {\n    origin = `https://${host}`;\n  }\n  return `${protocol}://${origin}/v0${urlPart}`;\n}\n\nexport function makeQueryString(params: UrlParams): string {\n  const encode = encodeURIComponent;\n  let queryPart = '?';\n  for (const key in params) {\n    if (params.hasOwnProperty(key)) {\n      const nextPart = encode(key) + '=' + encode(params[key]);\n      queryPart = queryPart + nextPart + '&';\n    }\n  }\n\n  // Chop off the extra '&' or '?' on the end\n  queryPart = queryPart.slice(0, -1);\n  return queryPart;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Network headers */\nexport type Headers = Record<string, string>;\n\n/** Response type exposed by the networking APIs. */\nexport type ConnectionType =\n  | string\n  | ArrayBuffer\n  | Blob\n  | ReadableStream<Uint8Array>;\n\n/**\n * A lightweight wrapper around XMLHttpRequest with a\n * goog.net.XhrIo-like interface.\n *\n * You can create a new connection by invoking `newTextConnection()`,\n * `newBytesConnection()` or `newStreamConnection()`.\n */\nexport interface Connection<T extends ConnectionType> {\n  /**\n   * Sends a request to the provided URL.\n   *\n   * This method never rejects its promise. In case of encountering an error,\n   * it sets an error code internally which can be accessed by calling\n   * getErrorCode() by callers.\n   */\n  send(\n    url: string,\n    method: string,\n    body?: ArrayBufferView | Blob | string | null,\n    headers?: Headers\n  ): Promise<void>;\n\n  getErrorCode(): ErrorCode;\n\n  getStatus(): number;\n\n  getResponse(): T;\n\n  getErrorText(): string;\n\n  /**\n   * Abort the request.\n   */\n  abort(): void;\n\n  getResponseHeader(header: string): string | null;\n\n  addUploadProgressListener(listener: (p1: ProgressEvent) => void): void;\n\n  removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void;\n}\n\n/**\n * Error codes for requests made by the XhrIo wrapper.\n */\nexport enum ErrorCode {\n  NO_ERROR = 0,\n  NETWORK_ERROR = 1,\n  ABORT = 2\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Checks the status code to see if the action should be retried.\n *\n * @param status Current HTTP status code returned by server.\n * @param additionalRetryCodes additional retry codes to check against\n */\nexport function isRetryStatusCode(\n  status: number,\n  additionalRetryCodes: number[]\n): boolean {\n  // The codes for which to retry came from this page:\n  // https://cloud.google.com/storage/docs/exponential-backoff\n  const isFiveHundredCode = status >= 500 && status < 600;\n  const extraRetryCodes = [\n    // Request Timeout: web server didn't receive full request in time.\n    408,\n    // Too Many Requests: you're getting rate-limited, basically.\n    429\n  ];\n  const isExtraRetryCode = extraRetryCodes.indexOf(status) !== -1;\n  const isAdditionalRetryCode = additionalRetryCodes.indexOf(status) !== -1;\n  return isFiveHundredCode || isExtraRetryCode || isAdditionalRetryCode;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines methods used to actually send HTTP requests from\n * abstract representations.\n */\n\nimport { id as backoffId, start, stop } from './backoff';\nimport { appDeleted, canceled, retryLimitExceeded, unknown } from './error';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RequestHandler, RequestInfo } from './requestinfo';\nimport { isJustDef } from './type';\nimport { makeQueryString } from './url';\nimport { Connection, ErrorCode, Headers, ConnectionType } from './connection';\nimport { isRetryStatusCode } from './utils';\n\nexport interface Request<T> {\n  getPromise(): Promise<T>;\n\n  /**\n   * Cancels the request. IMPORTANT: the promise may still be resolved with an\n   * appropriate value (if the request is finished before you call this method,\n   * but the promise has not yet been resolved), so don't just assume it will be\n   * rejected if you call this function.\n   * @param appDelete - True if the cancelation came from the app being deleted.\n   */\n  cancel(appDelete?: boolean): void;\n}\n\n/**\n * Handles network logic for all Storage Requests, including error reporting and\n * retries with backoff.\n *\n * @param I - the type of the backend's network response.\n * @param - O the output type used by the rest of the SDK. The conversion\n * happens in the specified `callback_`.\n */\nclass NetworkRequest<I extends ConnectionType, O> implements Request<O> {\n  private pendingConnection_: Connection<I> | null = null;\n  private backoffId_: backoffId | null = null;\n  private resolve_!: (value?: O | PromiseLike<O>) => void;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private reject_!: (reason?: any) => void;\n  private canceled_: boolean = false;\n  private appDelete_: boolean = false;\n  private promise_: Promise<O>;\n\n  constructor(\n    private url_: string,\n    private method_: string,\n    private headers_: Headers,\n    private body_: string | Blob | Uint8Array | null,\n    private successCodes_: number[],\n    private additionalRetryCodes_: number[],\n    private callback_: RequestHandler<I, O>,\n    private errorCallback_: ErrorHandler | null,\n    private timeout_: number,\n    private progressCallback_: ((p1: number, p2: number) => void) | null,\n    private connectionFactory_: () => Connection<I>,\n    private retry = true\n  ) {\n    this.promise_ = new Promise((resolve, reject) => {\n      this.resolve_ = resolve as (value?: O | PromiseLike<O>) => void;\n      this.reject_ = reject;\n      this.start_();\n    });\n  }\n\n  /**\n   * Actually starts the retry loop.\n   */\n  private start_(): void {\n    const doTheRequest: (\n      backoffCallback: (success: boolean, ...p2: unknown[]) => void,\n      canceled: boolean\n    ) => void = (backoffCallback, canceled) => {\n      if (canceled) {\n        backoffCallback(false, new RequestEndStatus(false, null, true));\n        return;\n      }\n      const connection = this.connectionFactory_();\n      this.pendingConnection_ = connection;\n\n      const progressListener: (\n        progressEvent: ProgressEvent\n      ) => void = progressEvent => {\n        const loaded = progressEvent.loaded;\n        const total = progressEvent.lengthComputable ? progressEvent.total : -1;\n        if (this.progressCallback_ !== null) {\n          this.progressCallback_(loaded, total);\n        }\n      };\n      if (this.progressCallback_ !== null) {\n        connection.addUploadProgressListener(progressListener);\n      }\n\n      // connection.send() never rejects, so we don't need to have a error handler or use catch on the returned promise.\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      connection\n        .send(this.url_, this.method_, this.body_, this.headers_)\n        .then(() => {\n          if (this.progressCallback_ !== null) {\n            connection.removeUploadProgressListener(progressListener);\n          }\n          this.pendingConnection_ = null;\n          const hitServer = connection.getErrorCode() === ErrorCode.NO_ERROR;\n          const status = connection.getStatus();\n          if (\n            !hitServer ||\n            (isRetryStatusCode(status, this.additionalRetryCodes_) &&\n              this.retry)\n          ) {\n            const wasCanceled = connection.getErrorCode() === ErrorCode.ABORT;\n            backoffCallback(\n              false,\n              new RequestEndStatus(false, null, wasCanceled)\n            );\n            return;\n          }\n          const successCode = this.successCodes_.indexOf(status) !== -1;\n          backoffCallback(true, new RequestEndStatus(successCode, connection));\n        });\n    };\n\n    /**\n     * @param requestWentThrough - True if the request eventually went\n     *     through, false if it hit the retry limit or was canceled.\n     */\n    const backoffDone: (\n      requestWentThrough: boolean,\n      status: RequestEndStatus<I>\n    ) => void = (requestWentThrough, status) => {\n      const resolve = this.resolve_;\n      const reject = this.reject_;\n      const connection = status.connection as Connection<I>;\n      if (status.wasSuccessCode) {\n        try {\n          const result = this.callback_(connection, connection.getResponse());\n          if (isJustDef(result)) {\n            resolve(result);\n          } else {\n            resolve();\n          }\n        } catch (e) {\n          reject(e);\n        }\n      } else {\n        if (connection !== null) {\n          const err = unknown();\n          err.serverResponse = connection.getErrorText();\n          if (this.errorCallback_) {\n            reject(this.errorCallback_(connection, err));\n          } else {\n            reject(err);\n          }\n        } else {\n          if (status.canceled) {\n            const err = this.appDelete_ ? appDeleted() : canceled();\n            reject(err);\n          } else {\n            const err = retryLimitExceeded();\n            reject(err);\n          }\n        }\n      }\n    };\n    if (this.canceled_) {\n      backoffDone(false, new RequestEndStatus(false, null, true));\n    } else {\n      this.backoffId_ = start(doTheRequest, backoffDone, this.timeout_);\n    }\n  }\n\n  /** @inheritDoc */\n  getPromise(): Promise<O> {\n    return this.promise_;\n  }\n\n  /** @inheritDoc */\n  cancel(appDelete?: boolean): void {\n    this.canceled_ = true;\n    this.appDelete_ = appDelete || false;\n    if (this.backoffId_ !== null) {\n      stop(this.backoffId_);\n    }\n    if (this.pendingConnection_ !== null) {\n      this.pendingConnection_.abort();\n    }\n  }\n}\n\n/**\n * A collection of information about the result of a network request.\n * @param opt_canceled - Defaults to false.\n */\nexport class RequestEndStatus<I extends ConnectionType> {\n  /**\n   * True if the request was canceled.\n   */\n  canceled: boolean;\n\n  constructor(\n    public wasSuccessCode: boolean,\n    public connection: Connection<I> | null,\n    canceled?: boolean\n  ) {\n    this.canceled = !!canceled;\n  }\n}\n\nexport function addAuthHeader_(\n  headers: Headers,\n  authToken: string | null\n): void {\n  if (authToken !== null && authToken.length > 0) {\n    headers['Authorization'] = 'Firebase ' + authToken;\n  }\n}\n\nexport function addVersionHeader_(\n  headers: Headers,\n  firebaseVersion?: string\n): void {\n  headers['X-Firebase-Storage-Version'] =\n    'webjs/' + (firebaseVersion ?? 'AppManager');\n}\n\nexport function addGmpidHeader_(headers: Headers, appId: string | null): void {\n  if (appId) {\n    headers['X-Firebase-GMPID'] = appId;\n  }\n}\n\nexport function addAppCheckHeader_(\n  headers: Headers,\n  appCheckToken: string | null\n): void {\n  if (appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n}\n\nexport function makeRequest<I extends ConnectionType, O>(\n  requestInfo: RequestInfo<I, O>,\n  appId: string | null,\n  authToken: string | null,\n  appCheckToken: string | null,\n  requestFactory: () => Connection<I>,\n  firebaseVersion?: string,\n  retry = true\n): Request<O> {\n  const queryPart = makeQueryString(requestInfo.urlParams);\n  const url = requestInfo.url + queryPart;\n  const headers = Object.assign({}, requestInfo.headers);\n  addGmpidHeader_(headers, appId);\n  addAuthHeader_(headers, authToken);\n  addVersionHeader_(headers, firebaseVersion);\n  addAppCheckHeader_(headers, appCheckToken);\n  return new NetworkRequest<I, O>(\n    url,\n    requestInfo.method,\n    headers,\n    requestInfo.body,\n    requestInfo.successCodes,\n    requestInfo.additionalRetryCodes,\n    requestInfo.handler,\n    requestInfo.errorHandler,\n    requestInfo.timeout,\n    requestInfo.progressCallback,\n    requestFactory,\n    retry\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Some methods copied from goog.fs.\n * We don't include goog.fs because it pulls in a bunch of Deferred code that\n * bloats the size of the released binary.\n */\nimport { isNativeBlobDefined } from './type';\nimport { StorageErrorCode, StorageError } from './error';\n\nfunction getBlobBuilder(): typeof IBlobBuilder | undefined {\n  if (typeof BlobBuilder !== 'undefined') {\n    return BlobBuilder;\n  } else if (typeof WebKitBlobBuilder !== 'undefined') {\n    return WebKitBlobBuilder;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Concatenates one or more values together and converts them to a Blob.\n *\n * @param args The values that will make up the resulting blob.\n * @return The blob.\n */\nexport function getBlob(...args: Array<string | Blob | ArrayBuffer>): Blob {\n  const BlobBuilder = getBlobBuilder();\n  if (BlobBuilder !== undefined) {\n    const bb = new BlobBuilder();\n    for (let i = 0; i < args.length; i++) {\n      bb.append(args[i]);\n    }\n    return bb.getBlob();\n  } else {\n    if (isNativeBlobDefined()) {\n      return new Blob(args);\n    } else {\n      throw new StorageError(\n        StorageErrorCode.UNSUPPORTED_ENVIRONMENT,\n        \"This browser doesn't seem to support creating Blobs\"\n      );\n    }\n  }\n}\n\n/**\n * Slices the blob. The returned blob contains data from the start byte\n * (inclusive) till the end byte (exclusive). Negative indices cannot be used.\n *\n * @param blob The blob to be sliced.\n * @param start Index of the starting byte.\n * @param end Index of the ending byte.\n * @return The blob slice or null if not supported.\n */\nexport function sliceBlob(blob: Blob, start: number, end: number): Blob | null {\n  if (blob.webkitSlice) {\n    return blob.webkitSlice(start, end);\n  } else if (blob.mozSlice) {\n    return blob.mozSlice(start, end);\n  } else if (blob.slice) {\n    return blob.slice(start, end);\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { missingPolyFill } from '../../implementation/error';\n\n/** Converts a Base64 encoded string to a binary string. */\nexport function decodeBase64(encoded: string): string {\n  if (typeof atob === 'undefined') {\n    throw missingPolyFill('base-64');\n  }\n  return atob(encoded);\n}\n\nexport function decodeUint8Array(data: Uint8Array): string {\n  return new TextDecoder().decode(data);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { unknown, invalidFormat } from './error';\nimport { decodeBase64 } from '../platform/base64';\n\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nexport type StringFormat = (typeof StringFormat)[keyof typeof StringFormat];\n/**\n * An enumeration of the possible string formats for upload.\n * @public\n */\nexport const StringFormat = {\n  /**\n   * Indicates the string should be interpreted \"raw\", that is, as normal text.\n   * The string will be interpreted as UTF-16, then uploaded as a UTF-8 byte\n   * sequence.\n   * Example: The string 'Hello! \\\\ud83d\\\\ude0a' becomes the byte sequence\n   * 48 65 6c 6c 6f 21 20 f0 9f 98 8a\n   */\n  RAW: 'raw',\n  /**\n   * Indicates the string should be interpreted as base64-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO++E6t7/rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64: 'base64',\n  /**\n   * Indicates the string should be interpreted as base64url-encoded data.\n   * Padding characters (trailing '='s) are optional.\n   * Example: The string 'rWmO--E6t7_rlw==' becomes the byte sequence\n   * ad 69 8e fb e1 3a b7 bf eb 97\n   */\n  BASE64URL: 'base64url',\n  /**\n   * Indicates the string is a data URL, such as one obtained from\n   * canvas.toDataURL().\n   * Example: the string 'data:application/octet-stream;base64,aaaa'\n   * becomes the byte sequence\n   * 69 a6 9a\n   * (the content-type \"application/octet-stream\" is also applied, but can\n   * be overridden in the metadata object).\n   */\n  DATA_URL: 'data_url'\n} as const;\n\nexport class StringData {\n  contentType: string | null;\n\n  constructor(public data: Uint8Array, contentType?: string | null) {\n    this.contentType = contentType || null;\n  }\n}\n\n/**\n * @internal\n */\nexport function dataFromString(\n  format: StringFormat,\n  stringData: string\n): StringData {\n  switch (format) {\n    case StringFormat.RAW:\n      return new StringData(utf8Bytes_(stringData));\n    case StringFormat.BASE64:\n    case StringFormat.BASE64URL:\n      return new StringData(base64Bytes_(format, stringData));\n    case StringFormat.DATA_URL:\n      return new StringData(\n        dataURLBytes_(stringData),\n        dataURLContentType_(stringData)\n      );\n    default:\n    // do nothing\n  }\n\n  // assert(false);\n  throw unknown();\n}\n\nexport function utf8Bytes_(value: string): Uint8Array {\n  const b: number[] = [];\n  for (let i = 0; i < value.length; i++) {\n    let c = value.charCodeAt(i);\n    if (c <= 127) {\n      b.push(c);\n    } else {\n      if (c <= 2047) {\n        b.push(192 | (c >> 6), 128 | (c & 63));\n      } else {\n        if ((c & 64512) === 55296) {\n          // The start of a surrogate pair.\n          const valid =\n            i < value.length - 1 && (value.charCodeAt(i + 1) & 64512) === 56320;\n          if (!valid) {\n            // The second surrogate wasn't there.\n            b.push(239, 191, 189);\n          } else {\n            const hi = c;\n            const lo = value.charCodeAt(++i);\n            c = 65536 | ((hi & 1023) << 10) | (lo & 1023);\n            b.push(\n              240 | (c >> 18),\n              128 | ((c >> 12) & 63),\n              128 | ((c >> 6) & 63),\n              128 | (c & 63)\n            );\n          }\n        } else {\n          if ((c & 64512) === 56320) {\n            // Invalid low surrogate.\n            b.push(239, 191, 189);\n          } else {\n            b.push(224 | (c >> 12), 128 | ((c >> 6) & 63), 128 | (c & 63));\n          }\n        }\n      }\n    }\n  }\n  return new Uint8Array(b);\n}\n\nexport function percentEncodedBytes_(value: string): Uint8Array {\n  let decoded;\n  try {\n    decoded = decodeURIComponent(value);\n  } catch (e) {\n    throw invalidFormat(StringFormat.DATA_URL, 'Malformed data URL.');\n  }\n  return utf8Bytes_(decoded);\n}\n\nexport function base64Bytes_(format: StringFormat, value: string): Uint8Array {\n  switch (format) {\n    case StringFormat.BASE64: {\n      const hasMinus = value.indexOf('-') !== -1;\n      const hasUnder = value.indexOf('_') !== -1;\n      if (hasMinus || hasUnder) {\n        const invalidChar = hasMinus ? '-' : '_';\n        throw invalidFormat(\n          format,\n          \"Invalid character '\" +\n            invalidChar +\n            \"' found: is it base64url encoded?\"\n        );\n      }\n      break;\n    }\n    case StringFormat.BASE64URL: {\n      const hasPlus = value.indexOf('+') !== -1;\n      const hasSlash = value.indexOf('/') !== -1;\n      if (hasPlus || hasSlash) {\n        const invalidChar = hasPlus ? '+' : '/';\n        throw invalidFormat(\n          format,\n          \"Invalid character '\" + invalidChar + \"' found: is it base64 encoded?\"\n        );\n      }\n      value = value.replace(/-/g, '+').replace(/_/g, '/');\n      break;\n    }\n    default:\n    // do nothing\n  }\n  let bytes;\n  try {\n    bytes = decodeBase64(value);\n  } catch (e) {\n    if ((e as Error).message.includes('polyfill')) {\n      throw e;\n    }\n    throw invalidFormat(format, 'Invalid character found');\n  }\n  const array = new Uint8Array(bytes.length);\n  for (let i = 0; i < bytes.length; i++) {\n    array[i] = bytes.charCodeAt(i);\n  }\n  return array;\n}\n\nclass DataURLParts {\n  base64: boolean = false;\n  contentType: string | null = null;\n  rest: string;\n\n  constructor(dataURL: string) {\n    const matches = dataURL.match(/^data:([^,]+)?,/);\n    if (matches === null) {\n      throw invalidFormat(\n        StringFormat.DATA_URL,\n        \"Must be formatted 'data:[<mediatype>][;base64],<data>\"\n      );\n    }\n    const middle = matches[1] || null;\n    if (middle != null) {\n      this.base64 = endsWith(middle, ';base64');\n      this.contentType = this.base64\n        ? middle.substring(0, middle.length - ';base64'.length)\n        : middle;\n    }\n    this.rest = dataURL.substring(dataURL.indexOf(',') + 1);\n  }\n}\n\nexport function dataURLBytes_(dataUrl: string): Uint8Array {\n  const parts = new DataURLParts(dataUrl);\n  if (parts.base64) {\n    return base64Bytes_(StringFormat.BASE64, parts.rest);\n  } else {\n    return percentEncodedBytes_(parts.rest);\n  }\n}\n\nexport function dataURLContentType_(dataUrl: string): string | null {\n  const parts = new DataURLParts(dataUrl);\n  return parts.contentType;\n}\n\nfunction endsWith(s: string, end: string): boolean {\n  const longEnough = s.length >= end.length;\n  if (!longEnough) {\n    return false;\n  }\n\n  return s.substring(s.length - end.length) === end;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @file Provides a Blob-like wrapper for various binary types (including the\n * native Blob type). This makes it possible to upload types like ArrayBuffers,\n * making uploads possible in environments without the native Blob type.\n */\nimport { sliceBlob, getBlob } from './fs';\nimport { StringFormat, dataFromString } from './string';\nimport { isNativeBlob, isNativeBlobDefined, isString } from './type';\n\n/**\n * @param opt_elideCopy - If true, doesn't copy mutable input data\n *     (e.g. Uint8Arrays). Pass true only if you know the objects will not be\n *     modified after this blob's construction.\n *\n * @internal\n */\nexport class FbsBlob {\n  private data_!: Blob | Uint8Array;\n  private size_: number;\n  private type_: string;\n\n  constructor(data: Blob | Uint8Array | ArrayBuffer, elideCopy?: boolean) {\n    let size: number = 0;\n    let blobType: string = '';\n    if (isNativeBlob(data)) {\n      this.data_ = data as Blob;\n      size = (data as Blob).size;\n      blobType = (data as Blob).type;\n    } else if (data instanceof ArrayBuffer) {\n      if (elideCopy) {\n        this.data_ = new Uint8Array(data);\n      } else {\n        this.data_ = new Uint8Array(data.byteLength);\n        this.data_.set(new Uint8Array(data));\n      }\n      size = this.data_.length;\n    } else if (data instanceof Uint8Array) {\n      if (elideCopy) {\n        this.data_ = data as Uint8Array;\n      } else {\n        this.data_ = new Uint8Array(data.length);\n        this.data_.set(data as Uint8Array);\n      }\n      size = data.length;\n    }\n    this.size_ = size;\n    this.type_ = blobType;\n  }\n\n  size(): number {\n    return this.size_;\n  }\n\n  type(): string {\n    return this.type_;\n  }\n\n  slice(startByte: number, endByte: number): FbsBlob | null {\n    if (isNativeBlob(this.data_)) {\n      const realBlob = this.data_ as Blob;\n      const sliced = sliceBlob(realBlob, startByte, endByte);\n      if (sliced === null) {\n        return null;\n      }\n      return new FbsBlob(sliced);\n    } else {\n      const slice = new Uint8Array(\n        (this.data_ as Uint8Array).buffer,\n        startByte,\n        endByte - startByte\n      );\n      return new FbsBlob(slice, true);\n    }\n  }\n\n  static getBlob(...args: Array<string | FbsBlob>): FbsBlob | null {\n    if (isNativeBlobDefined()) {\n      const blobby: Array<Blob | Uint8Array | string> = args.map(\n        (val: string | FbsBlob): Blob | Uint8Array | string => {\n          if (val instanceof FbsBlob) {\n            return val.data_;\n          } else {\n            return val;\n          }\n        }\n      );\n      return new FbsBlob(getBlob.apply(null, blobby));\n    } else {\n      const uint8Arrays: Uint8Array[] = args.map(\n        (val: string | FbsBlob): Uint8Array => {\n          if (isString(val)) {\n            return dataFromString(StringFormat.RAW, val as string).data;\n          } else {\n            // Blobs don't exist, so this has to be a Uint8Array.\n            return (val as FbsBlob).data_ as Uint8Array;\n          }\n        }\n      );\n      let finalLength = 0;\n      uint8Arrays.forEach((array: Uint8Array): void => {\n        finalLength += array.byteLength;\n      });\n      const merged = new Uint8Array(finalLength);\n      let index = 0;\n      uint8Arrays.forEach((array: Uint8Array) => {\n        for (let i = 0; i < array.length; i++) {\n          merged[index++] = array[i];\n        }\n      });\n      return new FbsBlob(merged, true);\n    }\n  }\n\n  uploadData(): Blob | Uint8Array {\n    return this.data_;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isNonArrayObject } from './type';\n\n/**\n * Returns the Object resulting from parsing the given JSON, or null if the\n * given string does not represent a JSON object.\n */\nexport function jsonObjectOrNull(\n  s: string\n): { [name: string]: unknown } | null {\n  let obj;\n  try {\n    obj = JSON.parse(s);\n  } catch (e) {\n    return null;\n  }\n  if (isNonArrayObject(obj)) {\n    return obj;\n  } else {\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Contains helper methods for manipulating paths.\n */\n\n/**\n * @return Null if the path is already at the root.\n */\nexport function parent(path: string): string | null {\n  if (path.length === 0) {\n    return null;\n  }\n  const index = path.lastIndexOf('/');\n  if (index === -1) {\n    return '';\n  }\n  const newPath = path.slice(0, index);\n  return newPath;\n}\n\nexport function child(path: string, childPath: string): string {\n  const canonicalChildPath = childPath\n    .split('/')\n    .filter(component => component.length > 0)\n    .join('/');\n  if (path.length === 0) {\n    return canonicalChildPath;\n  } else {\n    return path + '/' + canonicalChildPath;\n  }\n}\n\n/**\n * Returns the last component of a path.\n * '/foo/bar' -> 'bar'\n * '/foo/bar/baz/' -> 'baz/'\n * '/a' -> 'a'\n */\nexport function lastComponent(path: string): string {\n  const index = path.lastIndexOf('/', path.length - 2);\n  if (index === -1) {\n    return path;\n  } else {\n    return path.slice(index + 1);\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Documentation for the metadata format\n */\nimport { Metadata } from '../metadata';\n\nimport { jsonObjectOrNull } from './json';\nimport { Location } from './location';\nimport { lastComponent } from './path';\nimport { isString } from './type';\nimport { makeUrl, makeQueryString } from './url';\nimport { Reference } from '../reference';\nimport { FirebaseStorageImpl } from '../service';\n\nexport function noXform_<T>(metadata: Metadata, value: T): T {\n  return value;\n}\n\nclass Mapping<T> {\n  local: string;\n  writable: boolean;\n  xform: (p1: Metadata, p2?: T) => T | undefined;\n\n  constructor(\n    public server: string,\n    local?: string | null,\n    writable?: boolean,\n    xform?: ((p1: Metadata, p2?: T) => T | undefined) | null\n  ) {\n    this.local = local || server;\n    this.writable = !!writable;\n    this.xform = xform || noXform_;\n  }\n}\ntype Mappings = Array<Mapping<string> | Mapping<number>>;\n\nexport { Mappings };\n\nlet mappings_: Mappings | null = null;\n\nexport function xformPath(fullPath: string | undefined): string | undefined {\n  if (!isString(fullPath) || fullPath.length < 2) {\n    return fullPath;\n  } else {\n    return lastComponent(fullPath);\n  }\n}\n\nexport function getMappings(): Mappings {\n  if (mappings_) {\n    return mappings_;\n  }\n  const mappings: Mappings = [];\n  mappings.push(new Mapping<string>('bucket'));\n  mappings.push(new Mapping<string>('generation'));\n  mappings.push(new Mapping<string>('metageneration'));\n  mappings.push(new Mapping<string>('name', 'fullPath', true));\n\n  function mappingsXformPath(\n    _metadata: Metadata,\n    fullPath: string | undefined\n  ): string | undefined {\n    return xformPath(fullPath);\n  }\n  const nameMapping = new Mapping<string>('name');\n  nameMapping.xform = mappingsXformPath;\n  mappings.push(nameMapping);\n\n  /**\n   * Coerces the second param to a number, if it is defined.\n   */\n  function xformSize(\n    _metadata: Metadata,\n    size?: number | string\n  ): number | undefined {\n    if (size !== undefined) {\n      return Number(size);\n    } else {\n      return size;\n    }\n  }\n  const sizeMapping = new Mapping<number>('size');\n  sizeMapping.xform = xformSize;\n  mappings.push(sizeMapping);\n  mappings.push(new Mapping<number>('timeCreated'));\n  mappings.push(new Mapping<string>('updated'));\n  mappings.push(new Mapping<string>('md5Hash', null, true));\n  mappings.push(new Mapping<string>('cacheControl', null, true));\n  mappings.push(new Mapping<string>('contentDisposition', null, true));\n  mappings.push(new Mapping<string>('contentEncoding', null, true));\n  mappings.push(new Mapping<string>('contentLanguage', null, true));\n  mappings.push(new Mapping<string>('contentType', null, true));\n  mappings.push(new Mapping<string>('metadata', 'customMetadata', true));\n  mappings_ = mappings;\n  return mappings_;\n}\n\nexport function addRef(metadata: Metadata, service: FirebaseStorageImpl): void {\n  function generateRef(): Reference {\n    const bucket: string = metadata['bucket'] as string;\n    const path: string = metadata['fullPath'] as string;\n    const loc = new Location(bucket, path);\n    return service._makeStorageReference(loc);\n  }\n  Object.defineProperty(metadata, 'ref', { get: generateRef });\n}\n\nexport function fromResource(\n  service: FirebaseStorageImpl,\n  resource: { [name: string]: unknown },\n  mappings: Mappings\n): Metadata {\n  const metadata: Metadata = {} as Metadata;\n  metadata['type'] = 'file';\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    metadata[mapping.local] = (mapping as Mapping<unknown>).xform(\n      metadata,\n      resource[mapping.server]\n    );\n  }\n  addRef(metadata, service);\n  return metadata;\n}\n\nexport function fromResourceString(\n  service: FirebaseStorageImpl,\n  resourceString: string,\n  mappings: Mappings\n): Metadata | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj as Metadata;\n  return fromResource(service, resource, mappings);\n}\n\nexport function downloadUrlFromResourceString(\n  metadata: Metadata,\n  resourceString: string,\n  host: string,\n  protocol: string\n): string | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  if (!isString(obj['downloadTokens'])) {\n    // This can happen if objects are uploaded through GCS and retrieved\n    // through list, so we don't want to throw an Error.\n    return null;\n  }\n  const tokens: string = obj['downloadTokens'] as string;\n  if (tokens.length === 0) {\n    return null;\n  }\n  const encode = encodeURIComponent;\n  const tokensList = tokens.split(',');\n  const urls = tokensList.map((token: string): string => {\n    const bucket: string = metadata['bucket'] as string;\n    const path: string = metadata['fullPath'] as string;\n    const urlPart = '/b/' + encode(bucket) + '/o/' + encode(path);\n    const base = makeUrl(urlPart, host, protocol);\n    const queryString = makeQueryString({\n      alt: 'media',\n      token\n    });\n    return base + queryString;\n  });\n  return urls[0];\n}\n\nexport function toResourceString(\n  metadata: Partial<Metadata>,\n  mappings: Mappings\n): string {\n  const resource: {\n    [prop: string]: unknown;\n  } = {};\n  const len = mappings.length;\n  for (let i = 0; i < len; i++) {\n    const mapping = mappings[i];\n    if (mapping.writable) {\n      resource[mapping.server] = metadata[mapping.local];\n    }\n  }\n  return JSON.stringify(resource);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Documentation for the listOptions and listResult format\n */\nimport { Location } from './location';\nimport { jsonObjectOrNull } from './json';\nimport { ListResult } from '../list';\nimport { FirebaseStorageImpl } from '../service';\n\n/**\n * Represents the simplified object metadata returned by List API.\n * Other fields are filtered because list in Firebase Rules does not grant\n * the permission to read the metadata.\n */\ninterface ListMetadataResponse {\n  name: string;\n  bucket: string;\n}\n\n/**\n * Represents the JSON response of List API.\n */\ninterface ListResultResponse {\n  prefixes: string[];\n  items: ListMetadataResponse[];\n  nextPageToken?: string;\n}\n\nconst PREFIXES_KEY = 'prefixes';\nconst ITEMS_KEY = 'items';\n\nfunction fromBackendResponse(\n  service: FirebaseStorageImpl,\n  bucket: string,\n  resource: ListResultResponse\n): ListResult {\n  const listResult: ListResult = {\n    prefixes: [],\n    items: [],\n    nextPageToken: resource['nextPageToken']\n  };\n  if (resource[PREFIXES_KEY]) {\n    for (const path of resource[PREFIXES_KEY]) {\n      const pathWithoutTrailingSlash = path.replace(/\\/$/, '');\n      const reference = service._makeStorageReference(\n        new Location(bucket, pathWithoutTrailingSlash)\n      );\n      listResult.prefixes.push(reference);\n    }\n  }\n\n  if (resource[ITEMS_KEY]) {\n    for (const item of resource[ITEMS_KEY]) {\n      const reference = service._makeStorageReference(\n        new Location(bucket, item['name'])\n      );\n      listResult.items.push(reference);\n    }\n  }\n  return listResult;\n}\n\nexport function fromResponseString(\n  service: FirebaseStorageImpl,\n  bucket: string,\n  resourceString: string\n): ListResult | null {\n  const obj = jsonObjectOrNull(resourceString);\n  if (obj === null) {\n    return null;\n  }\n  const resource = obj as unknown as ListResultResponse;\n  return fromBackendResponse(service, bucket, resource);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { StorageError } from './error';\nimport { Headers, Connection, ConnectionType } from './connection';\n\n/**\n * Type for url params stored in RequestInfo.\n */\nexport interface UrlParams {\n  [name: string]: string | number;\n}\n\n/**\n * A function that converts a server response to the API type expected by the\n * SDK.\n *\n * @param I - the type of the backend's network response\n * @param O - the output response type used by the rest of the SDK.\n */\nexport type RequestHandler<I extends ConnectionType, O> = (\n  connection: Connection<I>,\n  response: I\n) => O;\n\n/** A function to handle an error. */\nexport type ErrorHandler = (\n  connection: Connection<ConnectionType>,\n  response: StorageError\n) => StorageError;\n\n/**\n * Contains a fully specified request.\n *\n * @param I - the type of the backend's network response.\n * @param O - the output response type used by the rest of the SDK.\n */\nexport class RequestInfo<I extends ConnectionType, O> {\n  urlParams: UrlParams = {};\n  headers: Headers = {};\n  body: Blob | string | Uint8Array | null = null;\n  errorHandler: ErrorHandler | null = null;\n\n  /**\n   * Called with the current number of bytes uploaded and total size (-1 if not\n   * computable) of the request body (i.e. used to report upload progress).\n   */\n  progressCallback: ((p1: number, p2: number) => void) | null = null;\n  successCodes: number[] = [200];\n  additionalRetryCodes: number[] = [];\n\n  constructor(\n    public url: string,\n    public method: string,\n    /**\n     * Returns the value with which to resolve the request's promise. Only called\n     * if the request is successful. Throw from this function to reject the\n     * returned Request's promise with the thrown error.\n     * Note: The XhrIo passed to this function may be reused after this callback\n     * returns. Do not keep a reference to it in any way.\n     */\n    public handler: RequestHandler<I, O>,\n    public timeout: number\n  ) {}\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines methods for interacting with the network.\n */\n\nimport { Metadata } from '../metadata';\nimport { ListResult } from '../list';\nimport { FbsBlob } from './blob';\nimport {\n  StorageError,\n  cannotSliceBlob,\n  unauthenticated,\n  quotaExceeded,\n  unauthorized,\n  objectNotFound,\n  serverFileWrongSize,\n  unknown,\n  unauthorizedApp\n} from './error';\nimport { Location } from './location';\nimport {\n  Mappings,\n  fromResourceString,\n  downloadUrlFromResourceString,\n  toResourceString\n} from './metadata';\nimport { fromResponseString } from './list';\nimport { RequestInfo, UrlParams } from './requestinfo';\nimport { isString } from './type';\nimport { makeUrl } from './url';\nimport { Connection, ConnectionType } from './connection';\nimport { FirebaseStorageImpl } from '../service';\n\n/**\n * Throws the UNKNOWN StorageError if cndn is false.\n */\nexport function handlerCheck(cndn: boolean): void {\n  if (!cndn) {\n    throw unknown();\n  }\n}\n\nexport function metadataHandler(\n  service: FirebaseStorageImpl,\n  mappings: Mappings\n): (p1: Connection<string>, p2: string) => Metadata {\n  function handler(xhr: Connection<string>, text: string): Metadata {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return metadata as Metadata;\n  }\n  return handler;\n}\n\nexport function listHandler(\n  service: FirebaseStorageImpl,\n  bucket: string\n): (p1: Connection<string>, p2: string) => ListResult {\n  function handler(xhr: Connection<string>, text: string): ListResult {\n    const listResult = fromResponseString(service, bucket, text);\n    handlerCheck(listResult !== null);\n    return listResult as ListResult;\n  }\n  return handler;\n}\n\nexport function downloadUrlHandler(\n  service: FirebaseStorageImpl,\n  mappings: Mappings\n): (p1: Connection<string>, p2: string) => string | null {\n  function handler(xhr: Connection<string>, text: string): string | null {\n    const metadata = fromResourceString(service, text, mappings);\n    handlerCheck(metadata !== null);\n    return downloadUrlFromResourceString(\n      metadata as Metadata,\n      text,\n      service.host,\n      service._protocol\n    );\n  }\n  return handler;\n}\n\nexport function sharedErrorHandler(\n  location: Location\n): (p1: Connection<ConnectionType>, p2: StorageError) => StorageError {\n  function errorHandler(\n    xhr: Connection<ConnectionType>,\n    err: StorageError\n  ): StorageError {\n    let newErr: StorageError;\n    if (xhr.getStatus() === 401) {\n      if (\n        // This exact message string is the only consistent part of the\n        // server's error response that identifies it as an App Check error.\n        xhr.getErrorText().includes('Firebase App Check token is invalid')\n      ) {\n        newErr = unauthorizedApp();\n      } else {\n        newErr = unauthenticated();\n      }\n    } else {\n      if (xhr.getStatus() === 402) {\n        newErr = quotaExceeded(location.bucket);\n      } else {\n        if (xhr.getStatus() === 403) {\n          newErr = unauthorized(location.path);\n        } else {\n          newErr = err;\n        }\n      }\n    }\n    newErr.status = xhr.getStatus();\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\n\nexport function objectErrorHandler(\n  location: Location\n): (p1: Connection<ConnectionType>, p2: StorageError) => StorageError {\n  const shared = sharedErrorHandler(location);\n\n  function errorHandler(\n    xhr: Connection<ConnectionType>,\n    err: StorageError\n  ): StorageError {\n    let newErr = shared(xhr, err);\n    if (xhr.getStatus() === 404) {\n      newErr = objectNotFound(location.path);\n    }\n    newErr.serverResponse = err.serverResponse;\n    return newErr;\n  }\n  return errorHandler;\n}\n\nexport function getMetadata(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings\n): RequestInfo<string, Metadata> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function list(\n  service: FirebaseStorageImpl,\n  location: Location,\n  delimiter?: string,\n  pageToken?: string | null,\n  maxResults?: number | null\n): RequestInfo<string, ListResult> {\n  const urlParams: UrlParams = {};\n  if (location.isRoot) {\n    urlParams['prefix'] = '';\n  } else {\n    urlParams['prefix'] = location.path + '/';\n  }\n  if (delimiter && delimiter.length > 0) {\n    urlParams['delimiter'] = delimiter;\n  }\n  if (pageToken) {\n    urlParams['pageToken'] = pageToken;\n  }\n  if (maxResults) {\n    urlParams['maxResults'] = maxResults;\n  }\n  const urlPart = location.bucketOnlyServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    listHandler(service, location.bucket),\n    timeout\n  );\n  requestInfo.urlParams = urlParams;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\nexport function getBytes<I extends ConnectionType>(\n  service: FirebaseStorageImpl,\n  location: Location,\n  maxDownloadSizeBytes?: number\n): RequestInfo<I, I> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol) + '?alt=media';\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    (_: Connection<I>, data: I) => data,\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  if (maxDownloadSizeBytes !== undefined) {\n    requestInfo.headers['Range'] = `bytes=0-${maxDownloadSizeBytes}`;\n    requestInfo.successCodes = [200 /* OK */, 206 /* Partial Content */];\n  }\n  return requestInfo;\n}\n\nexport function getDownloadUrl(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings\n): RequestInfo<string, string | null> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'GET';\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    downloadUrlHandler(service, mappings),\n    timeout\n  );\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function updateMetadata(\n  service: FirebaseStorageImpl,\n  location: Location,\n  metadata: Partial<Metadata>,\n  mappings: Mappings\n): RequestInfo<string, Metadata> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'PATCH';\n  const body = toResourceString(metadata, mappings);\n  const headers = { 'Content-Type': 'application/json; charset=utf-8' };\n  const timeout = service.maxOperationRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function deleteObject(\n  service: FirebaseStorageImpl,\n  location: Location\n): RequestInfo<string, void> {\n  const urlPart = location.fullServerUrl();\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'DELETE';\n  const timeout = service.maxOperationRetryTime;\n\n  function handler(_xhr: Connection<string>, _text: string): void {}\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.successCodes = [200, 204];\n  requestInfo.errorHandler = objectErrorHandler(location);\n  return requestInfo;\n}\n\nexport function determineContentType_(\n  metadata: Metadata | null,\n  blob: FbsBlob | null\n): string {\n  return (\n    (metadata && metadata['contentType']) ||\n    (blob && blob.type()) ||\n    'application/octet-stream'\n  );\n}\n\nexport function metadataForUpload_(\n  location: Location,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): Metadata {\n  const metadataClone = Object.assign({}, metadata);\n  metadataClone['fullPath'] = location.path;\n  metadataClone['size'] = blob.size();\n  if (!metadataClone['contentType']) {\n    metadataClone['contentType'] = determineContentType_(null, blob);\n  }\n  return metadataClone;\n}\n\n/**\n * Prepare RequestInfo for uploads as Content-Type: multipart.\n */\nexport function multipartUpload(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): RequestInfo<string, Metadata> {\n  const urlPart = location.bucketOnlyServerUrl();\n  const headers: { [prop: string]: string } = {\n    'X-Goog-Upload-Protocol': 'multipart'\n  };\n\n  function genBoundary(): string {\n    let str = '';\n    for (let i = 0; i < 2; i++) {\n      str = str + Math.random().toString().slice(2);\n    }\n    return str;\n  }\n  const boundary = genBoundary();\n  headers['Content-Type'] = 'multipart/related; boundary=' + boundary;\n  const metadata_ = metadataForUpload_(location, blob, metadata);\n  const metadataString = toResourceString(metadata_, mappings);\n  const preBlobPart =\n    '--' +\n    boundary +\n    '\\r\\n' +\n    'Content-Type: application/json; charset=utf-8\\r\\n\\r\\n' +\n    metadataString +\n    '\\r\\n--' +\n    boundary +\n    '\\r\\n' +\n    'Content-Type: ' +\n    metadata_['contentType'] +\n    '\\r\\n\\r\\n';\n  const postBlobPart = '\\r\\n--' + boundary + '--';\n  const body = FbsBlob.getBlob(preBlobPart, blob, postBlobPart);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n  const urlParams: UrlParams = { name: metadata_['fullPath']! };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(\n    url,\n    method,\n    metadataHandler(service, mappings),\n    timeout\n  );\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @param current The number of bytes that have been uploaded so far.\n * @param total The total number of bytes in the upload.\n * @param opt_finalized True if the server has finished the upload.\n * @param opt_metadata The upload metadata, should\n *     only be passed if opt_finalized is true.\n */\nexport class ResumableUploadStatus {\n  finalized: boolean;\n  metadata: Metadata | null;\n\n  constructor(\n    public current: number,\n    public total: number,\n    finalized?: boolean,\n    metadata?: Metadata | null\n  ) {\n    this.finalized = !!finalized;\n    this.metadata = metadata || null;\n  }\n}\n\nexport function checkResumeHeader_(\n  xhr: Connection<string>,\n  allowed?: string[]\n): string {\n  let status: string | null = null;\n  try {\n    status = xhr.getResponseHeader('X-Goog-Upload-Status');\n  } catch (e) {\n    handlerCheck(false);\n  }\n  const allowedStatus = allowed || ['active'];\n  handlerCheck(!!status && allowedStatus.indexOf(status) !== -1);\n  return status as string;\n}\n\nexport function createResumableUpload(\n  service: FirebaseStorageImpl,\n  location: Location,\n  mappings: Mappings,\n  blob: FbsBlob,\n  metadata?: Metadata | null\n): RequestInfo<string, string> {\n  const urlPart = location.bucketOnlyServerUrl();\n  const metadataForUpload = metadataForUpload_(location, blob, metadata);\n  const urlParams: UrlParams = { name: metadataForUpload['fullPath']! };\n  const url = makeUrl(urlPart, service.host, service._protocol);\n  const method = 'POST';\n  const headers = {\n    'X-Goog-Upload-Protocol': 'resumable',\n    'X-Goog-Upload-Command': 'start',\n    'X-Goog-Upload-Header-Content-Length': `${blob.size()}`,\n    'X-Goog-Upload-Header-Content-Type': metadataForUpload['contentType']!,\n    'Content-Type': 'application/json; charset=utf-8'\n  };\n  const body = toResourceString(metadataForUpload, mappings);\n  const timeout = service.maxUploadRetryTime;\n\n  function handler(xhr: Connection<string>): string {\n    checkResumeHeader_(xhr);\n    let url;\n    try {\n      url = xhr.getResponseHeader('X-Goog-Upload-URL');\n    } catch (e) {\n      handlerCheck(false);\n    }\n    handlerCheck(isString(url));\n    return url as string;\n  }\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.urlParams = urlParams;\n  requestInfo.headers = headers;\n  requestInfo.body = body;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n */\nexport function getResumableUploadStatus(\n  service: FirebaseStorageImpl,\n  location: Location,\n  url: string,\n  blob: FbsBlob\n): RequestInfo<string, ResumableUploadStatus> {\n  const headers = { 'X-Goog-Upload-Command': 'query' };\n\n  function handler(xhr: Connection<string>): ResumableUploadStatus {\n    const status = checkResumeHeader_(xhr, ['active', 'final']);\n    let sizeString: string | null = null;\n    try {\n      sizeString = xhr.getResponseHeader('X-Goog-Upload-Size-Received');\n    } catch (e) {\n      handlerCheck(false);\n    }\n\n    if (!sizeString) {\n      // null or empty string\n      handlerCheck(false);\n    }\n\n    const size = Number(sizeString);\n    handlerCheck(!isNaN(size));\n    return new ResumableUploadStatus(size, blob.size(), status === 'final');\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n\n/**\n * Any uploads via the resumable upload API must transfer a number of bytes\n * that is a multiple of this number.\n */\nexport const RESUMABLE_UPLOAD_CHUNK_SIZE: number = 256 * 1024;\n\n/**\n * @param url From a call to fbs.requests.createResumableUpload.\n * @param chunkSize Number of bytes to upload.\n * @param status The previous status.\n *     If not passed or null, we start from the beginning.\n * @throws fbs.Error If the upload is already complete, the passed in status\n *     has a final size inconsistent with the blob, or the blob cannot be sliced\n *     for upload.\n */\nexport function continueResumableUpload(\n  location: Location,\n  service: FirebaseStorageImpl,\n  url: string,\n  blob: FbsBlob,\n  chunkSize: number,\n  mappings: Mappings,\n  status?: ResumableUploadStatus | null,\n  progressCallback?: ((p1: number, p2: number) => void) | null\n): RequestInfo<string, ResumableUploadStatus> {\n  // TODO(andysoto): standardize on internal asserts\n  // assert(!(opt_status && opt_status.finalized));\n  const status_ = new ResumableUploadStatus(0, 0);\n  if (status) {\n    status_.current = status.current;\n    status_.total = status.total;\n  } else {\n    status_.current = 0;\n    status_.total = blob.size();\n  }\n  if (blob.size() !== status_.total) {\n    throw serverFileWrongSize();\n  }\n  const bytesLeft = status_.total - status_.current;\n  let bytesToUpload = bytesLeft;\n  if (chunkSize > 0) {\n    bytesToUpload = Math.min(bytesToUpload, chunkSize);\n  }\n  const startByte = status_.current;\n  const endByte = startByte + bytesToUpload;\n  let uploadCommand = '';\n  if (bytesToUpload === 0) {\n    uploadCommand = 'finalize';\n  } else if (bytesLeft === bytesToUpload) {\n    uploadCommand = 'upload, finalize';\n  } else {\n    uploadCommand = 'upload';\n  }\n  const headers = {\n    'X-Goog-Upload-Command': uploadCommand,\n    'X-Goog-Upload-Offset': `${status_.current}`\n  };\n  const body = blob.slice(startByte, endByte);\n  if (body === null) {\n    throw cannotSliceBlob();\n  }\n\n  function handler(\n    xhr: Connection<string>,\n    text: string\n  ): ResumableUploadStatus {\n    // TODO(andysoto): Verify the MD5 of each uploaded range:\n    // the 'x-range-md5' header comes back with status code 308 responses.\n    // We'll only be able to bail out though, because you can't re-upload a\n    // range that you previously uploaded.\n    const uploadStatus = checkResumeHeader_(xhr, ['active', 'final']);\n    const newCurrent = status_.current + bytesToUpload;\n    const size = blob.size();\n    let metadata;\n    if (uploadStatus === 'final') {\n      metadata = metadataHandler(service, mappings)(xhr, text);\n    } else {\n      metadata = null;\n    }\n    return new ResumableUploadStatus(\n      newCurrent,\n      size,\n      uploadStatus === 'final',\n      metadata\n    );\n  }\n  const method = 'POST';\n  const timeout = service.maxUploadRetryTime;\n  const requestInfo = new RequestInfo(url, method, handler, timeout);\n  requestInfo.headers = headers;\n  requestInfo.body = body.uploadData();\n  requestInfo.progressCallback = progressCallback || null;\n  requestInfo.errorHandler = sharedErrorHandler(location);\n  return requestInfo;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Enumerations used for upload tasks.\n */\n\n/**\n * An event that is triggered on a task.\n * @internal\n */\nexport type TaskEvent = string;\n\n/**\n * An event that is triggered on a task.\n * @internal\n */\nexport const TaskEvent = {\n  /**\n   * For this event,\n   * <ul>\n   *   <li>The `next` function is triggered on progress updates and when the\n   *       task is paused/resumed with an `UploadTaskSnapshot` as the first\n   *       argument.</li>\n   *   <li>The `error` function is triggered if the upload is canceled or fails\n   *       for another reason.</li>\n   *   <li>The `complete` function is triggered if the upload completes\n   *       successfully.</li>\n   * </ul>\n   */\n  STATE_CHANGED: 'state_changed'\n};\n\n/**\n * Internal enum for task state.\n */\nexport const enum InternalTaskState {\n  RUNNING = 'running',\n  PAUSING = 'pausing',\n  PAUSED = 'paused',\n  SUCCESS = 'success',\n  CANCELING = 'canceling',\n  CANCELED = 'canceled',\n  ERROR = 'error'\n}\n\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nexport type TaskState = (typeof TaskState)[keyof typeof TaskState];\n\n// type keys = keyof TaskState\n/**\n * Represents the current state of a running upload.\n * @internal\n */\nexport const TaskState = {\n  /** The task is currently transferring data. */\n  RUNNING: 'running',\n\n  /** The task was paused by the user. */\n  PAUSED: 'paused',\n\n  /** The task completed successfully. */\n  SUCCESS: 'success',\n\n  /** The task was canceled. */\n  CANCELED: 'canceled',\n\n  /** The task failed with an error. */\n  ERROR: 'error'\n} as const;\n\nexport function taskStateFromInternalTaskState(\n  state: InternalTaskState\n): TaskState {\n  switch (state) {\n    case InternalTaskState.RUNNING:\n    case InternalTaskState.PAUSING:\n    case InternalTaskState.CANCELING:\n      return TaskState.RUNNING;\n    case InternalTaskState.PAUSED:\n      return TaskState.PAUSED;\n    case InternalTaskState.SUCCESS:\n      return TaskState.SUCCESS;\n    case InternalTaskState.CANCELED:\n      return TaskState.CANCELED;\n    case InternalTaskState.ERROR:\n      return TaskState.ERROR;\n    default:\n      // TODO(andysoto): assert(false);\n      return TaskState.ERROR;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { isFunction } from './type';\nimport { StorageError } from './error';\n\n/**\n * Function that is called once for each value in a stream of values.\n */\nexport type NextFn<T> = (value: T) => void;\n\n/**\n * A function that is called with a `StorageError`\n * if the event stream ends due to an error.\n */\nexport type ErrorFn = (error: StorageError) => void;\n\n/**\n * A function that is called if the event stream ends normally.\n */\nexport type CompleteFn = () => void;\n\n/**\n * Unsubscribes from a stream.\n */\nexport type Unsubscribe = () => void;\n\n/**\n * An observer identical to the `Observer` defined in packages/util except the\n * error passed into the ErrorFn is specifically a `StorageError`.\n */\nexport interface StorageObserver<T> {\n  /**\n   * Function that is called once for each value in the event stream.\n   */\n  next?: NextFn<T>;\n  /**\n   * A function that is called with a `StorageError`\n   * if the event stream ends due to an error.\n   */\n  error?: ErrorFn;\n  /**\n   * A function that is called if the event stream ends normally.\n   */\n  complete?: CompleteFn;\n}\n\n/**\n * Subscribes to an event stream.\n */\nexport type Subscribe<T> = (\n  next?: NextFn<T> | StorageObserver<T>,\n  error?: ErrorFn,\n  complete?: CompleteFn\n) => Unsubscribe;\n\nexport class Observer<T> implements StorageObserver<T> {\n  next?: NextFn<T>;\n  error?: ErrorFn;\n  complete?: CompleteFn;\n\n  constructor(\n    nextOrObserver?: NextFn<T> | StorageObserver<T>,\n    error?: ErrorFn,\n    complete?: CompleteFn\n  ) {\n    const asFunctions =\n      isFunction(nextOrObserver) || error != null || complete != null;\n    if (asFunctions) {\n      this.next = nextOrObserver as NextFn<T>;\n      this.error = error ?? undefined;\n      this.complete = complete ?? undefined;\n    } else {\n      const observer = nextOrObserver as {\n        next?: NextFn<T>;\n        error?: ErrorFn;\n        complete?: CompleteFn;\n      };\n      this.next = observer.next;\n      this.error = observer.error;\n      this.complete = observer.complete;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Returns a function that invokes f with its arguments asynchronously as a\n * microtask, i.e. as soon as possible after the current script returns back\n * into browser code.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function async(f: Function): Function {\n  return (...argsToForward: unknown[]) => {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.resolve().then(() => f(...argsToForward));\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Connection,\n  ConnectionType,\n  ErrorCode,\n  Headers\n} from '../../implementation/connection';\nimport { internalError } from '../../implementation/error';\n\n/** An override for the text-based Connection. Used in tests. */\nlet textFactoryOverride: (() => Connection<string>) | null = null;\n\n/**\n * Network layer for browsers. We use this instead of goog.net.XhrIo because\n * goog.net.XhrIo is hyuuuuge and doesn't work in React Native on Android.\n */\nabstract class XhrConnection<T extends ConnectionType>\n  implements Connection<T>\n{\n  protected xhr_: XMLHttpRequest;\n  private errorCode_: ErrorCode;\n  private sendPromise_: Promise<void>;\n  protected sent_: boolean = false;\n\n  constructor() {\n    this.xhr_ = new XMLHttpRequest();\n    this.initXhr();\n    this.errorCode_ = ErrorCode.NO_ERROR;\n    this.sendPromise_ = new Promise(resolve => {\n      this.xhr_.addEventListener('abort', () => {\n        this.errorCode_ = ErrorCode.ABORT;\n        resolve();\n      });\n      this.xhr_.addEventListener('error', () => {\n        this.errorCode_ = ErrorCode.NETWORK_ERROR;\n        resolve();\n      });\n      this.xhr_.addEventListener('load', () => {\n        resolve();\n      });\n    });\n  }\n\n  abstract initXhr(): void;\n\n  send(\n    url: string,\n    method: string,\n    body?: ArrayBufferView | Blob | string,\n    headers?: Headers\n  ): Promise<void> {\n    if (this.sent_) {\n      throw internalError('cannot .send() more than once');\n    }\n    this.sent_ = true;\n    this.xhr_.open(method, url, true);\n    if (headers !== undefined) {\n      for (const key in headers) {\n        if (headers.hasOwnProperty(key)) {\n          this.xhr_.setRequestHeader(key, headers[key].toString());\n        }\n      }\n    }\n    if (body !== undefined) {\n      this.xhr_.send(body);\n    } else {\n      this.xhr_.send();\n    }\n    return this.sendPromise_;\n  }\n\n  getErrorCode(): ErrorCode {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorCode() before sending');\n    }\n    return this.errorCode_;\n  }\n\n  getStatus(): number {\n    if (!this.sent_) {\n      throw internalError('cannot .getStatus() before sending');\n    }\n    try {\n      return this.xhr_.status;\n    } catch (e) {\n      return -1;\n    }\n  }\n\n  getResponse(): T {\n    if (!this.sent_) {\n      throw internalError('cannot .getResponse() before sending');\n    }\n    return this.xhr_.response;\n  }\n\n  getErrorText(): string {\n    if (!this.sent_) {\n      throw internalError('cannot .getErrorText() before sending');\n    }\n    return this.xhr_.statusText;\n  }\n\n  /** Aborts the request. */\n  abort(): void {\n    this.xhr_.abort();\n  }\n\n  getResponseHeader(header: string): string | null {\n    return this.xhr_.getResponseHeader(header);\n  }\n\n  addUploadProgressListener(listener: (p1: ProgressEvent) => void): void {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.addEventListener('progress', listener);\n    }\n  }\n\n  removeUploadProgressListener(listener: (p1: ProgressEvent) => void): void {\n    if (this.xhr_.upload != null) {\n      this.xhr_.upload.removeEventListener('progress', listener);\n    }\n  }\n}\n\nexport class XhrTextConnection extends XhrConnection<string> {\n  initXhr(): void {\n    this.xhr_.responseType = 'text';\n  }\n}\n\nexport function newTextConnection(): Connection<string> {\n  return textFactoryOverride ? textFactoryOverride() : new XhrTextConnection();\n}\n\nexport class XhrBytesConnection extends XhrConnection<ArrayBuffer> {\n  private data_?: ArrayBuffer;\n\n  initXhr(): void {\n    this.xhr_.responseType = 'arraybuffer';\n  }\n}\n\nexport function newBytesConnection(): Connection<ArrayBuffer> {\n  return new XhrBytesConnection();\n}\n\nexport class XhrBlobConnection extends XhrConnection<Blob> {\n  initXhr(): void {\n    this.xhr_.responseType = 'blob';\n  }\n}\n\nexport function newBlobConnection(): Connection<Blob> {\n  return new XhrBlobConnection();\n}\n\nexport function newStreamConnection(): Connection<ReadableStream> {\n  throw new Error('Streams are only supported on Node');\n}\n\nexport function injectTestConnection(\n  factory: (() => Connection<string>) | null\n): void {\n  textFactoryOverride = factory;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Defines types for interacting with blob transfer tasks.\n */\n\nimport { FbsBlob } from './implementation/blob';\nimport {\n  canceled,\n  StorageErrorCode,\n  StorageError,\n  retryLimitExceeded\n} from './implementation/error';\nimport {\n  InternalTaskState,\n  TaskEvent,\n  TaskState,\n  taskStateFromInternalTaskState\n} from './implementation/taskenums';\nimport { Metadata } from './metadata';\nimport {\n  Observer,\n  Subscribe,\n  Unsubscribe,\n  StorageObserver as StorageObserverInternal,\n  NextFn\n} from './implementation/observer';\nimport { Request } from './implementation/request';\nimport { UploadTaskSnapshot, StorageObserver } from './public-types';\nimport { async as fbsAsync } from './implementation/async';\nimport { Mappings, getMappings } from './implementation/metadata';\nimport {\n  createResumableUpload,\n  getResumableUploadStatus,\n  RESUMABLE_UPLOAD_CHUNK_SIZE,\n  ResumableUploadStatus,\n  continueResumableUpload,\n  getMetadata,\n  multipartUpload\n} from './implementation/requests';\nimport { Reference } from './reference';\nimport { newTextConnection } from './platform/connection';\nimport { isRetryStatusCode } from './implementation/utils';\nimport { CompleteFn } from '@firebase/util';\nimport { DEFAULT_MIN_SLEEP_TIME_MILLIS } from './implementation/constants';\n\n/**\n * Represents a blob being uploaded. Can be used to pause/resume/cancel the\n * upload and manage callbacks for various events.\n * @internal\n */\nexport class UploadTask {\n  private _ref: Reference;\n  /**\n   * The data to be uploaded.\n   */\n  _blob: FbsBlob;\n  /**\n   * Metadata related to the upload.\n   */\n  _metadata: Metadata | null;\n  private _mappings: Mappings;\n  /**\n   * Number of bytes transferred so far.\n   */\n  _transferred: number = 0;\n  private _needToFetchStatus: boolean = false;\n  private _needToFetchMetadata: boolean = false;\n  private _observers: Array<StorageObserverInternal<UploadTaskSnapshot>> = [];\n  private _resumable: boolean;\n  /**\n   * Upload state.\n   */\n  _state: InternalTaskState;\n  private _error?: StorageError = undefined;\n  private _uploadUrl?: string = undefined;\n  private _request?: Request<unknown> = undefined;\n  private _chunkMultiplier: number = 1;\n  private _errorHandler: (p1: StorageError) => void;\n  private _metadataErrorHandler: (p1: StorageError) => void;\n  private _resolve?: (p1: UploadTaskSnapshot) => void = undefined;\n  private _reject?: (p1: StorageError) => void = undefined;\n  private pendingTimeout?: ReturnType<typeof setTimeout>;\n  private _promise: Promise<UploadTaskSnapshot>;\n\n  private sleepTime: number;\n\n  private maxSleepTime: number;\n\n  isExponentialBackoffExpired(): boolean {\n    return this.sleepTime > this.maxSleepTime;\n  }\n\n  /**\n   * @param ref - The firebaseStorage.Reference object this task came\n   *     from, untyped to avoid cyclic dependencies.\n   * @param blob - The blob to upload.\n   */\n  constructor(ref: Reference, blob: FbsBlob, metadata: Metadata | null = null) {\n    this._ref = ref;\n    this._blob = blob;\n    this._metadata = metadata;\n    this._mappings = getMappings();\n    this._resumable = this._shouldDoResumable(this._blob);\n    this._state = InternalTaskState.RUNNING;\n    this._errorHandler = error => {\n      this._request = undefined;\n      this._chunkMultiplier = 1;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this._needToFetchStatus = true;\n        this.completeTransitions_();\n      } else {\n        const backoffExpired = this.isExponentialBackoffExpired();\n        if (isRetryStatusCode(error.status, [])) {\n          if (backoffExpired) {\n            error = retryLimitExceeded();\n          } else {\n            this.sleepTime = Math.max(\n              this.sleepTime * 2,\n              DEFAULT_MIN_SLEEP_TIME_MILLIS\n            );\n            this._needToFetchStatus = true;\n            this.completeTransitions_();\n            return;\n          }\n        }\n        this._error = error;\n        this._transition(InternalTaskState.ERROR);\n      }\n    };\n    this._metadataErrorHandler = error => {\n      this._request = undefined;\n      if (error._codeEquals(StorageErrorCode.CANCELED)) {\n        this.completeTransitions_();\n      } else {\n        this._error = error;\n        this._transition(InternalTaskState.ERROR);\n      }\n    };\n    this.sleepTime = 0;\n    this.maxSleepTime = this._ref.storage.maxUploadRetryTime;\n    this._promise = new Promise((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n      this._start();\n    });\n\n    // Prevent uncaught rejections on the internal promise from bubbling out\n    // to the top level with a dummy handler.\n    this._promise.then(null, () => {});\n  }\n\n  private _makeProgressCallback(): (p1: number, p2: number) => void {\n    const sizeBefore = this._transferred;\n    return loaded => this._updateProgress(sizeBefore + loaded);\n  }\n\n  private _shouldDoResumable(blob: FbsBlob): boolean {\n    return blob.size() > 256 * 1024;\n  }\n\n  private _start(): void {\n    if (this._state !== InternalTaskState.RUNNING) {\n      // This can happen if someone pauses us in a resume callback, for example.\n      return;\n    }\n    if (this._request !== undefined) {\n      return;\n    }\n    if (this._resumable) {\n      if (this._uploadUrl === undefined) {\n        this._createResumable();\n      } else {\n        if (this._needToFetchStatus) {\n          this._fetchStatus();\n        } else {\n          if (this._needToFetchMetadata) {\n            // Happens if we miss the metadata on upload completion.\n            this._fetchMetadata();\n          } else {\n            this.pendingTimeout = setTimeout(() => {\n              this.pendingTimeout = undefined;\n              this._continueUpload();\n            }, this.sleepTime);\n          }\n        }\n      }\n    } else {\n      this._oneShotUpload();\n    }\n  }\n\n  private _resolveToken(\n    callback: (authToken: string | null, appCheckToken: string | null) => void\n  ): void {\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    Promise.all([\n      this._ref.storage._getAuthToken(),\n      this._ref.storage._getAppCheckToken()\n    ]).then(([authToken, appCheckToken]) => {\n      switch (this._state) {\n        case InternalTaskState.RUNNING:\n          callback(authToken, appCheckToken);\n          break;\n        case InternalTaskState.CANCELING:\n          this._transition(InternalTaskState.CANCELED);\n          break;\n        case InternalTaskState.PAUSING:\n          this._transition(InternalTaskState.PAUSED);\n          break;\n        default:\n      }\n    });\n  }\n\n  // TODO(andysoto): assert false\n\n  private _createResumable(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = createResumableUpload(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings,\n        this._blob,\n        this._metadata\n      );\n      const createRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = createRequest;\n      createRequest.getPromise().then((url: string) => {\n        this._request = undefined;\n        this._uploadUrl = url;\n        this._needToFetchStatus = false;\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n\n  private _fetchStatus(): void {\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl as string;\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getResumableUploadStatus(\n        this._ref.storage,\n        this._ref._location,\n        url,\n        this._blob\n      );\n      const statusRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = statusRequest;\n      statusRequest.getPromise().then(status => {\n        status = status as ResumableUploadStatus;\n        this._request = undefined;\n        this._updateProgress(status.current);\n        this._needToFetchStatus = false;\n        if (status.finalized) {\n          this._needToFetchMetadata = true;\n        }\n        this.completeTransitions_();\n      }, this._errorHandler);\n    });\n  }\n\n  private _continueUpload(): void {\n    const chunkSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n    const status = new ResumableUploadStatus(\n      this._transferred,\n      this._blob.size()\n    );\n\n    // TODO(andysoto): assert(this.uploadUrl_ !== null);\n    const url = this._uploadUrl as string;\n    this._resolveToken((authToken, appCheckToken) => {\n      let requestInfo;\n      try {\n        requestInfo = continueResumableUpload(\n          this._ref._location,\n          this._ref.storage,\n          url,\n          this._blob,\n          chunkSize,\n          this._mappings,\n          status,\n          this._makeProgressCallback()\n        );\n      } catch (e) {\n        this._error = e as StorageError;\n        this._transition(InternalTaskState.ERROR);\n        return;\n      }\n      const uploadRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken,\n        /*retry=*/ false // Upload requests should not be retried as each retry should be preceded by another query request. Which is handled in this file.\n      );\n      this._request = uploadRequest;\n      uploadRequest.getPromise().then((newStatus: ResumableUploadStatus) => {\n        this._increaseMultiplier();\n        this._request = undefined;\n        this._updateProgress(newStatus.current);\n        if (newStatus.finalized) {\n          this._metadata = newStatus.metadata;\n          this._transition(InternalTaskState.SUCCESS);\n        } else {\n          this.completeTransitions_();\n        }\n      }, this._errorHandler);\n    });\n  }\n\n  private _increaseMultiplier(): void {\n    const currentSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\n\n    // Max chunk size is 32M.\n    if (currentSize * 2 < 32 * 1024 * 1024) {\n      this._chunkMultiplier *= 2;\n    }\n  }\n\n  private _fetchMetadata(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = getMetadata(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings\n      );\n      const metadataRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = metadataRequest;\n      metadataRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._transition(InternalTaskState.SUCCESS);\n      }, this._metadataErrorHandler);\n    });\n  }\n\n  private _oneShotUpload(): void {\n    this._resolveToken((authToken, appCheckToken) => {\n      const requestInfo = multipartUpload(\n        this._ref.storage,\n        this._ref._location,\n        this._mappings,\n        this._blob,\n        this._metadata\n      );\n      const multipartRequest = this._ref.storage._makeRequest(\n        requestInfo,\n        newTextConnection,\n        authToken,\n        appCheckToken\n      );\n      this._request = multipartRequest;\n      multipartRequest.getPromise().then(metadata => {\n        this._request = undefined;\n        this._metadata = metadata;\n        this._updateProgress(this._blob.size());\n        this._transition(InternalTaskState.SUCCESS);\n      }, this._errorHandler);\n    });\n  }\n\n  private _updateProgress(transferred: number): void {\n    const old = this._transferred;\n    this._transferred = transferred;\n\n    // A progress update can make the \"transferred\" value smaller (e.g. a\n    // partial upload not completed by server, after which the \"transferred\"\n    // value may reset to the value at the beginning of the request).\n    if (this._transferred !== old) {\n      this._notifyObservers();\n    }\n  }\n\n  private _transition(state: InternalTaskState): void {\n    if (this._state === state) {\n      return;\n    }\n    switch (state) {\n      case InternalTaskState.CANCELING:\n      case InternalTaskState.PAUSING:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        if (this._request !== undefined) {\n          this._request.cancel();\n        } else if (this.pendingTimeout) {\n          clearTimeout(this.pendingTimeout);\n          this.pendingTimeout = undefined;\n          this.completeTransitions_();\n        }\n        break;\n      case InternalTaskState.RUNNING:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.PAUSING);\n        const wasPaused = this._state === InternalTaskState.PAUSED;\n        this._state = state;\n        if (wasPaused) {\n          this._notifyObservers();\n          this._start();\n        }\n        break;\n      case InternalTaskState.PAUSED:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.CANCELED:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.PAUSED ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._error = canceled();\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.ERROR:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      case InternalTaskState.SUCCESS:\n        // TODO(andysoto):\n        // assert(this.state_ === InternalTaskState.RUNNING ||\n        //        this.state_ === InternalTaskState.PAUSING ||\n        //        this.state_ === InternalTaskState.CANCELING);\n        this._state = state;\n        this._notifyObservers();\n        break;\n      default: // Ignore\n    }\n  }\n\n  private completeTransitions_(): void {\n    switch (this._state) {\n      case InternalTaskState.PAUSING:\n        this._transition(InternalTaskState.PAUSED);\n        break;\n      case InternalTaskState.CANCELING:\n        this._transition(InternalTaskState.CANCELED);\n        break;\n      case InternalTaskState.RUNNING:\n        this._start();\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        break;\n    }\n  }\n\n  /**\n   * A snapshot of the current task state.\n   */\n  get snapshot(): UploadTaskSnapshot {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    return {\n      bytesTransferred: this._transferred,\n      totalBytes: this._blob.size(),\n      state: externalState,\n      metadata: this._metadata!,\n      task: this,\n      ref: this._ref\n    };\n  }\n\n  /**\n   * Adds a callback for an event.\n   * @param type - The type of event to listen for.\n   * @param nextOrObserver -\n   *     The `next` function, which gets called for each item in\n   *     the event stream, or an observer object with some or all of these three\n   *     properties (`next`, `error`, `complete`).\n   * @param error - A function that gets called with a `StorageError`\n   *     if the event stream ends due to an error.\n   * @param completed - A function that gets called if the\n   *     event stream ends normally.\n   * @returns\n   *     If only the event argument is passed, returns a function you can use to\n   *     add callbacks (see the examples above). If more than just the event\n   *     argument is passed, returns a function you can call to unregister the\n   *     callbacks.\n   */\n  on(\n    type: TaskEvent,\n    nextOrObserver?:\n      | StorageObserver<UploadTaskSnapshot>\n      | null\n      | ((snapshot: UploadTaskSnapshot) => unknown),\n    error?: ((a: StorageError) => unknown) | null,\n    completed?: CompleteFn | null\n  ): Unsubscribe | Subscribe<UploadTaskSnapshot> {\n    // Note: `type` isn't being used. Its type is also incorrect. TaskEvent should not be a string.\n    const observer = new Observer(\n      (nextOrObserver as\n        | StorageObserverInternal<UploadTaskSnapshot>\n        | NextFn<UploadTaskSnapshot>) || undefined,\n      error || undefined,\n      completed || undefined\n    );\n    this._addObserver(observer);\n    return () => {\n      this._removeObserver(observer);\n    };\n  }\n\n  /**\n   * This object behaves like a Promise, and resolves with its snapshot data\n   * when the upload completes.\n   * @param onFulfilled - The fulfillment callback. Promise chaining works as normal.\n   * @param onRejected - The rejection callback.\n   */\n  then<U>(\n    onFulfilled?: ((value: UploadTaskSnapshot) => U | Promise<U>) | null,\n    onRejected?: ((error: StorageError) => U | Promise<U>) | null\n  ): Promise<U> {\n    // These casts are needed so that TypeScript can infer the types of the\n    // resulting Promise.\n    return this._promise.then<U>(\n      onFulfilled as (value: UploadTaskSnapshot) => U | Promise<U>,\n      onRejected as ((error: unknown) => Promise<never>) | null\n    );\n  }\n\n  /**\n   * Equivalent to calling `then(null, onRejected)`.\n   */\n  catch<T>(onRejected: (p1: StorageError) => T | Promise<T>): Promise<T> {\n    return this.then(null, onRejected);\n  }\n\n  /**\n   * Adds the given observer.\n   */\n  private _addObserver(observer: Observer<UploadTaskSnapshot>): void {\n    this._observers.push(observer);\n    this._notifyObserver(observer);\n  }\n\n  /**\n   * Removes the given observer.\n   */\n  private _removeObserver(observer: Observer<UploadTaskSnapshot>): void {\n    const i = this._observers.indexOf(observer);\n    if (i !== -1) {\n      this._observers.splice(i, 1);\n    }\n  }\n\n  private _notifyObservers(): void {\n    this._finishPromise();\n    const observers = this._observers.slice();\n    observers.forEach(observer => {\n      this._notifyObserver(observer);\n    });\n  }\n\n  private _finishPromise(): void {\n    if (this._resolve !== undefined) {\n      let triggered = true;\n      switch (taskStateFromInternalTaskState(this._state)) {\n        case TaskState.SUCCESS:\n          fbsAsync(this._resolve.bind(null, this.snapshot))();\n          break;\n        case TaskState.CANCELED:\n        case TaskState.ERROR:\n          const toCall = this._reject as (p1: StorageError) => void;\n          fbsAsync(toCall.bind(null, this._error as StorageError))();\n          break;\n        default:\n          triggered = false;\n          break;\n      }\n      if (triggered) {\n        this._resolve = undefined;\n        this._reject = undefined;\n      }\n    }\n  }\n\n  private _notifyObserver(observer: Observer<UploadTaskSnapshot>): void {\n    const externalState = taskStateFromInternalTaskState(this._state);\n    switch (externalState) {\n      case TaskState.RUNNING:\n      case TaskState.PAUSED:\n        if (observer.next) {\n          fbsAsync(observer.next.bind(observer, this.snapshot))();\n        }\n        break;\n      case TaskState.SUCCESS:\n        if (observer.complete) {\n          fbsAsync(observer.complete.bind(observer))();\n        }\n        break;\n      case TaskState.CANCELED:\n      case TaskState.ERROR:\n        if (observer.error) {\n          fbsAsync(\n            observer.error.bind(observer, this._error as StorageError)\n          )();\n        }\n        break;\n      default:\n        // TODO(andysoto): assert(false);\n        if (observer.error) {\n          fbsAsync(\n            observer.error.bind(observer, this._error as StorageError)\n          )();\n        }\n    }\n  }\n\n  /**\n   * Resumes a paused task. Has no effect on a currently running or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  resume(): boolean {\n    const valid =\n      this._state === InternalTaskState.PAUSED ||\n      this._state === InternalTaskState.PAUSING;\n    if (valid) {\n      this._transition(InternalTaskState.RUNNING);\n    }\n    return valid;\n  }\n\n  /**\n   * Pauses a currently running task. Has no effect on a paused or failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  pause(): boolean {\n    const valid = this._state === InternalTaskState.RUNNING;\n    if (valid) {\n      this._transition(InternalTaskState.PAUSING);\n    }\n    return valid;\n  }\n\n  /**\n   * Cancels a currently running or paused task. Has no effect on a complete or\n   * failed task.\n   * @returns True if the operation took effect, false if ignored.\n   */\n  cancel(): boolean {\n    const valid =\n      this._state === InternalTaskState.RUNNING ||\n      this._state === InternalTaskState.PAUSING;\n    if (valid) {\n      this._transition(InternalTaskState.CANCELING);\n    }\n    return valid;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Defines the Firebase StorageReference class.\n */\n\nimport { FbsBlob } from './implementation/blob';\nimport { Location } from './implementation/location';\nimport { getMappings } from './implementation/metadata';\nimport { child, lastComponent, parent } from './implementation/path';\nimport {\n  deleteObject as requestsDeleteObject,\n  getBytes,\n  getDownloadUrl as requestsGetDownloadUrl,\n  getMetadata as requestsGetMetadata,\n  list as requestsList,\n  multipartUpload,\n  updateMetadata as requestsUpdateMetadata\n} from './implementation/requests';\nimport { ListOptions, UploadResult } from './public-types';\nimport { dataFromString, StringFormat } from './implementation/string';\nimport { Metadata } from './metadata';\nimport { FirebaseStorageImpl } from './service';\nimport { ListResult } from './list';\nimport { UploadTask } from './task';\nimport { invalidRootOperation, noDownloadURL } from './implementation/error';\nimport { validateNumber } from './implementation/type';\nimport {\n  newBlobConnection,\n  newBytesConnection,\n  newStreamConnection,\n  newTextConnection\n} from './platform/connection';\nimport { RequestInfo } from './implementation/requestinfo';\n\n/**\n * Provides methods to interact with a bucket in the Firebase Storage service.\n * @internal\n * @param _location - An fbs.location, or the URL at\n *     which to base this object, in one of the following forms:\n *         gs://<bucket>/<object-path>\n *         http[s]://firebasestorage.googleapis.com/\n *                     <api-version>/b/<bucket>/o/<object-path>\n *     Any query or fragment strings will be ignored in the http[s]\n *     format. If no value is passed, the storage object will use a URL based on\n *     the project ID of the base firebase.App instance.\n */\nexport class Reference {\n  _location: Location;\n\n  constructor(\n    private _service: FirebaseStorageImpl,\n    location: string | Location\n  ) {\n    if (location instanceof Location) {\n      this._location = location;\n    } else {\n      this._location = Location.makeFromUrl(location, _service.host);\n    }\n  }\n\n  /**\n   * Returns the URL for the bucket and path this object references,\n   *     in the form gs://<bucket>/<object-path>\n   * @override\n   */\n  toString(): string {\n    return 'gs://' + this._location.bucket + '/' + this._location.path;\n  }\n\n  protected _newRef(\n    service: FirebaseStorageImpl,\n    location: Location\n  ): Reference {\n    return new Reference(service, location);\n  }\n\n  /**\n   * A reference to the root of this object's bucket.\n   */\n  get root(): Reference {\n    const location = new Location(this._location.bucket, '');\n    return this._newRef(this._service, location);\n  }\n\n  /**\n   * The name of the bucket containing this reference's object.\n   */\n  get bucket(): string {\n    return this._location.bucket;\n  }\n\n  /**\n   * The full path of this object.\n   */\n  get fullPath(): string {\n    return this._location.path;\n  }\n\n  /**\n   * The short name of this object, which is the last component of the full path.\n   * For example, if fullPath is 'full/path/image.png', name is 'image.png'.\n   */\n  get name(): string {\n    return lastComponent(this._location.path);\n  }\n\n  /**\n   * The `StorageService` instance this `StorageReference` is associated with.\n   */\n  get storage(): FirebaseStorageImpl {\n    return this._service;\n  }\n\n  /**\n   * A `StorageReference` pointing to the parent location of this `StorageReference`, or null if\n   * this reference is the root.\n   */\n  get parent(): Reference | null {\n    const newPath = parent(this._location.path);\n    if (newPath === null) {\n      return null;\n    }\n    const location = new Location(this._location.bucket, newPath);\n    return new Reference(this._service, location);\n  }\n\n  /**\n   * Utility function to throw an error in methods that do not accept a root reference.\n   */\n  _throwIfRoot(name: string): void {\n    if (this._location.path === '') {\n      throw invalidRootOperation(name);\n    }\n  }\n}\n\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded bytes.\n */\nexport function getBytesInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): Promise<ArrayBuffer> {\n  ref._throwIfRoot('getBytes');\n  const requestInfo = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newBytesConnection)\n    .then(bytes =>\n      maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n          (bytes as ArrayBuffer).slice(0, maxDownloadSizeBytes)\n        : (bytes as ArrayBuffer)\n    );\n}\n\n/**\n * Download the bytes at the object's location.\n * @returns A Promise containing the downloaded blob.\n */\nexport function getBlobInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): Promise<Blob> {\n  ref._throwIfRoot('getBlob');\n  const requestInfo = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newBlobConnection)\n    .then(blob =>\n      maxDownloadSizeBytes !== undefined\n        ? // GCS may not honor the Range header for small files\n          (blob as Blob).slice(0, maxDownloadSizeBytes)\n        : (blob as Blob)\n    );\n}\n\n/** Stream the bytes at the object's location. */\nexport function getStreamInternal(\n  ref: Reference,\n  maxDownloadSizeBytes?: number\n): ReadableStream {\n  ref._throwIfRoot('getStream');\n  const requestInfo: RequestInfo<ReadableStream, ReadableStream> = getBytes(\n    ref.storage,\n    ref._location,\n    maxDownloadSizeBytes\n  );\n\n  // Transforms the stream so that only `maxDownloadSizeBytes` bytes are piped to the result\n  const newMaxSizeTransform = (n: number): Transformer => {\n    let missingBytes = n;\n    return {\n      transform(chunk, controller: TransformStreamDefaultController) {\n        // GCS may not honor the Range header for small files\n        if (chunk.length < missingBytes) {\n          controller.enqueue(chunk);\n          missingBytes -= chunk.length;\n        } else {\n          controller.enqueue(chunk.slice(0, missingBytes));\n          controller.terminate();\n        }\n      }\n    };\n  };\n\n  const result =\n    maxDownloadSizeBytes !== undefined\n      ? new TransformStream(newMaxSizeTransform(maxDownloadSizeBytes))\n      : new TransformStream(); // The default transformer forwards all chunks to its readable side\n\n  ref.storage\n    .makeRequestWithTokens(requestInfo, newStreamConnection)\n    .then(readableStream => readableStream.pipeThrough(result))\n    .catch(err => result.writable.abort(err));\n\n  return result.readable;\n}\n\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n *\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadBytes(\n  ref: Reference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: Metadata\n): Promise<UploadResult> {\n  ref._throwIfRoot('uploadBytes');\n  const requestInfo = multipartUpload(\n    ref.storage,\n    ref._location,\n    getMappings(),\n    new FbsBlob(data, true),\n    metadata\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newTextConnection)\n    .then(finalMetadata => {\n      return {\n        metadata: finalMetadata,\n        ref\n      };\n    });\n}\n\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - StorageReference where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the newly uploaded data.\n * @returns An UploadTask\n */\nexport function uploadBytesResumable(\n  ref: Reference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: Metadata\n): UploadTask {\n  ref._throwIfRoot('uploadBytesResumable');\n  return new UploadTask(ref, new FbsBlob(data), metadata);\n}\n\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - StorageReference where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the newly uploaded string.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadString(\n  ref: Reference,\n  value: string,\n  format: StringFormat = StringFormat.RAW,\n  metadata?: Metadata\n): Promise<UploadResult> {\n  ref._throwIfRoot('uploadString');\n  const data = dataFromString(format, value);\n  const metadataClone = { ...metadata } as Metadata;\n  if (metadataClone['contentType'] == null && data.contentType != null) {\n    metadataClone['contentType'] = data.contentType!;\n  }\n  return uploadBytes(ref, data.data, metadataClone);\n}\n\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: listAll may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - StorageReference to get list from.\n *\n * @returns A Promise that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nexport function listAll(ref: Reference): Promise<ListResult> {\n  const accumulator: ListResult = {\n    prefixes: [],\n    items: []\n  };\n  return listAllHelper(ref, accumulator).then(() => accumulator);\n}\n\n/**\n * Separated from listAll because async functions can't use \"arguments\".\n * @param ref\n * @param accumulator\n * @param pageToken\n */\nasync function listAllHelper(\n  ref: Reference,\n  accumulator: ListResult,\n  pageToken?: string\n): Promise<void> {\n  const opt: ListOptions = {\n    // maxResults is 1000 by default.\n    pageToken\n  };\n  const nextPage = await list(ref, opt);\n  accumulator.prefixes.push(...nextPage.prefixes);\n  accumulator.items.push(...nextPage.items);\n  if (nextPage.nextPageToken != null) {\n    await listAllHelper(ref, accumulator, nextPage.nextPageToken);\n  }\n}\n\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - StorageReference to get list from.\n * @param options - See ListOptions for details.\n * @returns A Promise that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nexport function list(\n  ref: Reference,\n  options?: ListOptions | null\n): Promise<ListResult> {\n  if (options != null) {\n    if (typeof options.maxResults === 'number') {\n      validateNumber(\n        'options.maxResults',\n        /* minValue= */ 1,\n        /* maxValue= */ 1000,\n        options.maxResults\n      );\n    }\n  }\n  const op = options || {};\n  const requestInfo = requestsList(\n    ref.storage,\n    ref._location,\n    /*delimiter= */ '/',\n    op.pageToken,\n    op.maxResults\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - StorageReference to get metadata from.\n */\nexport function getMetadata(ref: Reference): Promise<Metadata> {\n  ref._throwIfRoot('getMetadata');\n  const requestInfo = requestsGetMetadata(\n    ref.storage,\n    ref._location,\n    getMappings()\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - StorageReference to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves\n *     with the new metadata for this object.\n *     See `firebaseStorage.Reference.prototype.getMetadata`\n */\nexport function updateMetadata(\n  ref: Reference,\n  metadata: Partial<Metadata>\n): Promise<Metadata> {\n  ref._throwIfRoot('updateMetadata');\n  const requestInfo = requestsUpdateMetadata(\n    ref.storage,\n    ref._location,\n    metadata,\n    getMappings()\n  );\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Returns the download URL for the given Reference.\n * @public\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nexport function getDownloadURL(ref: Reference): Promise<string> {\n  ref._throwIfRoot('getDownloadURL');\n  const requestInfo = requestsGetDownloadUrl(\n    ref.storage,\n    ref._location,\n    getMappings()\n  );\n  return ref.storage\n    .makeRequestWithTokens(requestInfo, newTextConnection)\n    .then(url => {\n      if (url === null) {\n        throw noDownloadURL();\n      }\n      return url;\n    });\n}\n\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - StorageReference for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nexport function deleteObject(ref: Reference): Promise<void> {\n  ref._throwIfRoot('deleteObject');\n  const requestInfo = requestsDeleteObject(ref.storage, ref._location);\n  return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\n}\n\n/**\n * Returns reference for object obtained by appending `childPath` to `ref`.\n *\n * @param ref - StorageReference to get child of.\n * @param childPath - Child path from provided ref.\n * @returns A reference to the object obtained by\n * appending childPath, removing any duplicate, beginning, or trailing\n * slashes.\n *\n */\nexport function _getChild(ref: Reference, childPath: string): Reference {\n  const newPath = child(ref._location.path, childPath);\n  const location = new Location(ref._location.bucket, newPath);\n  return new Reference(ref.storage, location);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Location } from './implementation/location';\nimport { FailRequest } from './implementation/failrequest';\nimport { Request, makeRequest } from './implementation/request';\nimport { RequestInfo } from './implementation/requestinfo';\nimport { Reference, _getChild } from './reference';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport {\n  CONFIG_STORAGE_BUCKET_KEY,\n  DEFAULT_HOST,\n  DEFAULT_MAX_OPERATION_RETRY_TIME,\n  DEFAULT_MAX_UPLOAD_RETRY_TIME\n} from './implementation/constants';\nimport {\n  invalidArgument,\n  appDeleted,\n  noDefaultBucket\n} from './implementation/error';\nimport { validateNumber } from './implementation/type';\nimport { FirebaseStorage } from './public-types';\nimport { createMockUserToken, EmulatorMockTokenOptions } from '@firebase/util';\nimport { Connection, ConnectionType } from './implementation/connection';\n\nexport function isUrl(path?: string): boolean {\n  return /^[A-Za-z]+:\\/\\//.test(path as string);\n}\n\n/**\n * Returns a firebaseStorage.Reference for the given url.\n */\nfunction refFromURL(service: FirebaseStorageImpl, url: string): Reference {\n  return new Reference(service, url);\n}\n\n/**\n * Returns a firebaseStorage.Reference for the given path in the default\n * bucket.\n */\nfunction refFromPath(\n  ref: FirebaseStorageImpl | Reference,\n  path?: string\n): Reference {\n  if (ref instanceof FirebaseStorageImpl) {\n    const service = ref;\n    if (service._bucket == null) {\n      throw noDefaultBucket();\n    }\n    const reference = new Reference(service, service._bucket!);\n    if (path != null) {\n      return refFromPath(reference, path);\n    } else {\n      return reference;\n    }\n  } else {\n    // ref is a Reference\n    if (path !== undefined) {\n      return _getChild(ref, path);\n    } else {\n      return ref;\n    }\n  }\n}\n\n/**\n * Returns a storage Reference for the given url.\n * @param storage - `Storage` instance.\n * @param url - URL. If empty, returns root reference.\n * @public\n */\nexport function ref(storage: FirebaseStorageImpl, url?: string): Reference;\n/**\n * Returns a storage Reference for the given path in the\n * default bucket.\n * @param storageOrRef - `Storage` service or storage `Reference`.\n * @param pathOrUrlStorage - path. If empty, returns root reference (if Storage\n * instance provided) or returns same reference (if Reference provided).\n * @public\n */\nexport function ref(\n  storageOrRef: FirebaseStorageImpl | Reference,\n  path?: string\n): Reference;\nexport function ref(\n  serviceOrRef: FirebaseStorageImpl | Reference,\n  pathOrUrl?: string\n): Reference | null {\n  if (pathOrUrl && isUrl(pathOrUrl)) {\n    if (serviceOrRef instanceof FirebaseStorageImpl) {\n      return refFromURL(serviceOrRef, pathOrUrl);\n    } else {\n      throw invalidArgument(\n        'To use ref(service, url), the first argument must be a Storage instance.'\n      );\n    }\n  } else {\n    return refFromPath(serviceOrRef, pathOrUrl);\n  }\n}\n\nfunction extractBucket(\n  host: string,\n  config?: FirebaseOptions\n): Location | null {\n  const bucketString = config?.[CONFIG_STORAGE_BUCKET_KEY];\n  if (bucketString == null) {\n    return null;\n  }\n  return Location.makeFromBucketSpec(bucketString, host);\n}\n\nexport function connectStorageEmulator(\n  storage: FirebaseStorageImpl,\n  host: string,\n  port: number,\n  options: {\n    mockUserToken?: EmulatorMockTokenOptions | string;\n  } = {}\n): void {\n  storage.host = `${host}:${port}`;\n  storage._protocol = 'http';\n  const { mockUserToken } = options;\n  if (mockUserToken) {\n    storage._overrideAuthToken =\n      typeof mockUserToken === 'string'\n        ? mockUserToken\n        : createMockUserToken(mockUserToken, storage.app.options.projectId);\n  }\n}\n\n/**\n * A service that provides Firebase Storage Reference instances.\n * @param opt_url - gs:// url to a custom Storage Bucket\n *\n * @internal\n */\nexport class FirebaseStorageImpl implements FirebaseStorage {\n  _bucket: Location | null = null;\n  /**\n   * This string can be in the formats:\n   * - host\n   * - host:port\n   */\n  private _host: string = DEFAULT_HOST;\n  _protocol: string = 'https';\n  protected readonly _appId: string | null = null;\n  private readonly _requests: Set<Request<unknown>>;\n  private _deleted: boolean = false;\n  private _maxOperationRetryTime: number;\n  private _maxUploadRetryTime: number;\n  _overrideAuthToken?: string;\n\n  constructor(\n    /**\n     * FirebaseApp associated with this StorageService instance.\n     */\n    readonly app: FirebaseApp,\n    readonly _authProvider: Provider<FirebaseAuthInternalName>,\n    /**\n     * @internal\n     */\n    readonly _appCheckProvider: Provider<AppCheckInternalComponentName>,\n    /**\n     * @internal\n     */\n    readonly _url?: string,\n    readonly _firebaseVersion?: string\n  ) {\n    this._maxOperationRetryTime = DEFAULT_MAX_OPERATION_RETRY_TIME;\n    this._maxUploadRetryTime = DEFAULT_MAX_UPLOAD_RETRY_TIME;\n    this._requests = new Set();\n    if (_url != null) {\n      this._bucket = Location.makeFromBucketSpec(_url, this._host);\n    } else {\n      this._bucket = extractBucket(this._host, this.app.options);\n    }\n  }\n\n  /**\n   * The host string for this service, in the form of `host` or\n   * `host:port`.\n   */\n  get host(): string {\n    return this._host;\n  }\n\n  set host(host: string) {\n    this._host = host;\n    if (this._url != null) {\n      this._bucket = Location.makeFromBucketSpec(this._url, host);\n    } else {\n      this._bucket = extractBucket(host, this.app.options);\n    }\n  }\n\n  /**\n   * The maximum time to retry uploads in milliseconds.\n   */\n  get maxUploadRetryTime(): number {\n    return this._maxUploadRetryTime;\n  }\n\n  set maxUploadRetryTime(time: number) {\n    validateNumber(\n      'time',\n      /* minValue=*/ 0,\n      /* maxValue= */ Number.POSITIVE_INFINITY,\n      time\n    );\n    this._maxUploadRetryTime = time;\n  }\n\n  /**\n   * The maximum time to retry operations other than uploads or downloads in\n   * milliseconds.\n   */\n  get maxOperationRetryTime(): number {\n    return this._maxOperationRetryTime;\n  }\n\n  set maxOperationRetryTime(time: number) {\n    validateNumber(\n      'time',\n      /* minValue=*/ 0,\n      /* maxValue= */ Number.POSITIVE_INFINITY,\n      time\n    );\n    this._maxOperationRetryTime = time;\n  }\n\n  async _getAuthToken(): Promise<string | null> {\n    if (this._overrideAuthToken) {\n      return this._overrideAuthToken;\n    }\n    const auth = this._authProvider.getImmediate({ optional: true });\n    if (auth) {\n      const tokenData = await auth.getToken();\n      if (tokenData !== null) {\n        return tokenData.accessToken;\n      }\n    }\n    return null;\n  }\n\n  async _getAppCheckToken(): Promise<string | null> {\n    const appCheck = this._appCheckProvider.getImmediate({ optional: true });\n    if (appCheck) {\n      const result = await appCheck.getToken();\n      // TODO: What do we want to do if there is an error getting the token?\n      // Context: appCheck.getToken() will never throw even if an error happened. In the error case, a dummy token will be\n      // returned along with an error field describing the error. In general, we shouldn't care about the error condition and just use\n      // the token (actual or dummy) to send requests.\n      return result.token;\n    }\n    return null;\n  }\n\n  /**\n   * Stop running requests and prevent more from being created.\n   */\n  _delete(): Promise<void> {\n    if (!this._deleted) {\n      this._deleted = true;\n      this._requests.forEach(request => request.cancel());\n      this._requests.clear();\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Returns a new firebaseStorage.Reference object referencing this StorageService\n   * at the given Location.\n   */\n  _makeStorageReference(loc: Location): Reference {\n    return new Reference(this, loc);\n  }\n\n  /**\n   * @param requestInfo - HTTP RequestInfo object\n   * @param authToken - Firebase auth token\n   */\n  _makeRequest<I extends ConnectionType, O>(\n    requestInfo: RequestInfo<I, O>,\n    requestFactory: () => Connection<I>,\n    authToken: string | null,\n    appCheckToken: string | null,\n    retry = true\n  ): Request<O> {\n    if (!this._deleted) {\n      const request = makeRequest(\n        requestInfo,\n        this._appId,\n        authToken,\n        appCheckToken,\n        requestFactory,\n        this._firebaseVersion,\n        retry\n      );\n      this._requests.add(request);\n      // Request removes itself from set when complete.\n      request.getPromise().then(\n        () => this._requests.delete(request),\n        () => this._requests.delete(request)\n      );\n      return request;\n    } else {\n      return new FailRequest(appDeleted());\n    }\n  }\n\n  async makeRequestWithTokens<I extends ConnectionType, O>(\n    requestInfo: RequestInfo<I, O>,\n    requestFactory: () => Connection<I>\n  ): Promise<O> {\n    const [authToken, appCheckToken] = await Promise.all([\n      this._getAuthToken(),\n      this._getAppCheckToken()\n    ]);\n\n    return this._makeRequest(\n      requestInfo,\n      requestFactory,\n      authToken,\n      appCheckToken\n    ).getPromise();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Storage.\n */\nexport const STORAGE_TYPE = 'storage';\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\n\nimport {\n  ref as refInternal,\n  FirebaseStorageImpl,\n  connectStorageEmulator as connectEmulatorInternal\n} from './service';\nimport { Provider } from '@firebase/component';\n\nimport {\n  StorageReference,\n  FirebaseStorage,\n  UploadResult,\n  ListOptions,\n  ListResult,\n  UploadTask,\n  SettableMetadata,\n  UploadMetadata,\n  FullMetadata\n} from './public-types';\nimport { Metadata as MetadataInternal } from './metadata';\nimport {\n  uploadBytes as uploadBytesInternal,\n  uploadBytesResumable as uploadBytesResumableInternal,\n  uploadString as uploadStringInternal,\n  getMetadata as getMetadataInternal,\n  updateMetadata as updateMetadataInternal,\n  list as listInternal,\n  listAll as listAllInternal,\n  getDownloadURL as getDownloadURLInternal,\n  deleteObject as deleteObjectInternal,\n  Reference,\n  _getChild as _getChildInternal,\n  getBytesInternal\n} from './reference';\nimport { STORAGE_TYPE } from './constants';\nimport {\n  EmulatorMockTokenOptions,\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\nimport { StringFormat } from './implementation/string';\n\nexport { EmulatorMockTokenOptions } from '@firebase/util';\n\nexport { StorageError, StorageErrorCode } from './implementation/error';\n\n/**\n * Public types.\n */\nexport * from './public-types';\n\nexport { Location as _Location } from './implementation/location';\nexport { UploadTask as _UploadTask } from './task';\nexport type { Reference as _Reference } from './reference';\nexport type { FirebaseStorageImpl as _FirebaseStorageImpl } from './service';\nexport { FbsBlob as _FbsBlob } from './implementation/blob';\nexport { dataFromString as _dataFromString } from './implementation/string';\nexport {\n  invalidRootOperation as _invalidRootOperation,\n  invalidArgument as _invalidArgument\n} from './implementation/error';\nexport {\n  TaskEvent as _TaskEvent,\n  TaskState as _TaskState\n} from './implementation/taskenums';\nexport { StringFormat };\n\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise containing the object's bytes\n */\nexport function getBytes(\n  ref: StorageReference,\n  maxDownloadSizeBytes?: number\n): Promise<ArrayBuffer> {\n  ref = getModularInstance(ref);\n  return getBytesInternal(ref as Reference, maxDownloadSizeBytes);\n}\n\n/**\n * Uploads data to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadBytes(\n  ref: StorageReference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: UploadMetadata\n): Promise<UploadResult> {\n  ref = getModularInstance(ref);\n  return uploadBytesInternal(\n    ref as Reference,\n    data,\n    metadata as MetadataInternal\n  );\n}\n\n/**\n * Uploads a string to this object's location.\n * The upload is not resumable.\n * @public\n * @param ref - {@link StorageReference} where string should be uploaded.\n * @param value - The string to upload.\n * @param format - The format of the string to upload.\n * @param metadata - Metadata for the string to upload.\n * @returns A Promise containing an UploadResult\n */\nexport function uploadString(\n  ref: StorageReference,\n  value: string,\n  format?: StringFormat,\n  metadata?: UploadMetadata\n): Promise<UploadResult> {\n  ref = getModularInstance(ref);\n  return uploadStringInternal(\n    ref as Reference,\n    value,\n    format,\n    metadata as MetadataInternal\n  );\n}\n\n/**\n * Uploads data to this object's location.\n * The upload can be paused and resumed, and exposes progress updates.\n * @public\n * @param ref - {@link StorageReference} where data should be uploaded.\n * @param data - The data to upload.\n * @param metadata - Metadata for the data to upload.\n * @returns An UploadTask\n */\nexport function uploadBytesResumable(\n  ref: StorageReference,\n  data: Blob | Uint8Array | ArrayBuffer,\n  metadata?: UploadMetadata\n): UploadTask {\n  ref = getModularInstance(ref);\n  return uploadBytesResumableInternal(\n    ref as Reference,\n    data,\n    metadata as MetadataInternal\n  ) as UploadTask;\n}\n\n/**\n * A `Promise` that resolves with the metadata for this object. If this\n * object doesn't exist or metadata cannot be retrieved, the promise is\n * rejected.\n * @public\n * @param ref - {@link StorageReference} to get metadata from.\n */\nexport function getMetadata(ref: StorageReference): Promise<FullMetadata> {\n  ref = getModularInstance(ref);\n  return getMetadataInternal(ref as Reference) as Promise<FullMetadata>;\n}\n\n/**\n * Updates the metadata for this object.\n * @public\n * @param ref - {@link StorageReference} to update metadata for.\n * @param metadata - The new metadata for the object.\n *     Only values that have been explicitly set will be changed. Explicitly\n *     setting a value to null will remove the metadata.\n * @returns A `Promise` that resolves with the new metadata for this object.\n */\nexport function updateMetadata(\n  ref: StorageReference,\n  metadata: SettableMetadata\n): Promise<FullMetadata> {\n  ref = getModularInstance(ref);\n  return updateMetadataInternal(\n    ref as Reference,\n    metadata as Partial<MetadataInternal>\n  ) as Promise<FullMetadata>;\n}\n\n/**\n * List items (files) and prefixes (folders) under this storage reference.\n *\n * List API is only available for Firebase Rules Version 2.\n *\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\n * delimited folder structure.\n * Refer to GCS's List API if you want to learn more.\n *\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\n * support objects whose paths end with \"/\" or contain two consecutive\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\n * list() may fail if there are too many unsupported objects in the bucket.\n * @public\n *\n * @param ref - {@link StorageReference} to get list from.\n * @param options - See {@link ListOptions} for details.\n * @returns A `Promise` that resolves with the items and prefixes.\n *      `prefixes` contains references to sub-folders and `items`\n *      contains references to objects in this folder. `nextPageToken`\n *      can be used to get the rest of the results.\n */\nexport function list(\n  ref: StorageReference,\n  options?: ListOptions\n): Promise<ListResult> {\n  ref = getModularInstance(ref);\n  return listInternal(ref as Reference, options);\n}\n\n/**\n * List all items (files) and prefixes (folders) under this storage reference.\n *\n * This is a helper method for calling list() repeatedly until there are\n * no more results. The default pagination size is 1000.\n *\n * Note: The results may not be consistent if objects are changed while this\n * operation is running.\n *\n * Warning: `listAll` may potentially consume too many resources if there are\n * too many results.\n * @public\n * @param ref - {@link StorageReference} to get list from.\n *\n * @returns A `Promise` that resolves with all the items and prefixes under\n *      the current storage reference. `prefixes` contains references to\n *      sub-directories and `items` contains references to objects in this\n *      folder. `nextPageToken` is never returned.\n */\nexport function listAll(ref: StorageReference): Promise<ListResult> {\n  ref = getModularInstance(ref);\n  return listAllInternal(ref as Reference);\n}\n\n/**\n * Returns the download URL for the given {@link StorageReference}.\n * @public\n * @param ref - {@link StorageReference} to get the download URL for.\n * @returns A `Promise` that resolves with the download\n *     URL for this object.\n */\nexport function getDownloadURL(ref: StorageReference): Promise<string> {\n  ref = getModularInstance(ref);\n  return getDownloadURLInternal(ref as Reference);\n}\n\n/**\n * Deletes the object at this location.\n * @public\n * @param ref - {@link StorageReference} for object to delete.\n * @returns A `Promise` that resolves if the deletion succeeds.\n */\nexport function deleteObject(ref: StorageReference): Promise<void> {\n  ref = getModularInstance(ref);\n  return deleteObjectInternal(ref as Reference);\n}\n\n/**\n * Returns a {@link StorageReference} for the given url.\n * @param storage - {@link FirebaseStorage} instance.\n * @param url - URL. If empty, returns root reference.\n * @public\n */\nexport function ref(storage: FirebaseStorage, url?: string): StorageReference;\n/**\n * Returns a {@link StorageReference} for the given path in the\n * default bucket.\n * @param storageOrRef - {@link FirebaseStorage} or {@link StorageReference}.\n * @param pathOrUrlStorage - path. If empty, returns root reference (if {@link FirebaseStorage}\n * instance provided) or returns same reference (if {@link StorageReference} provided).\n * @public\n */\nexport function ref(\n  storageOrRef: FirebaseStorage | StorageReference,\n  path?: string\n): StorageReference;\nexport function ref(\n  serviceOrRef: FirebaseStorage | StorageReference,\n  pathOrUrl?: string\n): StorageReference | null {\n  serviceOrRef = getModularInstance(serviceOrRef);\n  return refInternal(\n    serviceOrRef as FirebaseStorageImpl | Reference,\n    pathOrUrl\n  );\n}\n\n/**\n * @internal\n */\nexport function _getChild(ref: StorageReference, childPath: string): Reference {\n  return _getChildInternal(ref as Reference, childPath);\n}\n\n/**\n * Gets a {@link FirebaseStorage} instance for the given Firebase app.\n * @public\n * @param app - Firebase app to get {@link FirebaseStorage} instance for.\n * @param bucketUrl - The gs:// url to your Firebase Storage Bucket.\n * If not passed, uses the app's default Storage Bucket.\n * @returns A {@link FirebaseStorage} instance.\n */\nexport function getStorage(\n  app: FirebaseApp = getApp(),\n  bucketUrl?: string\n): FirebaseStorage {\n  app = getModularInstance(app);\n  const storageProvider: Provider<'storage'> = _getProvider(app, STORAGE_TYPE);\n  const storageInstance = storageProvider.getImmediate({\n    identifier: bucketUrl\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('storage');\n  if (emulator) {\n    connectStorageEmulator(storageInstance, ...emulator);\n  }\n  return storageInstance;\n}\n\n/**\n * Modify this {@link FirebaseStorage} instance to communicate with the Cloud Storage emulator.\n *\n * @param storage - The {@link FirebaseStorage} instance\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @param options - Emulator options. `options.mockUserToken` is the mock auth\n * token to use for unit testing Security Rules.\n * @public\n */\nexport function connectStorageEmulator(\n  storage: FirebaseStorage,\n  host: string,\n  port: number,\n  options: {\n    mockUserToken?: EmulatorMockTokenOptions | string;\n  } = {}\n): void {\n  connectEmulatorInternal(storage as FirebaseStorageImpl, host, port, options);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { StorageReference } from './public-types';\nimport { Reference, getBlobInternal } from './reference';\nimport { getModularInstance } from '@firebase/util';\n\n/**\n * Downloads the data at the object's location. Returns an error if the object\n * is not found.\n *\n * To use this functionality, you have to whitelist your app's origin in your\n * Cloud Storage bucket. See also\n * https://cloud.google.com/storage/docs/configuring-cors\n *\n * This API is not available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A Promise that resolves with a Blob containing the object's bytes\n */\nexport function getBlob(\n  ref: StorageReference,\n  maxDownloadSizeBytes?: number\n): Promise<Blob> {\n  ref = getModularInstance(ref);\n  return getBlobInternal(ref as Reference, maxDownloadSizeBytes);\n}\n\n/**\n * Downloads the data at the object's location. Raises an error event if the\n * object is not found.\n *\n * This API is only available in Node.\n *\n * @public\n * @param ref - StorageReference where data should be downloaded.\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\n * retrieve.\n * @returns A stream with the object's data as bytes\n */\nexport function getStream(\n  ref: StorageReference,\n  maxDownloadSizeBytes?: number\n): ReadableStream {\n  throw new Error('getStream() is only supported by NodeJS builds');\n}\n", "/**\n * Cloud Storage for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\n\nimport { FirebaseStorageImpl } from '../src/service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\n\nimport { name, version } from '../package.json';\n\nimport { FirebaseStorage } from './public-types';\nimport { STORAGE_TYPE } from './constants';\n\nexport * from './api';\nexport * from './api.browser';\n\nfunction factory(\n  container: ComponentContainer,\n  { instanceIdentifier: url }: InstanceFactoryOptions\n): FirebaseStorage {\n  const app = container.getProvider('app').getImmediate();\n  const authProvider = container.getProvider('auth-internal');\n  const appCheckProvider = container.getProvider('app-check-internal');\n\n  return new FirebaseStorageImpl(\n    app,\n    authProvider,\n    appCheckProvider,\n    url,\n    SDK_VERSION\n  );\n}\n\nfunction registerStorage(): void {\n  _registerComponent(\n    new Component(\n      STORAGE_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n  //RUNTIME_ENV will be replaced during the compilation to \"node\" for nodejs and an empty string for browser\n  registerVersion(name, version, '__RUNTIME_ENV__');\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterStorage();\n"], "mappings": ";;;;;;;;;;;;;;;AAuBO,IAAM,eAAe;AAKrB,IAAM,4BAA4B;AAOlC,IAAM,mCAAmC,IAAI,KAAK;AAOlD,IAAM,gCAAgC,KAAK,KAAK;AAKhD,IAAM,gCAAgC;ACtBvC,IAAO,eAAP,MAAO,sBAAqB,cAAa;;;;;;;EAa7C,YAAY,MAAwB,SAAyB,UAAU,GAAC;AACtE,UACE,YAAY,IAAI,GAChB,qBAAqB,OAAO,KAAK,YAAY,IAAI,CAAC,GAAG;AAHI,SAAO,UAAP;AAR7D,SAAA,aAAgD,EAAE,gBAAgB,KAAI;AAapE,SAAK,eAAe,KAAK;AAGzB,WAAO,eAAe,MAAM,cAAa,SAAS;;EAGpD,IAAI,SAAM;AACR,WAAO,KAAK;;EAGd,IAAI,OAAO,QAAc;AACvB,SAAK,UAAU;;;;;EAMjB,YAAY,MAAsB;AAChC,WAAO,YAAY,IAAI,MAAM,KAAK;;;;;EAMpC,IAAI,iBAAc;AAChB,WAAO,KAAK,WAAW;;EAGzB,IAAI,eAAe,gBAA6B;AAC9C,SAAK,WAAW,iBAAiB;AACjC,QAAI,KAAK,WAAW,gBAAgB;AAClC,WAAK,UAAU,GAAG,KAAK,YAAY;EAAK,KAAK,WAAW,cAAc;IACvE,OAAM;AACL,WAAK,UAAU,KAAK;IACrB;;AAEJ;IAQW;CAAZ,SAAYA,mBAAgB;AAE1B,EAAAA,kBAAA,SAAA,IAAA;AACA,EAAAA,kBAAA,kBAAA,IAAA;AACA,EAAAA,kBAAA,kBAAA,IAAA;AACA,EAAAA,kBAAA,mBAAA,IAAA;AACA,EAAAA,kBAAA,gBAAA,IAAA;AACA,EAAAA,kBAAA,iBAAA,IAAA;AACA,EAAAA,kBAAA,cAAA,IAAA;AACA,EAAAA,kBAAA,kBAAA,IAAA;AACA,EAAAA,kBAAA,sBAAA,IAAA;AACA,EAAAA,kBAAA,kBAAA,IAAA;AACA,EAAAA,kBAAA,UAAA,IAAA;AAEA,EAAAA,kBAAA,oBAAA,IAAA;AACA,EAAAA,kBAAA,aAAA,IAAA;AACA,EAAAA,kBAAA,wBAAA,IAAA;AACA,EAAAA,kBAAA,mBAAA,IAAA;AACA,EAAAA,kBAAA,mBAAA,IAAA;AACA,EAAAA,kBAAA,wBAAA,IAAA;AACA,EAAAA,kBAAA,iBAAA,IAAA;AACA,EAAAA,kBAAA,kBAAA,IAAA;AACA,EAAAA,kBAAA,wBAAA,IAAA;AACA,EAAAA,kBAAA,aAAA,IAAA;AACA,EAAAA,kBAAA,wBAAA,IAAA;AACA,EAAAA,kBAAA,gBAAA,IAAA;AACA,EAAAA,kBAAA,gBAAA,IAAA;AACA,EAAAA,kBAAA,yBAAA,IAAA;AACF,GA5BY,qBAAA,mBA4BX,CAAA,EAAA;AAEK,SAAU,YAAY,MAAsB;AAChD,SAAO,aAAa;AACtB;SAEgB,UAAO;AACrB,QAAM,UACJ;AAEF,SAAO,IAAI,aAAa,iBAAiB,SAAS,OAAO;AAC3D;AAEM,SAAU,eAAe,MAAY;AACzC,SAAO,IAAI,aACT,iBAAiB,kBACjB,aAAa,OAAO,mBAAmB;AAE3C;AAgBM,SAAU,cAAc,QAAc;AAC1C,SAAO,IAAI,aACT,iBAAiB,gBACjB,uBACE,SACA,wEACuC;AAE7C;SAEgB,kBAAe;AAC7B,QAAM,UACJ;AAEF,SAAO,IAAI,aAAa,iBAAiB,iBAAiB,OAAO;AACnE;SAEgB,kBAAe;AAC7B,SAAO,IAAI,aACT,iBAAiB,kBACjB,+EAA+E;AAEnF;AAEM,SAAU,aAAa,MAAY;AACvC,SAAO,IAAI,aACT,iBAAiB,cACjB,8CAA8C,OAAO,IAAI;AAE7D;SAEgB,qBAAkB;AAChC,SAAO,IAAI,aACT,iBAAiB,sBACjB,0DAA0D;AAE9D;SAmBgB,WAAQ;AACtB,SAAO,IAAI,aACT,iBAAiB,UACjB,oCAAoC;AAExC;AASM,SAAU,WAAW,KAAW;AACpC,SAAO,IAAI,aACT,iBAAiB,aACjB,kBAAkB,MAAM,IAAI;AAEhC;AAEM,SAAU,qBAAqB,QAAc;AACjD,SAAO,IAAI,aACT,iBAAiB,wBACjB,6BAA6B,SAAS,IAAI;AAE9C;SAEgB,kBAAe;AAC7B,SAAO,IAAI,aACT,iBAAiB,mBACjB,+CAEE,4BACA,uCAAuC;AAE7C;SAEgB,kBAAe;AAC7B,SAAO,IAAI,aACT,iBAAiB,mBACjB,wDAAwD;AAE5D;SAEgB,sBAAmB;AACjC,SAAO,IAAI,aACT,iBAAiB,wBACjB,sEAAsE;AAE1E;SAEgB,gBAAa;AAC3B,SAAO,IAAI,aACT,iBAAiB,iBACjB,iDAAiD;AAErD;AAEM,SAAU,gBAAgB,UAAgB;AAC9C,SAAO,IAAI,aACT,iBAAiB,yBACjB,GAAG,QAAQ,wJAAwJ;AAEvK;AAKM,SAAU,gBAAgB,SAAe;AAC7C,SAAO,IAAI,aAAa,iBAAiB,kBAAkB,OAAO;AACpE;SA+BgB,aAAU;AACxB,SAAO,IAAI,aACT,iBAAiB,aACjB,+BAA+B;AAEnC;AAOM,SAAU,qBAAqBC,OAAY;AAC/C,SAAO,IAAI,aACT,iBAAiB,wBACjB,oBACEA,QACA,iHACoD;AAE1D;AAMgB,SAAA,cAAc,QAAgB,SAAe;AAC3D,SAAO,IAAI,aACT,iBAAiB,gBACjB,mCAAmC,SAAS,QAAQ,OAAO;AAE/D;AAYM,SAAU,cAAc,SAAe;AAC3C,QAAM,IAAI,aACR,iBAAiB,gBACjB,qBAAqB,OAAO;AAEhC;ICpUa,iBAAA,UAAQ;EAGnB,YAA4B,QAAgB,MAAY;AAA5B,SAAM,SAAN;AAC1B,SAAK,QAAQ;;EAGf,IAAI,OAAI;AACN,WAAO,KAAK;;EAGd,IAAI,SAAM;AACR,WAAO,KAAK,KAAK,WAAW;;EAG9B,gBAAa;AACX,UAAM,SAAS;AACf,WAAO,QAAQ,OAAO,KAAK,MAAM,IAAI,QAAQ,OAAO,KAAK,IAAI;;EAG/D,sBAAmB;AACjB,UAAM,SAAS;AACf,WAAO,QAAQ,OAAO,KAAK,MAAM,IAAI;;EAGvC,OAAO,mBAAmB,cAAsB,MAAY;AAC1D,QAAI;AACJ,QAAI;AACF,uBAAiB,UAAS,YAAY,cAAc,IAAI;IACzD,SAAQ,GAAG;AAGV,aAAO,IAAI,UAAS,cAAc,EAAE;IACrC;AACD,QAAI,eAAe,SAAS,IAAI;AAC9B,aAAO;IACR,OAAM;AACL,YAAM,qBAAqB,YAAY;IACxC;;EAGH,OAAO,YAAY,KAAa,MAAY;AAC1C,QAAI,WAA4B;AAChC,UAAM,eAAe;AAErB,aAAS,SAAS,KAAa;AAC7B,UAAI,IAAI,KAAK,OAAO,IAAI,KAAK,SAAS,CAAC,MAAM,KAAK;AAChD,YAAI,QAAQ,IAAI,MAAM,MAAM,GAAG,EAAE;MAClC;;AAEH,UAAM,SAAS;AACf,UAAM,UAAU,IAAI,OAAO,WAAW,eAAe,QAAQ,GAAG;AAChE,UAAM,YAAY,EAAE,QAAQ,GAAG,MAAM,EAAC;AAEtC,aAAS,WAAW,KAAa;AAC/B,UAAI,QAAQ,mBAAmB,IAAI,IAAI;;AAEzC,UAAMC,WAAU;AAChB,UAAM,sBAAsB,KAAK,QAAQ,QAAQ,KAAK;AACtD,UAAM,sBAAsB;AAC5B,UAAM,wBAAwB,IAAI,OAChC,aAAa,mBAAmB,IAAIA,QAAO,MAAM,YAAY,KAAK,mBAAmB,IACrF,GAAG;AAEL,UAAM,yBAAyB,EAAE,QAAQ,GAAG,MAAM,EAAC;AAEnD,UAAM,mBACJ,SAAS,eACL,wDACA;AACN,UAAM,mBAAmB;AACzB,UAAM,qBAAqB,IAAI,OAC7B,aAAa,gBAAgB,IAAI,YAAY,IAAI,gBAAgB,IACjE,GAAG;AAEL,UAAM,sBAAsB,EAAE,QAAQ,GAAG,MAAM,EAAC;AAEhD,UAAM,SAAS;MACb,EAAE,OAAO,SAAS,SAAS,WAAW,YAAY,SAAQ;MAC1D;QACE,OAAO;QACP,SAAS;QACT,YAAY;MACb;MACD;QACE,OAAO;QACP,SAAS;QACT,YAAY;MACb;;AAEH,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,WAAW,MAAM,MAAM,KAAK,GAAG;AACrC,UAAI,UAAU;AACZ,cAAM,cAAc,SAAS,MAAM,QAAQ,MAAM;AACjD,YAAI,YAAY,SAAS,MAAM,QAAQ,IAAI;AAC3C,YAAI,CAAC,WAAW;AACd,sBAAY;QACb;AACD,mBAAW,IAAI,UAAS,aAAa,SAAS;AAC9C,cAAM,WAAW,QAAQ;AACzB;MACD;IACF;AACD,QAAI,YAAY,MAAM;AACpB,YAAM,WAAW,GAAG;IACrB;AACD,WAAO;;AAEV;ICrHY,oBAAW;EAGtB,YAAY,OAAmB;AAC7B,SAAK,WAAW,QAAQ,OAAU,KAAK;;;EAIzC,aAAU;AACR,WAAO,KAAK;;;EAId,OAAO,aAAa,OAAK;EAAA;AAC1B;ACCK,SAAU,MACd,WAKA,mBACA,SAAe;AAIf,MAAI,cAAc;AAIlB,MAAI,iBAAsB;AAE1B,MAAI,kBAAuB;AAC3B,MAAI,aAAa;AACjB,MAAI,cAAc;AAElB,WAASC,YAAQ;AACf,WAAO,gBAAgB;;AAEzB,MAAI,oBAAoB;AAExB,WAAS,mBAAmB,MAAW;AACrC,QAAI,CAAC,mBAAmB;AACtB,0BAAoB;AACpB,wBAAkB,MAAM,MAAM,IAAI;IACnC;;AAGH,WAAS,cAAc,QAAc;AACnC,qBAAiB,WAAW,MAAK;AAC/B,uBAAiB;AACjB,gBAAU,iBAAiBA,UAAQ,CAAE;OACpC,MAAM;;AAGX,WAAS,qBAAkB;AACzB,QAAI,iBAAiB;AACnB,mBAAa,eAAe;IAC7B;;AAGH,WAAS,gBAAgB,YAAqB,MAAW;AACvD,QAAI,mBAAmB;AACrB,yBAAkB;AAClB;IACD;AACD,QAAI,SAAS;AACX,yBAAkB;AAClB,sBAAgB,KAAK,MAAM,SAAS,GAAG,IAAI;AAC3C;IACD;AACD,UAAM,WAAWA,UAAQ,KAAM;AAC/B,QAAI,UAAU;AACZ,yBAAkB;AAClB,sBAAgB,KAAK,MAAM,SAAS,GAAG,IAAI;AAC3C;IACD;AACD,QAAI,cAAc,IAAI;AAEpB,qBAAe;IAChB;AACD,QAAI;AACJ,QAAI,gBAAgB,GAAG;AACrB,oBAAc;AACd,mBAAa;IACd,OAAM;AACL,oBAAc,cAAc,KAAK,OAAM,KAAM;IAC9C;AACD,kBAAc,UAAU;;AAE1B,MAAI,UAAU;AAEd,WAASC,MAAK,YAAmB;AAC/B,QAAI,SAAS;AACX;IACD;AACD,cAAU;AACV,uBAAkB;AAClB,QAAI,mBAAmB;AACrB;IACD;AACD,QAAI,mBAAmB,MAAM;AAC3B,UAAI,CAAC,YAAY;AACf,sBAAc;MACf;AACD,mBAAa,cAAc;AAC3B,oBAAc,CAAC;IAChB,OAAM;AACL,UAAI,CAAC,YAAY;AACf,sBAAc;MACf;IACF;;AAEH,gBAAc,CAAC;AACf,oBAAkB,WAAW,MAAK;AAChC,iBAAa;AACb,IAAAA,MAAK,IAAI;KACR,OAAO;AACV,SAAOA;AACT;AASM,SAAU,KAAK,IAAM;AACzB,KAAG,KAAK;AACV;ACrIM,SAAU,UAAa,GAAuB;AAClD,SAAO,MAAM;AACf;AAGM,SAAU,WAAW,GAAU;AACnC,SAAO,OAAO,MAAM;AACtB;AAEM,SAAU,iBAAiB,GAAU;AACzC,SAAO,OAAO,MAAM,YAAY,CAAC,MAAM,QAAQ,CAAC;AAClD;AAEM,SAAU,SAAS,GAAU;AACjC,SAAO,OAAO,MAAM,YAAY,aAAa;AAC/C;AAEM,SAAU,aAAa,GAAU;AACrC,SAAO,oBAAmB,KAAM,aAAa;AAC/C;SAEgB,sBAAmB;AACjC,SAAO,OAAO,SAAS;AACzB;AAEM,SAAU,eACd,UACA,UACA,UACA,OAAa;AAEb,MAAI,QAAQ,UAAU;AACpB,UAAM,gBACJ,sBAAsB,QAAQ,eAAe,QAAQ,cAAc;EAEtE;AACD,MAAI,QAAQ,UAAU;AACpB,UAAM,gBACJ,sBAAsB,QAAQ,eAAe,QAAQ,WAAW;EAEnE;AACH;SCtCgB,QACd,SACA,MACA,UAAgB;AAEhB,MAAI,SAAS;AACb,MAAI,YAAY,MAAM;AACpB,aAAS,WAAW,IAAI;EACzB;AACD,SAAO,GAAG,QAAQ,MAAM,MAAM,MAAM,OAAO;AAC7C;AAEM,SAAU,gBAAgB,QAAiB;AAC/C,QAAM,SAAS;AACf,MAAI,YAAY;AAChB,aAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,eAAe,GAAG,GAAG;AAC9B,YAAM,WAAW,OAAO,GAAG,IAAI,MAAM,OAAO,OAAO,GAAG,CAAC;AACvD,kBAAY,YAAY,WAAW;IACpC;EACF;AAGD,cAAY,UAAU,MAAM,GAAG,EAAE;AACjC,SAAO;AACT;ACwBA,IAAY;CAAZ,SAAYC,YAAS;AACnB,EAAAA,WAAAA,WAAA,UAAA,IAAA,CAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,eAAA,IAAA,CAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,OAAA,IAAA,CAAA,IAAA;AACF,GAJY,cAAA,YAIX,CAAA,EAAA;ACpDe,SAAA,kBACd,QACA,sBAA8B;AAI9B,QAAM,oBAAoB,UAAU,OAAO,SAAS;AACpD,QAAM,kBAAkB;;IAEtB;;IAEA;;AAEF,QAAM,mBAAmB,gBAAgB,QAAQ,MAAM,MAAM;AAC7D,QAAM,wBAAwB,qBAAqB,QAAQ,MAAM,MAAM;AACvE,SAAO,qBAAqB,oBAAoB;AAClD;ACYA,IAAM,iBAAN,MAAoB;EAUlB,YACU,MACA,SACA,UACA,OACA,eACA,uBACA,WACA,gBACA,UACA,mBACA,oBACA,QAAQ,MAAI;AAXZ,SAAI,OAAJ;AACA,SAAO,UAAP;AACA,SAAQ,WAAR;AACA,SAAK,QAAL;AACA,SAAa,gBAAb;AACA,SAAqB,wBAArB;AACA,SAAS,YAAT;AACA,SAAc,iBAAd;AACA,SAAQ,WAAR;AACA,SAAiB,oBAAjB;AACA,SAAkB,qBAAlB;AACA,SAAK,QAAL;AArBF,SAAkB,qBAAyB;AAC3C,SAAU,aAAqB;AAI/B,SAAS,YAAY;AACrB,SAAU,aAAY;AAiB5B,SAAK,WAAW,IAAI,QAAQ,CAAC,SAAS,WAAU;AAC9C,WAAK,WAAW;AAChB,WAAK,UAAU;AACf,WAAK,OAAM;IACb,CAAC;;;;;EAMK,SAAM;AACZ,UAAM,eAGM,CAAC,iBAAiBF,cAAY;AACxC,UAAIA,WAAU;AACZ,wBAAgB,OAAO,IAAI,iBAAiB,OAAO,MAAM,IAAI,CAAC;AAC9D;MACD;AACD,YAAM,aAAa,KAAK,mBAAkB;AAC1C,WAAK,qBAAqB;AAE1B,YAAM,mBAEM,mBAAgB;AAC1B,cAAM,SAAS,cAAc;AAC7B,cAAM,QAAQ,cAAc,mBAAmB,cAAc,QAAQ;AACrE,YAAI,KAAK,sBAAsB,MAAM;AACnC,eAAK,kBAAkB,QAAQ,KAAK;QACrC;MACH;AACA,UAAI,KAAK,sBAAsB,MAAM;AACnC,mBAAW,0BAA0B,gBAAgB;MACtD;AAID,iBACG,KAAK,KAAK,MAAM,KAAK,SAAS,KAAK,OAAO,KAAK,QAAQ,EACvD,KAAK,MAAK;AACT,YAAI,KAAK,sBAAsB,MAAM;AACnC,qBAAW,6BAA6B,gBAAgB;QACzD;AACD,aAAK,qBAAqB;AAC1B,cAAM,YAAY,WAAW,aAAY,MAAO,UAAU;AAC1D,cAAM,SAAS,WAAW,UAAS;AACnC,YACE,CAAC,aACA,kBAAkB,QAAQ,KAAK,qBAAqB,KACnD,KAAK,OACP;AACA,gBAAM,cAAc,WAAW,aAAY,MAAO,UAAU;AAC5D,0BACE,OACA,IAAI,iBAAiB,OAAO,MAAM,WAAW,CAAC;AAEhD;QACD;AACD,cAAM,cAAc,KAAK,cAAc,QAAQ,MAAM,MAAM;AAC3D,wBAAgB,MAAM,IAAI,iBAAiB,aAAa,UAAU,CAAC;MACrE,CAAC;IACL;AAMA,UAAM,cAGM,CAAC,oBAAoB,WAAU;AACzC,YAAM,UAAU,KAAK;AACrB,YAAM,SAAS,KAAK;AACpB,YAAM,aAAa,OAAO;AAC1B,UAAI,OAAO,gBAAgB;AACzB,YAAI;AACF,gBAAM,SAAS,KAAK,UAAU,YAAY,WAAW,YAAW,CAAE;AAClE,cAAI,UAAU,MAAM,GAAG;AACrB,oBAAQ,MAAM;UACf,OAAM;AACL,oBAAO;UACR;QACF,SAAQ,GAAG;AACV,iBAAO,CAAC;QACT;MACF,OAAM;AACL,YAAI,eAAe,MAAM;AACvB,gBAAM,MAAM,QAAO;AACnB,cAAI,iBAAiB,WAAW,aAAY;AAC5C,cAAI,KAAK,gBAAgB;AACvB,mBAAO,KAAK,eAAe,YAAY,GAAG,CAAC;UAC5C,OAAM;AACL,mBAAO,GAAG;UACX;QACF,OAAM;AACL,cAAI,OAAO,UAAU;AACnB,kBAAM,MAAM,KAAK,aAAa,WAAU,IAAK,SAAQ;AACrD,mBAAO,GAAG;UACX,OAAM;AACL,kBAAM,MAAM,mBAAkB;AAC9B,mBAAO,GAAG;UACX;QACF;MACF;IACH;AACA,QAAI,KAAK,WAAW;AAClB,kBAAY,OAAO,IAAI,iBAAiB,OAAO,MAAM,IAAI,CAAC;IAC3D,OAAM;AACL,WAAK,aAAa,MAAM,cAAc,aAAa,KAAK,QAAQ;IACjE;;;EAIH,aAAU;AACR,WAAO,KAAK;;;EAId,OAAO,WAAmB;AACxB,SAAK,YAAY;AACjB,SAAK,aAAa,aAAa;AAC/B,QAAI,KAAK,eAAe,MAAM;AAC5B,WAAK,KAAK,UAAU;IACrB;AACD,QAAI,KAAK,uBAAuB,MAAM;AACpC,WAAK,mBAAmB,MAAK;IAC9B;;AAEJ;IAMY,yBAAgB;EAM3B,YACS,gBACA,YACPA,WAAkB;AAFX,SAAc,iBAAd;AACA,SAAU,aAAV;AAGP,SAAK,WAAW,CAAC,CAACA;;AAErB;AAEe,SAAA,eACd,SACA,WAAwB;AAExB,MAAI,cAAc,QAAQ,UAAU,SAAS,GAAG;AAC9C,YAAQ,eAAe,IAAI,cAAc;EAC1C;AACH;AAEgB,SAAA,kBACd,SACA,iBAAwB;AAExB,UAAQ,4BAA4B,IAClC,YAAY,oBAAA,QAAA,oBAAe,SAAf,kBAAmB;AACnC;AAEgB,SAAA,gBAAgB,SAAkB,OAAoB;AACpE,MAAI,OAAO;AACT,YAAQ,kBAAkB,IAAI;EAC/B;AACH;AAEgB,SAAA,mBACd,SACA,eAA4B;AAE5B,MAAI,kBAAkB,MAAM;AAC1B,YAAQ,qBAAqB,IAAI;EAClC;AACH;SAEgB,YACd,aACA,OACA,WACA,eACA,gBACA,iBACA,QAAQ,MAAI;AAEZ,QAAM,YAAY,gBAAgB,YAAY,SAAS;AACvD,QAAM,MAAM,YAAY,MAAM;AAC9B,QAAM,UAAU,OAAO,OAAO,CAAA,GAAI,YAAY,OAAO;AACrD,kBAAgB,SAAS,KAAK;AAC9B,iBAAe,SAAS,SAAS;AACjC,oBAAkB,SAAS,eAAe;AAC1C,qBAAmB,SAAS,aAAa;AACzC,SAAO,IAAI,eACT,KACA,YAAY,QACZ,SACA,YAAY,MACZ,YAAY,cACZ,YAAY,sBACZ,YAAY,SACZ,YAAY,cACZ,YAAY,SACZ,YAAY,kBACZ,gBACA,KAAK;AAET;ACtQA,SAAS,iBAAc;AACrB,MAAI,OAAO,gBAAgB,aAAa;AACtC,WAAO;EACR,WAAU,OAAO,sBAAsB,aAAa;AACnD,WAAO;EACR,OAAM;AACL,WAAO;EACR;AACH;AAQgB,SAAAG,aAAW,MAAwC;AACjE,QAAMC,eAAc,eAAc;AAClC,MAAIA,iBAAgB,QAAW;AAC7B,UAAM,KAAK,IAAIA,aAAW;AAC1B,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,SAAG,OAAO,KAAK,CAAC,CAAC;IAClB;AACD,WAAO,GAAG,QAAO;EAClB,OAAM;AACL,QAAI,oBAAmB,GAAI;AACzB,aAAO,IAAI,KAAK,IAAI;IACrB,OAAM;AACL,YAAM,IAAI,aACR,iBAAiB,yBACjB,qDAAqD;IAExD;EACF;AACH;SAWgB,UAAU,MAAYC,QAAe,KAAW;AAC9D,MAAI,KAAK,aAAa;AACpB,WAAO,KAAK,YAAYA,QAAO,GAAG;EACnC,WAAU,KAAK,UAAU;AACxB,WAAO,KAAK,SAASA,QAAO,GAAG;EAChC,WAAU,KAAK,OAAO;AACrB,WAAO,KAAK,MAAMA,QAAO,GAAG;EAC7B;AACD,SAAO;AACT;AC1DM,SAAU,aAAa,SAAe;AAC1C,MAAI,OAAO,SAAS,aAAa;AAC/B,UAAM,gBAAgB,SAAS;EAChC;AACD,SAAO,KAAK,OAAO;AACrB;ACIa,IAAA,eAAe;;;;;;;;EAQ1B,KAAK;;;;;;;EAOL,QAAQ;;;;;;;EAOR,WAAW;;;;;;;;;;EAUX,UAAU;;IAGC,mBAAU;EAGrB,YAAmB,MAAkB,aAA2B;AAA7C,SAAI,OAAJ;AACjB,SAAK,cAAc,eAAe;;AAErC;AAKe,SAAA,eACd,QACA,YAAkB;AAElB,UAAQ,QAAM;IACZ,KAAK,aAAa;AAChB,aAAO,IAAI,WAAW,WAAW,UAAU,CAAC;IAC9C,KAAK,aAAa;IAClB,KAAK,aAAa;AAChB,aAAO,IAAI,WAAW,aAAa,QAAQ,UAAU,CAAC;IACxD,KAAK,aAAa;AAChB,aAAO,IAAI,WACT,cAAc,UAAU,GACxB,oBAAoB,UAAU,CAAC;EAIpC;AAGD,QAAM,QAAO;AACf;AAEM,SAAU,WAAW,OAAa;AACtC,QAAM,IAAc,CAAA;AACpB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,IAAI,MAAM,WAAW,CAAC;AAC1B,QAAI,KAAK,KAAK;AACZ,QAAE,KAAK,CAAC;IACT,OAAM;AACL,UAAI,KAAK,MAAM;AACb,UAAE,KAAK,MAAO,KAAK,GAAI,MAAO,IAAI,EAAG;MACtC,OAAM;AACL,aAAK,IAAI,WAAW,OAAO;AAEzB,gBAAM,QACJ,IAAI,MAAM,SAAS,MAAM,MAAM,WAAW,IAAI,CAAC,IAAI,WAAW;AAChE,cAAI,CAAC,OAAO;AAEV,cAAE,KAAK,KAAK,KAAK,GAAG;UACrB,OAAM;AACL,kBAAM,KAAK;AACX,kBAAM,KAAK,MAAM,WAAW,EAAE,CAAC;AAC/B,gBAAI,SAAU,KAAK,SAAS,KAAO,KAAK;AACxC,cAAE,KACA,MAAO,KAAK,IACZ,MAAQ,KAAK,KAAM,IACnB,MAAQ,KAAK,IAAK,IAClB,MAAO,IAAI,EAAG;UAEjB;QACF,OAAM;AACL,eAAK,IAAI,WAAW,OAAO;AAEzB,cAAE,KAAK,KAAK,KAAK,GAAG;UACrB,OAAM;AACL,cAAE,KAAK,MAAO,KAAK,IAAK,MAAQ,KAAK,IAAK,IAAK,MAAO,IAAI,EAAG;UAC9D;QACF;MACF;IACF;EACF;AACD,SAAO,IAAI,WAAW,CAAC;AACzB;AAEM,SAAU,qBAAqB,OAAa;AAChD,MAAI;AACJ,MAAI;AACF,cAAU,mBAAmB,KAAK;EACnC,SAAQ,GAAG;AACV,UAAM,cAAc,aAAa,UAAU,qBAAqB;EACjE;AACD,SAAO,WAAW,OAAO;AAC3B;AAEgB,SAAA,aAAa,QAAsB,OAAa;AAC9D,UAAQ,QAAM;IACZ,KAAK,aAAa,QAAQ;AACxB,YAAM,WAAW,MAAM,QAAQ,GAAG,MAAM;AACxC,YAAM,WAAW,MAAM,QAAQ,GAAG,MAAM;AACxC,UAAI,YAAY,UAAU;AACxB,cAAM,cAAc,WAAW,MAAM;AACrC,cAAM,cACJ,QACA,wBACE,cACA,mCAAmC;MAExC;AACD;IACD;IACD,KAAK,aAAa,WAAW;AAC3B,YAAM,UAAU,MAAM,QAAQ,GAAG,MAAM;AACvC,YAAM,WAAW,MAAM,QAAQ,GAAG,MAAM;AACxC,UAAI,WAAW,UAAU;AACvB,cAAM,cAAc,UAAU,MAAM;AACpC,cAAM,cACJ,QACA,wBAAwB,cAAc,gCAAgC;MAEzE;AACD,cAAQ,MAAM,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AAClD;IACD;EAGF;AACD,MAAI;AACJ,MAAI;AACF,YAAQ,aAAa,KAAK;EAC3B,SAAQ,GAAG;AACV,QAAK,EAAY,QAAQ,SAAS,UAAU,GAAG;AAC7C,YAAM;IACP;AACD,UAAM,cAAc,QAAQ,yBAAyB;EACtD;AACD,QAAM,QAAQ,IAAI,WAAW,MAAM,MAAM;AACzC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,CAAC,IAAI,MAAM,WAAW,CAAC;EAC9B;AACD,SAAO;AACT;AAEA,IAAM,eAAN,MAAkB;EAKhB,YAAY,SAAe;AAJ3B,SAAM,SAAY;AAClB,SAAW,cAAkB;AAI3B,UAAM,UAAU,QAAQ,MAAM,iBAAiB;AAC/C,QAAI,YAAY,MAAM;AACpB,YAAM,cACJ,aAAa,UACb,uDAAuD;IAE1D;AACD,UAAM,SAAS,QAAQ,CAAC,KAAK;AAC7B,QAAI,UAAU,MAAM;AAClB,WAAK,SAAS,SAAS,QAAQ,SAAS;AACxC,WAAK,cAAc,KAAK,SACpB,OAAO,UAAU,GAAG,OAAO,SAAS,UAAU,MAAM,IACpD;IACL;AACD,SAAK,OAAO,QAAQ,UAAU,QAAQ,QAAQ,GAAG,IAAI,CAAC;;AAEzD;AAEK,SAAU,cAAc,SAAe;AAC3C,QAAM,QAAQ,IAAI,aAAa,OAAO;AACtC,MAAI,MAAM,QAAQ;AAChB,WAAO,aAAa,aAAa,QAAQ,MAAM,IAAI;EACpD,OAAM;AACL,WAAO,qBAAqB,MAAM,IAAI;EACvC;AACH;AAEM,SAAU,oBAAoB,SAAe;AACjD,QAAM,QAAQ,IAAI,aAAa,OAAO;AACtC,SAAO,MAAM;AACf;AAEA,SAAS,SAAS,GAAW,KAAW;AACtC,QAAM,aAAa,EAAE,UAAU,IAAI;AACnC,MAAI,CAAC,YAAY;AACf,WAAO;EACR;AAED,SAAO,EAAE,UAAU,EAAE,SAAS,IAAI,MAAM,MAAM;AAChD;IClNa,gBAAA,SAAO;EAKlB,YAAY,MAAuC,WAAmB;AACpE,QAAI,OAAe;AACnB,QAAI,WAAmB;AACvB,QAAI,aAAa,IAAI,GAAG;AACtB,WAAK,QAAQ;AACb,aAAQ,KAAc;AACtB,iBAAY,KAAc;IAC3B,WAAU,gBAAgB,aAAa;AACtC,UAAI,WAAW;AACb,aAAK,QAAQ,IAAI,WAAW,IAAI;MACjC,OAAM;AACL,aAAK,QAAQ,IAAI,WAAW,KAAK,UAAU;AAC3C,aAAK,MAAM,IAAI,IAAI,WAAW,IAAI,CAAC;MACpC;AACD,aAAO,KAAK,MAAM;IACnB,WAAU,gBAAgB,YAAY;AACrC,UAAI,WAAW;AACb,aAAK,QAAQ;MACd,OAAM;AACL,aAAK,QAAQ,IAAI,WAAW,KAAK,MAAM;AACvC,aAAK,MAAM,IAAI,IAAkB;MAClC;AACD,aAAO,KAAK;IACb;AACD,SAAK,QAAQ;AACb,SAAK,QAAQ;;EAGf,OAAI;AACF,WAAO,KAAK;;EAGd,OAAI;AACF,WAAO,KAAK;;EAGd,MAAM,WAAmB,SAAe;AACtC,QAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,YAAM,WAAW,KAAK;AACtB,YAAM,SAAS,UAAU,UAAU,WAAW,OAAO;AACrD,UAAI,WAAW,MAAM;AACnB,eAAO;MACR;AACD,aAAO,IAAI,SAAQ,MAAM;IAC1B,OAAM;AACL,YAAM,QAAQ,IAAI,WACf,KAAK,MAAqB,QAC3B,WACA,UAAU,SAAS;AAErB,aAAO,IAAI,SAAQ,OAAO,IAAI;IAC/B;;EAGH,OAAO,WAAW,MAA6B;AAC7C,QAAI,oBAAmB,GAAI;AACzB,YAAM,SAA4C,KAAK,IACrD,CAAC,QAAqD;AACpD,YAAI,eAAe,UAAS;AAC1B,iBAAO,IAAI;QACZ,OAAM;AACL,iBAAO;QACR;MACH,CAAC;AAEH,aAAO,IAAI,SAAQF,UAAQ,MAAM,MAAM,MAAM,CAAC;IAC/C,OAAM;AACL,YAAM,cAA4B,KAAK,IACrC,CAAC,QAAqC;AACpC,YAAI,SAAS,GAAG,GAAG;AACjB,iBAAO,eAAe,aAAa,KAAK,GAAa,EAAE;QACxD,OAAM;AAEL,iBAAQ,IAAgB;QACzB;MACH,CAAC;AAEH,UAAI,cAAc;AAClB,kBAAY,QAAQ,CAAC,UAA2B;AAC9C,uBAAe,MAAM;MACvB,CAAC;AACD,YAAM,SAAS,IAAI,WAAW,WAAW;AACzC,UAAI,QAAQ;AACZ,kBAAY,QAAQ,CAAC,UAAqB;AACxC,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,iBAAO,OAAO,IAAI,MAAM,CAAC;QAC1B;MACH,CAAC;AACD,aAAO,IAAI,SAAQ,QAAQ,IAAI;IAChC;;EAGH,aAAU;AACR,WAAO,KAAK;;AAEf;AC/GK,SAAU,iBACd,GAAS;AAET,MAAI;AACJ,MAAI;AACF,UAAM,KAAK,MAAM,CAAC;EACnB,SAAQ,GAAG;AACV,WAAO;EACR;AACD,MAAI,iBAAiB,GAAG,GAAG;AACzB,WAAO;EACR,OAAM;AACL,WAAO;EACR;AACH;ACZM,SAAU,OAAO,MAAY;AACjC,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;EACR;AACD,QAAM,QAAQ,KAAK,YAAY,GAAG;AAClC,MAAI,UAAU,IAAI;AAChB,WAAO;EACR;AACD,QAAM,UAAU,KAAK,MAAM,GAAG,KAAK;AACnC,SAAO;AACT;AAEgB,SAAA,MAAM,MAAc,WAAiB;AACnD,QAAM,qBAAqB,UACxB,MAAM,GAAG,EACT,OAAO,eAAa,UAAU,SAAS,CAAC,EACxC,KAAK,GAAG;AACX,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;EACR,OAAM;AACL,WAAO,OAAO,MAAM;EACrB;AACH;AAQM,SAAU,cAAc,MAAY;AACxC,QAAM,QAAQ,KAAK,YAAY,KAAK,KAAK,SAAS,CAAC;AACnD,MAAI,UAAU,IAAI;AAChB,WAAO;EACR,OAAM;AACL,WAAO,KAAK,MAAM,QAAQ,CAAC;EAC5B;AACH;AC/BgB,SAAA,SAAY,UAAoB,OAAQ;AACtD,SAAO;AACT;AAEA,IAAM,UAAN,MAAa;EAKX,YACS,QACP,OACA,UACA,OAAwD;AAHjD,SAAM,SAAN;AAKP,SAAK,QAAQ,SAAS;AACtB,SAAK,WAAW,CAAC,CAAC;AAClB,SAAK,QAAQ,SAAS;;AAEzB;AAKD,IAAI,YAA6B;AAE3B,SAAU,UAAU,UAA4B;AACpD,MAAI,CAAC,SAAS,QAAQ,KAAK,SAAS,SAAS,GAAG;AAC9C,WAAO;EACR,OAAM;AACL,WAAO,cAAc,QAAQ;EAC9B;AACH;SAEgB,cAAW;AACzB,MAAI,WAAW;AACb,WAAO;EACR;AACD,QAAM,WAAqB,CAAA;AAC3B,WAAS,KAAK,IAAI,QAAgB,QAAQ,CAAC;AAC3C,WAAS,KAAK,IAAI,QAAgB,YAAY,CAAC;AAC/C,WAAS,KAAK,IAAI,QAAgB,gBAAgB,CAAC;AACnD,WAAS,KAAK,IAAI,QAAgB,QAAQ,YAAY,IAAI,CAAC;AAE3D,WAAS,kBACP,WACA,UAA4B;AAE5B,WAAO,UAAU,QAAQ;;AAE3B,QAAM,cAAc,IAAI,QAAgB,MAAM;AAC9C,cAAY,QAAQ;AACpB,WAAS,KAAK,WAAW;AAKzB,WAAS,UACP,WACA,MAAsB;AAEtB,QAAI,SAAS,QAAW;AACtB,aAAO,OAAO,IAAI;IACnB,OAAM;AACL,aAAO;IACR;;AAEH,QAAM,cAAc,IAAI,QAAgB,MAAM;AAC9C,cAAY,QAAQ;AACpB,WAAS,KAAK,WAAW;AACzB,WAAS,KAAK,IAAI,QAAgB,aAAa,CAAC;AAChD,WAAS,KAAK,IAAI,QAAgB,SAAS,CAAC;AAC5C,WAAS,KAAK,IAAI,QAAgB,WAAW,MAAM,IAAI,CAAC;AACxD,WAAS,KAAK,IAAI,QAAgB,gBAAgB,MAAM,IAAI,CAAC;AAC7D,WAAS,KAAK,IAAI,QAAgB,sBAAsB,MAAM,IAAI,CAAC;AACnE,WAAS,KAAK,IAAI,QAAgB,mBAAmB,MAAM,IAAI,CAAC;AAChE,WAAS,KAAK,IAAI,QAAgB,mBAAmB,MAAM,IAAI,CAAC;AAChE,WAAS,KAAK,IAAI,QAAgB,eAAe,MAAM,IAAI,CAAC;AAC5D,WAAS,KAAK,IAAI,QAAgB,YAAY,kBAAkB,IAAI,CAAC;AACrE,cAAY;AACZ,SAAO;AACT;AAEgB,SAAA,OAAO,UAAoB,SAA4B;AACrE,WAAS,cAAW;AAClB,UAAM,SAAiB,SAAS,QAAQ;AACxC,UAAM,OAAe,SAAS,UAAU;AACxC,UAAM,MAAM,IAAI,SAAS,QAAQ,IAAI;AACrC,WAAO,QAAQ,sBAAsB,GAAG;;AAE1C,SAAO,eAAe,UAAU,OAAO,EAAE,KAAK,YAAW,CAAE;AAC7D;SAEgB,aACd,SACA,UACA,UAAkB;AAElB,QAAM,WAAqB,CAAA;AAC3B,WAAS,MAAM,IAAI;AACnB,QAAM,MAAM,SAAS;AACrB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,UAAU,SAAS,CAAC;AAC1B,aAAS,QAAQ,KAAK,IAAK,QAA6B,MACtD,UACA,SAAS,QAAQ,MAAM,CAAC;EAE3B;AACD,SAAO,UAAU,OAAO;AACxB,SAAO;AACT;SAEgB,mBACd,SACA,gBACA,UAAkB;AAElB,QAAM,MAAM,iBAAiB,cAAc;AAC3C,MAAI,QAAQ,MAAM;AAChB,WAAO;EACR;AACD,QAAM,WAAW;AACjB,SAAO,aAAa,SAAS,UAAU,QAAQ;AACjD;AAEM,SAAU,8BACd,UACA,gBACA,MACA,UAAgB;AAEhB,QAAM,MAAM,iBAAiB,cAAc;AAC3C,MAAI,QAAQ,MAAM;AAChB,WAAO;EACR;AACD,MAAI,CAAC,SAAS,IAAI,gBAAgB,CAAC,GAAG;AAGpC,WAAO;EACR;AACD,QAAM,SAAiB,IAAI,gBAAgB;AAC3C,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO;EACR;AACD,QAAM,SAAS;AACf,QAAM,aAAa,OAAO,MAAM,GAAG;AACnC,QAAM,OAAO,WAAW,IAAI,CAAC,UAAyB;AACpD,UAAM,SAAiB,SAAS,QAAQ;AACxC,UAAM,OAAe,SAAS,UAAU;AACxC,UAAM,UAAU,QAAQ,OAAO,MAAM,IAAI,QAAQ,OAAO,IAAI;AAC5D,UAAM,OAAO,QAAQ,SAAS,MAAM,QAAQ;AAC5C,UAAM,cAAc,gBAAgB;MAClC,KAAK;MACL;IACD,CAAA;AACD,WAAO,OAAO;EAChB,CAAC;AACD,SAAO,KAAK,CAAC;AACf;AAEgB,SAAA,iBACd,UACA,UAAkB;AAElB,QAAM,WAEF,CAAA;AACJ,QAAM,MAAM,SAAS;AACrB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,UAAU,SAAS,CAAC;AAC1B,QAAI,QAAQ,UAAU;AACpB,eAAS,QAAQ,MAAM,IAAI,SAAS,QAAQ,KAAK;IAClD;EACF;AACD,SAAO,KAAK,UAAU,QAAQ;AAChC;ACjKA,IAAM,eAAe;AACrB,IAAM,YAAY;AAElB,SAAS,oBACP,SACA,QACA,UAA4B;AAE5B,QAAM,aAAyB;IAC7B,UAAU,CAAA;IACV,OAAO,CAAA;IACP,eAAe,SAAS,eAAe;;AAEzC,MAAI,SAAS,YAAY,GAAG;AAC1B,eAAW,QAAQ,SAAS,YAAY,GAAG;AACzC,YAAM,2BAA2B,KAAK,QAAQ,OAAO,EAAE;AACvD,YAAM,YAAY,QAAQ,sBACxB,IAAI,SAAS,QAAQ,wBAAwB,CAAC;AAEhD,iBAAW,SAAS,KAAK,SAAS;IACnC;EACF;AAED,MAAI,SAAS,SAAS,GAAG;AACvB,eAAW,QAAQ,SAAS,SAAS,GAAG;AACtC,YAAM,YAAY,QAAQ,sBACxB,IAAI,SAAS,QAAQ,KAAK,MAAM,CAAC,CAAC;AAEpC,iBAAW,MAAM,KAAK,SAAS;IAChC;EACF;AACD,SAAO;AACT;SAEgB,mBACd,SACA,QACA,gBAAsB;AAEtB,QAAM,MAAM,iBAAiB,cAAc;AAC3C,MAAI,QAAQ,MAAM;AAChB,WAAO;EACR;AACD,QAAM,WAAW;AACjB,SAAO,oBAAoB,SAAS,QAAQ,QAAQ;AACtD;ICvCa,oBAAW;EActB,YACS,KACA,QAQA,SACA,SAAe;AAVf,SAAG,MAAH;AACA,SAAM,SAAN;AAQA,SAAO,UAAP;AACA,SAAO,UAAP;AAxBT,SAAS,YAAc,CAAA;AACvB,SAAO,UAAY,CAAA;AACnB,SAAI,OAAsC;AAC1C,SAAY,eAAwB;AAMpC,SAAgB,mBAA8C;AAC9D,SAAA,eAAyB,CAAC,GAAG;AAC7B,SAAoB,uBAAa,CAAA;;AAelC;ACzBK,SAAU,aAAa,MAAa;AACxC,MAAI,CAAC,MAAM;AACT,UAAM,QAAO;EACd;AACH;AAEgB,SAAA,gBACd,SACA,UAAkB;AAElB,WAAS,QAAQ,KAAyB,MAAY;AACpD,UAAM,WAAW,mBAAmB,SAAS,MAAM,QAAQ;AAC3D,iBAAa,aAAa,IAAI;AAC9B,WAAO;;AAET,SAAO;AACT;AAEgB,SAAA,YACd,SACA,QAAc;AAEd,WAAS,QAAQ,KAAyB,MAAY;AACpD,UAAM,aAAa,mBAAmB,SAAS,QAAQ,IAAI;AAC3D,iBAAa,eAAe,IAAI;AAChC,WAAO;;AAET,SAAO;AACT;AAEgB,SAAA,mBACd,SACA,UAAkB;AAElB,WAAS,QAAQ,KAAyB,MAAY;AACpD,UAAM,WAAW,mBAAmB,SAAS,MAAM,QAAQ;AAC3D,iBAAa,aAAa,IAAI;AAC9B,WAAO,8BACL,UACA,MACA,QAAQ,MACR,QAAQ,SAAS;;AAGrB,SAAO;AACT;AAEM,SAAU,mBACd,UAAkB;AAElB,WAAS,aACP,KACA,KAAiB;AAEjB,QAAI;AACJ,QAAI,IAAI,UAAS,MAAO,KAAK;AAC3B;;;QAGE,IAAI,aAAY,EAAG,SAAS,qCAAqC;QACjE;AACA,iBAAS,gBAAe;MACzB,OAAM;AACL,iBAAS,gBAAe;MACzB;IACF,OAAM;AACL,UAAI,IAAI,UAAS,MAAO,KAAK;AAC3B,iBAAS,cAAc,SAAS,MAAM;MACvC,OAAM;AACL,YAAI,IAAI,UAAS,MAAO,KAAK;AAC3B,mBAAS,aAAa,SAAS,IAAI;QACpC,OAAM;AACL,mBAAS;QACV;MACF;IACF;AACD,WAAO,SAAS,IAAI,UAAS;AAC7B,WAAO,iBAAiB,IAAI;AAC5B,WAAO;;AAET,SAAO;AACT;AAEM,SAAU,mBACd,UAAkB;AAElB,QAAM,SAAS,mBAAmB,QAAQ;AAE1C,WAAS,aACP,KACA,KAAiB;AAEjB,QAAI,SAAS,OAAO,KAAK,GAAG;AAC5B,QAAI,IAAI,UAAS,MAAO,KAAK;AAC3B,eAAS,eAAe,SAAS,IAAI;IACtC;AACD,WAAO,iBAAiB,IAAI;AAC5B,WAAO;;AAET,SAAO;AACT;SAEgBG,cACd,SACA,UACA,UAAkB;AAElB,QAAM,UAAU,SAAS,cAAa;AACtC,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YACtB,KACA,QACA,gBAAgB,SAAS,QAAQ,GACjC,OAAO;AAET,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AAEM,SAAUC,OACd,SACA,UACA,WACA,WACA,YAA0B;AAE1B,QAAM,YAAuB,CAAA;AAC7B,MAAI,SAAS,QAAQ;AACnB,cAAU,QAAQ,IAAI;EACvB,OAAM;AACL,cAAU,QAAQ,IAAI,SAAS,OAAO;EACvC;AACD,MAAI,aAAa,UAAU,SAAS,GAAG;AACrC,cAAU,WAAW,IAAI;EAC1B;AACD,MAAI,WAAW;AACb,cAAU,WAAW,IAAI;EAC1B;AACD,MAAI,YAAY;AACd,cAAU,YAAY,IAAI;EAC3B;AACD,QAAM,UAAU,SAAS,oBAAmB;AAC5C,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YACtB,KACA,QACA,YAAY,SAAS,SAAS,MAAM,GACpC,OAAO;AAET,cAAY,YAAY;AACxB,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;SAEgBC,WACd,SACA,UACA,sBAA6B;AAE7B,QAAM,UAAU,SAAS,cAAa;AACtC,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS,IAAI;AAChE,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YACtB,KACA,QACA,CAAC,GAAkB,SAAY,MAC/B,OAAO;AAET,cAAY,eAAe,mBAAmB,QAAQ;AACtD,MAAI,yBAAyB,QAAW;AACtC,gBAAY,QAAQ,OAAO,IAAI,WAAW,oBAAoB;AAC9D,gBAAY,eAAe;MAAC;MAAc;;IAAG;EAC9C;AACD,SAAO;AACT;SAEgB,eACd,SACA,UACA,UAAkB;AAElB,QAAM,UAAU,SAAS,cAAa;AACtC,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YACtB,KACA,QACA,mBAAmB,SAAS,QAAQ,GACpC,OAAO;AAET,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AAEM,SAAUC,iBACd,SACA,UACA,UACA,UAAkB;AAElB,QAAM,UAAU,SAAS,cAAa;AACtC,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,OAAO,iBAAiB,UAAU,QAAQ;AAChD,QAAM,UAAU,EAAE,gBAAgB,kCAAiC;AACnE,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YACtB,KACA,QACA,gBAAgB,SAAS,QAAQ,GACjC,OAAO;AAET,cAAY,UAAU;AACtB,cAAY,OAAO;AACnB,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AAEgB,SAAAC,eACd,SACA,UAAkB;AAElB,QAAM,UAAU,SAAS,cAAa;AACtC,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AAExB,WAAS,QAAQ,MAA0B,OAAa;EAAA;AACxD,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,SAAS,OAAO;AACjE,cAAY,eAAe,CAAC,KAAK,GAAG;AACpC,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AAEgB,SAAA,sBACd,UACA,MAAoB;AAEpB,SACG,YAAY,SAAS,aAAa,KAClC,QAAQ,KAAK,KAAI,KAClB;AAEJ;SAEgB,mBACd,UACA,MACA,UAA0B;AAE1B,QAAM,gBAAgB,OAAO,OAAO,CAAA,GAAI,QAAQ;AAChD,gBAAc,UAAU,IAAI,SAAS;AACrC,gBAAc,MAAM,IAAI,KAAK,KAAI;AACjC,MAAI,CAAC,cAAc,aAAa,GAAG;AACjC,kBAAc,aAAa,IAAI,sBAAsB,MAAM,IAAI;EAChE;AACD,SAAO;AACT;AAKM,SAAU,gBACd,SACA,UACA,UACA,MACA,UAA0B;AAE1B,QAAM,UAAU,SAAS,oBAAmB;AAC5C,QAAM,UAAsC;IAC1C,0BAA0B;;AAG5B,WAAS,cAAW;AAClB,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,MAAM,KAAK,OAAM,EAAG,SAAQ,EAAG,MAAM,CAAC;IAC7C;AACD,WAAO;;AAET,QAAM,WAAW,YAAW;AAC5B,UAAQ,cAAc,IAAI,iCAAiC;AAC3D,QAAM,YAAY,mBAAmB,UAAU,MAAM,QAAQ;AAC7D,QAAM,iBAAiB,iBAAiB,WAAW,QAAQ;AAC3D,QAAM,cACJ,OACA,WACA,8DAEA,iBACA,WACA,WACA,uBAEA,UAAU,aAAa,IACvB;AACF,QAAM,eAAe,WAAW,WAAW;AAC3C,QAAM,OAAO,QAAQ,QAAQ,aAAa,MAAM,YAAY;AAC5D,MAAI,SAAS,MAAM;AACjB,UAAM,gBAAe;EACtB;AACD,QAAM,YAAuB,EAAE,MAAM,UAAU,UAAU,EAAE;AAC3D,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YACtB,KACA,QACA,gBAAgB,SAAS,QAAQ,GACjC,OAAO;AAET,cAAY,YAAY;AACxB,cAAY,UAAU;AACtB,cAAY,OAAO,KAAK,WAAU;AAClC,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;IASa,8BAAqB;EAIhC,YACS,SACA,OACP,WACA,UAA0B;AAHnB,SAAO,UAAP;AACA,SAAK,QAAL;AAIP,SAAK,YAAY,CAAC,CAAC;AACnB,SAAK,WAAW,YAAY;;AAE/B;AAEe,SAAA,mBACd,KACA,SAAkB;AAElB,MAAI,SAAwB;AAC5B,MAAI;AACF,aAAS,IAAI,kBAAkB,sBAAsB;EACtD,SAAQ,GAAG;AACV,iBAAa,KAAK;EACnB;AACD,QAAM,gBAAgB,WAAW,CAAC,QAAQ;AAC1C,eAAa,CAAC,CAAC,UAAU,cAAc,QAAQ,MAAM,MAAM,EAAE;AAC7D,SAAO;AACT;AAEM,SAAU,sBACd,SACA,UACA,UACA,MACA,UAA0B;AAE1B,QAAM,UAAU,SAAS,oBAAmB;AAC5C,QAAM,oBAAoB,mBAAmB,UAAU,MAAM,QAAQ;AACrE,QAAM,YAAuB,EAAE,MAAM,kBAAkB,UAAU,EAAE;AACnE,QAAM,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS;AAC5D,QAAM,SAAS;AACf,QAAM,UAAU;IACd,0BAA0B;IAC1B,yBAAyB;IACzB,uCAAuC,GAAG,KAAK,KAAI,CAAE;IACrD,qCAAqC,kBAAkB,aAAa;IACpE,gBAAgB;;AAElB,QAAM,OAAO,iBAAiB,mBAAmB,QAAQ;AACzD,QAAM,UAAU,QAAQ;AAExB,WAAS,QAAQ,KAAuB;AACtC,uBAAmB,GAAG;AACtB,QAAIC;AACJ,QAAI;AACF,MAAAA,OAAM,IAAI,kBAAkB,mBAAmB;IAChD,SAAQ,GAAG;AACV,mBAAa,KAAK;IACnB;AACD,iBAAa,SAASA,IAAG,CAAC;AAC1B,WAAOA;;AAET,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,SAAS,OAAO;AACjE,cAAY,YAAY;AACxB,cAAY,UAAU;AACtB,cAAY,OAAO;AACnB,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AAKM,SAAU,yBACd,SACA,UACA,KACA,MAAa;AAEb,QAAM,UAAU,EAAE,yBAAyB,QAAO;AAElD,WAAS,QAAQ,KAAuB;AACtC,UAAM,SAAS,mBAAmB,KAAK,CAAC,UAAU,OAAO,CAAC;AAC1D,QAAI,aAA4B;AAChC,QAAI;AACF,mBAAa,IAAI,kBAAkB,6BAA6B;IACjE,SAAQ,GAAG;AACV,mBAAa,KAAK;IACnB;AAED,QAAI,CAAC,YAAY;AAEf,mBAAa,KAAK;IACnB;AAED,UAAM,OAAO,OAAO,UAAU;AAC9B,iBAAa,CAAC,MAAM,IAAI,CAAC;AACzB,WAAO,IAAI,sBAAsB,MAAM,KAAK,KAAI,GAAI,WAAW,OAAO;;AAExE,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,SAAS,OAAO;AACjE,cAAY,UAAU;AACtB,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AAMO,IAAM,8BAAsC,MAAM;SAWzC,wBACd,UACA,SACA,KACA,MACA,WACA,UACA,QACA,kBAA4D;AAI5D,QAAM,UAAU,IAAI,sBAAsB,GAAG,CAAC;AAC9C,MAAI,QAAQ;AACV,YAAQ,UAAU,OAAO;AACzB,YAAQ,QAAQ,OAAO;EACxB,OAAM;AACL,YAAQ,UAAU;AAClB,YAAQ,QAAQ,KAAK,KAAI;EAC1B;AACD,MAAI,KAAK,KAAI,MAAO,QAAQ,OAAO;AACjC,UAAM,oBAAmB;EAC1B;AACD,QAAM,YAAY,QAAQ,QAAQ,QAAQ;AAC1C,MAAI,gBAAgB;AACpB,MAAI,YAAY,GAAG;AACjB,oBAAgB,KAAK,IAAI,eAAe,SAAS;EAClD;AACD,QAAM,YAAY,QAAQ;AAC1B,QAAM,UAAU,YAAY;AAC5B,MAAI,gBAAgB;AACpB,MAAI,kBAAkB,GAAG;AACvB,oBAAgB;EACjB,WAAU,cAAc,eAAe;AACtC,oBAAgB;EACjB,OAAM;AACL,oBAAgB;EACjB;AACD,QAAM,UAAU;IACd,yBAAyB;IACzB,wBAAwB,GAAG,QAAQ,OAAO;;AAE5C,QAAM,OAAO,KAAK,MAAM,WAAW,OAAO;AAC1C,MAAI,SAAS,MAAM;AACjB,UAAM,gBAAe;EACtB;AAED,WAAS,QACP,KACA,MAAY;AAMZ,UAAM,eAAe,mBAAmB,KAAK,CAAC,UAAU,OAAO,CAAC;AAChE,UAAM,aAAa,QAAQ,UAAU;AACrC,UAAM,OAAO,KAAK,KAAI;AACtB,QAAI;AACJ,QAAI,iBAAiB,SAAS;AAC5B,iBAAW,gBAAgB,SAAS,QAAQ,EAAE,KAAK,IAAI;IACxD,OAAM;AACL,iBAAW;IACZ;AACD,WAAO,IAAI,sBACT,YACA,MACA,iBAAiB,SACjB,QAAQ;;AAGZ,QAAM,SAAS;AACf,QAAM,UAAU,QAAQ;AACxB,QAAM,cAAc,IAAI,YAAY,KAAK,QAAQ,SAAS,OAAO;AACjE,cAAY,UAAU;AACtB,cAAY,OAAO,KAAK,WAAU;AAClC,cAAY,mBAAmB,oBAAoB;AACnD,cAAY,eAAe,mBAAmB,QAAQ;AACtD,SAAO;AACT;AC3iBa,IAAA,YAAY;;;;;;;;;;;;;EAavB,eAAe;;AA2BJ,IAAA,YAAY;;EAEvB,SAAS;;EAGT,QAAQ;;EAGR,SAAS;;EAGT,UAAU;;EAGV,OAAO;;AAGH,SAAU,+BACd,OAAwB;AAExB,UAAQ,OAAK;IACX,KAA+B;IAC/B,KAA+B;IAC/B,KAAA;AACE,aAAO,UAAU;IACnB,KAAA;AACE,aAAO,UAAU;IACnB,KAAA;AACE,aAAO,UAAU;IACnB,KAAA;AACE,aAAO,UAAU;IACnB,KAAA;AACE,aAAO,UAAU;IACnB;AAEE,aAAO,UAAU;EACpB;AACH;ICvCa,iBAAQ;EAKnB,YACE,gBACA,OACA,UAAqB;AAErB,UAAM,cACJ,WAAW,cAAc,KAAK,SAAS,QAAQ,YAAY;AAC7D,QAAI,aAAa;AACf,WAAK,OAAO;AACZ,WAAK,QAAQ,UAAK,QAAL,UAAA,SAAA,QAAS;AACtB,WAAK,WAAW,aAAQ,QAAR,aAAA,SAAA,WAAY;IAC7B,OAAM;AACL,YAAM,WAAW;AAKjB,WAAK,OAAO,SAAS;AACrB,WAAK,QAAQ,SAAS;AACtB,WAAK,WAAW,SAAS;IAC1B;;AAEJ;ACzEK,SAAU,MAAM,GAAW;AAC/B,SAAO,IAAI,kBAA4B;AAErC,YAAQ,QAAO,EAAG,KAAK,MAAM,EAAE,GAAG,aAAa,CAAC;EAClD;AACF;ACFA,IAAI,sBAAyD;AAM7D,IAAe,gBAAf,MAA4B;EAQ1B,cAAA;AAFU,SAAK,QAAY;AAGzB,SAAK,OAAO,IAAI,eAAc;AAC9B,SAAK,QAAO;AACZ,SAAK,aAAa,UAAU;AAC5B,SAAK,eAAe,IAAI,QAAQ,aAAU;AACxC,WAAK,KAAK,iBAAiB,SAAS,MAAK;AACvC,aAAK,aAAa,UAAU;AAC5B,gBAAO;MACT,CAAC;AACD,WAAK,KAAK,iBAAiB,SAAS,MAAK;AACvC,aAAK,aAAa,UAAU;AAC5B,gBAAO;MACT,CAAC;AACD,WAAK,KAAK,iBAAiB,QAAQ,MAAK;AACtC,gBAAO;MACT,CAAC;IACH,CAAC;;EAKH,KACE,KACA,QACA,MACA,SAAiB;AAEjB,QAAI,KAAK,OAAO;AACd,YAAM,cAAc,+BAA+B;IACpD;AACD,SAAK,QAAQ;AACb,SAAK,KAAK,KAAK,QAAQ,KAAK,IAAI;AAChC,QAAI,YAAY,QAAW;AACzB,iBAAW,OAAO,SAAS;AACzB,YAAI,QAAQ,eAAe,GAAG,GAAG;AAC/B,eAAK,KAAK,iBAAiB,KAAK,QAAQ,GAAG,EAAE,SAAQ,CAAE;QACxD;MACF;IACF;AACD,QAAI,SAAS,QAAW;AACtB,WAAK,KAAK,KAAK,IAAI;IACpB,OAAM;AACL,WAAK,KAAK,KAAI;IACf;AACD,WAAO,KAAK;;EAGd,eAAY;AACV,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,cAAc,uCAAuC;IAC5D;AACD,WAAO,KAAK;;EAGd,YAAS;AACP,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,cAAc,oCAAoC;IACzD;AACD,QAAI;AACF,aAAO,KAAK,KAAK;IAClB,SAAQ,GAAG;AACV,aAAO;IACR;;EAGH,cAAW;AACT,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,cAAc,sCAAsC;IAC3D;AACD,WAAO,KAAK,KAAK;;EAGnB,eAAY;AACV,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,cAAc,uCAAuC;IAC5D;AACD,WAAO,KAAK,KAAK;;;EAInB,QAAK;AACH,SAAK,KAAK,MAAK;;EAGjB,kBAAkB,QAAc;AAC9B,WAAO,KAAK,KAAK,kBAAkB,MAAM;;EAG3C,0BAA0B,UAAqC;AAC7D,QAAI,KAAK,KAAK,UAAU,MAAM;AAC5B,WAAK,KAAK,OAAO,iBAAiB,YAAY,QAAQ;IACvD;;EAGH,6BAA6B,UAAqC;AAChE,QAAI,KAAK,KAAK,UAAU,MAAM;AAC5B,WAAK,KAAK,OAAO,oBAAoB,YAAY,QAAQ;IAC1D;;AAEJ;AAEK,IAAO,oBAAP,cAAiC,cAAqB;EAC1D,UAAO;AACL,SAAK,KAAK,eAAe;;AAE5B;SAEe,oBAAiB;AAC/B,SAAO,sBAAsB,oBAAmB,IAAK,IAAI,kBAAiB;AAC5E;AAEM,IAAO,qBAAP,cAAkC,cAA0B;EAGhE,UAAO;AACL,SAAK,KAAK,eAAe;;AAE5B;SAEe,qBAAkB;AAChC,SAAO,IAAI,mBAAkB;AAC/B;AAEM,IAAO,oBAAP,cAAiC,cAAmB;EACxD,UAAO;AACL,SAAK,KAAK,eAAe;;AAE5B;SAEe,oBAAiB;AAC/B,SAAO,IAAI,kBAAiB;AAC9B;IC1Ga,mBAAU;;;;;;EA+CrB,YAAYC,MAAgB,MAAe,WAA4B,MAAI;AAjC3E,SAAY,eAAW;AACf,SAAkB,qBAAY;AAC9B,SAAoB,uBAAY;AAChC,SAAU,aAAuD,CAAA;AAMjE,SAAM,SAAkB;AACxB,SAAU,aAAY;AACtB,SAAQ,WAAsB;AAC9B,SAAgB,mBAAW;AAG3B,SAAQ,WAAsC;AAC9C,SAAO,UAAgC;AAkB7C,SAAK,OAAOA;AACZ,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,YAAY,YAAW;AAC5B,SAAK,aAAa,KAAK,mBAAmB,KAAK,KAAK;AACpD,SAAK,SAAM;AACX,SAAK,gBAAgB,WAAQ;AAC3B,WAAK,WAAW;AAChB,WAAK,mBAAmB;AACxB,UAAI,MAAM,YAAY,iBAAiB,QAAQ,GAAG;AAChD,aAAK,qBAAqB;AAC1B,aAAK,qBAAoB;MAC1B,OAAM;AACL,cAAM,iBAAiB,KAAK,4BAA2B;AACvD,YAAI,kBAAkB,MAAM,QAAQ,CAAA,CAAE,GAAG;AACvC,cAAI,gBAAgB;AAClB,oBAAQ,mBAAkB;UAC3B,OAAM;AACL,iBAAK,YAAY,KAAK,IACpB,KAAK,YAAY,GACjB,6BAA6B;AAE/B,iBAAK,qBAAqB;AAC1B,iBAAK,qBAAoB;AACzB;UACD;QACF;AACD,aAAK,SAAS;AACd,aAAK;UAAW;;QAAA;MACjB;IACH;AACA,SAAK,wBAAwB,WAAQ;AACnC,WAAK,WAAW;AAChB,UAAI,MAAM,YAAY,iBAAiB,QAAQ,GAAG;AAChD,aAAK,qBAAoB;MAC1B,OAAM;AACL,aAAK,SAAS;AACd,aAAK;UAAW;;QAAA;MACjB;IACH;AACA,SAAK,YAAY;AACjB,SAAK,eAAe,KAAK,KAAK,QAAQ;AACtC,SAAK,WAAW,IAAI,QAAQ,CAAC,SAAS,WAAU;AAC9C,WAAK,WAAW;AAChB,WAAK,UAAU;AACf,WAAK,OAAM;IACb,CAAC;AAID,SAAK,SAAS,KAAK,MAAM,MAAK;IAAA,CAAG;;EA5DnC,8BAA2B;AACzB,WAAO,KAAK,YAAY,KAAK;;EA8DvB,wBAAqB;AAC3B,UAAM,aAAa,KAAK;AACxB,WAAO,YAAU,KAAK,gBAAgB,aAAa,MAAM;;EAGnD,mBAAmB,MAAa;AACtC,WAAO,KAAK,KAAI,IAAK,MAAM;;EAGrB,SAAM;AACZ,QAAI,KAAK,WAAM,WAAgC;AAE7C;IACD;AACD,QAAI,KAAK,aAAa,QAAW;AAC/B;IACD;AACD,QAAI,KAAK,YAAY;AACnB,UAAI,KAAK,eAAe,QAAW;AACjC,aAAK,iBAAgB;MACtB,OAAM;AACL,YAAI,KAAK,oBAAoB;AAC3B,eAAK,aAAY;QAClB,OAAM;AACL,cAAI,KAAK,sBAAsB;AAE7B,iBAAK,eAAc;UACpB,OAAM;AACL,iBAAK,iBAAiB,WAAW,MAAK;AACpC,mBAAK,iBAAiB;AACtB,mBAAK,gBAAe;YACtB,GAAG,KAAK,SAAS;UAClB;QACF;MACF;IACF,OAAM;AACL,WAAK,eAAc;IACpB;;EAGK,cACN,UAA0E;AAG1E,YAAQ,IAAI;MACV,KAAK,KAAK,QAAQ,cAAa;MAC/B,KAAK,KAAK,QAAQ,kBAAiB;KACpC,EAAE,KAAK,CAAC,CAAC,WAAW,aAAa,MAAK;AACrC,cAAQ,KAAK,QAAM;QACjB,KAAA;AACE,mBAAS,WAAW,aAAa;AACjC;QACF,KAAA;AACE,eAAK;YAAW;;UAAA;AAChB;QACF,KAAA;AACE,eAAK;YAAW;;UAAA;AAChB;MAEH;IACH,CAAC;;;EAKK,mBAAgB;AACtB,SAAK,cAAc,CAAC,WAAW,kBAAiB;AAC9C,YAAM,cAAc,sBAClB,KAAK,KAAK,SACV,KAAK,KAAK,WACV,KAAK,WACL,KAAK,OACL,KAAK,SAAS;AAEhB,YAAM,gBAAgB,KAAK,KAAK,QAAQ,aACtC,aACA,mBACA,WACA,aAAa;AAEf,WAAK,WAAW;AAChB,oBAAc,WAAU,EAAG,KAAK,CAAC,QAAe;AAC9C,aAAK,WAAW;AAChB,aAAK,aAAa;AAClB,aAAK,qBAAqB;AAC1B,aAAK,qBAAoB;MAC3B,GAAG,KAAK,aAAa;IACvB,CAAC;;EAGK,eAAY;AAElB,UAAM,MAAM,KAAK;AACjB,SAAK,cAAc,CAAC,WAAW,kBAAiB;AAC9C,YAAM,cAAc,yBAClB,KAAK,KAAK,SACV,KAAK,KAAK,WACV,KACA,KAAK,KAAK;AAEZ,YAAM,gBAAgB,KAAK,KAAK,QAAQ,aACtC,aACA,mBACA,WACA,aAAa;AAEf,WAAK,WAAW;AAChB,oBAAc,WAAU,EAAG,KAAK,YAAS;AACvC,iBAAS;AACT,aAAK,WAAW;AAChB,aAAK,gBAAgB,OAAO,OAAO;AACnC,aAAK,qBAAqB;AAC1B,YAAI,OAAO,WAAW;AACpB,eAAK,uBAAuB;QAC7B;AACD,aAAK,qBAAoB;MAC3B,GAAG,KAAK,aAAa;IACvB,CAAC;;EAGK,kBAAe;AACrB,UAAM,YAAY,8BAA8B,KAAK;AACrD,UAAM,SAAS,IAAI,sBACjB,KAAK,cACL,KAAK,MAAM,KAAI,CAAE;AAInB,UAAM,MAAM,KAAK;AACjB,SAAK,cAAc,CAAC,WAAW,kBAAiB;AAC9C,UAAI;AACJ,UAAI;AACF,sBAAc,wBACZ,KAAK,KAAK,WACV,KAAK,KAAK,SACV,KACA,KAAK,OACL,WACA,KAAK,WACL,QACA,KAAK,sBAAqB,CAAE;MAE/B,SAAQ,GAAG;AACV,aAAK,SAAS;AACd,aAAK;UAAW;;QAAA;AAChB;MACD;AACD,YAAM,gBAAgB,KAAK,KAAK,QAAQ;QACtC;QACA;QACA;QACA;;QACW;;;AAEb,WAAK,WAAW;AAChB,oBAAc,WAAU,EAAG,KAAK,CAAC,cAAoC;AACnE,aAAK,oBAAmB;AACxB,aAAK,WAAW;AAChB,aAAK,gBAAgB,UAAU,OAAO;AACtC,YAAI,UAAU,WAAW;AACvB,eAAK,YAAY,UAAU;AAC3B,eAAK;YAAW;;UAAA;QACjB,OAAM;AACL,eAAK,qBAAoB;QAC1B;MACH,GAAG,KAAK,aAAa;IACvB,CAAC;;EAGK,sBAAmB;AACzB,UAAM,cAAc,8BAA8B,KAAK;AAGvD,QAAI,cAAc,IAAI,KAAK,OAAO,MAAM;AACtC,WAAK,oBAAoB;IAC1B;;EAGK,iBAAc;AACpB,SAAK,cAAc,CAAC,WAAW,kBAAiB;AAC9C,YAAM,cAAcN,cAClB,KAAK,KAAK,SACV,KAAK,KAAK,WACV,KAAK,SAAS;AAEhB,YAAM,kBAAkB,KAAK,KAAK,QAAQ,aACxC,aACA,mBACA,WACA,aAAa;AAEf,WAAK,WAAW;AAChB,sBAAgB,WAAU,EAAG,KAAK,cAAW;AAC3C,aAAK,WAAW;AAChB,aAAK,YAAY;AACjB,aAAK;UAAW;;QAAA;MAClB,GAAG,KAAK,qBAAqB;IAC/B,CAAC;;EAGK,iBAAc;AACpB,SAAK,cAAc,CAAC,WAAW,kBAAiB;AAC9C,YAAM,cAAc,gBAClB,KAAK,KAAK,SACV,KAAK,KAAK,WACV,KAAK,WACL,KAAK,OACL,KAAK,SAAS;AAEhB,YAAM,mBAAmB,KAAK,KAAK,QAAQ,aACzC,aACA,mBACA,WACA,aAAa;AAEf,WAAK,WAAW;AAChB,uBAAiB,WAAU,EAAG,KAAK,cAAW;AAC5C,aAAK,WAAW;AAChB,aAAK,YAAY;AACjB,aAAK,gBAAgB,KAAK,MAAM,KAAI,CAAE;AACtC,aAAK;UAAW;;QAAA;MAClB,GAAG,KAAK,aAAa;IACvB,CAAC;;EAGK,gBAAgB,aAAmB;AACzC,UAAM,MAAM,KAAK;AACjB,SAAK,eAAe;AAKpB,QAAI,KAAK,iBAAiB,KAAK;AAC7B,WAAK,iBAAgB;IACtB;;EAGK,YAAY,OAAwB;AAC1C,QAAI,KAAK,WAAW,OAAO;AACzB;IACD;AACD,YAAQ,OAAK;MACX,KAAiC;MACjC,KAAA;AAIE,aAAK,SAAS;AACd,YAAI,KAAK,aAAa,QAAW;AAC/B,eAAK,SAAS,OAAM;QACrB,WAAU,KAAK,gBAAgB;AAC9B,uBAAa,KAAK,cAAc;AAChC,eAAK,iBAAiB;AACtB,eAAK,qBAAoB;QAC1B;AACD;MACF,KAAA;AAIE,cAAM,YAAY,KAAK,WAAM;AAC7B,aAAK,SAAS;AACd,YAAI,WAAW;AACb,eAAK,iBAAgB;AACrB,eAAK,OAAM;QACZ;AACD;MACF,KAAA;AAGE,aAAK,SAAS;AACd,aAAK,iBAAgB;AACrB;MACF,KAAA;AAIE,aAAK,SAAS,SAAQ;AACtB,aAAK,SAAS;AACd,aAAK,iBAAgB;AACrB;MACF,KAAA;AAKE,aAAK,SAAS;AACd,aAAK,iBAAgB;AACrB;MACF,KAAA;AAKE,aAAK,SAAS;AACd,aAAK,iBAAgB;AACrB;IAEH;;EAGK,uBAAoB;AAC1B,YAAQ,KAAK,QAAM;MACjB,KAAA;AACE,aAAK;UAAW;;QAAA;AAChB;MACF,KAAA;AACE,aAAK;UAAW;;QAAA;AAChB;MACF,KAAA;AACE,aAAK,OAAM;AACX;IAIH;;;;;EAMH,IAAI,WAAQ;AACV,UAAM,gBAAgB,+BAA+B,KAAK,MAAM;AAChE,WAAO;MACL,kBAAkB,KAAK;MACvB,YAAY,KAAK,MAAM,KAAI;MAC3B,OAAO;MACP,UAAU,KAAK;MACf,MAAM;MACN,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;EAqBd,GACE,MACA,gBAIA,OACA,WAA6B;AAG7B,UAAM,WAAW,IAAI,SAClB,kBAEkC,QACnC,SAAS,QACT,aAAa,MAAS;AAExB,SAAK,aAAa,QAAQ;AAC1B,WAAO,MAAK;AACV,WAAK,gBAAgB,QAAQ;IAC/B;;;;;;;;EASF,KACE,aACA,YAA6D;AAI7D,WAAO,KAAK,SAAS,KACnB,aACA,UAAyD;;;;;EAO7D,MAAS,YAAgD;AACvD,WAAO,KAAK,KAAK,MAAM,UAAU;;;;;EAM3B,aAAa,UAAsC;AACzD,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,gBAAgB,QAAQ;;;;;EAMvB,gBAAgB,UAAsC;AAC5D,UAAM,IAAI,KAAK,WAAW,QAAQ,QAAQ;AAC1C,QAAI,MAAM,IAAI;AACZ,WAAK,WAAW,OAAO,GAAG,CAAC;IAC5B;;EAGK,mBAAgB;AACtB,SAAK,eAAc;AACnB,UAAM,YAAY,KAAK,WAAW,MAAK;AACvC,cAAU,QAAQ,cAAW;AAC3B,WAAK,gBAAgB,QAAQ;IAC/B,CAAC;;EAGK,iBAAc;AACpB,QAAI,KAAK,aAAa,QAAW;AAC/B,UAAI,YAAY;AAChB,cAAQ,+BAA+B,KAAK,MAAM,GAAC;QACjD,KAAK,UAAU;AACbO,gBAAS,KAAK,SAAS,KAAK,MAAM,KAAK,QAAQ,GAAE;AACjD;QACF,KAAK,UAAU;QACf,KAAK,UAAU;AACb,gBAAM,SAAS,KAAK;AACpBA,gBAAS,OAAO,KAAK,MAAM,KAAK,MAAsB,GAAE;AACxD;QACF;AACE,sBAAY;AACZ;MACH;AACD,UAAI,WAAW;AACb,aAAK,WAAW;AAChB,aAAK,UAAU;MAChB;IACF;;EAGK,gBAAgB,UAAsC;AAC5D,UAAM,gBAAgB,+BAA+B,KAAK,MAAM;AAChE,YAAQ,eAAa;MACnB,KAAK,UAAU;MACf,KAAK,UAAU;AACb,YAAI,SAAS,MAAM;AACjBA,gBAAS,SAAS,KAAK,KAAK,UAAU,KAAK,QAAQ,GAAE;QACtD;AACD;MACF,KAAK,UAAU;AACb,YAAI,SAAS,UAAU;AACrBA,gBAAS,SAAS,SAAS,KAAK,QAAQ,GAAE;QAC3C;AACD;MACF,KAAK,UAAU;MACf,KAAK,UAAU;AACb,YAAI,SAAS,OAAO;AAClBA,gBACE,SAAS,MAAM,KAAK,UAAU,KAAK,MAAsB,GAC1D;QACF;AACD;MACF;AAEE,YAAI,SAAS,OAAO;AAClBA,gBACE,SAAS,MAAM,KAAK,UAAU,KAAK,MAAsB,GAC1D;QACF;IACJ;;;;;;EAOH,SAAM;AACJ,UAAM,QACJ,KAAK,WAAmC,YACxC,KAAK,WAAM;AACb,QAAI,OAAO;AACT,WAAK;QAAW;;MAAA;IACjB;AACD,WAAO;;;;;;EAOT,QAAK;AACH,UAAM,QAAQ,KAAK,WAAM;AACzB,QAAI,OAAO;AACT,WAAK;QAAW;;MAAA;IACjB;AACD,WAAO;;;;;;;EAQT,SAAM;AACJ,UAAM,QACJ,KAAK,WAAoC,aACzC,KAAK,WAAM;AACb,QAAI,OAAO;AACT,WAAK;QAAW;;MAAA;IACjB;AACD,WAAO;;AAEV;IC/mBY,kBAAA,WAAS;EAGpB,YACU,UACR,UAA2B;AADnB,SAAQ,WAAR;AAGR,QAAI,oBAAoB,UAAU;AAChC,WAAK,YAAY;IAClB,OAAM;AACL,WAAK,YAAY,SAAS,YAAY,UAAU,SAAS,IAAI;IAC9D;;;;;;;EAQH,WAAQ;AACN,WAAO,UAAU,KAAK,UAAU,SAAS,MAAM,KAAK,UAAU;;EAGtD,QACR,SACA,UAAkB;AAElB,WAAO,IAAI,WAAU,SAAS,QAAQ;;;;;EAMxC,IAAI,OAAI;AACN,UAAM,WAAW,IAAI,SAAS,KAAK,UAAU,QAAQ,EAAE;AACvD,WAAO,KAAK,QAAQ,KAAK,UAAU,QAAQ;;;;;EAM7C,IAAI,SAAM;AACR,WAAO,KAAK,UAAU;;;;;EAMxB,IAAI,WAAQ;AACV,WAAO,KAAK,UAAU;;;;;;EAOxB,IAAI,OAAI;AACN,WAAO,cAAc,KAAK,UAAU,IAAI;;;;;EAM1C,IAAI,UAAO;AACT,WAAO,KAAK;;;;;;EAOd,IAAI,SAAM;AACR,UAAM,UAAU,OAAO,KAAK,UAAU,IAAI;AAC1C,QAAI,YAAY,MAAM;AACpB,aAAO;IACR;AACD,UAAM,WAAW,IAAI,SAAS,KAAK,UAAU,QAAQ,OAAO;AAC5D,WAAO,IAAI,WAAU,KAAK,UAAU,QAAQ;;;;;EAM9C,aAAaf,OAAY;AACvB,QAAI,KAAK,UAAU,SAAS,IAAI;AAC9B,YAAM,qBAAqBA,KAAI;IAChC;;AAEJ;AAMe,SAAA,iBACdc,MACA,sBAA6B;AAE7B,EAAAA,KAAI,aAAa,UAAU;AAC3B,QAAM,cAAcJ,WAClBI,KAAI,SACJA,KAAI,WACJ,oBAAoB;AAEtB,SAAOA,KAAI,QACR,sBAAsB,aAAa,kBAAkB,EACrD,KAAK,WACJ,yBAAyB;;IAEpB,MAAsB,MAAM,GAAG,oBAAoB;MACnD,KAAqB;AAEhC;AAMgB,SAAA,gBACdA,MACA,sBAA6B;AAE7B,EAAAA,KAAI,aAAa,SAAS;AAC1B,QAAM,cAAcJ,WAClBI,KAAI,SACJA,KAAI,WACJ,oBAAoB;AAEtB,SAAOA,KAAI,QACR,sBAAsB,aAAa,iBAAiB,EACpD,KAAK,UACJ,yBAAyB;;IAEpB,KAAc,MAAM,GAAG,oBAAoB;MAC3C,IAAa;AAExB;SAqDgBE,cACdF,MACA,MACA,UAAmB;AAEnB,EAAAA,KAAI,aAAa,aAAa;AAC9B,QAAM,cAAc,gBAClBA,KAAI,SACJA,KAAI,WACJ,YAAW,GACX,IAAI,QAAQ,MAAM,IAAI,GACtB,QAAQ;AAEV,SAAOA,KAAI,QACR,sBAAsB,aAAa,iBAAiB,EACpD,KAAK,mBAAgB;AACpB,WAAO;MACL,UAAU;MACV,KAAAA;;EAEJ,CAAC;AACL;SAWgBG,uBACdH,MACA,MACA,UAAmB;AAEnB,EAAAA,KAAI,aAAa,sBAAsB;AACvC,SAAO,IAAI,WAAWA,MAAK,IAAI,QAAQ,IAAI,GAAG,QAAQ;AACxD;AAYgB,SAAAI,eACdJ,MACA,OACA,SAAuB,aAAa,KACpC,UAAmB;AAEnB,EAAAA,KAAI,aAAa,cAAc;AAC/B,QAAM,OAAO,eAAe,QAAQ,KAAK;AACzC,QAAM,gBAAgB,OAAK,OAAA,CAAA,GAAA,QAAQ;AACnC,MAAI,cAAc,aAAa,KAAK,QAAQ,KAAK,eAAe,MAAM;AACpE,kBAAc,aAAa,IAAI,KAAK;EACrC;AACD,SAAOE,cAAYF,MAAK,KAAK,MAAM,aAAa;AAClD;AAqBM,SAAUK,UAAQL,MAAc;AACpC,QAAM,cAA0B;IAC9B,UAAU,CAAA;IACV,OAAO,CAAA;;AAET,SAAO,cAAcA,MAAK,WAAW,EAAE,KAAK,MAAM,WAAW;AAC/D;AAQA,eAAe,cACbA,MACA,aACA,WAAkB;AAElB,QAAM,MAAmB;;IAEvB;;AAEF,QAAM,WAAW,MAAML,OAAKK,MAAK,GAAG;AACpC,cAAY,SAAS,KAAK,GAAG,SAAS,QAAQ;AAC9C,cAAY,MAAM,KAAK,GAAG,SAAS,KAAK;AACxC,MAAI,SAAS,iBAAiB,MAAM;AAClC,UAAM,cAAcA,MAAK,aAAa,SAAS,aAAa;EAC7D;AACH;AAwBgB,SAAAL,OACdK,MACA,SAA4B;AAE5B,MAAI,WAAW,MAAM;AACnB,QAAI,OAAO,QAAQ,eAAe,UAAU;AAC1C;QACE;;QACgB;;QACA;QAChB,QAAQ;MAAU;IAErB;EACF;AACD,QAAM,KAAK,WAAW,CAAA;AACtB,QAAM,cAAcM;IAClBN,KAAI;IACJA,KAAI;;IACY;IAChB,GAAG;IACH,GAAG;EAAU;AAEf,SAAOA,KAAI,QAAQ,sBAAsB,aAAa,iBAAiB;AACzE;AASM,SAAUN,cAAYM,MAAc;AACxC,EAAAA,KAAI,aAAa,aAAa;AAC9B,QAAM,cAAcO,cAClBP,KAAI,SACJA,KAAI,WACJ,YAAW,CAAE;AAEf,SAAOA,KAAI,QAAQ,sBAAsB,aAAa,iBAAiB;AACzE;AAagB,SAAAH,iBACdG,MACA,UAA2B;AAE3B,EAAAA,KAAI,aAAa,gBAAgB;AACjC,QAAM,cAAcQ,iBAClBR,KAAI,SACJA,KAAI,WACJ,UACA,YAAW,CAAE;AAEf,SAAOA,KAAI,QAAQ,sBAAsB,aAAa,iBAAiB;AACzE;AAQM,SAAUS,iBAAeT,MAAc;AAC3C,EAAAA,KAAI,aAAa,gBAAgB;AACjC,QAAM,cAAcU,eAClBV,KAAI,SACJA,KAAI,WACJ,YAAW,CAAE;AAEf,SAAOA,KAAI,QACR,sBAAsB,aAAa,iBAAiB,EACpD,KAAK,SAAM;AACV,QAAI,QAAQ,MAAM;AAChB,YAAM,cAAa;IACpB;AACD,WAAO;EACT,CAAC;AACL;AAQM,SAAUF,eAAaE,MAAc;AACzC,EAAAA,KAAI,aAAa,cAAc;AAC/B,QAAM,cAAcW,eAAqBX,KAAI,SAASA,KAAI,SAAS;AACnE,SAAOA,KAAI,QAAQ,sBAAsB,aAAa,iBAAiB;AACzE;AAYgB,SAAAY,YAAUZ,MAAgB,WAAiB;AACzD,QAAM,UAAU,MAAMA,KAAI,UAAU,MAAM,SAAS;AACnD,QAAM,WAAW,IAAI,SAASA,KAAI,UAAU,QAAQ,OAAO;AAC3D,SAAO,IAAI,UAAUA,KAAI,SAAS,QAAQ;AAC5C;AC9cM,SAAU,MAAM,MAAa;AACjC,SAAO,kBAAkB,KAAK,IAAc;AAC9C;AAKA,SAAS,WAAW,SAA8B,KAAW;AAC3D,SAAO,IAAI,UAAU,SAAS,GAAG;AACnC;AAMA,SAAS,YACPA,MACA,MAAa;AAEb,MAAIA,gBAAe,qBAAqB;AACtC,UAAM,UAAUA;AAChB,QAAI,QAAQ,WAAW,MAAM;AAC3B,YAAM,gBAAe;IACtB;AACD,UAAM,YAAY,IAAI,UAAU,SAAS,QAAQ,OAAQ;AACzD,QAAI,QAAQ,MAAM;AAChB,aAAO,YAAY,WAAW,IAAI;IACnC,OAAM;AACL,aAAO;IACR;EACF,OAAM;AAEL,QAAI,SAAS,QAAW;AACtB,aAAOY,YAAUZ,MAAK,IAAI;IAC3B,OAAM;AACL,aAAOA;IACR;EACF;AACH;AAqBgB,SAAAA,MACd,cACA,WAAkB;AAElB,MAAI,aAAa,MAAM,SAAS,GAAG;AACjC,QAAI,wBAAwB,qBAAqB;AAC/C,aAAO,WAAW,cAAc,SAAS;IAC1C,OAAM;AACL,YAAM,gBACJ,0EAA0E;IAE7E;EACF,OAAM;AACL,WAAO,YAAY,cAAc,SAAS;EAC3C;AACH;AAEA,SAAS,cACP,MACA,QAAwB;AAExB,QAAM,eAAe,WAAA,QAAA,WAAM,SAAA,SAAN,OAAS,yBAAyB;AACvD,MAAI,gBAAgB,MAAM;AACxB,WAAO;EACR;AACD,SAAO,SAAS,mBAAmB,cAAc,IAAI;AACvD;AAEM,SAAUa,yBACd,SACA,MACA,MACA,UAEI,CAAA,GAAE;AAEN,UAAQ,OAAO,GAAG,IAAI,IAAI,IAAI;AAC9B,UAAQ,YAAY;AACpB,QAAM,EAAE,cAAa,IAAK;AAC1B,MAAI,eAAe;AACjB,YAAQ,qBACN,OAAO,kBAAkB,WACrB,gBACA,oBAAoB,eAAe,QAAQ,IAAI,QAAQ,SAAS;EACvE;AACH;IAQa,4BAAmB;EAgB9B,YAIW,KACA,eAIA,mBAIA,MACA,kBAAyB;AAVzB,SAAG,MAAH;AACA,SAAa,gBAAb;AAIA,SAAiB,oBAAjB;AAIA,SAAI,OAAJ;AACA,SAAgB,mBAAhB;AA7BX,SAAO,UAAoB;AAMnB,SAAK,QAAW;AACxB,SAAS,YAAW;AACD,SAAM,SAAkB;AAEnC,SAAQ,WAAY;AAqB1B,SAAK,yBAAyB;AAC9B,SAAK,sBAAsB;AAC3B,SAAK,YAAY,oBAAI,IAAG;AACxB,QAAI,QAAQ,MAAM;AAChB,WAAK,UAAU,SAAS,mBAAmB,MAAM,KAAK,KAAK;IAC5D,OAAM;AACL,WAAK,UAAU,cAAc,KAAK,OAAO,KAAK,IAAI,OAAO;IAC1D;;;;;;EAOH,IAAI,OAAI;AACN,WAAO,KAAK;;EAGd,IAAI,KAAK,MAAY;AACnB,SAAK,QAAQ;AACb,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,UAAU,SAAS,mBAAmB,KAAK,MAAM,IAAI;IAC3D,OAAM;AACL,WAAK,UAAU,cAAc,MAAM,KAAK,IAAI,OAAO;IACpD;;;;;EAMH,IAAI,qBAAkB;AACpB,WAAO,KAAK;;EAGd,IAAI,mBAAmB,MAAY;AACjC;MACE;;MACe;;MACC,OAAO;MACvB;IAAI;AAEN,SAAK,sBAAsB;;;;;;EAO7B,IAAI,wBAAqB;AACvB,WAAO,KAAK;;EAGd,IAAI,sBAAsB,MAAY;AACpC;MACE;;MACe;;MACC,OAAO;MACvB;IAAI;AAEN,SAAK,yBAAyB;;EAGhC,MAAM,gBAAa;AACjB,QAAI,KAAK,oBAAoB;AAC3B,aAAO,KAAK;IACb;AACD,UAAM,OAAO,KAAK,cAAc,aAAa,EAAE,UAAU,KAAI,CAAE;AAC/D,QAAI,MAAM;AACR,YAAM,YAAY,MAAM,KAAK,SAAQ;AACrC,UAAI,cAAc,MAAM;AACtB,eAAO,UAAU;MAClB;IACF;AACD,WAAO;;EAGT,MAAM,oBAAiB;AACrB,UAAM,WAAW,KAAK,kBAAkB,aAAa,EAAE,UAAU,KAAI,CAAE;AACvE,QAAI,UAAU;AACZ,YAAM,SAAS,MAAM,SAAS,SAAQ;AAKtC,aAAO,OAAO;IACf;AACD,WAAO;;;;;EAMT,UAAO;AACL,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAChB,WAAK,UAAU,QAAQ,aAAW,QAAQ,OAAM,CAAE;AAClD,WAAK,UAAU,MAAK;IACrB;AACD,WAAO,QAAQ,QAAO;;;;;;EAOxB,sBAAsB,KAAa;AACjC,WAAO,IAAI,UAAU,MAAM,GAAG;;;;;;EAOhC,aACE,aACA,gBACA,WACA,eACA,QAAQ,MAAI;AAEZ,QAAI,CAAC,KAAK,UAAU;AAClB,YAAM,UAAU,YACd,aACA,KAAK,QACL,WACA,eACA,gBACA,KAAK,kBACL,KAAK;AAEP,WAAK,UAAU,IAAI,OAAO;AAE1B,cAAQ,WAAU,EAAG,KACnB,MAAM,KAAK,UAAU,OAAO,OAAO,GACnC,MAAM,KAAK,UAAU,OAAO,OAAO,CAAC;AAEtC,aAAO;IACR,OAAM;AACL,aAAO,IAAI,YAAY,WAAU,CAAE;IACpC;;EAGH,MAAM,sBACJ,aACA,gBAAmC;AAEnC,UAAM,CAAC,WAAW,aAAa,IAAI,MAAM,QAAQ,IAAI;MACnD,KAAK,cAAa;MAClB,KAAK,kBAAiB;IACvB,CAAA;AAED,WAAO,KAAK,aACV,aACA,gBACA,WACA,aAAa,EACb,WAAU;;AAEf;;;ACrUM,IAAM,eAAe;AC8EZ,SAAA,SACdb,MACA,sBAA6B;AAE7B,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,iBAAiBA,MAAkB,oBAAoB;AAChE;SAWgB,YACdA,MACA,MACA,UAAyB;AAEzB,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAOc,cACLd,MACA,MACA,QAA4B;AAEhC;AAYM,SAAU,aACdA,MACA,OACA,QACA,UAAyB;AAEzB,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAOe,eACLf,MACA,OACA,QACA,QAA4B;AAEhC;SAWgB,qBACdA,MACA,MACA,UAAyB;AAEzB,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAOgB,uBACLhB,MACA,MACA,QAA4B;AAEhC;AASM,SAAU,YAAYA,MAAqB;AAC/C,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAOiB,cAAoBjB,IAAgB;AAC7C;AAWgB,SAAA,eACdA,MACA,UAA0B;AAE1B,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAOkB,iBACLlB,MACA,QAAqC;AAEzC;AAwBgB,SAAA,KACdA,MACA,SAAqB;AAErB,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAOmB,OAAanB,MAAkB,OAAO;AAC/C;AAqBM,SAAU,QAAQA,MAAqB;AAC3C,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAOoB,UAAgBpB,IAAgB;AACzC;AASM,SAAU,eAAeA,MAAqB;AAClD,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAOqB,iBAAuBrB,IAAgB;AAChD;AAQM,SAAU,aAAaA,MAAqB;AAChD,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAOsB,eAAqBtB,IAAgB;AAC9C;AAqBgB,SAAA,IACd,cACA,WAAkB;AAElB,iBAAe,mBAAmB,YAAY;AAC9C,SAAOuB,MACL,cACA,SAAS;AAEb;AAKgB,SAAA,UAAUvB,MAAuB,WAAiB;AAChE,SAAOwB,YAAkBxB,MAAkB,SAAS;AACtD;SAUgB,WACd,MAAmB,OAAM,GACzB,WAAkB;AAElB,QAAM,mBAAmB,GAAG;AAC5B,QAAM,kBAAuC,aAAa,KAAK,YAAY;AAC3E,QAAM,kBAAkB,gBAAgB,aAAa;IACnD,YAAY;EACb,CAAA;AACD,QAAM,WAAW,kCAAkC,SAAS;AAC5D,MAAI,UAAU;AACZ,2BAAuB,iBAAiB,GAAG,QAAQ;EACpD;AACD,SAAO;AACT;AAYM,SAAU,uBACd,SACA,MACA,MACA,UAEI,CAAA,GAAE;AAENyB,2BAAwB,SAAgC,MAAM,MAAM,OAAO;AAC7E;ACvUgB,SAAA,QACdzB,MACA,sBAA6B;AAE7B,EAAAA,OAAM,mBAAmBA,IAAG;AAC5B,SAAO,gBAAgBA,MAAkB,oBAAoB;AAC/D;AAcgB,SAAA,UACdA,MACA,sBAA6B;AAE7B,QAAM,IAAI,MAAM,gDAAgD;AAClE;ACjBA,SAAS,QACP,WACA,EAAE,oBAAoB,IAAG,GAA0B;AAEnD,QAAM,MAAM,UAAU,YAAY,KAAK,EAAE,aAAY;AACrD,QAAM,eAAe,UAAU,YAAY,eAAe;AAC1D,QAAM,mBAAmB,UAAU,YAAY,oBAAoB;AAEnE,SAAO,IAAI,oBACT,KACA,cACA,kBACA,KACA,WAAW;AAEf;AAEA,SAAS,kBAAe;AACtB,qBACE,IAAI;IACF;IACA;IAED;;EAAA,EAAC,qBAAqB,IAAI,CAAC;AAG9B,kBAAgB,MAAM,SAAS,EAAiB;AAEhD,kBAAgB,MAAM,SAAS,SAAkB;AACnD;AAEA,gBAAe;", "names": ["StorageErrorCode", "name", "version", "canceled", "stop", "ErrorCode", "getBlob", "BlobBuilder", "start", "getMetadata", "list", "getBytes", "updateMetadata", "deleteObject", "url", "ref", "fbsAsync", "uploadBytes", "uploadBytesResumable", "uploadString", "listAll", "requestsList", "requestsGetMetadata", "requestsUpdateMetadata", "getDownloadURL", "requestsGetDownloadUrl", "requestsDeleteObject", "_get<PERSON><PERSON>d", "connectStorageEmulator", "uploadBytesInternal", "uploadStringInternal", "uploadBytesResumableInternal", "getMetadataInternal", "updateMetadataInternal", "listInternal", "listAllInternal", "getDownloadURLInternal", "deleteObjectInternal", "refInternal", "_getChildInternal", "connectEmulatorInternal"]}