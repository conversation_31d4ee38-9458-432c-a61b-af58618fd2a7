// Google Maps utility functions with AdvancedMarkerElement support
export const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
export const GOOGLE_MAPS_LIBRARIES = ['places', 'geometry', 'marker'];

// Default map center (you can change this to your preferred location)
export const DEFAULT_MAP_CENTER = {
  lat: 28.6139, // New Delhi
  lng: 77.2090
};

// Map styling options
export const createMapOptions = (options = {}) => {
  const {
    darkMode = false,
    useAdvancedMarkers = true,
    enableCustomStyling = true,
    mapId = 'DEMO_MAP_ID' // Default map ID for AdvancedMarkerElement
  } = options;

  const baseOptions = {
    zoomControl: true,
    mapTypeControl: false,
    scaleControl: true,
    streetViewControl: false,
    rotateControl: false,
    fullscreenControl: true,
    gestureHandling: 'cooperative',
    clickableIcons: false,
    // Always include mapId when using AdvancedMarkerElement
    ...(useAdvancedMarkers && { mapId })
  };

  // Note: When mapId is present, styles cannot be set here
  // Styles must be configured in Google Cloud Console for the specific map ID
  if (!mapId && enableCustomStyling) {
    baseOptions.styles = darkMode ? getDarkModeStyles() : getLightModeStyles();
  }

  return baseOptions;
};

// Light mode map styles
const getLightModeStyles = () => [
  {
    featureType: 'all',
    elementType: 'geometry.fill',
    stylers: [{ color: '#f5f5f5' }]
  },
  {
    featureType: 'road',
    elementType: 'geometry',
    stylers: [{ color: '#ffffff' }]
  },
  {
    featureType: 'water',
    elementType: 'geometry',
    stylers: [{ color: '#c9c9c9' }]
  }
];

// Dark mode map styles
const getDarkModeStyles = () => [
  {
    elementType: 'geometry',
    stylers: [{ color: '#242f3e' }]
  },
  {
    elementType: 'labels.text.stroke',
    stylers: [{ color: '#242f3e' }]
  },
  {
    elementType: 'labels.text.fill',
    stylers: [{ color: '#746855' }]
  },
  {
    featureType: 'administrative.locality',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#d59563' }]
  },
  {
    featureType: 'poi',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#d59563' }]
  },
  {
    featureType: 'poi.park',
    elementType: 'geometry',
    stylers: [{ color: '#263c3f' }]
  },
  {
    featureType: 'poi.park',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#6b9a76' }]
  },
  {
    featureType: 'road',
    elementType: 'geometry',
    stylers: [{ color: '#38414e' }]
  },
  {
    featureType: 'road',
    elementType: 'geometry.stroke',
    stylers: [{ color: '#212a37' }]
  },
  {
    featureType: 'road',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#9ca5b3' }]
  },
  {
    featureType: 'road.highway',
    elementType: 'geometry',
    stylers: [{ color: '#746855' }]
  },
  {
    featureType: 'road.highway',
    elementType: 'geometry.stroke',
    stylers: [{ color: '#1f2835' }]
  },
  {
    featureType: 'road.highway',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#f3d19c' }]
  },
  {
    featureType: 'transit',
    elementType: 'geometry',
    stylers: [{ color: '#2f3948' }]
  },
  {
    featureType: 'transit.station',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#d59563' }]
  },
  {
    featureType: 'water',
    elementType: 'geometry',
    stylers: [{ color: '#17263c' }]
  },
  {
    featureType: 'water',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#515c6d' }]
  },
  {
    featureType: 'water',
    elementType: 'labels.text.stroke',
    stylers: [{ color: '#17263c' }]
  }
];

// Create custom marker content for AdvancedMarkerElement
export const createMarkerContent = (options = {}) => {
  const {
    type = 'default',
    color = '#4285F4',
    text = '',
    size = 'medium',
    icon = null
  } = options;

  const markerElement = document.createElement('div');
  markerElement.style.position = 'relative';
  markerElement.style.cursor = 'pointer';

  let sizeClass = '';
  switch (size) {
    case 'small':
      sizeClass = 'w-6 h-6 text-xs';
      break;
    case 'large':
      sizeClass = 'w-12 h-12 text-lg';
      break;
    default:
      sizeClass = 'w-8 h-8 text-sm';
  }

  switch (type) {
    case 'pickup':
      markerElement.innerHTML = `
        <div class="${sizeClass} bg-green-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg border-2 border-white">
          P
        </div>
      `;
      break;
    case 'delivery':
      markerElement.innerHTML = `
        <div class="${sizeClass} bg-red-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg border-2 border-white">
          D
        </div>
      `;
      break;
    case 'courier':
      markerElement.innerHTML = `
        <div class="${sizeClass} bg-blue-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg border-2 border-white animate-pulse">
          🚚
        </div>
      `;
      break;
    case 'custom':
      markerElement.innerHTML = `
        <div class="${sizeClass} rounded-full flex items-center justify-center text-white font-bold shadow-lg border-2 border-white" style="background-color: ${color}">
          ${text || icon || '📍'}
        </div>
      `;
      break;
    default:
      markerElement.innerHTML = `
        <div class="${sizeClass} bg-blue-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg border-2 border-white">
          📍
        </div>
      `;
  }

  return markerElement;
};

// Create AdvancedMarkerElement (replaces deprecated Marker)
export const createAdvancedMarker = (map, position, options = {}) => {
  if (!window.google || !window.google.maps || !window.google.maps.marker) {
    throw new Error('Google Maps API with marker library not loaded');
  }

  const {
    title = '',
    content = null,
    type = 'default',
    color = '#4285F4',
    text = '',
    size = 'medium',
    icon = null,
    zIndex = 0
  } = options;

  const markerContent = content || createMarkerContent({ type, color, text, size, icon });

  const marker = new window.google.maps.marker.AdvancedMarkerElement({
    map,
    position,
    content: markerContent,
    title,
    zIndex
  });

  return marker;
};

// Fallback to legacy marker if AdvancedMarkerElement is not available
export const createMarker = (map, position, options = {}) => {
  // Try to use AdvancedMarkerElement first
  if (window.google?.maps?.marker?.AdvancedMarkerElement) {
    return createAdvancedMarker(map, position, options);
  }

  // Fallback to legacy Marker
  if (!window.google || !window.google.maps) {
    throw new Error('Google Maps API not loaded');
  }

  const {
    title = '',
    icon = null,
    draggable = false,
    animation = null
  } = options;

  return new window.google.maps.Marker({
    position,
    map,
    title,
    icon,
    draggable,
    animation
  });
};

// Geocoding function
export const geocodeAddress = async (address) => {
  if (!window.google || !window.google.maps) {
    throw new Error('Google Maps API not loaded');
  }

  const geocoder = new window.google.maps.Geocoder();

  return new Promise((resolve, reject) => {
    geocoder.geocode({ address }, (results, status) => {
      if (status === 'OK' && results[0]) {
        const location = results[0].geometry.location;
        resolve({
          lat: location.lat(),
          lng: location.lng(),
          formatted_address: results[0].formatted_address
        });
      } else {
        reject(new Error(`Geocoding failed: ${status}`));
      }
    });
  });
};

// Calculate distance between two points
export const calculateDistance = (point1, point2) => {
  if (!window.google || !window.google.maps) {
    throw new Error('Google Maps API not loaded');
  }

  const service = new window.google.maps.DistanceMatrixService();

  return new Promise((resolve, reject) => {
    service.getDistanceMatrix({
      origins: [point1],
      destinations: [point2],
      travelMode: window.google.maps.TravelMode.DRIVING,
      unitSystem: window.google.maps.UnitSystem.METRIC,
      avoidHighways: false,
      avoidTolls: false
    }, (response, status) => {
      if (status === 'OK') {
        const element = response.rows[0].elements[0];
        if (element.status === 'OK') {
          resolve({
            distance: element.distance.text,
            duration: element.duration.text,
            distanceValue: element.distance.value, // in meters
            durationValue: element.duration.value  // in seconds
          });
        } else {
          reject(new Error(`Distance calculation failed: ${element.status}`));
        }
      } else {
        reject(new Error(`Distance Matrix API failed: ${status}`));
      }
    });
  });
};

// Create directions service
export const createDirectionsService = () => {
  if (!window.google || !window.google.maps) {
    throw new Error('Google Maps API not loaded');
  }

  return new window.google.maps.DirectionsService();
};

// Create directions renderer
export const createDirectionsRenderer = (options = {}) => {
  if (!window.google || !window.google.maps) {
    throw new Error('Google Maps API not loaded');
  }

  const {
    suppressMarkers = false,
    polylineOptions = {}
  } = options;

  return new window.google.maps.DirectionsRenderer({
    suppressMarkers,
    polylineOptions: {
      strokeColor: '#4285F4',
      strokeWeight: 4,
      strokeOpacity: 0.8,
      ...polylineOptions
    }
  });
};

// Get route between two points
export const getRoute = async (origin, destination, waypoints = []) => {
  if (!window.google || !window.google.maps) {
    throw new Error('Google Maps API not loaded');
  }

  const directionsService = createDirectionsService();

  return new Promise((resolve, reject) => {
    directionsService.route({
      origin,
      destination,
      waypoints: waypoints.map(point => ({ location: point, stopover: true })),
      travelMode: window.google.maps.TravelMode.DRIVING,
      optimizeWaypoints: true
    }, (result, status) => {
      if (status === 'OK') {
        resolve(result);
      } else {
        reject(new Error(`Directions request failed: ${status}`));
      }
    });
  });
};

// Real-time location tracking utilities
export class LocationTracker {
  constructor(options = {}) {
    this.watchId = null;
    this.onLocationUpdate = options.onLocationUpdate || (() => {});
    this.onError = options.onError || console.error;
    this.options = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000,
      ...options.geolocationOptions
    };
  }

  startTracking() {
    if (!navigator.geolocation) {
      this.onError(new Error('Geolocation is not supported by this browser'));
      return;
    }

    this.watchId = navigator.geolocation.watchPosition(
      (position) => {
        const location = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp
        };
        this.onLocationUpdate(location);
      },
      this.onError,
      this.options
    );
  }

  stopTracking() {
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = null;
    }
  }

  getCurrentPosition() {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp
          });
        },
        reject,
        this.options
      );
    });
  }
}

// Update courier location in Firebase
export const updateCourierLocation = async (courierId, location) => {
  try {
    // This would typically use your Firebase service
    const response = await fetch('/api/couriers/update-location', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
      },
      body: JSON.stringify({
        courierId,
        location,
        timestamp: new Date().toISOString()
      })
    });

    if (!response.ok) {
      throw new Error('Failed to update location');
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating courier location:', error);
    throw error;
  }
};
