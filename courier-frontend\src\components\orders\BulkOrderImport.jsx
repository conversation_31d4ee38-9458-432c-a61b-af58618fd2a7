// AI-Powered Bulk Order Import Component
import React, { useState, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Alert,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import {
  CloudUpload,
  AutoFixHigh,
  Preview,
  CheckCircle,
  Error,
  Warning,
  Download,
  Visibility,
  Delete,
  Refresh
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';
import Papa from 'papaparse';

const BulkOrderImport = ({ onOrdersImported, onClose }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [rawData, setRawData] = useState([]);
  const [mappedData, setMappedData] = useState([]);
  const [fieldMapping, setFieldMapping] = useState({});
  const [validationResults, setValidationResults] = useState([]);
  const [processing, setProcessing] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [selectedRowData, setSelectedRowData] = useState(null);
  
  const fileInputRef = useRef(null);

  const steps = [
    'Upload File',
    'AI Field Mapping',
    'Data Validation',
    'Preview & Confirm',
    'Import Orders'
  ];

  // Required fields for order creation
  const requiredFields = {
    customerName: { label: 'Customer Name', required: true },
    customerPhone: { label: 'Customer Phone', required: true },
    customerEmail: { label: 'Customer Email', required: false },
    pickupAddress: { label: 'Pickup Address', required: true },
    deliveryAddress: { label: 'Delivery Address', required: true },
    productName: { label: 'Product Name', required: true },
    packageWeight: { label: 'Package Weight (kg)', required: true },
    packageType: { label: 'Package Type', required: false },
    priority: { label: 'Priority', required: false },
    deliveryInstructions: { label: 'Delivery Instructions', required: false }
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setUploadedFile(file);
    setProcessing(true);

    const fileExtension = file.name.split('.').pop().toLowerCase();

    if (fileExtension === 'csv') {
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          setRawData(results.data);
          setProcessing(false);
          setActiveStep(1);
          performAIMapping(results.data);
        },
        error: (error) => {
          console.error('CSV parsing error:', error);
          toast.error('Failed to parse CSV file');
          setProcessing(false);
        }
      });
    } else if (['xlsx', 'xls'].includes(fileExtension)) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const workbook = XLSX.read(e.target.result, { type: 'binary' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          
          setRawData(jsonData);
          setProcessing(false);
          setActiveStep(1);
          performAIMapping(jsonData);
        } catch (error) {
          console.error('Excel parsing error:', error);
          toast.error('Failed to parse Excel file');
          setProcessing(false);
        }
      };
      reader.readAsBinaryString(file);
    } else {
      toast.error('Please upload a CSV or Excel file');
      setProcessing(false);
    }
  };

  const performAIMapping = (data) => {
    if (!data || data.length === 0) return;

    setProcessing(true);
    
    // AI-powered field mapping logic
    const headers = Object.keys(data[0]);
    const mapping = {};

    // Smart field mapping based on common patterns
    const fieldPatterns = {
      customerName: ['name', 'customer', 'client', 'sender', 'from_name'],
      customerPhone: ['phone', 'mobile', 'contact', 'number', 'tel'],
      customerEmail: ['email', 'mail', 'e-mail'],
      pickupAddress: ['pickup', 'from', 'origin', 'sender_address', 'collection'],
      deliveryAddress: ['delivery', 'to', 'destination', 'receiver_address', 'drop'],
      productName: ['product', 'item', 'goods', 'package_name', 'description'],
      packageWeight: ['weight', 'kg', 'mass', 'wt'],
      packageType: ['type', 'category', 'package_type'],
      priority: ['priority', 'urgency', 'speed'],
      deliveryInstructions: ['instructions', 'notes', 'remarks', 'comments']
    };

    // Perform intelligent mapping
    Object.keys(fieldPatterns).forEach(field => {
      const patterns = fieldPatterns[field];
      const matchedHeader = headers.find(header => 
        patterns.some(pattern => 
          header.toLowerCase().includes(pattern.toLowerCase())
        )
      );
      if (matchedHeader) {
        mapping[field] = matchedHeader;
      }
    });

    setFieldMapping(mapping);
    
    // Apply mapping to data
    const mapped = data.map((row, index) => {
      const mappedRow = { originalIndex: index };
      Object.keys(mapping).forEach(field => {
        const sourceField = mapping[field];
        mappedRow[field] = row[sourceField] || '';
      });
      return mappedRow;
    });

    setMappedData(mapped);
    setProcessing(false);
    setActiveStep(2);
    performValidation(mapped);
  };

  const performValidation = (data) => {
    setProcessing(true);
    
    const results = data.map((row, index) => {
      const errors = [];
      const warnings = [];

      // Validate required fields
      Object.keys(requiredFields).forEach(field => {
        if (requiredFields[field].required && (!row[field] || row[field].toString().trim() === '')) {
          errors.push(`${requiredFields[field].label} is required`);
        }
      });

      // Validate phone number
      if (row.customerPhone) {
        const phoneRegex = /^\d{10}$/;
        if (!phoneRegex.test(row.customerPhone.toString().replace(/\D/g, ''))) {
          errors.push('Invalid phone number format');
        }
      }

      // Validate email
      if (row.customerEmail) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(row.customerEmail)) {
          errors.push('Invalid email format');
        }
      }

      // Validate weight
      if (row.packageWeight) {
        const weight = parseFloat(row.packageWeight);
        if (isNaN(weight) || weight <= 0) {
          errors.push('Invalid package weight');
        } else if (weight > 50) {
          warnings.push('Package weight exceeds 50kg');
        }
      }

      // Check for duplicate addresses
      if (row.pickupAddress && row.deliveryAddress) {
        if (row.pickupAddress.toLowerCase() === row.deliveryAddress.toLowerCase()) {
          errors.push('Pickup and delivery addresses cannot be the same');
        }
      }

      return {
        index,
        status: errors.length > 0 ? 'error' : warnings.length > 0 ? 'warning' : 'valid',
        errors,
        warnings,
        data: row
      };
    });

    setValidationResults(results);
    setProcessing(false);
    setActiveStep(3);
  };

  const getValidationSummary = () => {
    const valid = validationResults.filter(r => r.status === 'valid').length;
    const warnings = validationResults.filter(r => r.status === 'warning').length;
    const errors = validationResults.filter(r => r.status === 'error').length;
    
    return { valid, warnings, errors, total: validationResults.length };
  };

  const handleImport = async () => {
    const validOrders = validationResults.filter(r => r.status !== 'error');
    
    if (validOrders.length === 0) {
      toast.error('No valid orders to import');
      return;
    }

    setProcessing(true);
    setImportProgress(0);
    setActiveStep(4);

    try {
      const importedOrders = [];
      
      for (let i = 0; i < validOrders.length; i++) {
        const orderData = validOrders[i].data;
        
        // Simulate order creation with geocoding
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const order = {
          ...orderData,
          id: `bulk_${Date.now()}_${i}`,
          status: 'pending',
          createdAt: new Date().toISOString(),
          source: 'bulk_import'
        };
        
        importedOrders.push(order);
        setImportProgress(((i + 1) / validOrders.length) * 100);
      }

      if (onOrdersImported) {
        onOrdersImported(importedOrders);
      }

      toast.success(`Successfully imported ${importedOrders.length} orders`);
      setProcessing(false);
      
    } catch (error) {
      console.error('Import error:', error);
      toast.error('Failed to import orders');
      setProcessing(false);
    }
  };

  const downloadTemplate = () => {
    const template = [{
      'Customer Name': 'John Doe',
      'Customer Phone': '9876543210',
      'Customer Email': '<EMAIL>',
      'Pickup Address': '123 Main St, Bangalore, Karnataka',
      'Delivery Address': '456 Park Ave, Mumbai, Maharashtra',
      'Product Name': 'Electronics',
      'Package Weight': '2.5',
      'Package Type': 'standard',
      'Priority': 'standard',
      'Delivery Instructions': 'Handle with care'
    }];

    const ws = XLSX.utils.json_to_sheet(template);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Orders Template');
    XLSX.writeFile(wb, 'bulk_orders_template.xlsx');
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="body2" sx={{ mb: 2 }}>
              Upload a CSV or Excel file containing order data. The AI will automatically detect and map the fields.
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <Button
                variant="outlined"
                startIcon={<Download />}
                onClick={downloadTemplate}
              >
                Download Template
              </Button>
            </Box>

            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileUpload}
              accept=".csv,.xlsx,.xls"
              style={{ display: 'none' }}
            />
            
            <Button
              variant="contained"
              startIcon={<CloudUpload />}
              onClick={() => fileInputRef.current?.click()}
              disabled={processing}
              size="large"
            >
              {processing ? 'Processing...' : 'Upload File'}
            </Button>
            
            {uploadedFile && (
              <Alert severity="info" sx={{ mt: 2 }}>
                File uploaded: {uploadedFile.name} ({rawData.length} rows)
              </Alert>
            )}
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="body2" sx={{ mb: 2 }}>
              AI has automatically mapped the following fields:
            </Typography>
            
            <Grid container spacing={2}>
              {Object.keys(requiredFields).map(field => (
                <Grid item xs={12} sm={6} key={field}>
                  <Card variant="outlined">
                    <CardContent sx={{ py: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        {requiredFields[field].label}
                        {requiredFields[field].required && ' *'}
                      </Typography>
                      <Typography variant="body2">
                        {fieldMapping[field] ? (
                          <Chip 
                            label={fieldMapping[field]} 
                            size="small" 
                            color="primary" 
                          />
                        ) : (
                          <Chip 
                            label="Not mapped" 
                            size="small" 
                            color="default" 
                          />
                        )}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        );

      case 2:
        const summary = getValidationSummary();
        return (
          <Box>
            <Typography variant="body2" sx={{ mb: 2 }}>
              Data validation completed:
            </Typography>
            
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center', py: 1 }}>
                    <CheckCircle color="success" />
                    <Typography variant="h6">{summary.valid}</Typography>
                    <Typography variant="caption">Valid</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center', py: 1 }}>
                    <Warning color="warning" />
                    <Typography variant="h6">{summary.warnings}</Typography>
                    <Typography variant="caption">Warnings</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center', py: 1 }}>
                    <Error color="error" />
                    <Typography variant="h6">{summary.errors}</Typography>
                    <Typography variant="caption">Errors</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center', py: 1 }}>
                    <Typography variant="h6">{summary.total}</Typography>
                    <Typography variant="caption">Total</Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {summary.errors > 0 && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                {summary.errors} rows have errors and will be skipped during import.
              </Alert>
            )}
          </Box>
        );

      case 3:
        return (
          <Box>
            <Typography variant="body2" sx={{ mb: 2 }}>
              Review the orders before importing:
            </Typography>
            
            <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Status</TableCell>
                    <TableCell>Customer</TableCell>
                    <TableCell>Product</TableCell>
                    <TableCell>Pickup</TableCell>
                    <TableCell>Delivery</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {validationResults.slice(0, 10).map((result, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        {result.status === 'valid' && <CheckCircle color="success" fontSize="small" />}
                        {result.status === 'warning' && <Warning color="warning" fontSize="small" />}
                        {result.status === 'error' && <Error color="error" fontSize="small" />}
                      </TableCell>
                      <TableCell>{result.data.customerName}</TableCell>
                      <TableCell>{result.data.productName}</TableCell>
                      <TableCell>{result.data.pickupAddress?.substring(0, 30)}...</TableCell>
                      <TableCell>{result.data.deliveryAddress?.substring(0, 30)}...</TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => {
                            setSelectedRowData(result);
                            setPreviewDialogOpen(true);
                          }}
                        >
                          <Visibility fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            
            {validationResults.length > 10 && (
              <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
                Showing first 10 rows. Total: {validationResults.length} rows
              </Typography>
            )}
          </Box>
        );

      case 4:
        return (
          <Box>
            <Typography variant="body2" sx={{ mb: 2 }}>
              Importing orders...
            </Typography>
            
            <LinearProgress 
              variant="determinate" 
              value={importProgress} 
              sx={{ mb: 2 }}
            />
            
            <Typography variant="caption">
              Progress: {Math.round(importProgress)}%
            </Typography>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h5" gutterBottom>
          AI-Powered Bulk Order Import
        </Typography>
        
        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((label, index) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
              <StepContent>
                {renderStepContent(index)}
                
                <Box sx={{ mt: 2 }}>
                  {index === 3 && (
                    <Button
                      variant="contained"
                      onClick={handleImport}
                      disabled={processing || getValidationSummary().valid === 0}
                      startIcon={<CheckCircle />}
                    >
                      Import {getValidationSummary().valid} Orders
                    </Button>
                  )}
                  
                  {index === 4 && importProgress === 100 && (
                    <Button
                      variant="contained"
                      onClick={onClose}
                      color="success"
                    >
                      Complete
                    </Button>
                  )}
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </Paper>

      {/* Preview Dialog */}
      <Dialog
        open={previewDialogOpen}
        onClose={() => setPreviewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Order Preview</DialogTitle>
        <DialogContent>
          {selectedRowData && (
            <Box>
              <Grid container spacing={2}>
                {Object.keys(requiredFields).map(field => (
                  <Grid item xs={12} sm={6} key={field}>
                    <Typography variant="caption" color="text.secondary">
                      {requiredFields[field].label}
                    </Typography>
                    <Typography variant="body2">
                      {selectedRowData.data[field] || 'Not provided'}
                    </Typography>
                  </Grid>
                ))}
              </Grid>
              
              {selectedRowData.errors.length > 0 && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  <Typography variant="subtitle2">Errors:</Typography>
                  <ul>
                    {selectedRowData.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </Alert>
              )}
              
              {selectedRowData.warnings.length > 0 && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                  <Typography variant="subtitle2">Warnings:</Typography>
                  <ul>
                    {selectedRowData.warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </Alert>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BulkOrderImport;
