import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Chip,
  Tabs,
  Tab,
  Avatar
} from '@mui/material';
import {
  Search as SearchIcon,
  MyLocation as MyLocationIcon,
  DirectionsCar as CarIcon,
  Refresh as RefreshIcon,
  Person as PersonIcon,
  LocalShipping as ShippingIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  AccessTime as TimeIcon,
  Inventory as InventoryIcon,
  Paid as PaidIcon,
  History as HistoryIcon,
  Timeline as TimelineIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { collection, query, where, getDocs, onSnapshot, doc, getDoc, orderBy, limit } from 'firebase/firestore';
import { db } from '../../firebase';
import { useAuth } from '../../context/AuthContext';
import { useOffline } from '../../context/OfflineContext';
import { useMultiUserTracking, useLocationFeatures } from '../../hooks/useLocationTracking';

import AdminLayout from '../../components/layout/AdminLayout';
import UserStatus from '../../components/common/UserStatus';
import GoogleMapComponent from '../../components/maps/GoogleMapComponent';
import {
  DEFAULT_CENTER
} from '../../utils/googleMaps';

const UnifiedTracking = () => {
  const { user } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const mapRef = useRef(null);

  const { isOnline } = useOffline();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [mobileOpen, setMobileOpen] = useState(false);
  const [couriers, setCouriers] = useState([]);
  const [activeDeliveries, setActiveDeliveries] = useState([]);
  const [selectedCourier, setSelectedCourier] = useState(null);
  const [selectedDelivery, setSelectedDelivery] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [center, setCenter] = useState(DEFAULT_CENTER);
  const [locationHistory, setLocationHistory] = useState([]);
  const [showLocationHistory, setShowLocationHistory] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [mapError, setMapError] = useState(null);
  const [mapInstance, setMapInstance] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(new Date());

  // Get courier IDs for multi-user tracking
  const courierIds = couriers.map(courier => courier.id);

  // Use multi-user tracking hook
  const {
    userLocations,
    loading: trackingLoading,
    getUserLocation,
    getActiveUsers,
    activeUsers: activeUserCount
  } = useMultiUserTracking(courierIds);

  // Use location features
  const { calculateDistance, formatDistance, findNearestPoint } = useLocationFeatures();

  const handleDrawerToggle = () => setMobileOpen(!mobileOpen);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Get markers for the enhanced map
  const getUnifiedTrackingMarkers = () => {
    const markers = [];

    // Add courier markers
    Object.entries(userLocations).forEach(([userId, locationData]) => {
      const courier = couriers.find(c => c.id === userId);
      if (courier && locationData.location) {
        markers.push({
          position: {
            lat: locationData.location.lat || locationData.location.latitude,
            lng: locationData.location.lng || locationData.location.longitude
          },
          type: 'courier',
          title: `${courier.name || courier.fullName || 'Courier'} - ${locationData.isOnline ? 'Online' : 'Offline'}`,
          status: locationData.trackingActive ? 'active' : 'inactive',
          data: {
            type: 'courier',
            courier,
            locationData,
            lastUpdate: locationData.lastUpdate
          }
        });
      }
    });

    // Add delivery markers
    activeDeliveries.forEach(delivery => {
      if (delivery.deliveryCoordinates || delivery.destinationCoordinates) {
        const coords = delivery.deliveryCoordinates || delivery.destinationCoordinates;
        markers.push({
          position: {
            lat: coords.lat || coords.latitude,
            lng: coords.lng || coords.longitude
          },
          type: 'delivery',
          title: `Delivery: ${delivery.trackingId || delivery.id.substring(0, 8)}`,
          status: delivery.status === 'delivered' ? 'completed' : 'pending',
          data: {
            type: 'delivery',
            delivery,
            trackingId: delivery.trackingId,
            customerName: delivery.customerName
          }
        });
      }
    });

    return markers;
  };

  // Fetch couriers and their active deliveries
  useEffect(() => {
    if (!user?.uid) return;

    fetchCouriersAndDeliveries();

    // Set up real-time listeners for courier locations from both collections
    let unsubscribeUsers;
    let unsubscribeCouriers;

    try {
      // Listen to users collection - primary source of courier location data
      const usersRef = collection(db, 'users');
      const usersQuery = query(usersRef, where('role', '==', 'courier'));

      unsubscribeUsers = onSnapshot(usersQuery, (snapshot) => {
        try {
          const usersData = snapshot.docs.map(doc => {
            const data = doc.data();

            // Check if location data exists and is valid
            const hasValidLocation = data.currentLocation &&
              (data.currentLocation.latitude || data.currentLocation.lat) &&
              (data.currentLocation.longitude || data.currentLocation.lng);

            // Get coordinates, ensuring we have valid numbers
            const lat = hasValidLocation ?
              parseFloat(data.currentLocation.latitude || data.currentLocation.lat) : 0;
            const lng = hasValidLocation ?
              parseFloat(data.currentLocation.longitude || data.currentLocation.lng) : 0;

            // Check if the courier has valid location data
            const isValidLocation = hasValidLocation && lat !== 0 && lng !== 0;

            return {
              id: doc.id,
              fullName: data.fullName || data.name || 'Courier',
              email: data.email || '',
              phone: data.phone || '',
              ...data,
              lastUpdated: data.lastLocationUpdate?.toDate() || data.lastUpdated?.toDate() || new Date(),
              currentLocation: isValidLocation ? {
                latitude: lat,
                longitude: lng,
                lat: lat,
                lng: lng,
                accuracy: data.currentLocation.accuracy || 0,
                timestamp: data.currentLocation.clientTimestamp || new Date().toISOString()
              } : null,
              hasValidLocation: isValidLocation
            };
          });

          // Filter out couriers without valid location data for display purposes
          const couriersWithLocation = usersData.filter(c => c.hasValidLocation);

          // Always update couriers data
          setCouriers(usersData);

          // Update selected courier if needed
          if (couriersWithLocation.length > 0) {
            // If we already have a selected courier, try to find it in the updated list
            if (selectedCourier) {
              const updatedSelectedCourier = couriersWithLocation.find(c => c.id === selectedCourier.id);
              if (updatedSelectedCourier) {
                setSelectedCourier(updatedSelectedCourier);

                // Update map center if courier has moved
                if (updatedSelectedCourier.currentLocation) {
                  setCenter({
                    lat: updatedSelectedCourier.currentLocation.lat,
                    lng: updatedSelectedCourier.currentLocation.lng
                  });
                }
              } else {
                // If previously selected courier is no longer available, select the first one
                setSelectedCourier(couriersWithLocation[0]);
              }
            } else {
              // No courier selected yet, select the first one with location
              setSelectedCourier(couriersWithLocation[0]);

              // Center map on courier
              if (couriersWithLocation[0].currentLocation) {
                setCenter({
                  lat: couriersWithLocation[0].currentLocation.lat,
                  lng: couriersWithLocation[0].currentLocation.lng
                });
              }
            }
          }
        } catch (err) {
          console.error('Error processing user courier data:', err);
        }
      });
    } catch (err) {
      console.log('Could not set up users listener:', err);
    }

    // Return cleanup function to unsubscribe from both listeners
    return () => {
      if (unsubscribeUsers) unsubscribeUsers();
      if (unsubscribeCouriers) unsubscribeCouriers();
    };
  }, [user]); // Only depend on user to prevent excessive refreshing

  // Fetch active deliveries when selected courier changes
  useEffect(() => {
    if (selectedCourier) {
      fetchCourierDeliveries(selectedCourier.id);
      fetchLocationHistory(selectedCourier.id);
    }
  }, [selectedCourier]);

  // Update map center when data changes
  useEffect(() => {
    if (mapInstance && selectedCourier?.currentLocation) {
      const lat = selectedCourier.currentLocation.lat || selectedCourier.currentLocation.latitude;
      const lng = selectedCourier.currentLocation.lng || selectedCourier.currentLocation.longitude;
      if (lat && lng) {
        setCenter({ lat, lng });
      }
    }
  }, [selectedCourier, mapInstance]);

  // Fetch location history for a courier
  const fetchLocationHistory = async (courierId) => {
    try {
      const historyRef = collection(db, 'locationHistory');
      const historyQuery = query(
        historyRef,
        where('courierId', '==', courierId),
        orderBy('timestamp', 'desc'),
        limit(100)
      );

      const historySnapshot = await getDocs(historyQuery);

      const historyData = historySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate() || new Date(),
          position: {
            lat: data.latitude || 0,
            lng: data.longitude || 0
          }
        };
      });

      // Sort by timestamp ascending for path drawing
      historyData.sort((a, b) => a.timestamp - b.timestamp);

      setLocationHistory(historyData);
    } catch (err) {
      console.error('Error fetching location history:', err);
      // Non-critical, so don't show error to user
    }
  };

  const fetchCouriersAndDeliveries = async () => {
    setLoading(true);
    setError('');

    try {
      // Try to fetch couriers from both users and couriers collections
      let couriersData = [];

      try {
        // First try users collection
        const usersRef = collection(db, 'users');
        const usersQuery = query(usersRef, where('role', '==', 'courier'));
        const usersSnapshot = await getDocs(usersQuery);

        couriersData = usersSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            lastUpdated: data.lastLocationUpdate?.toDate() || new Date(),
            // Ensure currentLocation exists and has proper format with both lat/lng and latitude/longitude
            currentLocation: data.currentLocation ? {
              latitude: data.currentLocation.latitude || data.currentLocation.lat || 0,
              longitude: data.currentLocation.longitude || data.currentLocation.lng || 0,
              lat: data.currentLocation.latitude || data.currentLocation.lat || 0,
              lng: data.currentLocation.longitude || data.currentLocation.lng || 0
            } : {
              // Provide default location for couriers without location data
              latitude: 0,
              longitude: 0,
              lat: 0,
              lng: 0
            }
          };
        });
      } catch (err) {
        console.log('Error fetching from users collection:', err);
        // If error, try couriers collection
      }

      setCouriers(couriersData);

      if (couriersData.length > 0) {
        setSelectedCourier(couriersData[0]);

        // If courier has location, center map on it
        if (couriersData[0].currentLocation) {
          const lat = couriersData[0].currentLocation.latitude || couriersData[0].currentLocation.lat;
          const lng = couriersData[0].currentLocation.longitude || couriersData[0].currentLocation.lng;

          if (lat && lng) {
            setCenter({
              lat: lat,
              lng: lng
            });
          }
        }

        // Fetch deliveries for the first courier
        await fetchCourierDeliveries(couriersData[0].id);
      }
    } catch (err) {
      console.error('Error fetching couriers:', err);
      setError('Failed to load couriers. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchCourierDeliveries = async (courierId) => {
    try {
      // Try both possible field names for courier assignment
      let deliveriesData = [];

      // First try assignedCourierId field
      try {
        const deliveriesRef = collection(db, 'orders');
        const deliveriesQuery = query(
          deliveriesRef,
          where('assignedCourierId', '==', courierId),
          where('status', 'in', ['pending', 'processing', 'in_transit'])
        );

        const deliveriesSnapshot = await getDocs(deliveriesQuery);

        deliveriesData = deliveriesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date()
        }));
      } catch (err) {
        console.error('Error fetching deliveries with assignedCourierId:', err);
      }

      // If no deliveries found, try courierId field
      if (deliveriesData.length === 0) {
        try {
          const deliveriesRef = collection(db, 'orders');
          const deliveriesQuery = query(
            deliveriesRef,
            where('courierId', '==', courierId),
            where('status', 'in', ['pending', 'processing', 'in_transit'])
          );

          const deliveriesSnapshot = await getDocs(deliveriesQuery);

          deliveriesData = deliveriesSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            createdAt: doc.data().createdAt?.toDate() || new Date()
          }));
        } catch (err) {
          console.error('Error fetching deliveries with courierId:', err);
        }
      }

      // If still no deliveries found, try assignedCourier field
      if (deliveriesData.length === 0) {
        try {
          const deliveriesRef = collection(db, 'orders');
          const deliveriesQuery = query(
            deliveriesRef,
            where('assignedCourier', '==', courierId),
            where('status', 'in', ['pending', 'processing', 'in_transit'])
          );

          const deliveriesSnapshot = await getDocs(deliveriesQuery);

          deliveriesData = deliveriesSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            createdAt: doc.data().createdAt?.toDate() || new Date()
          }));
        } catch (err) {
          console.error('Error fetching deliveries with assignedCourier:', err);
        }
      }

      setActiveDeliveries(deliveriesData);

      if (deliveriesData.length > 0) {
        setSelectedDelivery(deliveriesData[0]);
      } else {
        setSelectedDelivery(null);
      }
    } catch (err) {
      console.error('Error fetching deliveries:', err);
      setError('Failed to load deliveries. Please try again.');
    }
  };

  const handleCourierSelect = (courier) => {
    setSelectedCourier(courier);

    // Center map on selected courier
    if (courier.currentLocation) {
      const lat = courier.currentLocation.latitude || courier.currentLocation.lat;
      const lng = courier.currentLocation.longitude || courier.currentLocation.lng;

      if (lat && lng) {
        setCenter({
          lat: lat,
          lng: lng
        });
      }
    }
  };

  const handleDeliverySelect = (delivery) => {
    setSelectedDelivery(delivery);

    // If delivery has destination coordinates, center map on it
    if (delivery.destinationCoordinates) {
      const lat = delivery.destinationCoordinates.latitude || delivery.destinationCoordinates.lat;
      const lng = delivery.destinationCoordinates.longitude || delivery.destinationCoordinates.lng;

      if (lat && lng) {
        setCenter({
          lat: lat,
          lng: lng
        });
      }
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchCouriersAndDeliveries();
      setLastRefresh(new Date());
      console.log('Tracking data refreshed successfully');
    } catch (error) {
      console.error('Error refreshing tracking data:', error);
      setError('Failed to refresh tracking data');
    } finally {
      setRefreshing(false);
    }
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  const filteredCouriers = couriers.filter(courier =>
    courier.fullName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    courier.email?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleMapLoad = (map) => {
    mapRef.current = map;
    setMapInstance(map);
    setMapError(null);
  };

  const handleMapError = (error) => {
    console.error('Map loading error:', error);
    setMapError('Failed to load map. Please check your internet connection.');
  };

  const handleToggleLocationHistory = () => {
    setShowLocationHistory(!showLocationHistory);
  };

  return (
    <AdminLayout>
      <Container maxWidth="xl" sx={{ mt: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h4" fontWeight="bold" color="primary">
              Live Tracking & Monitoring
            </Typography>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefresh}
              disabled={loading}
            >
              Refresh
            </Button>
          </Box>

          <Paper sx={{ mb: 3 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="fullWidth"
            >
              <Tab icon={<MyLocationIcon />} label="Live Courier Locations" />
              <Tab icon={<TimelineIcon />} label="Route History" />
              <Tab icon={<HistoryIcon />} label="Delivery Management" />
            </Tabs>
          </Paper>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Grid container spacing={3}>
              <Grid item xs={12} md={3}>
                <Paper sx={{ p: 2, borderRadius: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <TextField
                      fullWidth
                      size="small"
                      placeholder="Search couriers..."
                      value={searchQuery}
                      onChange={handleSearch}
                      InputProps={{
                        startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                      sx={{ mr: 1 }}
                    />
                  </Box>

                  <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold', mt: 2 }}>
                    Available Couriers ({filteredCouriers.length})
                  </Typography>

                  <List sx={{ maxHeight: '60vh', overflow: 'auto' }}>
                    {filteredCouriers.length === 0 ? (
                      <ListItem>
                        <ListItemText
                          primary="No couriers found"
                          secondary="Try adjusting your search"
                        />
                      </ListItem>
                    ) : (
                      filteredCouriers.map(courier => (
                        <ListItem
                          key={courier.id}
                          onClick={() => handleCourierSelect(courier)}
                          sx={{
                            borderRadius: 1,
                            mb: 0.5,
                            bgcolor: selectedCourier?.id === courier.id ? 'action.selected' : 'transparent',
                            cursor: 'pointer'
                          }}
                        >
                          <ListItemIcon>
                            <Avatar sx={{ bgcolor: courier.hasValidLocation ? 'success.main' : 'text.disabled' }}>
                              <PersonIcon />
                            </Avatar>
                          </ListItemIcon>
                          <ListItemText
                            primary={courier.fullName || courier.name || 'Courier'}
                            secondary={
                              <React.Fragment>
                                <Typography variant="body2" component="span" color="text.secondary">
                                  {courier.email || 'No email'}
                                </Typography>
                                <br />
                                <UserStatus userId={courier.id} inline={true} />
                              </React.Fragment>
                            }
                          />
                        </ListItem>
                      ))
                    )}
                  </List>
                </Paper>
              </Grid>

              <Grid item xs={12} md={9}>
                <Paper sx={{ p: 2, borderRadius: 2, mb: 3 }}>
                  {mapError && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                      {mapError}
                    </Alert>
                  )}
                  <GoogleMapComponent
                    center={center}
                    zoom={13}
                    markers={getUnifiedTrackingMarkers()}
                    showUserLocation={false}
                    enableFullscreen={true}
                    enableRefresh={true}
                    enableMyLocation={false}
                    height={600}
                    onMapLoad={handleMapLoad}
                    onMarkerClick={(markerData) => {
                      if (markerData.data.type === 'courier') {
                        setSelectedCourier(markerData.data.courier);
                      } else {
                        setSelectedDelivery(markerData.data.delivery);
                      }
                    }}
                    onRefresh={() => {
                      fetchCouriersAndDeliveries();
                    }}
                  />
                </Paper>

                {selectedCourier && (
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Card sx={{ height: '100%' }}>
                        <CardContent>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                            <Typography variant="h6" fontWeight="bold">
                              Courier Details
                            </Typography>
                            <UserStatus userId={selectedCourier.id} showIcon={true} />
                          </Box>
                          <Divider sx={{ mb: 2 }} />
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="body2" color="text.secondary">
                                <PersonIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 1 }} />
                                Name
                              </Typography>
                              <Typography variant="body1" gutterBottom fontWeight="bold">
                                {selectedCourier.fullName || selectedCourier.name || 'N/A'}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="body2" color="text.secondary">
                                <PhoneIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 1 }} />
                                Phone
                              </Typography>
                              <Typography variant="body1" gutterBottom>
                                {selectedCourier.phone || 'N/A'}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="body2" color="text.secondary">
                                <TimeIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 1 }} />
                                Last Updated
                              </Typography>
                              <Typography variant="body1" gutterBottom>
                                {selectedCourier.lastUpdated.toLocaleString()}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="body2" color="text.secondary">
                                <CarIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 1 }} />
                                Vehicle
                              </Typography>
                              <Typography variant="body1" gutterBottom>
                                {selectedCourier.vehicleType || selectedCourier.vehicle || 'N/A'}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="body2" color="text.secondary">
                                <MyLocationIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 1 }} />
                                Current Location
                              </Typography>
                              <Typography variant="body1" gutterBottom>
                                {selectedCourier.currentLocation ?
                                  `${selectedCourier.currentLocation.lat.toFixed(6)}, ${selectedCourier.currentLocation.lng.toFixed(6)}` :
                                  'Location not available'
                                }
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="body2" color="text.secondary">
                                📧 Email
                              </Typography>
                              <Typography variant="body1" gutterBottom>
                                {selectedCourier.email || 'N/A'}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="body2" color="text.secondary">
                                🎯 Accuracy
                              </Typography>
                              <Typography variant="body1" gutterBottom>
                                {selectedCourier.currentLocation?.accuracy ?
                                  `±${selectedCourier.currentLocation.accuracy}m` :
                                  'N/A'
                                }
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="body2" color="text.secondary">
                                📊 Active Deliveries
                              </Typography>
                              <Typography variant="body1" gutterBottom>
                                {activeDeliveries.length} orders
                              </Typography>
                            </Grid>
                          </Grid>
                          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
                            <Button
                              variant="outlined"
                              size="small"
                              startIcon={<TimelineIcon />}
                              onClick={handleToggleLocationHistory}
                              color={showLocationHistory ? 'primary' : 'inherit'}
                            >
                              {showLocationHistory ? 'Hide Path' : 'Show Path'}
                            </Button>
                            <Button
                              variant="outlined"
                              size="small"
                              component={Link}
                              to={`/couriers/edit/${selectedCourier.id}`}
                              startIcon={<EditIcon />}
                            >
                              Edit Courier
                            </Button>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Card sx={{ height: '100%' }}>
                        <CardContent>
                          <Typography variant="h6" fontWeight="bold" gutterBottom>
                            Active Deliveries
                          </Typography>
                          <Divider sx={{ mb: 2 }} />
                          {activeDeliveries.length > 0 ? (
                            <List dense>
                              {activeDeliveries.map(delivery => (
                                <ListItem
                                  key={delivery.id}
                                  onClick={() => handleDeliverySelect(delivery)}
                                  sx={{
                                    borderRadius: 1,
                                    mb: 0.5,
                                    bgcolor: selectedDelivery?.id === delivery.id ? 'action.selected' : 'transparent',
                                    cursor: 'pointer'
                                  }}
                                >
                                  <ListItemIcon>
                                    <ShippingIcon color="primary" />
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={
                                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                        <Typography variant="body1" fontWeight="bold">
                                          {delivery.trackingId || `TRK-${delivery.id.substring(0, 6)}`}
                                        </Typography>
                                        <Chip
                                          size="small"
                                          label={delivery.status || 'pending'}
                                          color={
                                            delivery.status === 'delivered' ? 'success' :
                                            delivery.status === 'in_transit' ? 'primary' :
                                            delivery.status === 'processing' ? 'warning' :
                                            'default'
                                          }
                                          sx={{ height: 20, fontSize: '0.7rem' }}
                                        />
                                      </Box>
                                    }
                                    secondary={
                                      <React.Fragment>
                                        <Typography variant="body2" component="div" color="text.secondary">
                                          👤 {delivery.customerName || 'Customer'}
                                        </Typography>
                                        <Typography variant="body2" component="div" color="text.secondary">
                                          📦 {delivery.productName || 'Product'}
                                        </Typography>
                                        <Typography variant="body2" component="div" color="text.secondary">
                                          💰 ₹{delivery.totalAmount || delivery.amount || '0.00'}
                                        </Typography>
                                        <Typography variant="body2" component="div" color="text.secondary">
                                          📍 {delivery.deliveryAddress ? delivery.deliveryAddress.substring(0, 30) + '...' : 'Address not available'}
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary">
                                          Created: {delivery.createdAt ? delivery.createdAt.toLocaleDateString() : 'N/A'}
                                        </Typography>
                                      </React.Fragment>
                                    }
                                  />
                                </ListItem>
                              ))}
                            </List>
                          ) : (
                            <Box sx={{ textAlign: 'center', py: 3 }}>
                              <Typography color="text.secondary" gutterBottom>
                                No active deliveries found for this courier
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Deliveries with status 'pending', 'processing', or 'in_transit' will appear here.
                              </Typography>
                            </Box>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                )}
              </Grid>
            </Grid>
          )}
        </Container>
    </AdminLayout>
  );
};

export default UnifiedTracking;