import {
  MuiPickersAdapterContext
} from "./chunk-BLIVCLMD.js";
import {
  Button_default,
  DialogActions_default,
  DialogContent_default,
  Dialog_default,
  Fade_default,
  FocusTrap_default,
  Grid_default,
  Grow_default,
  IconButton_default,
  InputAdornment_default,
  Paper_default,
  Popper_default,
  Typography_default,
  dialogClasses_default
} from "./chunk-MZGTZESS.js";
import {
  ButtonBase_default
} from "./chunk-5DL76I5G.js";
import {
  CSSTransition_default,
  TransitionGroup_default
} from "./chunk-53NJFVWS.js";
import {
  useTheme,
  useThemeProps
} from "./chunk-WPV7LSQT.js";
import {
  alpha,
  useTheme_default
} from "./chunk-BKDYJJLC.js";
import {
  init_utils,
  ownerDocument_default,
  useControlled_default,
  useEnhancedEffect_default as useEnhancedEffect_default2,
  useId_default
} from "./chunk-2ARNDKYY.js";
import {
  capitalize_default,
  createSvgIcon,
  useEventCallback_default,
  useForkRef_default
} from "./chunk-RZZYHUX5.js";
import {
  styled_default
} from "./chunk-FWU3CBNV.js";
import {
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses,
  init_esm,
  useControlled,
  useEnhancedEffect_default
} from "./chunk-HYFG2SCB.js";
import {
  _extends,
  _objectWithoutPropertiesLoose,
  init_extends,
  init_objectWithoutPropertiesLoose
} from "./chunk-R4ZBIWV7.js";
import {
  require_prop_types
} from "./chunk-PSO6HBME.js";
import {
  require_jsx_runtime
} from "./chunk-CRNJR6QK.js";
import {
  require_react
} from "./chunk-ZMLY2J2T.js";
import {
  __toESM
} from "./chunk-4B2QHNJT.js";

// node_modules/@mui/x-date-pickers/MonthPicker/pickersMonthClasses.js
function getPickersMonthUtilityClass(slot) {
  return generateUtilityClass("PrivatePickersMonth", slot);
}
var pickersMonthClasses = generateUtilityClasses(
  // TODO v6 Rename 'PrivatePickersMonth' to 'MuiPickersMonth' to follow convention
  "PrivatePickersMonth",
  ["root", "selected"]
);

// node_modules/@mui/x-date-pickers/MonthPicker/monthPickerClasses.js
function getMonthPickerUtilityClass(slot) {
  return generateUtilityClass("MuiMonthPicker", slot);
}
var monthPickerClasses = generateUtilityClasses("MuiMonthPicker", ["root"]);

// node_modules/@mui/x-date-pickers/MonthPicker/MonthPicker.js
init_objectWithoutPropertiesLoose();
init_extends();
var React3 = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());

// node_modules/@mui/x-date-pickers/node_modules/clsx/dist/clsx.m.js
function r(e) {
  var t, f, n = "";
  if ("string" == typeof e || "number" == typeof e) n += e;
  else if ("object" == typeof e) if (Array.isArray(e)) for (t = 0; t < e.length; t++) e[t] && (f = r(e[t])) && (n && (n += " "), n += f);
  else for (t in e) e[t] && (n && (n += " "), n += t);
  return n;
}
function clsx() {
  for (var e, t, f = 0, n = ""; f < arguments.length; ) (e = arguments[f++]) && (t = r(e)) && (n && (n += " "), n += t);
  return n;
}
var clsx_m_default = clsx;

// node_modules/@mui/x-date-pickers/MonthPicker/PickersMonth.js
init_objectWithoutPropertiesLoose();
init_extends();
var React = __toESM(require_react());
init_utils();

// node_modules/@mui/x-date-pickers/internals/utils/utils.js
function arrayIncludes(array, itemOrItems) {
  if (Array.isArray(itemOrItems)) {
    return itemOrItems.every((item) => array.indexOf(item) !== -1);
  }
  return array.indexOf(itemOrItems) !== -1;
}
var onSpaceOrEnter = (innerFn, onFocus) => (event) => {
  if (event.key === "Enter" || event.key === " ") {
    innerFn(event);
    event.preventDefault();
    event.stopPropagation();
  }
  if (onFocus) {
    onFocus(event);
  }
};
var getActiveElement = (root = document) => {
  const activeEl = root.activeElement;
  if (!activeEl) {
    return null;
  }
  if (activeEl.shadowRoot) {
    return getActiveElement(activeEl.shadowRoot);
  }
  return activeEl;
};

// node_modules/@mui/x-date-pickers/MonthPicker/PickersMonth.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var _excluded = ["disabled", "onSelect", "selected", "value", "tabIndex", "hasFocus", "onFocus", "onBlur"];
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    selected
  } = ownerState;
  const slots = {
    root: ["root", selected && "selected"]
  };
  return composeClasses(slots, getPickersMonthUtilityClass, classes);
};
var PickersMonthRoot = styled_default(Typography_default, {
  name: "PrivatePickersMonth",
  slot: "Root",
  overridesResolver: (_, styles) => [styles.root, {
    [`&.${pickersMonthClasses.selected}`]: styles.selected
  }]
})(({
  theme
}) => _extends({
  flex: "1 0 33.33%",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  color: "unset",
  backgroundColor: "transparent",
  border: 0,
  outline: 0
}, theme.typography.subtitle1, {
  margin: "8px 0",
  height: 36,
  borderRadius: 18,
  cursor: "pointer",
  "&:focus, &:hover": {
    backgroundColor: alpha(theme.palette.action.active, theme.palette.action.hoverOpacity)
  },
  "&:disabled": {
    pointerEvents: "none",
    color: theme.palette.text.secondary
  },
  [`&.${pickersMonthClasses.selected}`]: {
    color: theme.palette.primary.contrastText,
    backgroundColor: theme.palette.primary.main,
    "&:focus, &:hover": {
      backgroundColor: theme.palette.primary.dark
    }
  }
}));
var noop = () => {
};
var PickersMonth = (props) => {
  const {
    disabled,
    onSelect,
    selected,
    value,
    tabIndex,
    hasFocus,
    onFocus = noop,
    onBlur = noop
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const classes = useUtilityClasses(props);
  const handleSelection = () => {
    onSelect(value);
  };
  const ref = React.useRef(null);
  useEnhancedEffect_default2(() => {
    if (hasFocus) {
      var _ref$current;
      (_ref$current = ref.current) == null ? void 0 : _ref$current.focus();
    }
  }, [hasFocus]);
  return (0, import_jsx_runtime.jsx)(PickersMonthRoot, _extends({
    ref,
    component: "button",
    type: "button",
    className: classes.root,
    tabIndex,
    onClick: handleSelection,
    onKeyDown: onSpaceOrEnter(handleSelection),
    color: selected ? "primary" : void 0,
    variant: selected ? "h5" : "subtitle1",
    disabled,
    onFocus: (event) => onFocus(event, value),
    onBlur: (event) => onBlur(event, value)
  }, other));
};

// node_modules/@mui/x-date-pickers/internals/hooks/useUtils.js
var React2 = __toESM(require_react());
var useLocalizationContext = () => {
  const localization = React2.useContext(MuiPickersAdapterContext);
  if (localization === null) {
    throw new Error("MUI: Can not find utils in context. It looks like you forgot to wrap your component in LocalizationProvider, or pass dateAdapter prop directly.");
  }
  return localization;
};
var useUtils = () => useLocalizationContext().utils;
var useDefaultDates = () => useLocalizationContext().defaultDates;
var useLocaleText = () => useLocalizationContext().localeText;
var useNow = () => {
  const utils = useUtils();
  const now = React2.useRef(utils.date());
  return now.current;
};

// node_modules/@mui/x-date-pickers/internals/utils/date-utils.js
var findClosestEnabledDate = ({
  date,
  disableFuture,
  disablePast,
  maxDate,
  minDate,
  isDateDisabled,
  utils
}) => {
  const today = utils.startOfDay(utils.date());
  if (disablePast && utils.isBefore(minDate, today)) {
    minDate = today;
  }
  if (disableFuture && utils.isAfter(maxDate, today)) {
    maxDate = today;
  }
  let forward = date;
  let backward = date;
  if (utils.isBefore(date, minDate)) {
    forward = utils.date(minDate);
    backward = null;
  }
  if (utils.isAfter(date, maxDate)) {
    if (backward) {
      backward = utils.date(maxDate);
    }
    forward = null;
  }
  while (forward || backward) {
    if (forward && utils.isAfter(forward, maxDate)) {
      forward = null;
    }
    if (backward && utils.isBefore(backward, minDate)) {
      backward = null;
    }
    if (forward) {
      if (!isDateDisabled(forward)) {
        return forward;
      }
      forward = utils.addDays(forward, 1);
    }
    if (backward) {
      if (!isDateDisabled(backward)) {
        return backward;
      }
      backward = utils.addDays(backward, -1);
    }
  }
  return null;
};
var parsePickerInputValue = (utils, value) => {
  const parsedValue = utils.date(value);
  return utils.isValid(parsedValue) ? parsedValue : null;
};
var parseNonNullablePickerDate = (utils, value, defaultValue) => {
  if (value == null) {
    return defaultValue;
  }
  const parsedValue = utils.date(value);
  const isDateValid = utils.isValid(parsedValue);
  if (isDateValid) {
    return parsedValue;
  }
  return defaultValue;
};

// node_modules/@mui/x-date-pickers/MonthPicker/MonthPicker.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var _excluded2 = ["className", "date", "disabled", "disableFuture", "disablePast", "maxDate", "minDate", "onChange", "shouldDisableMonth", "readOnly", "disableHighlightToday", "autoFocus", "onMonthFocus", "hasFocus", "onFocusedViewChange"];
var useUtilityClasses2 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getMonthPickerUtilityClass, classes);
};
function useMonthPickerDefaultizedProps(props, name) {
  const utils = useUtils();
  const defaultDates = useDefaultDates();
  const themeProps = useThemeProps({
    props,
    name
  });
  return _extends({
    disableFuture: false,
    disablePast: false
  }, themeProps, {
    minDate: parseNonNullablePickerDate(utils, themeProps.minDate, defaultDates.minDate),
    maxDate: parseNonNullablePickerDate(utils, themeProps.maxDate, defaultDates.maxDate)
  });
}
var MonthPickerRoot = styled_default("div", {
  name: "MuiMonthPicker",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})({
  width: 310,
  display: "flex",
  flexWrap: "wrap",
  alignContent: "stretch",
  margin: "0 4px"
});
var MonthPicker = React3.forwardRef(function MonthPicker2(inProps, ref) {
  const utils = useUtils();
  const now = useNow();
  const props = useMonthPickerDefaultizedProps(inProps, "MuiMonthPicker");
  const {
    className,
    date,
    disabled,
    disableFuture,
    disablePast,
    maxDate,
    minDate,
    onChange,
    shouldDisableMonth,
    readOnly,
    disableHighlightToday,
    autoFocus = false,
    onMonthFocus,
    hasFocus,
    onFocusedViewChange
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded2);
  const ownerState = props;
  const classes = useUtilityClasses2(ownerState);
  const theme = useTheme_default();
  const selectedDateOrStartOfMonth = React3.useMemo(() => date != null ? date : utils.startOfMonth(now), [now, utils, date]);
  const selectedMonth = React3.useMemo(() => {
    if (date != null) {
      return utils.getMonth(date);
    }
    if (disableHighlightToday) {
      return null;
    }
    return utils.getMonth(now);
  }, [now, date, utils, disableHighlightToday]);
  const [focusedMonth, setFocusedMonth] = React3.useState(() => selectedMonth || utils.getMonth(now));
  const isMonthDisabled = React3.useCallback((month) => {
    const firstEnabledMonth = utils.startOfMonth(disablePast && utils.isAfter(now, minDate) ? now : minDate);
    const lastEnabledMonth = utils.startOfMonth(disableFuture && utils.isBefore(now, maxDate) ? now : maxDate);
    if (utils.isBefore(month, firstEnabledMonth)) {
      return true;
    }
    if (utils.isAfter(month, lastEnabledMonth)) {
      return true;
    }
    if (!shouldDisableMonth) {
      return false;
    }
    return shouldDisableMonth(month);
  }, [disableFuture, disablePast, maxDate, minDate, now, shouldDisableMonth, utils]);
  const onMonthSelect = (month) => {
    if (readOnly) {
      return;
    }
    const newDate = utils.setMonth(selectedDateOrStartOfMonth, month);
    onChange(newDate, "finish");
  };
  const [internalHasFocus, setInternalHasFocus] = useControlled_default({
    name: "MonthPicker",
    state: "hasFocus",
    controlled: hasFocus,
    default: autoFocus
  });
  const changeHasFocus = React3.useCallback((newHasFocus) => {
    setInternalHasFocus(newHasFocus);
    if (onFocusedViewChange) {
      onFocusedViewChange(newHasFocus);
    }
  }, [setInternalHasFocus, onFocusedViewChange]);
  const focusMonth = React3.useCallback((month) => {
    if (!isMonthDisabled(utils.setMonth(selectedDateOrStartOfMonth, month))) {
      setFocusedMonth(month);
      changeHasFocus(true);
      if (onMonthFocus) {
        onMonthFocus(month);
      }
    }
  }, [isMonthDisabled, utils, selectedDateOrStartOfMonth, changeHasFocus, onMonthFocus]);
  React3.useEffect(() => {
    setFocusedMonth((prevFocusedMonth) => selectedMonth !== null && prevFocusedMonth !== selectedMonth ? selectedMonth : prevFocusedMonth);
  }, [selectedMonth]);
  const handleKeyDown = useEventCallback_default((event) => {
    const monthsInYear = 12;
    const monthsInRow = 3;
    switch (event.key) {
      case "ArrowUp":
        focusMonth((monthsInYear + focusedMonth - monthsInRow) % monthsInYear);
        event.preventDefault();
        break;
      case "ArrowDown":
        focusMonth((monthsInYear + focusedMonth + monthsInRow) % monthsInYear);
        event.preventDefault();
        break;
      case "ArrowLeft":
        focusMonth((monthsInYear + focusedMonth + (theme.direction === "ltr" ? -1 : 1)) % monthsInYear);
        event.preventDefault();
        break;
      case "ArrowRight":
        focusMonth((monthsInYear + focusedMonth + (theme.direction === "ltr" ? 1 : -1)) % monthsInYear);
        event.preventDefault();
        break;
      default:
        break;
    }
  });
  const handleMonthFocus = React3.useCallback((event, month) => {
    focusMonth(month);
  }, [focusMonth]);
  const handleMonthBlur = React3.useCallback(() => {
    changeHasFocus(false);
  }, [changeHasFocus]);
  const currentMonthNumber = utils.getMonth(now);
  return (0, import_jsx_runtime2.jsx)(MonthPickerRoot, _extends({
    ref,
    className: clsx_m_default(classes.root, className),
    ownerState,
    onKeyDown: handleKeyDown
  }, other, {
    children: utils.getMonthArray(selectedDateOrStartOfMonth).map((month) => {
      const monthNumber = utils.getMonth(month);
      const monthText = utils.format(month, "monthShort");
      const isDisabled = disabled || isMonthDisabled(month);
      return (0, import_jsx_runtime2.jsx)(PickersMonth, {
        value: monthNumber,
        selected: monthNumber === selectedMonth,
        tabIndex: monthNumber === focusedMonth && !isDisabled ? 0 : -1,
        hasFocus: internalHasFocus && monthNumber === focusedMonth,
        onSelect: onMonthSelect,
        onFocus: handleMonthFocus,
        onBlur: handleMonthBlur,
        disabled: isDisabled,
        "aria-current": currentMonthNumber === monthNumber ? "date" : void 0,
        children: monthText
      }, monthText);
    })
  }));
});
true ? MonthPicker.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  autoFocus: import_prop_types.default.bool,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object,
  /**
   * className applied to the root element.
   */
  className: import_prop_types.default.string,
  /**
   * Date value for the MonthPicker
   */
  date: import_prop_types.default.any,
  /**
   * If `true` picker is disabled
   */
  disabled: import_prop_types.default.bool,
  /**
   * If `true` future days are disabled.
   * @default false
   */
  disableFuture: import_prop_types.default.bool,
  /**
   * If `true`, today's date is rendering without highlighting with circle.
   * @default false
   */
  disableHighlightToday: import_prop_types.default.bool,
  /**
   * If `true` past days are disabled.
   * @default false
   */
  disablePast: import_prop_types.default.bool,
  hasFocus: import_prop_types.default.bool,
  /**
   * Maximal selectable date. @DateIOType
   */
  maxDate: import_prop_types.default.any,
  /**
   * Minimal selectable date. @DateIOType
   */
  minDate: import_prop_types.default.any,
  /**
   * Callback fired on date change.
   */
  onChange: import_prop_types.default.func.isRequired,
  onFocusedViewChange: import_prop_types.default.func,
  onMonthFocus: import_prop_types.default.func,
  /**
   * If `true` picker is readonly
   */
  readOnly: import_prop_types.default.bool,
  /**
   * Disable specific months dynamically.
   * Works like `shouldDisableDate` but for month selection view @DateIOType.
   * @template TDate
   * @param {TDate} month The month to check.
   * @returns {boolean} If `true` the month will be disabled.
   */
  shouldDisableMonth: import_prop_types.default.func,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object, import_prop_types.default.bool])), import_prop_types.default.func, import_prop_types.default.object])
} : void 0;

// node_modules/@mui/x-date-pickers/CalendarPicker/pickersFadeTransitionGroupClasses.js
var getPickersFadeTransitionGroupUtilityClass = (slot) => generateUtilityClass("MuiPickersFadeTransitionGroup", slot);
var pickersFadeTransitionGroupClasses = generateUtilityClasses("MuiPickersFadeTransitionGroup", ["root"]);

// node_modules/@mui/x-date-pickers/PickersDay/pickersDayClasses.js
function getPickersDayUtilityClass(slot) {
  return generateUtilityClass("MuiPickersDay", slot);
}
var pickersDayClasses = generateUtilityClasses("MuiPickersDay", ["root", "dayWithMargin", "dayOutsideMonth", "hiddenDaySpacingFiller", "today", "selected", "disabled"]);

// node_modules/@mui/x-date-pickers/PickersDay/PickersDay.js
init_objectWithoutPropertiesLoose();
init_extends();
var React4 = __toESM(require_react());
var import_prop_types2 = __toESM(require_prop_types());
init_esm();
init_utils();

// node_modules/@mui/x-date-pickers/internals/constants/dimensions.js
var DAY_SIZE = 36;
var DAY_MARGIN = 2;
var DIALOG_WIDTH = 320;
var VIEW_HEIGHT = 358;

// node_modules/@mui/x-date-pickers/PickersDay/PickersDay.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var _excluded3 = ["autoFocus", "className", "day", "disabled", "disableHighlightToday", "disableMargin", "hidden", "isAnimating", "onClick", "onDaySelect", "onFocus", "onBlur", "onKeyDown", "onMouseDown", "outsideCurrentMonth", "selected", "showDaysOutsideCurrentMonth", "children", "today"];
var useUtilityClasses3 = (ownerState) => {
  const {
    selected,
    disableMargin,
    disableHighlightToday,
    today,
    disabled,
    outsideCurrentMonth,
    showDaysOutsideCurrentMonth,
    classes
  } = ownerState;
  const slots = {
    root: ["root", selected && "selected", disabled && "disabled", !disableMargin && "dayWithMargin", !disableHighlightToday && today && "today", outsideCurrentMonth && showDaysOutsideCurrentMonth && "dayOutsideMonth", outsideCurrentMonth && !showDaysOutsideCurrentMonth && "hiddenDaySpacingFiller"],
    hiddenDaySpacingFiller: ["hiddenDaySpacingFiller"]
  };
  return composeClasses(slots, getPickersDayUtilityClass, classes);
};
var styleArg = ({
  theme,
  ownerState
}) => _extends({}, theme.typography.caption, {
  width: DAY_SIZE,
  height: DAY_SIZE,
  borderRadius: "50%",
  padding: 0,
  // background required here to prevent collides with the other days when animating with transition group
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  "&:hover": {
    backgroundColor: alpha(theme.palette.action.active, theme.palette.action.hoverOpacity)
  },
  "&:focus": {
    backgroundColor: alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),
    [`&.${pickersDayClasses.selected}`]: {
      willChange: "background-color",
      backgroundColor: theme.palette.primary.dark
    }
  },
  [`&.${pickersDayClasses.selected}`]: {
    color: theme.palette.primary.contrastText,
    backgroundColor: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
    transition: theme.transitions.create("background-color", {
      duration: theme.transitions.duration.short
    }),
    "&:hover": {
      willChange: "background-color",
      backgroundColor: theme.palette.primary.dark
    }
  },
  [`&.${pickersDayClasses.disabled}`]: {
    color: theme.palette.text.disabled
  }
}, !ownerState.disableMargin && {
  margin: `0 ${DAY_MARGIN}px`
}, ownerState.outsideCurrentMonth && ownerState.showDaysOutsideCurrentMonth && {
  color: theme.palette.text.secondary
}, !ownerState.disableHighlightToday && ownerState.today && {
  [`&:not(.${pickersDayClasses.selected})`]: {
    border: `1px solid ${theme.palette.text.secondary}`
  }
});
var overridesResolver = (props, styles) => {
  const {
    ownerState
  } = props;
  return [styles.root, !ownerState.disableMargin && styles.dayWithMargin, !ownerState.disableHighlightToday && ownerState.today && styles.today, !ownerState.outsideCurrentMonth && ownerState.showDaysOutsideCurrentMonth && styles.dayOutsideMonth, ownerState.outsideCurrentMonth && !ownerState.showDaysOutsideCurrentMonth && styles.hiddenDaySpacingFiller];
};
var PickersDayRoot = styled_default(ButtonBase_default, {
  name: "MuiPickersDay",
  slot: "Root",
  overridesResolver
})(styleArg);
var PickersDayFiller = styled_default("div", {
  name: "MuiPickersDay",
  slot: "Root",
  overridesResolver
})(({
  theme,
  ownerState
}) => _extends({}, styleArg({
  theme,
  ownerState
}), {
  // visibility: 'hidden' does not work here as it hides the element from screen readers as well
  opacity: 0,
  pointerEvents: "none"
}));
var noop2 = () => {
};
var PickersDayRaw = React4.forwardRef(function PickersDay(inProps, forwardedRef) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiPickersDay"
  });
  const {
    autoFocus = false,
    className,
    day,
    disabled = false,
    disableHighlightToday = false,
    disableMargin = false,
    isAnimating,
    onClick,
    onDaySelect,
    onFocus = noop2,
    onBlur = noop2,
    onKeyDown = noop2,
    onMouseDown,
    outsideCurrentMonth,
    selected = false,
    showDaysOutsideCurrentMonth = false,
    children,
    today: isToday = false
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded3);
  const ownerState = _extends({}, props, {
    autoFocus,
    disabled,
    disableHighlightToday,
    disableMargin,
    selected,
    showDaysOutsideCurrentMonth,
    today: isToday
  });
  const classes = useUtilityClasses3(ownerState);
  const utils = useUtils();
  const ref = React4.useRef(null);
  const handleRef = useForkRef_default(ref, forwardedRef);
  useEnhancedEffect_default(() => {
    if (autoFocus && !disabled && !isAnimating && !outsideCurrentMonth) {
      ref.current.focus();
    }
  }, [autoFocus, disabled, isAnimating, outsideCurrentMonth]);
  const handleMouseDown = (event) => {
    if (onMouseDown) {
      onMouseDown(event);
    }
    if (outsideCurrentMonth) {
      event.preventDefault();
    }
  };
  const handleClick = (event) => {
    if (!disabled) {
      onDaySelect(day, "finish");
    }
    if (outsideCurrentMonth) {
      event.currentTarget.focus();
    }
    if (onClick) {
      onClick(event);
    }
  };
  if (outsideCurrentMonth && !showDaysOutsideCurrentMonth) {
    return (0, import_jsx_runtime3.jsx)(PickersDayFiller, {
      className: clsx_m_default(classes.root, classes.hiddenDaySpacingFiller, className),
      ownerState,
      role: other.role
    });
  }
  return (0, import_jsx_runtime3.jsx)(PickersDayRoot, _extends({
    className: clsx_m_default(classes.root, className),
    ownerState,
    ref: handleRef,
    centerRipple: true,
    disabled,
    tabIndex: selected ? 0 : -1,
    onKeyDown: (event) => onKeyDown(event, day),
    onFocus: (event) => onFocus(event, day),
    onBlur: (event) => onBlur(event, day),
    onClick: handleClick,
    onMouseDown: handleMouseDown
  }, other, {
    children: !children ? utils.format(day, "dayOfMonth") : children
  }));
});
var areDayPropsEqual = (prevProps, nextProps) => {
  return prevProps.autoFocus === nextProps.autoFocus && prevProps.isAnimating === nextProps.isAnimating && prevProps.today === nextProps.today && prevProps.disabled === nextProps.disabled && prevProps.selected === nextProps.selected && prevProps.disableMargin === nextProps.disableMargin && prevProps.showDaysOutsideCurrentMonth === nextProps.showDaysOutsideCurrentMonth && prevProps.disableHighlightToday === nextProps.disableHighlightToday && prevProps.className === nextProps.className && prevProps.sx === nextProps.sx && prevProps.outsideCurrentMonth === nextProps.outsideCurrentMonth && prevProps.onFocus === nextProps.onFocus && prevProps.onBlur === nextProps.onBlur && prevProps.onDaySelect === nextProps.onDaySelect;
};
true ? PickersDayRaw.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types2.default.object,
  /**
   * The date to show.
   */
  day: import_prop_types2.default.any.isRequired,
  /**
   * If `true`, renders as disabled.
   * @default false
   */
  disabled: import_prop_types2.default.bool,
  /**
   * If `true`, today's date is rendering without highlighting with circle.
   * @default false
   */
  disableHighlightToday: import_prop_types2.default.bool,
  /**
   * If `true`, days are rendering without margin. Useful for displaying linked range of days.
   * @default false
   */
  disableMargin: import_prop_types2.default.bool,
  isAnimating: import_prop_types2.default.bool,
  onBlur: import_prop_types2.default.func,
  onDaySelect: import_prop_types2.default.func.isRequired,
  onFocus: import_prop_types2.default.func,
  onKeyDown: import_prop_types2.default.func,
  /**
   * If `true`, day is outside of month and will be hidden.
   */
  outsideCurrentMonth: import_prop_types2.default.bool.isRequired,
  /**
   * If `true`, renders as selected.
   * @default false
   */
  selected: import_prop_types2.default.bool,
  /**
   * If `true`, days that have `outsideCurrentMonth={true}` are displayed.
   * @default false
   */
  showDaysOutsideCurrentMonth: import_prop_types2.default.bool,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types2.default.oneOfType([import_prop_types2.default.arrayOf(import_prop_types2.default.oneOfType([import_prop_types2.default.func, import_prop_types2.default.object, import_prop_types2.default.bool])), import_prop_types2.default.func, import_prop_types2.default.object]),
  /**
   * If `true`, renders as today date.
   * @default false
   */
  today: import_prop_types2.default.bool
} : void 0;
var PickersDay2 = React4.memo(PickersDayRaw, areDayPropsEqual);

// node_modules/@mui/x-date-pickers/CalendarPicker/pickersSlideTransitionClasses.js
var getPickersSlideTransitionUtilityClass = (slot) => (
  // TODO v6: Rename 'PrivatePickersSlideTransition' to 'MuiPickersSlideTransition' to follow convention
  generateUtilityClass("PrivatePickersSlideTransition", slot)
);
var pickersSlideTransitionClasses = generateUtilityClasses(
  // TODO v6: Rename 'PrivatePickersSlideTransition' to 'MuiPickersSlideTransition' to follow convention
  "PrivatePickersSlideTransition",
  ["root", "slideEnter-left", "slideEnter-right", "slideEnterActive", "slideExit", "slideExitActiveLeft-left", "slideExitActiveLeft-right"]
);

// node_modules/@mui/x-date-pickers/CalendarPicker/dayPickerClasses.js
var getDayPickerUtilityClass = (slot) => generateUtilityClass("MuiDayPicker", slot);
var dayPickerClasses = generateUtilityClasses("MuiDayPicker", ["header", "weekDayLabel", "loadingContainer", "slideTransition", "monthContainer", "weekContainer"]);

// node_modules/@mui/x-date-pickers/CalendarPicker/pickersCalendarHeaderClasses.js
var getPickersCalendarHeaderUtilityClass = (slot) => generateUtilityClass("MuiPickersCalendarHeader", slot);
var pickersCalendarHeaderClasses = generateUtilityClasses("MuiPickersCalendarHeader", ["root", "labelContainer", "label", "switchViewButton", "switchViewIcon"]);

// node_modules/@mui/x-date-pickers/YearPicker/pickersYearClasses.js
function getPickersYearUtilityClass(slot) {
  return generateUtilityClass("PrivatePickersYear", slot);
}
var pickersYearClasses = generateUtilityClasses("PrivatePickersYear", ["root", "modeDesktop", "modeMobile", "yearButton", "selected", "disabled"]);

// node_modules/@mui/x-date-pickers/YearPicker/yearPickerClasses.js
function getYearPickerUtilityClass(slot) {
  return generateUtilityClass("MuiYearPicker", slot);
}
var yearPickerClasses = generateUtilityClasses("MuiYearPicker", ["root"]);

// node_modules/@mui/x-date-pickers/YearPicker/YearPicker.js
init_extends();
var React7 = __toESM(require_react());
var import_prop_types3 = __toESM(require_prop_types());
init_utils();
init_esm();

// node_modules/@mui/x-date-pickers/YearPicker/PickersYear.js
init_objectWithoutPropertiesLoose();
init_extends();
var React6 = __toESM(require_react());
init_utils();

// node_modules/@mui/x-date-pickers/internals/components/wrappers/WrapperVariantContext.js
var React5 = __toESM(require_react());
var WrapperVariantContext = React5.createContext(null);

// node_modules/@mui/x-date-pickers/YearPicker/PickersYear.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var _excluded4 = ["autoFocus", "className", "children", "disabled", "onClick", "onKeyDown", "value", "tabIndex", "onFocus", "onBlur"];
var useUtilityClasses4 = (ownerState) => {
  const {
    wrapperVariant,
    disabled,
    selected,
    classes
  } = ownerState;
  const slots = {
    root: ["root", wrapperVariant && `mode${capitalize_default(wrapperVariant)}`],
    yearButton: ["yearButton", disabled && "disabled", selected && "selected"]
  };
  return composeClasses(slots, getPickersYearUtilityClass, classes);
};
var PickersYearRoot = styled_default("div", {
  name: "PrivatePickersYear",
  slot: "Root",
  overridesResolver: (_, styles) => [styles.root, {
    [`&.${pickersYearClasses.modeDesktop}`]: styles.modeDesktop
  }, {
    [`&.${pickersYearClasses.modeMobile}`]: styles.modeMobile
  }]
})(({
  ownerState
}) => _extends({
  flexBasis: "33.3%",
  display: "flex",
  alignItems: "center",
  justifyContent: "center"
}, (ownerState == null ? void 0 : ownerState.wrapperVariant) === "desktop" && {
  flexBasis: "25%"
}));
var PickersYearButton = styled_default("button", {
  name: "PrivatePickersYear",
  slot: "Button",
  overridesResolver: (_, styles) => [styles.button, {
    [`&.${pickersYearClasses.disabled}`]: styles.disabled
  }, {
    [`&.${pickersYearClasses.selected}`]: styles.selected
  }]
})(({
  theme
}) => _extends({
  color: "unset",
  backgroundColor: "transparent",
  border: 0,
  outline: 0
}, theme.typography.subtitle1, {
  margin: "8px 0",
  height: 36,
  width: 72,
  borderRadius: 18,
  cursor: "pointer",
  "&:focus, &:hover": {
    backgroundColor: alpha(theme.palette.action.active, theme.palette.action.hoverOpacity)
  },
  [`&.${pickersYearClasses.disabled}`]: {
    color: theme.palette.text.secondary
  },
  [`&.${pickersYearClasses.selected}`]: {
    color: theme.palette.primary.contrastText,
    backgroundColor: theme.palette.primary.main,
    "&:focus, &:hover": {
      backgroundColor: theme.palette.primary.dark
    }
  }
}));
var noop3 = () => {
};
var PickersYear = React6.forwardRef(function PickersYear2(props, forwardedRef) {
  const {
    autoFocus,
    className,
    children,
    disabled,
    onClick,
    onKeyDown,
    value,
    tabIndex,
    onFocus = noop3,
    onBlur = noop3
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded4);
  const ref = React6.useRef(null);
  const refHandle = useForkRef_default(ref, forwardedRef);
  const wrapperVariant = React6.useContext(WrapperVariantContext);
  const ownerState = _extends({}, props, {
    wrapperVariant
  });
  const classes = useUtilityClasses4(ownerState);
  React6.useEffect(() => {
    if (autoFocus) {
      ref.current.focus();
    }
  }, [autoFocus]);
  return (0, import_jsx_runtime4.jsx)(PickersYearRoot, {
    className: clsx_m_default(classes.root, className),
    ownerState,
    children: (0, import_jsx_runtime4.jsx)(PickersYearButton, _extends({
      ref: refHandle,
      disabled,
      type: "button",
      tabIndex: disabled ? -1 : tabIndex,
      onClick: (event) => onClick(event, value),
      onKeyDown: (event) => onKeyDown(event, value),
      onFocus: (event) => onFocus(event, value),
      onBlur: (event) => onBlur(event, value),
      className: classes.yearButton,
      ownerState
    }, other, {
      children
    }))
  });
});

// node_modules/@mui/x-date-pickers/YearPicker/YearPicker.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var useUtilityClasses5 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getYearPickerUtilityClass, classes);
};
function useYearPickerDefaultizedProps(props, name) {
  const utils = useUtils();
  const defaultDates = useDefaultDates();
  const themeProps = useThemeProps({
    props,
    name
  });
  return _extends({
    disablePast: false,
    disableFuture: false
  }, themeProps, {
    minDate: parseNonNullablePickerDate(utils, themeProps.minDate, defaultDates.minDate),
    maxDate: parseNonNullablePickerDate(utils, themeProps.maxDate, defaultDates.maxDate)
  });
}
var YearPickerRoot = styled_default("div", {
  name: "MuiYearPicker",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})({
  display: "flex",
  flexDirection: "row",
  flexWrap: "wrap",
  overflowY: "auto",
  height: "100%",
  padding: "0 4px",
  maxHeight: "304px"
});
var YearPicker = React7.forwardRef(function YearPicker2(inProps, ref) {
  const now = useNow();
  const theme = useTheme();
  const utils = useUtils();
  const props = useYearPickerDefaultizedProps(inProps, "MuiYearPicker");
  const {
    autoFocus,
    className,
    date,
    disabled,
    disableFuture,
    disablePast,
    maxDate,
    minDate,
    onChange,
    readOnly,
    shouldDisableYear,
    disableHighlightToday,
    onYearFocus,
    hasFocus,
    onFocusedViewChange
  } = props;
  const ownerState = props;
  const classes = useUtilityClasses5(ownerState);
  const selectedDateOrStartOfYear = React7.useMemo(() => date != null ? date : utils.startOfYear(now), [now, utils, date]);
  const currentYear = React7.useMemo(() => {
    if (date != null) {
      return utils.getYear(date);
    }
    if (disableHighlightToday) {
      return null;
    }
    return utils.getYear(now);
  }, [now, date, utils, disableHighlightToday]);
  const wrapperVariant = React7.useContext(WrapperVariantContext);
  const selectedYearRef = React7.useRef(null);
  const [focusedYear, setFocusedYear] = React7.useState(() => currentYear || utils.getYear(now));
  const [internalHasFocus, setInternalHasFocus] = useControlled({
    name: "YearPicker",
    state: "hasFocus",
    controlled: hasFocus,
    default: autoFocus
  });
  const changeHasFocus = React7.useCallback((newHasFocus) => {
    setInternalHasFocus(newHasFocus);
    if (onFocusedViewChange) {
      onFocusedViewChange(newHasFocus);
    }
  }, [setInternalHasFocus, onFocusedViewChange]);
  const isYearDisabled = React7.useCallback((dateToValidate) => {
    if (disablePast && utils.isBeforeYear(dateToValidate, now)) {
      return true;
    }
    if (disableFuture && utils.isAfterYear(dateToValidate, now)) {
      return true;
    }
    if (minDate && utils.isBeforeYear(dateToValidate, minDate)) {
      return true;
    }
    if (maxDate && utils.isAfterYear(dateToValidate, maxDate)) {
      return true;
    }
    if (shouldDisableYear && shouldDisableYear(dateToValidate)) {
      return true;
    }
    return false;
  }, [disableFuture, disablePast, maxDate, minDate, now, shouldDisableYear, utils]);
  const handleYearSelection = (event, year, isFinish = "finish") => {
    if (readOnly) {
      return;
    }
    const newDate = utils.setYear(selectedDateOrStartOfYear, year);
    onChange(newDate, isFinish);
  };
  const focusYear = React7.useCallback((year) => {
    if (!isYearDisabled(utils.setYear(selectedDateOrStartOfYear, year))) {
      setFocusedYear(year);
      changeHasFocus(true);
      onYearFocus == null ? void 0 : onYearFocus(year);
    }
  }, [isYearDisabled, utils, selectedDateOrStartOfYear, changeHasFocus, onYearFocus]);
  React7.useEffect(() => {
    setFocusedYear((prevFocusedYear) => currentYear !== null && prevFocusedYear !== currentYear ? currentYear : prevFocusedYear);
  }, [currentYear]);
  const yearsInRow = wrapperVariant === "desktop" ? 4 : 3;
  const handleKeyDown = React7.useCallback((event, year) => {
    switch (event.key) {
      case "ArrowUp":
        focusYear(year - yearsInRow);
        event.preventDefault();
        break;
      case "ArrowDown":
        focusYear(year + yearsInRow);
        event.preventDefault();
        break;
      case "ArrowLeft":
        focusYear(year + (theme.direction === "ltr" ? -1 : 1));
        event.preventDefault();
        break;
      case "ArrowRight":
        focusYear(year + (theme.direction === "ltr" ? 1 : -1));
        event.preventDefault();
        break;
      default:
        break;
    }
  }, [focusYear, theme.direction, yearsInRow]);
  const handleFocus = React7.useCallback((event, year) => {
    focusYear(year);
  }, [focusYear]);
  const handleBlur = React7.useCallback((event, year) => {
    if (focusedYear === year) {
      changeHasFocus(false);
    }
  }, [focusedYear, changeHasFocus]);
  const nowYear = utils.getYear(now);
  const scrollerRef = React7.useRef(null);
  const handleRef = useForkRef_default(ref, scrollerRef);
  React7.useEffect(() => {
    if (autoFocus || scrollerRef.current === null) {
      return;
    }
    const tabbableButton = scrollerRef.current.querySelector('[tabindex="0"]');
    if (!tabbableButton) {
      return;
    }
    const offsetHeight = tabbableButton.offsetHeight;
    const offsetTop = tabbableButton.offsetTop;
    const clientHeight = scrollerRef.current.clientHeight;
    const scrollTop = scrollerRef.current.scrollTop;
    const elementBottom = offsetTop + offsetHeight;
    if (offsetHeight > clientHeight || offsetTop < scrollTop) {
      return;
    }
    scrollerRef.current.scrollTop = elementBottom - clientHeight / 2 - offsetHeight / 2;
  }, [autoFocus]);
  return (0, import_jsx_runtime5.jsx)(YearPickerRoot, {
    ref: handleRef,
    className: clsx_m_default(classes.root, className),
    ownerState,
    children: utils.getYearRange(minDate, maxDate).map((year) => {
      const yearNumber = utils.getYear(year);
      const selected = yearNumber === currentYear;
      return (0, import_jsx_runtime5.jsx)(PickersYear, {
        selected,
        value: yearNumber,
        onClick: handleYearSelection,
        onKeyDown: handleKeyDown,
        autoFocus: internalHasFocus && yearNumber === focusedYear,
        ref: selected ? selectedYearRef : void 0,
        disabled: disabled || isYearDisabled(year),
        tabIndex: yearNumber === focusedYear ? 0 : -1,
        onFocus: handleFocus,
        onBlur: handleBlur,
        "aria-current": nowYear === yearNumber ? "date" : void 0,
        children: utils.format(year, "year")
      }, utils.format(year, "year"));
    })
  });
});
true ? YearPicker.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  autoFocus: import_prop_types3.default.bool,
  classes: import_prop_types3.default.object,
  className: import_prop_types3.default.string,
  date: import_prop_types3.default.any,
  disabled: import_prop_types3.default.bool,
  /**
   * If `true` future days are disabled.
   * @default false
   */
  disableFuture: import_prop_types3.default.bool,
  /**
   * If `true`, today's date is rendering without highlighting with circle.
   * @default false
   */
  disableHighlightToday: import_prop_types3.default.bool,
  /**
   * If `true` past days are disabled.
   * @default false
   */
  disablePast: import_prop_types3.default.bool,
  hasFocus: import_prop_types3.default.bool,
  /**
   * Maximal selectable date. @DateIOType
   */
  maxDate: import_prop_types3.default.any,
  /**
   * Minimal selectable date. @DateIOType
   */
  minDate: import_prop_types3.default.any,
  onChange: import_prop_types3.default.func.isRequired,
  onFocusedDayChange: import_prop_types3.default.func,
  onFocusedViewChange: import_prop_types3.default.func,
  onYearFocus: import_prop_types3.default.func,
  readOnly: import_prop_types3.default.bool,
  /**
   * Disable specific years dynamically.
   * Works like `shouldDisableDate` but for year selection view @DateIOType.
   * @template TDate
   * @param {TDate} year The year to test.
   * @returns {boolean} Returns `true` if the year should be disabled.
   */
  shouldDisableYear: import_prop_types3.default.func
} : void 0;

// node_modules/@mui/x-date-pickers/CalendarPicker/calendarPickerClasses.js
var getCalendarPickerUtilityClass = (slot) => generateUtilityClass("MuiCalendarPicker", slot);
var calendarPickerClasses = generateUtilityClasses("MuiCalendarPicker", ["root", "viewTransitionContainer"]);

// node_modules/@mui/x-date-pickers/CalendarPicker/CalendarPicker.js
init_objectWithoutPropertiesLoose();
init_extends();
var React19 = __toESM(require_react());
var import_prop_types4 = __toESM(require_prop_types());
init_utils();

// node_modules/@mui/x-date-pickers/CalendarPicker/useCalendarState.js
init_extends();
var React10 = __toESM(require_react());

// node_modules/@mui/x-date-pickers/internals/hooks/validation/useDateValidation.js
var React9 = __toESM(require_react());

// node_modules/@mui/x-date-pickers/internals/hooks/validation/useValidation.js
var React8 = __toESM(require_react());
function useValidation(props, validate, isSameError) {
  const {
    value,
    onError
  } = props;
  const adapter = useLocalizationContext();
  const previousValidationErrorRef = React8.useRef(null);
  const validationError = validate({
    adapter,
    value,
    props
  });
  React8.useEffect(() => {
    if (onError && !isSameError(validationError, previousValidationErrorRef.current)) {
      onError(validationError, value);
    }
    previousValidationErrorRef.current = validationError;
  }, [isSameError, onError, previousValidationErrorRef, validationError, value]);
  return validationError;
}

// node_modules/@mui/x-date-pickers/internals/hooks/validation/useDateValidation.js
var validateDate = ({
  props,
  value,
  adapter
}) => {
  const now = adapter.utils.date();
  const date = adapter.utils.date(value);
  const minDate = parseNonNullablePickerDate(adapter.utils, props.minDate, adapter.defaultDates.minDate);
  const maxDate = parseNonNullablePickerDate(adapter.utils, props.maxDate, adapter.defaultDates.maxDate);
  if (date === null) {
    return null;
  }
  switch (true) {
    case !adapter.utils.isValid(value):
      return "invalidDate";
    case Boolean(props.shouldDisableDate && props.shouldDisableDate(date)):
      return "shouldDisableDate";
    case Boolean(props.disableFuture && adapter.utils.isAfterDay(date, now)):
      return "disableFuture";
    case Boolean(props.disablePast && adapter.utils.isBeforeDay(date, now)):
      return "disablePast";
    case Boolean(minDate && adapter.utils.isBeforeDay(date, minDate)):
      return "minDate";
    case Boolean(maxDate && adapter.utils.isAfterDay(date, maxDate)):
      return "maxDate";
    default:
      return null;
  }
};
var useIsDayDisabled = ({
  shouldDisableDate,
  minDate,
  maxDate,
  disableFuture,
  disablePast
}) => {
  const adapter = useLocalizationContext();
  return React9.useCallback((day) => validateDate({
    adapter,
    value: day,
    props: {
      shouldDisableDate,
      minDate,
      maxDate,
      disableFuture,
      disablePast
    }
  }) !== null, [adapter, shouldDisableDate, minDate, maxDate, disableFuture, disablePast]);
};
var isSameDateError = (a, b) => a === b;
var useDateValidation = (props) => useValidation(props, validateDate, isSameDateError);

// node_modules/@mui/x-date-pickers/CalendarPicker/useCalendarState.js
var createCalendarStateReducer = (reduceAnimations, disableSwitchToMonthOnDayFocus, utils) => (state, action) => {
  switch (action.type) {
    case "changeMonth":
      return _extends({}, state, {
        slideDirection: action.direction,
        currentMonth: action.newMonth,
        isMonthSwitchingAnimating: !reduceAnimations
      });
    case "finishMonthSwitchingAnimation":
      return _extends({}, state, {
        isMonthSwitchingAnimating: false
      });
    case "changeFocusedDay": {
      if (state.focusedDay != null && action.focusedDay != null && utils.isSameDay(action.focusedDay, state.focusedDay)) {
        return state;
      }
      const needMonthSwitch = action.focusedDay != null && !disableSwitchToMonthOnDayFocus && !utils.isSameMonth(state.currentMonth, action.focusedDay);
      return _extends({}, state, {
        focusedDay: action.focusedDay,
        isMonthSwitchingAnimating: needMonthSwitch && !reduceAnimations && !action.withoutMonthSwitchingAnimation,
        currentMonth: needMonthSwitch ? utils.startOfMonth(action.focusedDay) : state.currentMonth,
        slideDirection: action.focusedDay != null && utils.isAfterDay(action.focusedDay, state.currentMonth) ? "left" : "right"
      });
    }
    default:
      throw new Error("missing support");
  }
};
var useCalendarState = ({
  date,
  defaultCalendarMonth,
  disableFuture,
  disablePast,
  disableSwitchToMonthOnDayFocus = false,
  maxDate,
  minDate,
  onMonthChange,
  reduceAnimations,
  shouldDisableDate
}) => {
  var _ref;
  const now = useNow();
  const utils = useUtils();
  const reducerFn = React10.useRef(createCalendarStateReducer(Boolean(reduceAnimations), disableSwitchToMonthOnDayFocus, utils)).current;
  const [calendarState, dispatch] = React10.useReducer(reducerFn, {
    isMonthSwitchingAnimating: false,
    focusedDay: date || now,
    currentMonth: utils.startOfMonth((_ref = date != null ? date : defaultCalendarMonth) != null ? _ref : now),
    slideDirection: "left"
  });
  const handleChangeMonth = React10.useCallback((payload) => {
    dispatch(_extends({
      type: "changeMonth"
    }, payload));
    if (onMonthChange) {
      onMonthChange(payload.newMonth);
    }
  }, [onMonthChange]);
  const changeMonth = React10.useCallback((newDate) => {
    const newDateRequested = newDate != null ? newDate : now;
    if (utils.isSameMonth(newDateRequested, calendarState.currentMonth)) {
      return;
    }
    handleChangeMonth({
      newMonth: utils.startOfMonth(newDateRequested),
      direction: utils.isAfterDay(newDateRequested, calendarState.currentMonth) ? "left" : "right"
    });
  }, [calendarState.currentMonth, handleChangeMonth, now, utils]);
  const isDateDisabled = useIsDayDisabled({
    shouldDisableDate,
    minDate,
    maxDate,
    disableFuture,
    disablePast
  });
  const onMonthSwitchingAnimationEnd = React10.useCallback(() => {
    dispatch({
      type: "finishMonthSwitchingAnimation"
    });
  }, []);
  const changeFocusedDay = React10.useCallback((newFocusedDate, withoutMonthSwitchingAnimation) => {
    if (!isDateDisabled(newFocusedDate)) {
      dispatch({
        type: "changeFocusedDay",
        focusedDay: newFocusedDate,
        withoutMonthSwitchingAnimation
      });
    }
  }, [isDateDisabled]);
  return {
    calendarState,
    changeMonth,
    changeFocusedDay,
    isDateDisabled,
    onMonthSwitchingAnimationEnd,
    handleChangeMonth
  };
};

// node_modules/@mui/x-date-pickers/CalendarPicker/PickersFadeTransitionGroup.js
var React11 = __toESM(require_react());
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var useUtilityClasses6 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getPickersFadeTransitionGroupUtilityClass, classes);
};
var animationDuration = 500;
var PickersFadeTransitionGroupRoot = styled_default(TransitionGroup_default, {
  name: "MuiPickersFadeTransitionGroup",
  slot: "Root",
  overridesResolver: (_, styles) => styles.root
})({
  display: "block",
  position: "relative"
});
function PickersFadeTransitionGroup(inProps) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiPickersFadeTransitionGroup"
  });
  const {
    children,
    className,
    reduceAnimations,
    transKey
  } = props;
  const classes = useUtilityClasses6(props);
  if (reduceAnimations) {
    return children;
  }
  return (0, import_jsx_runtime6.jsx)(PickersFadeTransitionGroupRoot, {
    className: clsx_m_default(classes.root, className),
    children: (0, import_jsx_runtime6.jsx)(Fade_default, {
      appear: false,
      mountOnEnter: true,
      unmountOnExit: true,
      timeout: {
        appear: animationDuration,
        enter: animationDuration / 2,
        exit: 0
      },
      children
    }, transKey)
  });
}

// node_modules/@mui/x-date-pickers/CalendarPicker/DayPicker.js
init_extends();
var React13 = __toESM(require_react());

// node_modules/@mui/x-date-pickers/CalendarPicker/PickersSlideTransition.js
init_extends();
init_objectWithoutPropertiesLoose();
var React12 = __toESM(require_react());
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
var _excluded5 = ["children", "className", "reduceAnimations", "slideDirection", "transKey"];
var useUtilityClasses7 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getPickersSlideTransitionUtilityClass, classes);
};
var slideAnimationDuration = 350;
var PickersSlideTransitionRoot = styled_default(TransitionGroup_default, {
  name: "PrivatePickersSlideTransition",
  slot: "Root",
  overridesResolver: (_, styles) => [styles.root, {
    [`.${pickersSlideTransitionClasses["slideEnter-left"]}`]: styles["slideEnter-left"]
  }, {
    [`.${pickersSlideTransitionClasses["slideEnter-right"]}`]: styles["slideEnter-right"]
  }, {
    [`.${pickersSlideTransitionClasses.slideEnterActive}`]: styles.slideEnterActive
  }, {
    [`.${pickersSlideTransitionClasses.slideExit}`]: styles.slideExit
  }, {
    [`.${pickersSlideTransitionClasses["slideExitActiveLeft-left"]}`]: styles["slideExitActiveLeft-left"]
  }, {
    [`.${pickersSlideTransitionClasses["slideExitActiveLeft-right"]}`]: styles["slideExitActiveLeft-right"]
  }]
})(({
  theme
}) => {
  const slideTransition = theme.transitions.create("transform", {
    duration: slideAnimationDuration,
    easing: "cubic-bezier(0.35, 0.8, 0.4, 1)"
  });
  return {
    display: "block",
    position: "relative",
    overflowX: "hidden",
    "& > *": {
      position: "absolute",
      top: 0,
      right: 0,
      left: 0
    },
    [`& .${pickersSlideTransitionClasses["slideEnter-left"]}`]: {
      willChange: "transform",
      transform: "translate(100%)",
      zIndex: 1
    },
    [`& .${pickersSlideTransitionClasses["slideEnter-right"]}`]: {
      willChange: "transform",
      transform: "translate(-100%)",
      zIndex: 1
    },
    [`& .${pickersSlideTransitionClasses.slideEnterActive}`]: {
      transform: "translate(0%)",
      transition: slideTransition
    },
    [`& .${pickersSlideTransitionClasses.slideExit}`]: {
      transform: "translate(0%)"
    },
    [`& .${pickersSlideTransitionClasses["slideExitActiveLeft-left"]}`]: {
      willChange: "transform",
      transform: "translate(-100%)",
      transition: slideTransition,
      zIndex: 0
    },
    [`& .${pickersSlideTransitionClasses["slideExitActiveLeft-right"]}`]: {
      willChange: "transform",
      transform: "translate(100%)",
      transition: slideTransition,
      zIndex: 0
    }
  };
});
var PickersSlideTransition = (props) => {
  const {
    children,
    className,
    reduceAnimations,
    slideDirection,
    transKey
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded5);
  const classes = useUtilityClasses7(props);
  if (reduceAnimations) {
    return (0, import_jsx_runtime7.jsx)("div", {
      className: clsx_m_default(classes.root, className),
      children
    });
  }
  const transitionClasses = {
    exit: pickersSlideTransitionClasses.slideExit,
    enterActive: pickersSlideTransitionClasses.slideEnterActive,
    enter: pickersSlideTransitionClasses[`slideEnter-${slideDirection}`],
    exitActive: pickersSlideTransitionClasses[`slideExitActiveLeft-${slideDirection}`]
  };
  return (0, import_jsx_runtime7.jsx)(PickersSlideTransitionRoot, {
    className: clsx_m_default(classes.root, className),
    childFactory: (element) => React12.cloneElement(element, {
      classNames: transitionClasses
    }),
    role: "presentation",
    children: (0, import_jsx_runtime7.jsx)(CSSTransition_default, _extends({
      mountOnEnter: true,
      unmountOnExit: true,
      timeout: slideAnimationDuration,
      classNames: transitionClasses
    }, other, {
      children
    }), transKey)
  });
};

// node_modules/@mui/x-date-pickers/CalendarPicker/DayPicker.js
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var import_react = __toESM(require_react());
var import_jsx_runtime9 = __toESM(require_jsx_runtime());
var useUtilityClasses8 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    header: ["header"],
    weekDayLabel: ["weekDayLabel"],
    loadingContainer: ["loadingContainer"],
    slideTransition: ["slideTransition"],
    monthContainer: ["monthContainer"],
    weekContainer: ["weekContainer"]
  };
  return composeClasses(slots, getDayPickerUtilityClass, classes);
};
var defaultDayOfWeekFormatter = (day) => day.charAt(0).toUpperCase();
var weeksContainerHeight = (DAY_SIZE + DAY_MARGIN * 2) * 6;
var PickersCalendarDayHeader = styled_default("div", {
  name: "MuiDayPicker",
  slot: "Header",
  overridesResolver: (_, styles) => styles.header
})({
  display: "flex",
  justifyContent: "center",
  alignItems: "center"
});
var PickersCalendarWeekDayLabel = styled_default(Typography_default, {
  name: "MuiDayPicker",
  slot: "WeekDayLabel",
  overridesResolver: (_, styles) => styles.weekDayLabel
})(({
  theme
}) => ({
  width: 36,
  height: 40,
  margin: "0 2px",
  textAlign: "center",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  color: theme.palette.text.secondary
}));
var PickersCalendarLoadingContainer = styled_default("div", {
  name: "MuiDayPicker",
  slot: "LoadingContainer",
  overridesResolver: (_, styles) => styles.loadingContainer
})({
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  minHeight: weeksContainerHeight
});
var PickersCalendarSlideTransition = styled_default(PickersSlideTransition, {
  name: "MuiDayPicker",
  slot: "SlideTransition",
  overridesResolver: (_, styles) => styles.slideTransition
})({
  minHeight: weeksContainerHeight
});
var PickersCalendarWeekContainer = styled_default("div", {
  name: "MuiDayPicker",
  slot: "MonthContainer",
  overridesResolver: (_, styles) => styles.monthContainer
})({
  overflow: "hidden"
});
var PickersCalendarWeek = styled_default("div", {
  name: "MuiDayPicker",
  slot: "WeekContainer",
  overridesResolver: (_, styles) => styles.weekContainer
})({
  margin: `${DAY_MARGIN}px 0`,
  display: "flex",
  justifyContent: "center"
});
function DayPicker(inProps) {
  const now = useNow();
  const utils = useUtils();
  const props = useThemeProps({
    props: inProps,
    name: "MuiDayPicker"
  });
  const classes = useUtilityClasses8(props);
  const {
    onFocusedDayChange,
    className,
    currentMonth,
    selectedDays,
    disabled,
    disableHighlightToday,
    focusedDay,
    isMonthSwitchingAnimating,
    loading,
    onSelectedDaysChange,
    onMonthSwitchingAnimationEnd,
    readOnly,
    reduceAnimations,
    renderDay,
    renderLoading = () => (0, import_jsx_runtime8.jsx)("span", {
      children: "..."
    }),
    showDaysOutsideCurrentMonth,
    slideDirection,
    TransitionProps,
    disablePast,
    disableFuture,
    minDate,
    maxDate,
    shouldDisableDate,
    dayOfWeekFormatter = defaultDayOfWeekFormatter,
    hasFocus,
    onFocusedViewChange,
    gridLabelId
  } = props;
  const isDateDisabled = useIsDayDisabled({
    shouldDisableDate,
    minDate,
    maxDate,
    disablePast,
    disableFuture
  });
  const [internalFocusedDay, setInternalFocusedDay] = React13.useState(() => focusedDay || now);
  const changeHasFocus = React13.useCallback((newHasFocus) => {
    if (onFocusedViewChange) {
      onFocusedViewChange(newHasFocus);
    }
  }, [onFocusedViewChange]);
  const handleDaySelect = React13.useCallback((day, isFinish = "finish") => {
    if (readOnly) {
      return;
    }
    onSelectedDaysChange(day, isFinish);
  }, [onSelectedDaysChange, readOnly]);
  const focusDay = React13.useCallback((day) => {
    if (!isDateDisabled(day)) {
      onFocusedDayChange(day);
      setInternalFocusedDay(day);
      changeHasFocus(true);
    }
  }, [isDateDisabled, onFocusedDayChange, changeHasFocus]);
  const theme = useTheme();
  function handleKeyDown(event, day) {
    switch (event.key) {
      case "ArrowUp":
        focusDay(utils.addDays(day, -7));
        event.preventDefault();
        break;
      case "ArrowDown":
        focusDay(utils.addDays(day, 7));
        event.preventDefault();
        break;
      case "ArrowLeft": {
        const newFocusedDayDefault = utils.addDays(day, theme.direction === "ltr" ? -1 : 1);
        const nextAvailableMonth = theme.direction === "ltr" ? utils.getPreviousMonth(day) : utils.getNextMonth(day);
        const closestDayToFocus = findClosestEnabledDate({
          utils,
          date: newFocusedDayDefault,
          minDate: theme.direction === "ltr" ? utils.startOfMonth(nextAvailableMonth) : newFocusedDayDefault,
          maxDate: theme.direction === "ltr" ? newFocusedDayDefault : utils.endOfMonth(nextAvailableMonth),
          isDateDisabled
        });
        focusDay(closestDayToFocus || newFocusedDayDefault);
        event.preventDefault();
        break;
      }
      case "ArrowRight": {
        const newFocusedDayDefault = utils.addDays(day, theme.direction === "ltr" ? 1 : -1);
        const nextAvailableMonth = theme.direction === "ltr" ? utils.getNextMonth(day) : utils.getPreviousMonth(day);
        const closestDayToFocus = findClosestEnabledDate({
          utils,
          date: newFocusedDayDefault,
          minDate: theme.direction === "ltr" ? newFocusedDayDefault : utils.startOfMonth(nextAvailableMonth),
          maxDate: theme.direction === "ltr" ? utils.endOfMonth(nextAvailableMonth) : newFocusedDayDefault,
          isDateDisabled
        });
        focusDay(closestDayToFocus || newFocusedDayDefault);
        event.preventDefault();
        break;
      }
      case "Home":
        focusDay(utils.startOfWeek(day));
        event.preventDefault();
        break;
      case "End":
        focusDay(utils.endOfWeek(day));
        event.preventDefault();
        break;
      case "PageUp":
        focusDay(utils.getNextMonth(day));
        event.preventDefault();
        break;
      case "PageDown":
        focusDay(utils.getPreviousMonth(day));
        event.preventDefault();
        break;
      default:
        break;
    }
  }
  function handleFocus(event, day) {
    focusDay(day);
  }
  function handleBlur(event, day) {
    if (hasFocus && utils.isSameDay(internalFocusedDay, day)) {
      changeHasFocus(false);
    }
  }
  const currentMonthNumber = utils.getMonth(currentMonth);
  const validSelectedDays = selectedDays.filter((day) => !!day).map((day) => utils.startOfDay(day));
  const transitionKey = currentMonthNumber;
  const slideNodeRef = React13.useMemo(() => React13.createRef(), [transitionKey]);
  const startOfCurrentWeek = utils.startOfWeek(now);
  const focusableDay = React13.useMemo(() => {
    const startOfMonth = utils.startOfMonth(currentMonth);
    const endOfMonth = utils.endOfMonth(currentMonth);
    if (isDateDisabled(internalFocusedDay) || utils.isAfterDay(internalFocusedDay, endOfMonth) || utils.isBeforeDay(internalFocusedDay, startOfMonth)) {
      return findClosestEnabledDate({
        utils,
        date: internalFocusedDay,
        minDate: startOfMonth,
        maxDate: endOfMonth,
        disablePast,
        disableFuture,
        isDateDisabled
      });
    }
    return internalFocusedDay;
  }, [currentMonth, disableFuture, disablePast, internalFocusedDay, isDateDisabled, utils]);
  return (0, import_jsx_runtime9.jsxs)("div", {
    role: "grid",
    "aria-labelledby": gridLabelId,
    children: [(0, import_jsx_runtime8.jsx)(PickersCalendarDayHeader, {
      role: "row",
      className: classes.header,
      children: utils.getWeekdays().map((day, i) => {
        var _dayOfWeekFormatter;
        return (0, import_jsx_runtime8.jsx)(PickersCalendarWeekDayLabel, {
          variant: "caption",
          role: "columnheader",
          "aria-label": utils.format(utils.addDays(startOfCurrentWeek, i), "weekday"),
          className: classes.weekDayLabel,
          children: (_dayOfWeekFormatter = dayOfWeekFormatter == null ? void 0 : dayOfWeekFormatter(day)) != null ? _dayOfWeekFormatter : day
        }, day + i.toString());
      })
    }), loading ? (0, import_jsx_runtime8.jsx)(PickersCalendarLoadingContainer, {
      className: classes.loadingContainer,
      children: renderLoading()
    }) : (0, import_jsx_runtime8.jsx)(PickersCalendarSlideTransition, _extends({
      transKey: transitionKey,
      onExited: onMonthSwitchingAnimationEnd,
      reduceAnimations,
      slideDirection,
      className: clsx_m_default(className, classes.slideTransition)
    }, TransitionProps, {
      nodeRef: slideNodeRef,
      children: (0, import_jsx_runtime8.jsx)(PickersCalendarWeekContainer, {
        ref: slideNodeRef,
        role: "rowgroup",
        className: classes.monthContainer,
        children: utils.getWeekArray(currentMonth).map((week) => (0, import_jsx_runtime8.jsx)(PickersCalendarWeek, {
          role: "row",
          className: classes.weekContainer,
          children: week.map((day) => {
            const isFocusableDay = focusableDay !== null && utils.isSameDay(day, focusableDay);
            const isSelected = validSelectedDays.some((selectedDay) => utils.isSameDay(selectedDay, day));
            const isToday = utils.isSameDay(day, now);
            const pickersDayProps = {
              key: day == null ? void 0 : day.toString(),
              day,
              isAnimating: isMonthSwitchingAnimating,
              disabled: disabled || isDateDisabled(day),
              autoFocus: hasFocus && isFocusableDay,
              today: isToday,
              outsideCurrentMonth: utils.getMonth(day) !== currentMonthNumber,
              selected: isSelected,
              disableHighlightToday,
              showDaysOutsideCurrentMonth,
              onKeyDown: handleKeyDown,
              onFocus: handleFocus,
              onBlur: handleBlur,
              onDaySelect: handleDaySelect,
              tabIndex: isFocusableDay ? 0 : -1,
              role: "gridcell",
              "aria-selected": isSelected
            };
            if (isToday) {
              pickersDayProps["aria-current"] = "date";
            }
            return renderDay ? renderDay(day, validSelectedDays, pickersDayProps) : (0, import_react.createElement)(PickersDay2, _extends({}, pickersDayProps, {
              key: pickersDayProps.key
            }));
          })
        }, `week-${week[0]}`))
      })
    }))]
  });
}

// node_modules/@mui/x-date-pickers/internals/hooks/useViews.js
var React14 = __toESM(require_react());
init_utils();
function useViews({
  onChange,
  onViewChange,
  openTo,
  view,
  views
}) {
  var _views, _views2;
  const [openView, setOpenView] = useControlled_default({
    name: "Picker",
    state: "view",
    controlled: view,
    default: openTo && arrayIncludes(views, openTo) ? openTo : views[0]
  });
  const previousView = (_views = views[views.indexOf(openView) - 1]) != null ? _views : null;
  const nextView = (_views2 = views[views.indexOf(openView) + 1]) != null ? _views2 : null;
  const changeView = React14.useCallback((newView) => {
    setOpenView(newView);
    if (onViewChange) {
      onViewChange(newView);
    }
  }, [setOpenView, onViewChange]);
  const openNext = React14.useCallback(() => {
    if (nextView) {
      changeView(nextView);
    }
  }, [nextView, changeView]);
  const handleChangeAndOpenNext = React14.useCallback((date, currentViewSelectionState) => {
    const isSelectionFinishedOnCurrentView = currentViewSelectionState === "finish";
    const globalSelectionState = isSelectionFinishedOnCurrentView && Boolean(nextView) ? "partial" : currentViewSelectionState;
    onChange(date, globalSelectionState);
    if (isSelectionFinishedOnCurrentView) {
      openNext();
    }
  }, [nextView, onChange, openNext]);
  return {
    handleChangeAndOpenNext,
    nextView,
    previousView,
    openNext,
    openView,
    setOpenView: changeView
  };
}

// node_modules/@mui/x-date-pickers/CalendarPicker/PickersCalendarHeader.js
init_extends();
var React18 = __toESM(require_react());

// node_modules/@mui/x-date-pickers/internals/components/icons/index.js
init_utils();
var React15 = __toESM(require_react());
var import_jsx_runtime10 = __toESM(require_jsx_runtime());
var import_jsx_runtime11 = __toESM(require_jsx_runtime());
var ArrowDropDown = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M7 10l5 5 5-5z"
}), "ArrowDropDown");
var ArrowLeft = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
}), "ArrowLeft");
var ArrowRight = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
}), "ArrowRight");
var Calendar = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
}), "Calendar");
var Clock = createSvgIcon((0, import_jsx_runtime11.jsxs)(React15.Fragment, {
  children: [(0, import_jsx_runtime10.jsx)("path", {
    d: "M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
  }), (0, import_jsx_runtime10.jsx)("path", {
    d: "M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"
  })]
}), "Clock");
var DateRange = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"
}), "DateRange");
var Pen = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.9959.9959 0 00-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
}), "Pen");
var Time = createSvgIcon((0, import_jsx_runtime11.jsxs)(React15.Fragment, {
  children: [(0, import_jsx_runtime10.jsx)("path", {
    d: "M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
  }), (0, import_jsx_runtime10.jsx)("path", {
    d: "M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"
  })]
}), "Time");

// node_modules/@mui/x-date-pickers/internals/components/PickersArrowSwitcher.js
init_objectWithoutPropertiesLoose();
init_extends();
var React16 = __toESM(require_react());

// node_modules/@mui/x-date-pickers/internals/components/pickersArrowSwitcherClasses.js
function getPickersArrowSwitcherUtilityClass(slot) {
  return generateUtilityClass("MuiPickersArrowSwitcher", slot);
}
var pickersArrowSwitcherClasses = generateUtilityClasses("MuiPickersArrowSwitcher", ["root", "spacer", "button"]);

// node_modules/@mui/x-date-pickers/internals/components/PickersArrowSwitcher.js
var import_jsx_runtime12 = __toESM(require_jsx_runtime());
var import_jsx_runtime13 = __toESM(require_jsx_runtime());
var _excluded6 = ["children", "className", "components", "componentsProps", "isLeftDisabled", "isLeftHidden", "isRightDisabled", "isRightHidden", "leftArrowButtonText", "onLeftClick", "onRightClick", "rightArrowButtonText"];
var useUtilityClasses9 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"],
    spacer: ["spacer"],
    button: ["button"]
  };
  return composeClasses(slots, getPickersArrowSwitcherUtilityClass, classes);
};
var PickersArrowSwitcherRoot = styled_default("div", {
  name: "MuiPickersArrowSwitcher",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})({
  display: "flex"
});
var PickersArrowSwitcherSpacer = styled_default("div", {
  name: "MuiPickersArrowSwitcher",
  slot: "Spacer",
  overridesResolver: (props, styles) => styles.spacer
})(({
  theme
}) => ({
  width: theme.spacing(3)
}));
var PickersArrowSwitcherButton = styled_default(IconButton_default, {
  name: "MuiPickersArrowSwitcher",
  slot: "Button",
  overridesResolver: (props, styles) => styles.button
})(({
  ownerState
}) => _extends({}, ownerState.hidden && {
  visibility: "hidden"
}));
var PickersArrowSwitcher = React16.forwardRef(function PickersArrowSwitcher2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiPickersArrowSwitcher"
  });
  const {
    children,
    className,
    components,
    componentsProps,
    isLeftDisabled,
    isLeftHidden,
    isRightDisabled,
    isRightHidden,
    leftArrowButtonText,
    onLeftClick,
    onRightClick,
    rightArrowButtonText
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded6);
  const theme = useTheme();
  const isRtl = theme.direction === "rtl";
  const leftArrowButtonProps = (componentsProps == null ? void 0 : componentsProps.leftArrowButton) || {};
  const LeftArrowIcon = (components == null ? void 0 : components.LeftArrowIcon) || ArrowLeft;
  const rightArrowButtonProps = (componentsProps == null ? void 0 : componentsProps.rightArrowButton) || {};
  const RightArrowIcon = (components == null ? void 0 : components.RightArrowIcon) || ArrowRight;
  const ownerState = props;
  const classes = useUtilityClasses9(ownerState);
  return (0, import_jsx_runtime13.jsxs)(PickersArrowSwitcherRoot, _extends({
    ref,
    className: clsx_m_default(classes.root, className),
    ownerState
  }, other, {
    children: [(0, import_jsx_runtime12.jsx)(PickersArrowSwitcherButton, _extends({
      as: components == null ? void 0 : components.LeftArrowButton,
      size: "small",
      "aria-label": leftArrowButtonText,
      title: leftArrowButtonText,
      disabled: isLeftDisabled,
      edge: "end",
      onClick: onLeftClick
    }, leftArrowButtonProps, {
      className: clsx_m_default(classes.button, leftArrowButtonProps.className),
      ownerState: _extends({}, ownerState, leftArrowButtonProps, {
        hidden: isLeftHidden
      }),
      children: isRtl ? (0, import_jsx_runtime12.jsx)(RightArrowIcon, {}) : (0, import_jsx_runtime12.jsx)(LeftArrowIcon, {})
    })), children ? (0, import_jsx_runtime12.jsx)(Typography_default, {
      variant: "subtitle1",
      component: "span",
      children
    }) : (0, import_jsx_runtime12.jsx)(PickersArrowSwitcherSpacer, {
      className: classes.spacer,
      ownerState
    }), (0, import_jsx_runtime12.jsx)(PickersArrowSwitcherButton, _extends({
      as: components == null ? void 0 : components.RightArrowButton,
      size: "small",
      "aria-label": rightArrowButtonText,
      title: rightArrowButtonText,
      edge: "start",
      disabled: isRightDisabled,
      onClick: onRightClick
    }, rightArrowButtonProps, {
      className: clsx_m_default(classes.button, rightArrowButtonProps.className),
      ownerState: _extends({}, ownerState, rightArrowButtonProps, {
        hidden: isRightHidden
      }),
      children: isRtl ? (0, import_jsx_runtime12.jsx)(LeftArrowIcon, {}) : (0, import_jsx_runtime12.jsx)(RightArrowIcon, {})
    }))]
  }));
});

// node_modules/@mui/x-date-pickers/internals/hooks/date-helpers-hooks.js
var React17 = __toESM(require_react());

// node_modules/@mui/x-date-pickers/internals/utils/time-utils.js
var getMeridiem = (date, utils) => {
  if (!date) {
    return null;
  }
  return utils.getHours(date) >= 12 ? "pm" : "am";
};
var convertValueToMeridiem = (value, meridiem, ampm) => {
  if (ampm) {
    const currentMeridiem = value >= 12 ? "pm" : "am";
    if (currentMeridiem !== meridiem) {
      return meridiem === "am" ? value - 12 : value + 12;
    }
  }
  return value;
};
var convertToMeridiem = (time, meridiem, ampm, utils) => {
  const newHoursAmount = convertValueToMeridiem(utils.getHours(time), meridiem, ampm);
  return utils.setHours(time, newHoursAmount);
};
var getSecondsInDay = (date, utils) => {
  return utils.getHours(date) * 3600 + utils.getMinutes(date) * 60 + utils.getSeconds(date);
};
var createIsAfterIgnoreDatePart = (disableIgnoringDatePartForTimeValidation = false, utils) => (dateLeft, dateRight) => {
  if (disableIgnoringDatePartForTimeValidation) {
    return utils.isAfter(dateLeft, dateRight);
  }
  return getSecondsInDay(dateLeft, utils) > getSecondsInDay(dateRight, utils);
};

// node_modules/@mui/x-date-pickers/internals/hooks/date-helpers-hooks.js
function useNextMonthDisabled(month, {
  disableFuture,
  maxDate
}) {
  const utils = useUtils();
  return React17.useMemo(() => {
    const now = utils.date();
    const lastEnabledMonth = utils.startOfMonth(disableFuture && utils.isBefore(now, maxDate) ? now : maxDate);
    return !utils.isAfter(lastEnabledMonth, month);
  }, [disableFuture, maxDate, month, utils]);
}
function usePreviousMonthDisabled(month, {
  disablePast,
  minDate
}) {
  const utils = useUtils();
  return React17.useMemo(() => {
    const now = utils.date();
    const firstEnabledMonth = utils.startOfMonth(disablePast && utils.isAfter(now, minDate) ? now : minDate);
    return !utils.isBefore(firstEnabledMonth, month);
  }, [disablePast, minDate, month, utils]);
}
function useMeridiemMode(date, ampm, onChange) {
  const utils = useUtils();
  const meridiemMode = getMeridiem(date, utils);
  const handleMeridiemChange = React17.useCallback((mode) => {
    const timeWithMeridiem = date == null ? null : convertToMeridiem(date, mode, Boolean(ampm), utils);
    onChange(timeWithMeridiem, "partial");
  }, [ampm, date, onChange, utils]);
  return {
    meridiemMode,
    handleMeridiemChange
  };
}

// node_modules/@mui/x-date-pickers/internals/utils/warning.js
var buildDeprecatedPropsWarning = (message) => {
  let alreadyWarned = false;
  if (false) {
    return () => {
    };
  }
  const cleanMessage = Array.isArray(message) ? message.join("\n") : message;
  return (deprecatedProps) => {
    const deprecatedKeys = Object.entries(deprecatedProps).filter(([, value]) => value !== void 0).map(([key]) => `- ${key}`);
    if (!alreadyWarned && deprecatedKeys.length > 0) {
      alreadyWarned = true;
      console.warn([cleanMessage, "deprecated props observed:", ...deprecatedKeys].join("\n"));
    }
  };
};

// node_modules/@mui/x-date-pickers/CalendarPicker/PickersCalendarHeader.js
var import_jsx_runtime14 = __toESM(require_jsx_runtime());
var import_jsx_runtime15 = __toESM(require_jsx_runtime());
var useUtilityClasses10 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"],
    labelContainer: ["labelContainer"],
    label: ["label"],
    switchViewButton: ["switchViewButton"],
    switchViewIcon: ["switchViewIcon"]
  };
  return composeClasses(slots, getPickersCalendarHeaderUtilityClass, classes);
};
var PickersCalendarHeaderRoot = styled_default("div", {
  name: "MuiPickersCalendarHeader",
  slot: "Root",
  overridesResolver: (_, styles) => styles.root
})({
  display: "flex",
  alignItems: "center",
  marginTop: 16,
  marginBottom: 8,
  paddingLeft: 24,
  paddingRight: 12,
  // prevent jumping in safari
  maxHeight: 30,
  minHeight: 30
});
var PickersCalendarHeaderLabelContainer = styled_default("div", {
  name: "MuiPickersCalendarHeader",
  slot: "LabelContainer",
  overridesResolver: (_, styles) => styles.labelContainer
})(({
  theme
}) => _extends({
  display: "flex",
  maxHeight: 30,
  overflow: "hidden",
  alignItems: "center",
  cursor: "pointer",
  marginRight: "auto"
}, theme.typography.body1, {
  fontWeight: theme.typography.fontWeightMedium
}));
var PickersCalendarHeaderLabel = styled_default("div", {
  name: "MuiPickersCalendarHeader",
  slot: "Label",
  overridesResolver: (_, styles) => styles.label
})({
  marginRight: 6
});
var PickersCalendarHeaderSwitchViewButton = styled_default(IconButton_default, {
  name: "MuiPickersCalendarHeader",
  slot: "SwitchViewButton",
  overridesResolver: (_, styles) => styles.switchViewButton
})({
  marginRight: "auto"
});
var PickersCalendarHeaderSwitchViewIcon = styled_default(ArrowDropDown, {
  name: "MuiPickersCalendarHeader",
  slot: "SwitchViewIcon",
  overridesResolver: (_, styles) => styles.switchViewIcon
})(({
  theme,
  ownerState
}) => _extends({
  willChange: "transform",
  transition: theme.transitions.create("transform"),
  transform: "rotate(0deg)"
}, ownerState.openView === "year" && {
  transform: "rotate(180deg)"
}));
var deprecatedPropsWarning = buildDeprecatedPropsWarning("Props for translation are deprecated. See https://mui.com/x/react-date-pickers/localization for more information.");
function PickersCalendarHeader(inProps) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiPickersCalendarHeader"
  });
  const {
    components = {},
    componentsProps = {},
    currentMonth: month,
    disabled,
    disableFuture,
    disablePast,
    getViewSwitchingButtonText: getViewSwitchingButtonTextProp,
    leftArrowButtonText: leftArrowButtonTextProp,
    maxDate,
    minDate,
    onMonthChange,
    onViewChange,
    openView: currentView,
    reduceAnimations,
    rightArrowButtonText: rightArrowButtonTextProp,
    views,
    labelId
  } = props;
  deprecatedPropsWarning({
    leftArrowButtonText: leftArrowButtonTextProp,
    rightArrowButtonText: rightArrowButtonTextProp,
    getViewSwitchingButtonText: getViewSwitchingButtonTextProp
  });
  const localeText = useLocaleText();
  const leftArrowButtonText = leftArrowButtonTextProp != null ? leftArrowButtonTextProp : localeText.previousMonth;
  const rightArrowButtonText = rightArrowButtonTextProp != null ? rightArrowButtonTextProp : localeText.nextMonth;
  const getViewSwitchingButtonText = getViewSwitchingButtonTextProp != null ? getViewSwitchingButtonTextProp : localeText.calendarViewSwitchingButtonAriaLabel;
  const utils = useUtils();
  const classes = useUtilityClasses10(props);
  const switchViewButtonProps = componentsProps.switchViewButton || {};
  const selectNextMonth = () => onMonthChange(utils.getNextMonth(month), "left");
  const selectPreviousMonth = () => onMonthChange(utils.getPreviousMonth(month), "right");
  const isNextMonthDisabled = useNextMonthDisabled(month, {
    disableFuture,
    maxDate
  });
  const isPreviousMonthDisabled = usePreviousMonthDisabled(month, {
    disablePast,
    minDate
  });
  const handleToggleView = () => {
    if (views.length === 1 || !onViewChange || disabled) {
      return;
    }
    if (views.length === 2) {
      onViewChange(views.find((view) => view !== currentView) || views[0]);
    } else {
      const nextIndexToOpen = views.indexOf(currentView) !== 0 ? 0 : 1;
      onViewChange(views[nextIndexToOpen]);
    }
  };
  if (views.length === 1 && views[0] === "year") {
    return null;
  }
  const ownerState = props;
  return (0, import_jsx_runtime15.jsxs)(PickersCalendarHeaderRoot, {
    ownerState,
    className: classes.root,
    children: [(0, import_jsx_runtime15.jsxs)(PickersCalendarHeaderLabelContainer, {
      role: "presentation",
      onClick: handleToggleView,
      ownerState,
      "aria-live": "polite",
      className: classes.labelContainer,
      children: [(0, import_jsx_runtime14.jsx)(PickersFadeTransitionGroup, {
        reduceAnimations,
        transKey: utils.format(month, "monthAndYear"),
        children: (0, import_jsx_runtime14.jsx)(PickersCalendarHeaderLabel, {
          id: labelId,
          ownerState,
          className: classes.label,
          children: utils.format(month, "monthAndYear")
        })
      }), views.length > 1 && !disabled && (0, import_jsx_runtime14.jsx)(PickersCalendarHeaderSwitchViewButton, _extends({
        size: "small",
        as: components.SwitchViewButton,
        "aria-label": getViewSwitchingButtonText(currentView),
        className: classes.switchViewButton
      }, switchViewButtonProps, {
        children: (0, import_jsx_runtime14.jsx)(PickersCalendarHeaderSwitchViewIcon, {
          as: components.SwitchViewIcon,
          ownerState,
          className: classes.switchViewIcon
        })
      }))]
    }), (0, import_jsx_runtime14.jsx)(Fade_default, {
      in: currentView === "day",
      children: (0, import_jsx_runtime14.jsx)(PickersArrowSwitcher, {
        leftArrowButtonText,
        rightArrowButtonText,
        components,
        componentsProps,
        onLeftClick: selectPreviousMonth,
        onRightClick: selectNextMonth,
        isLeftDisabled: isPreviousMonthDisabled,
        isRightDisabled: isNextMonthDisabled
      })
    })]
  });
}

// node_modules/@mui/x-date-pickers/internals/components/PickerViewRoot/PickerViewRoot.js
var PickerViewRoot = styled_default("div")({
  overflowX: "hidden",
  width: DIALOG_WIDTH,
  maxHeight: VIEW_HEIGHT,
  display: "flex",
  flexDirection: "column",
  margin: "0 auto"
});

// node_modules/@mui/x-date-pickers/internals/utils/defaultReduceAnimations.js
var defaultReduceAnimations = typeof navigator !== "undefined" && /(android)/i.test(navigator.userAgent);

// node_modules/@mui/x-date-pickers/CalendarPicker/CalendarPicker.js
var import_jsx_runtime16 = __toESM(require_jsx_runtime());
var import_jsx_runtime17 = __toESM(require_jsx_runtime());
var _excluded7 = ["autoFocus", "onViewChange", "date", "disableFuture", "disablePast", "defaultCalendarMonth", "onChange", "onYearChange", "onMonthChange", "reduceAnimations", "shouldDisableDate", "shouldDisableMonth", "shouldDisableYear", "view", "views", "openTo", "className", "disabled", "readOnly", "minDate", "maxDate", "disableHighlightToday", "focusedView", "onFocusedViewChange", "classes"];
var useUtilityClasses11 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"],
    viewTransitionContainer: ["viewTransitionContainer"]
  };
  return composeClasses(slots, getCalendarPickerUtilityClass, classes);
};
function useCalendarPickerDefaultizedProps(props, name) {
  const utils = useUtils();
  const defaultDates = useDefaultDates();
  const themeProps = useThemeProps({
    props,
    name
  });
  return _extends({
    loading: false,
    disablePast: false,
    disableFuture: false,
    openTo: "day",
    views: ["year", "day"],
    reduceAnimations: defaultReduceAnimations,
    renderLoading: () => (0, import_jsx_runtime16.jsx)("span", {
      children: "..."
    })
  }, themeProps, {
    minDate: parseNonNullablePickerDate(utils, themeProps.minDate, defaultDates.minDate),
    maxDate: parseNonNullablePickerDate(utils, themeProps.maxDate, defaultDates.maxDate)
  });
}
var CalendarPickerRoot = styled_default(PickerViewRoot, {
  name: "MuiCalendarPicker",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})({
  display: "flex",
  flexDirection: "column"
});
var CalendarPickerViewTransitionContainer = styled_default(PickersFadeTransitionGroup, {
  name: "MuiCalendarPicker",
  slot: "ViewTransitionContainer",
  overridesResolver: (props, styles) => styles.viewTransitionContainer
})({});
var CalendarPicker = React19.forwardRef(function CalendarPicker2(inProps, ref) {
  const utils = useUtils();
  const id = useId_default();
  const props = useCalendarPickerDefaultizedProps(inProps, "MuiCalendarPicker");
  const {
    autoFocus,
    onViewChange,
    date,
    disableFuture,
    disablePast,
    defaultCalendarMonth,
    onChange,
    onYearChange,
    onMonthChange,
    reduceAnimations,
    shouldDisableDate,
    shouldDisableMonth,
    shouldDisableYear,
    view,
    views,
    openTo,
    className,
    disabled,
    readOnly,
    minDate,
    maxDate,
    disableHighlightToday,
    focusedView,
    onFocusedViewChange
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded7);
  const {
    openView,
    setOpenView,
    openNext
  } = useViews({
    view,
    views,
    openTo,
    onChange,
    onViewChange
  });
  const {
    calendarState,
    changeFocusedDay,
    changeMonth,
    handleChangeMonth,
    isDateDisabled,
    onMonthSwitchingAnimationEnd
  } = useCalendarState({
    date,
    defaultCalendarMonth,
    reduceAnimations,
    onMonthChange,
    minDate,
    maxDate,
    shouldDisableDate,
    disablePast,
    disableFuture
  });
  const handleDateMonthChange = React19.useCallback((newDate, selectionState) => {
    const startOfMonth = utils.startOfMonth(newDate);
    const endOfMonth = utils.endOfMonth(newDate);
    const closestEnabledDate = isDateDisabled(newDate) ? findClosestEnabledDate({
      utils,
      date: newDate,
      minDate: utils.isBefore(minDate, startOfMonth) ? startOfMonth : minDate,
      maxDate: utils.isAfter(maxDate, endOfMonth) ? endOfMonth : maxDate,
      disablePast,
      disableFuture,
      isDateDisabled
    }) : newDate;
    if (closestEnabledDate) {
      onChange(closestEnabledDate, selectionState);
      onMonthChange == null ? void 0 : onMonthChange(startOfMonth);
    } else {
      openNext();
      changeMonth(startOfMonth);
    }
    changeFocusedDay(closestEnabledDate, true);
  }, [changeFocusedDay, disableFuture, disablePast, isDateDisabled, maxDate, minDate, onChange, onMonthChange, changeMonth, openNext, utils]);
  const handleDateYearChange = React19.useCallback((newDate, selectionState) => {
    const startOfYear = utils.startOfYear(newDate);
    const endOfYear = utils.endOfYear(newDate);
    const closestEnabledDate = isDateDisabled(newDate) ? findClosestEnabledDate({
      utils,
      date: newDate,
      minDate: utils.isBefore(minDate, startOfYear) ? startOfYear : minDate,
      maxDate: utils.isAfter(maxDate, endOfYear) ? endOfYear : maxDate,
      disablePast,
      disableFuture,
      isDateDisabled
    }) : newDate;
    if (closestEnabledDate) {
      onChange(closestEnabledDate, selectionState);
      onYearChange == null ? void 0 : onYearChange(closestEnabledDate);
    } else {
      openNext();
      changeMonth(startOfYear);
    }
    changeFocusedDay(closestEnabledDate, true);
  }, [changeFocusedDay, disableFuture, disablePast, isDateDisabled, maxDate, minDate, onChange, onYearChange, openNext, utils, changeMonth]);
  const onSelectedDayChange = React19.useCallback((day, isFinish) => {
    if (date && day) {
      return onChange(utils.mergeDateAndTime(day, date), isFinish);
    }
    return onChange(day, isFinish);
  }, [utils, date, onChange]);
  React19.useEffect(() => {
    if (date) {
      changeMonth(date);
    }
  }, [date]);
  const ownerState = props;
  const classes = useUtilityClasses11(ownerState);
  const baseDateValidationProps = {
    disablePast,
    disableFuture,
    maxDate,
    minDate
  };
  const minDateWithDisabled = disabled && date || minDate;
  const maxDateWithDisabled = disabled && date || maxDate;
  const commonViewProps = {
    disableHighlightToday,
    readOnly,
    disabled
  };
  const gridLabelId = `${id}-grid-label`;
  const [internalFocusedView, setInternalFocusedView] = useControlled_default({
    name: "DayPicker",
    state: "focusedView",
    controlled: focusedView,
    default: autoFocus ? openView : null
  });
  const hasFocus = internalFocusedView !== null;
  const handleFocusedViewChange = useEventCallback_default((eventView) => (newHasFocus) => {
    if (onFocusedViewChange) {
      onFocusedViewChange(eventView)(newHasFocus);
      return;
    }
    if (newHasFocus) {
      setInternalFocusedView(eventView);
    } else {
      setInternalFocusedView((prevView) => prevView === eventView ? null : prevView);
    }
  });
  const prevOpenViewRef = React19.useRef(openView);
  React19.useEffect(() => {
    if (prevOpenViewRef.current === openView) {
      return;
    }
    prevOpenViewRef.current = openView;
    handleFocusedViewChange(openView)(true);
  }, [openView, handleFocusedViewChange]);
  return (0, import_jsx_runtime17.jsxs)(CalendarPickerRoot, {
    ref,
    className: clsx_m_default(classes.root, className),
    ownerState,
    children: [(0, import_jsx_runtime16.jsx)(PickersCalendarHeader, _extends({}, other, {
      views,
      openView,
      currentMonth: calendarState.currentMonth,
      onViewChange: setOpenView,
      onMonthChange: (newMonth, direction) => handleChangeMonth({
        newMonth,
        direction
      }),
      minDate: minDateWithDisabled,
      maxDate: maxDateWithDisabled,
      disabled,
      disablePast,
      disableFuture,
      reduceAnimations,
      labelId: gridLabelId
    })), (0, import_jsx_runtime16.jsx)(CalendarPickerViewTransitionContainer, {
      reduceAnimations,
      className: classes.viewTransitionContainer,
      transKey: openView,
      ownerState,
      children: (0, import_jsx_runtime17.jsxs)("div", {
        children: [openView === "year" && (0, import_jsx_runtime16.jsx)(YearPicker, _extends({}, other, baseDateValidationProps, commonViewProps, {
          autoFocus,
          date,
          onChange: handleDateYearChange,
          shouldDisableYear,
          hasFocus,
          onFocusedViewChange: handleFocusedViewChange("year")
        })), openView === "month" && (0, import_jsx_runtime16.jsx)(MonthPicker, _extends({}, baseDateValidationProps, commonViewProps, {
          autoFocus,
          hasFocus,
          className,
          date,
          onChange: handleDateMonthChange,
          shouldDisableMonth,
          onFocusedViewChange: handleFocusedViewChange("month")
        })), openView === "day" && (0, import_jsx_runtime16.jsx)(DayPicker, _extends({}, other, calendarState, baseDateValidationProps, commonViewProps, {
          autoFocus,
          onMonthSwitchingAnimationEnd,
          onFocusedDayChange: changeFocusedDay,
          reduceAnimations,
          selectedDays: [date],
          onSelectedDaysChange: onSelectedDayChange,
          shouldDisableDate,
          hasFocus,
          onFocusedViewChange: handleFocusedViewChange("day"),
          gridLabelId
        }))]
      })
    })]
  });
});
true ? CalendarPicker.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  autoFocus: import_prop_types4.default.bool,
  classes: import_prop_types4.default.object,
  className: import_prop_types4.default.string,
  /**
   * Overrideable components.
   * @default {}
   */
  components: import_prop_types4.default.object,
  /**
   * The props used for each component slot.
   * @default {}
   */
  componentsProps: import_prop_types4.default.object,
  date: import_prop_types4.default.any,
  /**
   * Formats the day of week displayed in the calendar header.
   * @param {string} day The day of week provided by the adapter's method `getWeekdays`.
   * @returns {string} The name to display.
   * @default (day) => day.charAt(0).toUpperCase()
   */
  dayOfWeekFormatter: import_prop_types4.default.func,
  /**
   * Default calendar month displayed when `value={null}`.
   */
  defaultCalendarMonth: import_prop_types4.default.any,
  /**
   * If `true`, the picker and text field are disabled.
   * @default false
   */
  disabled: import_prop_types4.default.bool,
  /**
   * If `true` future days are disabled.
   * @default false
   */
  disableFuture: import_prop_types4.default.bool,
  /**
   * If `true`, today's date is rendering without highlighting with circle.
   * @default false
   */
  disableHighlightToday: import_prop_types4.default.bool,
  /**
   * If `true` past days are disabled.
   * @default false
   */
  disablePast: import_prop_types4.default.bool,
  focusedView: import_prop_types4.default.oneOf(["day", "month", "year"]),
  /**
   * Get aria-label text for switching between views button.
   * @param {CalendarPickerView} currentView The view from which we want to get the button text.
   * @returns {string} The label of the view.
   * @deprecated Use the `localeText` prop of `LocalizationProvider` instead, see https://mui.com/x/react-date-pickers/localization/.
   */
  getViewSwitchingButtonText: import_prop_types4.default.func,
  /**
   * Left arrow icon aria-label text.
   * @deprecated
   */
  leftArrowButtonText: import_prop_types4.default.string,
  /**
   * If `true` renders `LoadingComponent` in calendar instead of calendar view.
   * Can be used to preload information and show it in calendar.
   * @default false
   */
  loading: import_prop_types4.default.bool,
  /**
   * Maximal selectable date. @DateIOType
   */
  maxDate: import_prop_types4.default.any,
  /**
   * Minimal selectable date. @DateIOType
   */
  minDate: import_prop_types4.default.any,
  /**
   * Callback fired on date change
   */
  onChange: import_prop_types4.default.func.isRequired,
  onFocusedViewChange: import_prop_types4.default.func,
  /**
   * Callback firing on month change @DateIOType.
   * @template TDate
   * @param {TDate} month The new month.
   * @returns {void|Promise} -
   */
  onMonthChange: import_prop_types4.default.func,
  /**
   * Callback fired on view change.
   * @param {CalendarPickerView} view The new view.
   */
  onViewChange: import_prop_types4.default.func,
  /**
   * Callback firing on year change @DateIOType.
   * @template TDate
   * @param {TDate} year The new year.
   */
  onYearChange: import_prop_types4.default.func,
  /**
   * Initially open view.
   * @default 'day'
   */
  openTo: import_prop_types4.default.oneOf(["day", "month", "year"]),
  /**
   * Make picker read only.
   * @default false
   */
  readOnly: import_prop_types4.default.bool,
  /**
   * Disable heavy animations.
   * @default typeof navigator !== 'undefined' && /(android)/i.test(navigator.userAgent)
   */
  reduceAnimations: import_prop_types4.default.bool,
  /**
   * Custom renderer for day. Check the [PickersDay](https://mui.com/x/api/date-pickers/pickers-day/) component.
   * @template TDate
   * @param {TDate} day The day to render.
   * @param {Array<TDate | null>} selectedDays The days currently selected.
   * @param {PickersDayProps<TDate>} pickersDayProps The props of the day to render.
   * @returns {JSX.Element} The element representing the day.
   */
  renderDay: import_prop_types4.default.func,
  /**
   * Component displaying when passed `loading` true.
   * @returns {React.ReactNode} The node to render when loading.
   * @default () => <span data-mui-test="loading-progress">...</span>
   */
  renderLoading: import_prop_types4.default.func,
  /**
   * Right arrow icon aria-label text.
   * @deprecated
   */
  rightArrowButtonText: import_prop_types4.default.string,
  /**
   * Disable specific date. @DateIOType
   * @template TDate
   * @param {TDate} day The date to test.
   * @returns {boolean} Returns `true` if the date should be disabled.
   */
  shouldDisableDate: import_prop_types4.default.func,
  /**
   * Disable specific months dynamically.
   * Works like `shouldDisableDate` but for month selection view @DateIOType.
   * @template TDate
   * @param {TDate} month The month to check.
   * @returns {boolean} If `true` the month will be disabled.
   */
  shouldDisableMonth: import_prop_types4.default.func,
  /**
   * Disable specific years dynamically.
   * Works like `shouldDisableDate` but for year selection view @DateIOType.
   * @template TDate
   * @param {TDate} year The year to test.
   * @returns {boolean} Returns `true` if the year should be disabled.
   */
  shouldDisableYear: import_prop_types4.default.func,
  /**
   * If `true`, days that have `outsideCurrentMonth={true}` are displayed.
   * @default false
   */
  showDaysOutsideCurrentMonth: import_prop_types4.default.bool,
  /**
   * Controlled open view.
   */
  view: import_prop_types4.default.oneOf(["day", "month", "year"]),
  /**
   * Views for calendar picker.
   * @default ['year', 'day']
   */
  views: import_prop_types4.default.arrayOf(import_prop_types4.default.oneOf(["day", "month", "year"]).isRequired)
} : void 0;

// node_modules/@mui/x-date-pickers/ClockPicker/clockPointerClasses.js
function getClockPointerUtilityClass(slot) {
  return generateUtilityClass("MuiClockPointer", slot);
}
var clockPointerClasses = generateUtilityClasses("MuiClockPointer", ["root", "thumb"]);

// node_modules/@mui/x-date-pickers/ClockPicker/clockClasses.js
function getClockUtilityClass(slot) {
  return generateUtilityClass("MuiClock", slot);
}
var clockClasses = generateUtilityClasses("MuiClock", ["root", "clock", "wrapper", "squareMask", "pin", "amButton", "pmButton"]);

// node_modules/@mui/x-date-pickers/ClockPicker/clockNumberClasses.js
function getClockNumberUtilityClass(slot) {
  return generateUtilityClass("MuiClockNumber", slot);
}
var clockNumberClasses = generateUtilityClasses("MuiClockNumber", ["root", "selected", "disabled"]);

// node_modules/@mui/x-date-pickers/ClockPicker/clockPickerClasses.js
function getClockPickerUtilityClass(slot) {
  return generateUtilityClass("MuiClockPicker", slot);
}
var clockPickerClasses = generateUtilityClasses("MuiClockPicker", ["root", "arrowSwitcher"]);

// node_modules/@mui/x-date-pickers/ClockPicker/ClockPicker.js
init_extends();
var React24 = __toESM(require_react());
var import_prop_types5 = __toESM(require_prop_types());
init_utils();

// node_modules/@mui/x-date-pickers/ClockPicker/Clock.js
init_extends();
var React21 = __toESM(require_react());
init_esm();

// node_modules/@mui/x-date-pickers/ClockPicker/ClockPointer.js
init_objectWithoutPropertiesLoose();
init_extends();
var React20 = __toESM(require_react());

// node_modules/@mui/x-date-pickers/ClockPicker/shared.js
var CLOCK_WIDTH = 220;
var CLOCK_HOUR_WIDTH = 36;
var clockCenter = {
  x: CLOCK_WIDTH / 2,
  y: CLOCK_WIDTH / 2
};
var baseClockPoint = {
  x: clockCenter.x,
  y: 0
};
var cx = baseClockPoint.x - clockCenter.x;
var cy = baseClockPoint.y - clockCenter.y;
var rad2deg = (rad) => rad * (180 / Math.PI);
var getAngleValue = (step, offsetX, offsetY) => {
  const x = offsetX - clockCenter.x;
  const y = offsetY - clockCenter.y;
  const atan = Math.atan2(cx, cy) - Math.atan2(x, y);
  let deg = rad2deg(atan);
  deg = Math.round(deg / step) * step;
  deg %= 360;
  const value = Math.floor(deg / step) || 0;
  const delta = x ** 2 + y ** 2;
  const distance = Math.sqrt(delta);
  return {
    value,
    distance
  };
};
var getMinutes = (offsetX, offsetY, step = 1) => {
  const angleStep = step * 6;
  let {
    value
  } = getAngleValue(angleStep, offsetX, offsetY);
  value = value * step % 60;
  return value;
};
var getHours = (offsetX, offsetY, ampm) => {
  const {
    value,
    distance
  } = getAngleValue(30, offsetX, offsetY);
  let hour = value || 12;
  if (!ampm) {
    if (distance < CLOCK_WIDTH / 2 - CLOCK_HOUR_WIDTH) {
      hour += 12;
      hour %= 24;
    }
  } else {
    hour %= 12;
  }
  return hour;
};

// node_modules/@mui/x-date-pickers/ClockPicker/ClockPointer.js
var import_jsx_runtime18 = __toESM(require_jsx_runtime());
var _excluded8 = ["className", "hasSelected", "isInner", "type", "value"];
var useUtilityClasses12 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"],
    thumb: ["thumb"]
  };
  return composeClasses(slots, getClockPointerUtilityClass, classes);
};
var ClockPointerRoot = styled_default("div", {
  name: "MuiClockPointer",
  slot: "Root",
  overridesResolver: (_, styles) => styles.root
})(({
  theme,
  ownerState
}) => _extends({
  width: 2,
  backgroundColor: theme.palette.primary.main,
  position: "absolute",
  left: "calc(50% - 1px)",
  bottom: "50%",
  transformOrigin: "center bottom 0px"
}, ownerState.shouldAnimate && {
  transition: theme.transitions.create(["transform", "height"])
}));
var ClockPointerThumb = styled_default("div", {
  name: "MuiClockPointer",
  slot: "Thumb",
  overridesResolver: (_, styles) => styles.thumb
})(({
  theme,
  ownerState
}) => _extends({
  width: 4,
  height: 4,
  backgroundColor: theme.palette.primary.contrastText,
  borderRadius: "50%",
  position: "absolute",
  top: -21,
  left: `calc(50% - ${CLOCK_HOUR_WIDTH / 2}px)`,
  border: `${(CLOCK_HOUR_WIDTH - 4) / 2}px solid ${theme.palette.primary.main}`,
  boxSizing: "content-box"
}, ownerState.hasSelected && {
  backgroundColor: theme.palette.primary.main
}));
function ClockPointer(inProps) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiClockPointer"
  });
  const {
    className,
    isInner,
    type,
    value
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded8);
  const previousType = React20.useRef(type);
  React20.useEffect(() => {
    previousType.current = type;
  }, [type]);
  const ownerState = _extends({}, props, {
    shouldAnimate: previousType.current !== type
  });
  const classes = useUtilityClasses12(ownerState);
  const getAngleStyle = () => {
    const max = type === "hours" ? 12 : 60;
    let angle = 360 / max * value;
    if (type === "hours" && value > 12) {
      angle -= 360;
    }
    return {
      height: Math.round((isInner ? 0.26 : 0.4) * CLOCK_WIDTH),
      transform: `rotateZ(${angle}deg)`
    };
  };
  return (0, import_jsx_runtime18.jsx)(ClockPointerRoot, _extends({
    style: getAngleStyle(),
    className: clsx_m_default(className, classes.root),
    ownerState
  }, other, {
    children: (0, import_jsx_runtime18.jsx)(ClockPointerThumb, {
      ownerState,
      className: classes.thumb
    })
  }));
}

// node_modules/@mui/x-date-pickers/ClockPicker/Clock.js
var import_jsx_runtime19 = __toESM(require_jsx_runtime());
var import_jsx_runtime20 = __toESM(require_jsx_runtime());
var useUtilityClasses13 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"],
    clock: ["clock"],
    wrapper: ["wrapper"],
    squareMask: ["squareMask"],
    pin: ["pin"],
    amButton: ["amButton"],
    pmButton: ["pmButton"]
  };
  return composeClasses(slots, getClockUtilityClass, classes);
};
var ClockRoot = styled_default("div", {
  name: "MuiClock",
  slot: "Root",
  overridesResolver: (_, styles) => styles.root
})(({
  theme
}) => ({
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  margin: theme.spacing(2)
}));
var ClockClock = styled_default("div", {
  name: "MuiClock",
  slot: "Clock",
  overridesResolver: (_, styles) => styles.clock
})({
  backgroundColor: "rgba(0,0,0,.07)",
  borderRadius: "50%",
  height: 220,
  width: 220,
  flexShrink: 0,
  position: "relative",
  pointerEvents: "none"
});
var ClockWrapper = styled_default("div", {
  name: "MuiClock",
  slot: "Wrapper",
  overridesResolver: (_, styles) => styles.wrapper
})({
  "&:focus": {
    outline: "none"
  }
});
var ClockSquareMask = styled_default("div", {
  name: "MuiClock",
  slot: "SquareMask",
  overridesResolver: (_, styles) => styles.squareMask
})(({
  ownerState
}) => _extends({
  width: "100%",
  height: "100%",
  position: "absolute",
  pointerEvents: "auto",
  outline: 0,
  // Disable scroll capabilities.
  touchAction: "none",
  userSelect: "none"
}, ownerState.disabled ? {} : {
  "@media (pointer: fine)": {
    cursor: "pointer",
    borderRadius: "50%"
  },
  "&:active": {
    cursor: "move"
  }
}));
var ClockPin = styled_default("div", {
  name: "MuiClock",
  slot: "Pin",
  overridesResolver: (_, styles) => styles.pin
})(({
  theme
}) => ({
  width: 6,
  height: 6,
  borderRadius: "50%",
  backgroundColor: theme.palette.primary.main,
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)"
}));
var ClockAmButton = styled_default(IconButton_default, {
  name: "MuiClock",
  slot: "AmButton",
  overridesResolver: (_, styles) => styles.amButton
})(({
  theme,
  ownerState
}) => _extends({
  zIndex: 1,
  position: "absolute",
  bottom: ownerState.ampmInClock ? 64 : 8,
  left: 8
}, ownerState.meridiemMode === "am" && {
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  "&:hover": {
    backgroundColor: theme.palette.primary.light
  }
}));
var ClockPmButton = styled_default(IconButton_default, {
  name: "MuiClock",
  slot: "PmButton",
  overridesResolver: (_, styles) => styles.pmButton
})(({
  theme,
  ownerState
}) => _extends({
  zIndex: 1,
  position: "absolute",
  bottom: ownerState.ampmInClock ? 64 : 8,
  right: 8
}, ownerState.meridiemMode === "pm" && {
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  "&:hover": {
    backgroundColor: theme.palette.primary.light
  }
}));
function Clock2(inProps) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiClock"
  });
  const {
    ampm,
    ampmInClock,
    autoFocus,
    children,
    date,
    getClockLabelText,
    handleMeridiemChange,
    isTimeDisabled,
    meridiemMode,
    minutesStep = 1,
    onChange,
    selectedId,
    type,
    value,
    disabled,
    readOnly,
    className
  } = props;
  const ownerState = props;
  const utils = useUtils();
  const wrapperVariant = React21.useContext(WrapperVariantContext);
  const isMoving = React21.useRef(false);
  const classes = useUtilityClasses13(ownerState);
  const isSelectedTimeDisabled = isTimeDisabled(value, type);
  const isPointerInner = !ampm && type === "hours" && (value < 1 || value > 12);
  const handleValueChange = (newValue, isFinish) => {
    if (disabled || readOnly) {
      return;
    }
    if (isTimeDisabled(newValue, type)) {
      return;
    }
    onChange(newValue, isFinish);
  };
  const setTime = (event, isFinish) => {
    let {
      offsetX,
      offsetY
    } = event;
    if (offsetX === void 0) {
      const rect = event.target.getBoundingClientRect();
      offsetX = event.changedTouches[0].clientX - rect.left;
      offsetY = event.changedTouches[0].clientY - rect.top;
    }
    const newSelectedValue = type === "seconds" || type === "minutes" ? getMinutes(offsetX, offsetY, minutesStep) : getHours(offsetX, offsetY, Boolean(ampm));
    handleValueChange(newSelectedValue, isFinish);
  };
  const handleTouchMove = (event) => {
    isMoving.current = true;
    setTime(event, "shallow");
  };
  const handleTouchEnd = (event) => {
    if (isMoving.current) {
      setTime(event, "finish");
      isMoving.current = false;
    }
  };
  const handleMouseMove = (event) => {
    if (event.buttons > 0) {
      setTime(event.nativeEvent, "shallow");
    }
  };
  const handleMouseUp = (event) => {
    if (isMoving.current) {
      isMoving.current = false;
    }
    setTime(event.nativeEvent, "finish");
  };
  const hasSelected = React21.useMemo(() => {
    if (type === "hours") {
      return true;
    }
    return value % 5 === 0;
  }, [type, value]);
  const keyboardControlStep = type === "minutes" ? minutesStep : 1;
  const listboxRef = React21.useRef(null);
  useEnhancedEffect_default(() => {
    if (autoFocus) {
      listboxRef.current.focus();
    }
  }, [autoFocus]);
  const handleKeyDown = (event) => {
    if (isMoving.current) {
      return;
    }
    switch (event.key) {
      case "Home":
        handleValueChange(0, "partial");
        event.preventDefault();
        break;
      case "End":
        handleValueChange(type === "minutes" ? 59 : 23, "partial");
        event.preventDefault();
        break;
      case "ArrowUp":
        handleValueChange(value + keyboardControlStep, "partial");
        event.preventDefault();
        break;
      case "ArrowDown":
        handleValueChange(value - keyboardControlStep, "partial");
        event.preventDefault();
        break;
      default:
    }
  };
  return (0, import_jsx_runtime20.jsxs)(ClockRoot, {
    className: clsx_m_default(className, classes.root),
    children: [(0, import_jsx_runtime20.jsxs)(ClockClock, {
      className: classes.clock,
      children: [(0, import_jsx_runtime19.jsx)(ClockSquareMask, {
        onTouchMove: handleTouchMove,
        onTouchEnd: handleTouchEnd,
        onMouseUp: handleMouseUp,
        onMouseMove: handleMouseMove,
        ownerState: {
          disabled
        },
        className: classes.squareMask
      }), !isSelectedTimeDisabled && (0, import_jsx_runtime20.jsxs)(React21.Fragment, {
        children: [(0, import_jsx_runtime19.jsx)(ClockPin, {
          className: classes.pin
        }), date && (0, import_jsx_runtime19.jsx)(ClockPointer, {
          type,
          value,
          isInner: isPointerInner,
          hasSelected
        })]
      }), (0, import_jsx_runtime19.jsx)(ClockWrapper, {
        "aria-activedescendant": selectedId,
        "aria-label": getClockLabelText(type, date, utils),
        ref: listboxRef,
        role: "listbox",
        onKeyDown: handleKeyDown,
        tabIndex: 0,
        className: classes.wrapper,
        children
      })]
    }), ampm && (wrapperVariant === "desktop" || ampmInClock) && (0, import_jsx_runtime20.jsxs)(React21.Fragment, {
      children: [(0, import_jsx_runtime19.jsx)(ClockAmButton, {
        onClick: readOnly ? void 0 : () => handleMeridiemChange("am"),
        disabled: disabled || meridiemMode === null,
        ownerState,
        className: classes.amButton,
        children: (0, import_jsx_runtime19.jsx)(Typography_default, {
          variant: "caption",
          children: "AM"
        })
      }), (0, import_jsx_runtime19.jsx)(ClockPmButton, {
        disabled: disabled || meridiemMode === null,
        onClick: readOnly ? void 0 : () => handleMeridiemChange("pm"),
        ownerState,
        className: classes.pmButton,
        children: (0, import_jsx_runtime19.jsx)(Typography_default, {
          variant: "caption",
          children: "PM"
        })
      })]
    })]
  });
}

// node_modules/@mui/x-date-pickers/ClockPicker/ClockNumbers.js
var React23 = __toESM(require_react());

// node_modules/@mui/x-date-pickers/ClockPicker/ClockNumber.js
init_objectWithoutPropertiesLoose();
init_extends();
var React22 = __toESM(require_react());
var import_jsx_runtime21 = __toESM(require_jsx_runtime());
var _excluded9 = ["className", "disabled", "index", "inner", "label", "selected"];
var useUtilityClasses14 = (ownerState) => {
  const {
    classes,
    selected,
    disabled
  } = ownerState;
  const slots = {
    root: ["root", selected && "selected", disabled && "disabled"]
  };
  return composeClasses(slots, getClockNumberUtilityClass, classes);
};
var ClockNumberRoot = styled_default("span", {
  name: "MuiClockNumber",
  slot: "Root",
  overridesResolver: (_, styles) => [styles.root, {
    [`&.${clockNumberClasses.disabled}`]: styles.disabled
  }, {
    [`&.${clockNumberClasses.selected}`]: styles.selected
  }]
})(({
  theme,
  ownerState
}) => _extends({
  height: CLOCK_HOUR_WIDTH,
  width: CLOCK_HOUR_WIDTH,
  position: "absolute",
  left: `calc((100% - ${CLOCK_HOUR_WIDTH}px) / 2)`,
  display: "inline-flex",
  justifyContent: "center",
  alignItems: "center",
  borderRadius: "50%",
  color: theme.palette.text.primary,
  fontFamily: theme.typography.fontFamily,
  "&:focused": {
    backgroundColor: theme.palette.background.paper
  },
  [`&.${clockNumberClasses.selected}`]: {
    color: theme.palette.primary.contrastText
  },
  [`&.${clockNumberClasses.disabled}`]: {
    pointerEvents: "none",
    color: theme.palette.text.disabled
  }
}, ownerState.inner && _extends({}, theme.typography.body2, {
  color: theme.palette.text.secondary
})));
function ClockNumber(inProps) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiClockNumber"
  });
  const {
    className,
    disabled,
    index,
    inner,
    label,
    selected
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded9);
  const ownerState = props;
  const classes = useUtilityClasses14(ownerState);
  const angle = index % 12 / 12 * Math.PI * 2 - Math.PI / 2;
  const length = (CLOCK_WIDTH - CLOCK_HOUR_WIDTH - 2) / 2 * (inner ? 0.65 : 1);
  const x = Math.round(Math.cos(angle) * length);
  const y = Math.round(Math.sin(angle) * length);
  return (0, import_jsx_runtime21.jsx)(ClockNumberRoot, _extends({
    className: clsx_m_default(className, classes.root),
    "aria-disabled": disabled ? true : void 0,
    "aria-selected": selected ? true : void 0,
    role: "option",
    style: {
      transform: `translate(${x}px, ${y + (CLOCK_WIDTH - CLOCK_HOUR_WIDTH) / 2}px`
    },
    ownerState
  }, other, {
    children: label
  }));
}

// node_modules/@mui/x-date-pickers/ClockPicker/ClockNumbers.js
var import_jsx_runtime22 = __toESM(require_jsx_runtime());
var getHourNumbers = ({
  ampm,
  date,
  getClockNumberText,
  isDisabled,
  selectedId,
  utils
}) => {
  const currentHours = date ? utils.getHours(date) : null;
  const hourNumbers = [];
  const startHour = ampm ? 1 : 0;
  const endHour = ampm ? 12 : 23;
  const isSelected = (hour) => {
    if (currentHours === null) {
      return false;
    }
    if (ampm) {
      if (hour === 12) {
        return currentHours === 12 || currentHours === 0;
      }
      return currentHours === hour || currentHours - 12 === hour;
    }
    return currentHours === hour;
  };
  for (let hour = startHour; hour <= endHour; hour += 1) {
    let label = hour.toString();
    if (hour === 0) {
      label = "00";
    }
    const inner = !ampm && (hour === 0 || hour > 12);
    label = utils.formatNumber(label);
    const selected = isSelected(hour);
    hourNumbers.push((0, import_jsx_runtime22.jsx)(ClockNumber, {
      id: selected ? selectedId : void 0,
      index: hour,
      inner,
      selected,
      disabled: isDisabled(hour),
      label,
      "aria-label": getClockNumberText(label)
    }, hour));
  }
  return hourNumbers;
};
var getMinutesNumbers = ({
  utils,
  value,
  isDisabled,
  getClockNumberText,
  selectedId
}) => {
  const f = utils.formatNumber;
  return [[5, f("05")], [10, f("10")], [15, f("15")], [20, f("20")], [25, f("25")], [30, f("30")], [35, f("35")], [40, f("40")], [45, f("45")], [50, f("50")], [55, f("55")], [0, f("00")]].map(([numberValue, label], index) => {
    const selected = numberValue === value;
    return (0, import_jsx_runtime22.jsx)(ClockNumber, {
      label,
      id: selected ? selectedId : void 0,
      index: index + 1,
      inner: false,
      disabled: isDisabled(numberValue),
      selected,
      "aria-label": getClockNumberText(label)
    }, numberValue);
  });
};

// node_modules/@mui/x-date-pickers/ClockPicker/ClockPicker.js
var import_jsx_runtime23 = __toESM(require_jsx_runtime());
var import_jsx_runtime24 = __toESM(require_jsx_runtime());
var useUtilityClasses15 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"],
    arrowSwitcher: ["arrowSwitcher"]
  };
  return composeClasses(slots, getClockPickerUtilityClass, classes);
};
var ClockPickerRoot = styled_default(PickerViewRoot, {
  name: "MuiClockPicker",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})({
  display: "flex",
  flexDirection: "column"
});
var ClockPickerArrowSwitcher = styled_default(PickersArrowSwitcher, {
  name: "MuiClockPicker",
  slot: "ArrowSwitcher",
  overridesResolver: (props, styles) => styles.arrowSwitcher
})({
  position: "absolute",
  right: 12,
  top: 15
});
var deprecatedPropsWarning2 = buildDeprecatedPropsWarning("Props for translation are deprecated. See https://mui.com/x/react-date-pickers/localization for more information.");
var ClockPicker = React24.forwardRef(function ClockPicker2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiClockPicker"
  });
  const {
    ampm = false,
    ampmInClock = false,
    autoFocus,
    components,
    componentsProps,
    date,
    disableIgnoringDatePartForTimeValidation,
    getClockLabelText: getClockLabelTextProp,
    getHoursClockNumberText: getHoursClockNumberTextProp,
    getMinutesClockNumberText: getMinutesClockNumberTextProp,
    getSecondsClockNumberText: getSecondsClockNumberTextProp,
    leftArrowButtonText: leftArrowButtonTextProp,
    maxTime,
    minTime,
    minutesStep = 1,
    rightArrowButtonText: rightArrowButtonTextProp,
    shouldDisableTime,
    showViewSwitcher,
    onChange,
    view,
    views = ["hours", "minutes"],
    openTo,
    onViewChange,
    className,
    disabled,
    readOnly
  } = props;
  deprecatedPropsWarning2({
    leftArrowButtonText: leftArrowButtonTextProp,
    rightArrowButtonText: rightArrowButtonTextProp,
    getClockLabelText: getClockLabelTextProp,
    getHoursClockNumberText: getHoursClockNumberTextProp,
    getMinutesClockNumberText: getMinutesClockNumberTextProp,
    getSecondsClockNumberText: getSecondsClockNumberTextProp
  });
  const localeText = useLocaleText();
  const leftArrowButtonText = leftArrowButtonTextProp != null ? leftArrowButtonTextProp : localeText.openPreviousView;
  const rightArrowButtonText = rightArrowButtonTextProp != null ? rightArrowButtonTextProp : localeText.openNextView;
  const getClockLabelText = getClockLabelTextProp != null ? getClockLabelTextProp : localeText.clockLabelText;
  const getHoursClockNumberText = getHoursClockNumberTextProp != null ? getHoursClockNumberTextProp : localeText.hoursClockNumberText;
  const getMinutesClockNumberText = getMinutesClockNumberTextProp != null ? getMinutesClockNumberTextProp : localeText.minutesClockNumberText;
  const getSecondsClockNumberText = getSecondsClockNumberTextProp != null ? getSecondsClockNumberTextProp : localeText.secondsClockNumberText;
  const {
    openView,
    setOpenView,
    nextView,
    previousView,
    handleChangeAndOpenNext
  } = useViews({
    view,
    views,
    openTo,
    onViewChange,
    onChange
  });
  const now = useNow();
  const utils = useUtils();
  const dateOrMidnight = React24.useMemo(() => date || utils.setSeconds(utils.setMinutes(utils.setHours(now, 0), 0), 0), [date, now, utils]);
  const {
    meridiemMode,
    handleMeridiemChange
  } = useMeridiemMode(dateOrMidnight, ampm, handleChangeAndOpenNext);
  const isTimeDisabled = React24.useCallback((rawValue, viewType) => {
    const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, utils);
    const containsValidTime = ({
      start,
      end
    }) => {
      if (minTime && isAfter(minTime, end)) {
        return false;
      }
      if (maxTime && isAfter(start, maxTime)) {
        return false;
      }
      return true;
    };
    const isValidValue = (value, step = 1) => {
      if (value % step !== 0) {
        return false;
      }
      if (shouldDisableTime) {
        return !shouldDisableTime(value, viewType);
      }
      return true;
    };
    switch (viewType) {
      case "hours": {
        const value = convertValueToMeridiem(rawValue, meridiemMode, ampm);
        const dateWithNewHours = utils.setHours(dateOrMidnight, value);
        const start = utils.setSeconds(utils.setMinutes(dateWithNewHours, 0), 0);
        const end = utils.setSeconds(utils.setMinutes(dateWithNewHours, 59), 59);
        return !containsValidTime({
          start,
          end
        }) || !isValidValue(value);
      }
      case "minutes": {
        const dateWithNewMinutes = utils.setMinutes(dateOrMidnight, rawValue);
        const start = utils.setSeconds(dateWithNewMinutes, 0);
        const end = utils.setSeconds(dateWithNewMinutes, 59);
        return !containsValidTime({
          start,
          end
        }) || !isValidValue(rawValue, minutesStep);
      }
      case "seconds": {
        const dateWithNewSeconds = utils.setSeconds(dateOrMidnight, rawValue);
        const start = dateWithNewSeconds;
        const end = dateWithNewSeconds;
        return !containsValidTime({
          start,
          end
        }) || !isValidValue(rawValue);
      }
      default:
        throw new Error("not supported");
    }
  }, [ampm, dateOrMidnight, disableIgnoringDatePartForTimeValidation, maxTime, meridiemMode, minTime, minutesStep, shouldDisableTime, utils]);
  const selectedId = useId_default();
  const viewProps = React24.useMemo(() => {
    switch (openView) {
      case "hours": {
        const handleHoursChange = (value, isFinish) => {
          const valueWithMeridiem = convertValueToMeridiem(value, meridiemMode, ampm);
          handleChangeAndOpenNext(utils.setHours(dateOrMidnight, valueWithMeridiem), isFinish);
        };
        return {
          onChange: handleHoursChange,
          value: utils.getHours(dateOrMidnight),
          children: getHourNumbers({
            date,
            utils,
            ampm,
            onChange: handleHoursChange,
            getClockNumberText: getHoursClockNumberText,
            isDisabled: (value) => disabled || isTimeDisabled(value, "hours"),
            selectedId
          })
        };
      }
      case "minutes": {
        const minutesValue = utils.getMinutes(dateOrMidnight);
        const handleMinutesChange = (value, isFinish) => {
          handleChangeAndOpenNext(utils.setMinutes(dateOrMidnight, value), isFinish);
        };
        return {
          value: minutesValue,
          onChange: handleMinutesChange,
          children: getMinutesNumbers({
            utils,
            value: minutesValue,
            onChange: handleMinutesChange,
            getClockNumberText: getMinutesClockNumberText,
            isDisabled: (value) => disabled || isTimeDisabled(value, "minutes"),
            selectedId
          })
        };
      }
      case "seconds": {
        const secondsValue = utils.getSeconds(dateOrMidnight);
        const handleSecondsChange = (value, isFinish) => {
          handleChangeAndOpenNext(utils.setSeconds(dateOrMidnight, value), isFinish);
        };
        return {
          value: secondsValue,
          onChange: handleSecondsChange,
          children: getMinutesNumbers({
            utils,
            value: secondsValue,
            onChange: handleSecondsChange,
            getClockNumberText: getSecondsClockNumberText,
            isDisabled: (value) => disabled || isTimeDisabled(value, "seconds"),
            selectedId
          })
        };
      }
      default:
        throw new Error("You must provide the type for ClockView");
    }
  }, [openView, utils, date, ampm, getHoursClockNumberText, getMinutesClockNumberText, getSecondsClockNumberText, meridiemMode, handleChangeAndOpenNext, dateOrMidnight, isTimeDisabled, selectedId, disabled]);
  const ownerState = props;
  const classes = useUtilityClasses15(ownerState);
  return (0, import_jsx_runtime24.jsxs)(ClockPickerRoot, {
    ref,
    className: clsx_m_default(classes.root, className),
    ownerState,
    children: [showViewSwitcher && (0, import_jsx_runtime23.jsx)(ClockPickerArrowSwitcher, {
      className: classes.arrowSwitcher,
      leftArrowButtonText,
      rightArrowButtonText,
      components,
      componentsProps,
      onLeftClick: () => setOpenView(previousView),
      onRightClick: () => setOpenView(nextView),
      isLeftDisabled: !previousView,
      isRightDisabled: !nextView,
      ownerState
    }), (0, import_jsx_runtime23.jsx)(Clock2, _extends({
      autoFocus,
      date,
      ampmInClock,
      type: openView,
      ampm,
      getClockLabelText,
      minutesStep,
      isTimeDisabled,
      meridiemMode,
      handleMeridiemChange,
      selectedId,
      disabled,
      readOnly
    }, viewProps))]
  });
});
true ? ClockPicker.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * 12h/24h view for hour selection clock.
   * @default false
   */
  ampm: import_prop_types5.default.bool,
  /**
   * Display ampm controls under the clock (instead of in the toolbar).
   * @default false
   */
  ampmInClock: import_prop_types5.default.bool,
  /**
   * Set to `true` if focus should be moved to clock picker.
   */
  autoFocus: import_prop_types5.default.bool,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types5.default.object,
  className: import_prop_types5.default.string,
  /**
   * Overrideable components.
   * @default {}
   */
  components: import_prop_types5.default.object,
  /**
   * The props used for each component slot.
   * @default {}
   */
  componentsProps: import_prop_types5.default.object,
  /**
   * Selected date @DateIOType.
   */
  date: import_prop_types5.default.any,
  /**
   * If `true`, the picker and text field are disabled.
   * @default false
   */
  disabled: import_prop_types5.default.bool,
  /**
   * Do not ignore date part when validating min/max time.
   * @default false
   */
  disableIgnoringDatePartForTimeValidation: import_prop_types5.default.bool,
  /**
   * Accessible text that helps user to understand which time and view is selected.
   * @template TDate
   * @param {ClockPickerView} view The current view rendered.
   * @param {TDate | null} time The current time.
   * @param {MuiPickersAdapter<TDate>} adapter The current date adapter.
   * @returns {string} The clock label.
   * @deprecated Use the `localeText` prop of `LocalizationProvider` instead, see https://mui.com/x/react-date-pickers/localization/.
   * @default <TDate extends any>(
   *   view: ClockView,
   *   time: TDate | null,
   *   adapter: MuiPickersAdapter<TDate>,
   * ) =>
   *   `Select ${view}. ${
   *     time === null ? 'No time selected' : `Selected time is ${adapter.format(time, 'fullTime')}`
   *   }`
   */
  getClockLabelText: import_prop_types5.default.func,
  /**
   * Get clock number aria-text for hours.
   * @param {string} hours The hours to format.
   * @returns {string} the formatted hours text.
   * @default (hours: string) => `${hours} hours`
   * @deprecated Use the `localeText` prop of `LocalizationProvider` instead, see https://mui.com/x/react-date-pickers/localization/.
   */
  getHoursClockNumberText: import_prop_types5.default.func,
  /**
   * Get clock number aria-text for minutes.
   * @param {string} minutes The minutes to format.
   * @returns {string} the formatted minutes text.
   * @default (minutes: string) => `${minutes} minutes`
   * @deprecated Use the `localeText` prop of `LocalizationProvider` instead, see https://mui.com/x/react-date-pickers/localization/.
   */
  getMinutesClockNumberText: import_prop_types5.default.func,
  /**
   * Get clock number aria-text for seconds.
   * @param {string} seconds The seconds to format.
   * @returns {string} the formatted seconds text.
   * @default (seconds: string) => `${seconds} seconds`
   * @deprecated Use the `localeText` prop of `LocalizationProvider` instead, see https://mui.com/x/react-date-pickers/localization/.
   */
  getSecondsClockNumberText: import_prop_types5.default.func,
  /**
   * Left arrow icon aria-label text.
   * @default 'open previous view'
   * @deprecated Use the `localeText` prop of `LocalizationProvider` instead, see https://mui.com/x/react-date-pickers/localization/.
   */
  leftArrowButtonText: import_prop_types5.default.string,
  /**
   * Max time acceptable time.
   * For input validation date part of passed object will be ignored if `disableIgnoringDatePartForTimeValidation` not specified.
   */
  maxTime: import_prop_types5.default.any,
  /**
   * Min time acceptable time.
   * For input validation date part of passed object will be ignored if `disableIgnoringDatePartForTimeValidation` not specified.
   */
  minTime: import_prop_types5.default.any,
  /**
   * Step over minutes.
   * @default 1
   */
  minutesStep: import_prop_types5.default.number,
  /**
   * On change callback @DateIOType.
   */
  onChange: import_prop_types5.default.func.isRequired,
  /**
   * Callback fired on view change.
   * @param {ClockPickerView} view The new view.
   */
  onViewChange: import_prop_types5.default.func,
  /**
   * Initially open view.
   * @default 'hours'
   */
  openTo: import_prop_types5.default.oneOf(["hours", "minutes", "seconds"]),
  /**
   * Make picker read only.
   * @default false
   */
  readOnly: import_prop_types5.default.bool,
  /**
   * Right arrow icon aria-label text.
   * @default 'open next view'
   * @deprecated Use the `localeText` prop of `LocalizationProvider` instead, see https://mui.com/x/react-date-pickers/localization/.
   */
  rightArrowButtonText: import_prop_types5.default.string,
  /**
   * Dynamically check if time is disabled or not.
   * If returns `false` appropriate time point will ot be acceptable.
   * @param {number} timeValue The value to check.
   * @param {ClockPickerView} clockType The clock type of the timeValue.
   * @returns {boolean} Returns `true` if the time should be disabled
   */
  shouldDisableTime: import_prop_types5.default.func,
  showViewSwitcher: import_prop_types5.default.bool,
  /**
   * Controlled open view.
   */
  view: import_prop_types5.default.oneOf(["hours", "minutes", "seconds"]),
  /**
   * Views for calendar picker.
   * @default ['hours', 'minutes']
   */
  views: import_prop_types5.default.arrayOf(import_prop_types5.default.oneOf(["hours", "minutes", "seconds"]).isRequired)
} : void 0;

// node_modules/@mui/x-date-pickers/PickersActionBar/PickersActionBar.js
init_extends();
init_objectWithoutPropertiesLoose();
var React25 = __toESM(require_react());
var import_jsx_runtime25 = __toESM(require_jsx_runtime());
var _excluded10 = ["onAccept", "onClear", "onCancel", "onSetToday", "actions"];
var PickersActionBar = (props) => {
  const {
    onAccept,
    onClear,
    onCancel,
    onSetToday,
    actions
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded10);
  const wrapperVariant = React25.useContext(WrapperVariantContext);
  const localeText = useLocaleText();
  const actionsArray = typeof actions === "function" ? actions(wrapperVariant) : actions;
  if (actionsArray == null || actionsArray.length === 0) {
    return null;
  }
  const buttons = actionsArray == null ? void 0 : actionsArray.map((actionType) => {
    switch (actionType) {
      case "clear":
        return (0, import_jsx_runtime25.jsx)(Button_default, {
          onClick: onClear,
          children: localeText.clearButtonLabel
        }, actionType);
      case "cancel":
        return (0, import_jsx_runtime25.jsx)(Button_default, {
          onClick: onCancel,
          children: localeText.cancelButtonLabel
        }, actionType);
      case "accept":
        return (0, import_jsx_runtime25.jsx)(Button_default, {
          onClick: onAccept,
          children: localeText.okButtonLabel
        }, actionType);
      case "today":
        return (0, import_jsx_runtime25.jsx)(Button_default, {
          onClick: onSetToday,
          children: localeText.todayButtonLabel
        }, actionType);
      default:
        return null;
    }
  });
  return (0, import_jsx_runtime25.jsx)(DialogActions_default, _extends({}, other, {
    children: buttons
  }));
};

// node_modules/@mui/x-date-pickers/internals/components/wrappers/DesktopWrapper.js
init_extends();
var React27 = __toESM(require_react());
init_utils();

// node_modules/@mui/x-date-pickers/internals/components/PickersPopper.js
init_objectWithoutPropertiesLoose();
init_extends();
var React26 = __toESM(require_react());
init_utils();

// node_modules/@mui/x-date-pickers/internals/components/pickersPopperClasses.js
function getPickersPopperUtilityClass(slot) {
  return generateUtilityClass("MuiPickersPopper", slot);
}
var pickersPopperClasses = generateUtilityClasses("MuiPickersPopper", ["root", "paper"]);

// node_modules/@mui/x-date-pickers/internals/components/PickersPopper.js
var import_jsx_runtime26 = __toESM(require_jsx_runtime());
var import_jsx_runtime27 = __toESM(require_jsx_runtime());
var _excluded11 = ["onClick", "onTouchStart"];
var useUtilityClasses16 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"],
    paper: ["paper"]
  };
  return composeClasses(slots, getPickersPopperUtilityClass, classes);
};
var PickersPopperRoot = styled_default(Popper_default, {
  name: "MuiPickersPopper",
  slot: "Root",
  overridesResolver: (_, styles) => styles.root
})(({
  theme
}) => ({
  zIndex: theme.zIndex.modal
}));
var PickersPopperPaper = styled_default(Paper_default, {
  name: "MuiPickersPopper",
  slot: "Paper",
  overridesResolver: (_, styles) => styles.paper
})(({
  ownerState
}) => _extends({
  transformOrigin: "top center",
  outline: 0
}, ownerState.placement === "top" && {
  transformOrigin: "bottom center"
}));
function clickedRootScrollbar(event, doc) {
  return doc.documentElement.clientWidth < event.clientX || doc.documentElement.clientHeight < event.clientY;
}
function useClickAwayListener(active, onClickAway) {
  const movedRef = React26.useRef(false);
  const syntheticEventRef = React26.useRef(false);
  const nodeRef = React26.useRef(null);
  const activatedRef = React26.useRef(false);
  React26.useEffect(() => {
    if (!active) {
      return void 0;
    }
    function armClickAwayListener() {
      activatedRef.current = true;
    }
    document.addEventListener("mousedown", armClickAwayListener, true);
    document.addEventListener("touchstart", armClickAwayListener, true);
    return () => {
      document.removeEventListener("mousedown", armClickAwayListener, true);
      document.removeEventListener("touchstart", armClickAwayListener, true);
      activatedRef.current = false;
    };
  }, [active]);
  const handleClickAway = useEventCallback_default((event) => {
    if (!activatedRef.current) {
      return;
    }
    const insideReactTree = syntheticEventRef.current;
    syntheticEventRef.current = false;
    const doc = ownerDocument_default(nodeRef.current);
    if (!nodeRef.current || // is a TouchEvent?
    "clientX" in event && clickedRootScrollbar(event, doc)) {
      return;
    }
    if (movedRef.current) {
      movedRef.current = false;
      return;
    }
    let insideDOM;
    if (event.composedPath) {
      insideDOM = event.composedPath().indexOf(nodeRef.current) > -1;
    } else {
      insideDOM = !doc.documentElement.contains(event.target) || nodeRef.current.contains(event.target);
    }
    if (!insideDOM && !insideReactTree) {
      onClickAway(event);
    }
  });
  const handleSynthetic = () => {
    syntheticEventRef.current = true;
  };
  React26.useEffect(() => {
    if (active) {
      const doc = ownerDocument_default(nodeRef.current);
      const handleTouchMove = () => {
        movedRef.current = true;
      };
      doc.addEventListener("touchstart", handleClickAway);
      doc.addEventListener("touchmove", handleTouchMove);
      return () => {
        doc.removeEventListener("touchstart", handleClickAway);
        doc.removeEventListener("touchmove", handleTouchMove);
      };
    }
    return void 0;
  }, [active, handleClickAway]);
  React26.useEffect(() => {
    if (active) {
      const doc = ownerDocument_default(nodeRef.current);
      doc.addEventListener("click", handleClickAway);
      return () => {
        doc.removeEventListener("click", handleClickAway);
        syntheticEventRef.current = false;
      };
    }
    return void 0;
  }, [active, handleClickAway]);
  return [nodeRef, handleSynthetic, handleSynthetic];
}
function PickersPopper(inProps) {
  var _components$ActionBar;
  const props = useThemeProps({
    props: inProps,
    name: "MuiPickersPopper"
  });
  const {
    anchorEl,
    children,
    containerRef = null,
    onBlur,
    onClose,
    onClear,
    onAccept,
    onCancel,
    onSetToday,
    open,
    PopperProps,
    role,
    TransitionComponent = Grow_default,
    TrapFocusProps,
    PaperProps = {},
    components,
    componentsProps
  } = props;
  React26.useEffect(() => {
    function handleKeyDown2(nativeEvent) {
      if (open && (nativeEvent.key === "Escape" || nativeEvent.key === "Esc")) {
        onClose();
      }
    }
    document.addEventListener("keydown", handleKeyDown2);
    return () => {
      document.removeEventListener("keydown", handleKeyDown2);
    };
  }, [onClose, open]);
  const lastFocusedElementRef = React26.useRef(null);
  React26.useEffect(() => {
    if (role === "tooltip") {
      return;
    }
    if (open) {
      lastFocusedElementRef.current = getActiveElement(document);
    } else if (lastFocusedElementRef.current && lastFocusedElementRef.current instanceof HTMLElement) {
      setTimeout(() => {
        if (lastFocusedElementRef.current instanceof HTMLElement) {
          lastFocusedElementRef.current.focus();
        }
      });
    }
  }, [open, role]);
  const [clickAwayRef, onPaperClick, onPaperTouchStart] = useClickAwayListener(open, onBlur != null ? onBlur : onClose);
  const paperRef = React26.useRef(null);
  const handleRef = useForkRef_default(paperRef, containerRef);
  const handlePaperRef = useForkRef_default(handleRef, clickAwayRef);
  const ownerState = props;
  const classes = useUtilityClasses16(ownerState);
  const {
    onClick: onPaperClickProp,
    onTouchStart: onPaperTouchStartProp
  } = PaperProps, otherPaperProps = _objectWithoutPropertiesLoose(PaperProps, _excluded11);
  const handleKeyDown = (event) => {
    if (event.key === "Escape") {
      event.stopPropagation();
      onClose();
    }
  };
  const ActionBar = (_components$ActionBar = components == null ? void 0 : components.ActionBar) != null ? _components$ActionBar : PickersActionBar;
  const PaperContent = (components == null ? void 0 : components.PaperContent) || React26.Fragment;
  return (0, import_jsx_runtime26.jsx)(PickersPopperRoot, _extends({
    transition: true,
    role,
    open,
    anchorEl,
    onKeyDown: handleKeyDown,
    className: classes.root
  }, PopperProps, {
    children: ({
      TransitionProps,
      placement
    }) => (0, import_jsx_runtime26.jsx)(FocusTrap_default, _extends({
      open,
      disableAutoFocus: true,
      disableRestoreFocus: true,
      disableEnforceFocus: role === "tooltip",
      isEnabled: () => true
    }, TrapFocusProps, {
      children: (0, import_jsx_runtime26.jsx)(TransitionComponent, _extends({}, TransitionProps, {
        children: (0, import_jsx_runtime26.jsx)(PickersPopperPaper, _extends({
          tabIndex: -1,
          elevation: 8,
          ref: handlePaperRef,
          onClick: (event) => {
            onPaperClick(event);
            if (onPaperClickProp) {
              onPaperClickProp(event);
            }
          },
          onTouchStart: (event) => {
            onPaperTouchStart(event);
            if (onPaperTouchStartProp) {
              onPaperTouchStartProp(event);
            }
          },
          ownerState: _extends({}, ownerState, {
            placement
          }),
          className: classes.paper
        }, otherPaperProps, {
          children: (0, import_jsx_runtime27.jsxs)(PaperContent, _extends({}, componentsProps == null ? void 0 : componentsProps.paperContent, {
            children: [children, (0, import_jsx_runtime26.jsx)(ActionBar, _extends({
              onAccept,
              onClear,
              onCancel,
              onSetToday,
              actions: []
            }, componentsProps == null ? void 0 : componentsProps.actionBar))]
          }))
        }))
      }))
    }))
  }));
}

// node_modules/@mui/x-date-pickers/internals/components/wrappers/DesktopWrapper.js
var import_jsx_runtime28 = __toESM(require_jsx_runtime());
var import_jsx_runtime29 = __toESM(require_jsx_runtime());
function DesktopWrapper(props) {
  const {
    children,
    DateInputProps,
    KeyboardDateInputComponent,
    onClear,
    onDismiss,
    onCancel,
    onAccept,
    onSetToday,
    open,
    PopperProps,
    PaperProps,
    TransitionComponent,
    components,
    componentsProps
  } = props;
  const ownInputRef = React27.useRef(null);
  const inputRef = useForkRef_default(DateInputProps.inputRef, ownInputRef);
  return (0, import_jsx_runtime29.jsxs)(WrapperVariantContext.Provider, {
    value: "desktop",
    children: [(0, import_jsx_runtime28.jsx)(KeyboardDateInputComponent, _extends({}, DateInputProps, {
      inputRef
    })), (0, import_jsx_runtime28.jsx)(PickersPopper, {
      role: "dialog",
      open,
      anchorEl: ownInputRef.current,
      TransitionComponent,
      PopperProps,
      PaperProps,
      onClose: onDismiss,
      onCancel,
      onClear,
      onAccept,
      onSetToday,
      components,
      componentsProps,
      children
    })]
  });
}

// node_modules/@mui/x-date-pickers/internals/components/KeyboardDateInput.js
init_extends();
init_objectWithoutPropertiesLoose();
var React29 = __toESM(require_react());

// node_modules/@mui/x-date-pickers/internals/hooks/useMaskedInput.js
init_extends();
var React28 = __toESM(require_react());

// node_modules/rifm/dist/rifm.esm.js
var import_react2 = __toESM(require_react());
var useRifm = (props) => {
  const [, refresh] = (0, import_react2.useReducer)((c) => c + 1, 0);
  const valueRef = (0, import_react2.useRef)(null);
  const {
    replace,
    append
  } = props;
  const userValue = replace ? replace(props.format(props.value)) : props.format(props.value);
  const isDeleleteButtonDownRef = (0, import_react2.useRef)(false);
  const onChange = (evt) => {
    if (true) {
      if (evt.target.type === "number") {
        console.error("Rifm does not support input type=number, use type=tel instead.");
        return;
      }
      if (evt.target.type === "date") {
        console.error("Rifm does not support input type=date.");
        return;
      }
    }
    const eventValue = evt.target.value;
    valueRef.current = [
      eventValue,
      // eventValue
      evt.target,
      // input
      eventValue.length > userValue.length,
      // isSizeIncreaseOperation
      isDeleleteButtonDownRef.current,
      // isDeleleteButtonDown
      userValue === props.format(eventValue)
      // isNoOperation
    ];
    if (true) {
      const formattedEventValue = props.format(eventValue);
      if (eventValue !== formattedEventValue && eventValue.toLowerCase() === formattedEventValue.toLowerCase()) {
        console.warn("Case enforcement does not work with format. Please use replace={value => value.toLowerCase()} instead");
      }
    }
    refresh();
  };
  if (typeof window !== "undefined") {
    (0, import_react2.useLayoutEffect)(() => {
      if (valueRef.current == null) return;
      let [
        eventValue,
        input,
        isSizeIncreaseOperation,
        isDeleleteButtonDown,
        // No operation means that value itself hasn't been changed, BTW cursor, selection etc can be changed
        isNoOperation
      ] = valueRef.current;
      valueRef.current = null;
      const deleteWasNoOp = isDeleleteButtonDown && isNoOperation;
      const valueAfterSelectionStart = eventValue.slice(input.selectionStart);
      const acceptedCharIndexAfterDelete = valueAfterSelectionStart.search(props.accept || /\d/g);
      const charsToSkipAfterDelete = acceptedCharIndexAfterDelete !== -1 ? acceptedCharIndexAfterDelete : 0;
      const clean = (str) => (str.match(props.accept || /\d/g) || []).join("");
      const valueBeforeSelectionStart = clean(eventValue.substr(0, input.selectionStart));
      const getCursorPosition = (val) => {
        let start = 0;
        let cleanPos = 0;
        for (let i = 0; i !== valueBeforeSelectionStart.length; ++i) {
          let newPos = val.indexOf(valueBeforeSelectionStart[i], start) + 1;
          let newCleanPos = clean(val).indexOf(valueBeforeSelectionStart[i], cleanPos) + 1;
          if (newCleanPos - cleanPos > 1) {
            newPos = start;
            newCleanPos = cleanPos;
          }
          cleanPos = Math.max(newCleanPos, cleanPos);
          start = Math.max(start, newPos);
        }
        return start;
      };
      if (props.mask === true && isSizeIncreaseOperation && !isNoOperation) {
        let start = getCursorPosition(eventValue);
        const c = clean(eventValue.substr(start))[0];
        start = eventValue.indexOf(c, start);
        eventValue = `${eventValue.substr(0, start)}${eventValue.substr(start + 1)}`;
      }
      let formattedValue = props.format(eventValue);
      if (append != null && // cursor at the end
      input.selectionStart === eventValue.length && !isNoOperation) {
        if (isSizeIncreaseOperation) {
          formattedValue = append(formattedValue);
        } else {
          if (clean(formattedValue.slice(-1)) === "") {
            formattedValue = formattedValue.slice(0, -1);
          }
        }
      }
      const replacedValue = replace ? replace(formattedValue) : formattedValue;
      if (userValue === replacedValue) {
        refresh();
      } else {
        props.onChange(replacedValue);
      }
      return () => {
        let start = getCursorPosition(formattedValue);
        if (props.mask != null && (isSizeIncreaseOperation || isDeleleteButtonDown && !deleteWasNoOp)) {
          while (formattedValue[start] && clean(formattedValue[start]) === "") {
            start += 1;
          }
        }
        input.selectionStart = input.selectionEnd = start + (deleteWasNoOp ? 1 + charsToSkipAfterDelete : 0);
      };
    });
  }
  (0, import_react2.useEffect)(() => {
    const handleKeyDown = (evt) => {
      if (evt.code === "Delete") {
        isDeleleteButtonDownRef.current = true;
      }
    };
    const handleKeyUp = (evt) => {
      if (evt.code === "Delete") {
        isDeleleteButtonDownRef.current = false;
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("keyup", handleKeyUp);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("keyup", handleKeyUp);
    };
  }, []);
  return {
    value: valueRef.current != null ? valueRef.current[0] : userValue,
    onChange
  };
};

// node_modules/@mui/x-date-pickers/internals/utils/text-field-helper.js
var getDisplayDate = (utils, rawValue, inputFormat) => {
  const date = utils.date(rawValue);
  const isEmpty = rawValue === null;
  if (isEmpty) {
    return "";
  }
  return utils.isValid(date) ? utils.formatByString(
    // TODO: should `isValid` narrow `TDate | null` to `NonNullable<TDate>`?
    // Either we allow `TDate | null` to be valid and guard against calling `formatByString` with `null`.
    // Or we ensure `formatByString` is callable with `null`.
    date,
    inputFormat
  ) : "";
};
var MASK_USER_INPUT_SYMBOL = "_";
var staticDateWith2DigitTokens = "2019-11-21T22:30:00.000";
var staticDateWith1DigitTokens = "2019-01-01T09:00:00.000";
function getMaskFromCurrentFormat(mask, format, acceptRegex, utils) {
  if (mask) {
    return mask;
  }
  const formattedDateWith1Digit = utils.formatByString(utils.date(staticDateWith1DigitTokens), format);
  const inferredFormatPatternWith1Digits = formattedDateWith1Digit.replace(acceptRegex, MASK_USER_INPUT_SYMBOL);
  const inferredFormatPatternWith2Digits = utils.formatByString(utils.date(staticDateWith2DigitTokens), format).replace(acceptRegex, "_");
  if (inferredFormatPatternWith1Digits === inferredFormatPatternWith2Digits) {
    return inferredFormatPatternWith1Digits;
  }
  if (true) {
    console.warn([`Mask does not support numbers with variable length such as 'M'.`, `Either use numbers with fix length or disable mask feature with 'disableMaskedInput' prop`, `Falling down to uncontrolled no-mask input.`].join("\n"));
  }
  return "";
}
function checkMaskIsValidForCurrentFormat(mask, format, acceptRegex, utils) {
  if (!mask) {
    return false;
  }
  const formattedDateWith1Digit = utils.formatByString(utils.date(staticDateWith1DigitTokens), format);
  const inferredFormatPatternWith1Digits = formattedDateWith1Digit.replace(acceptRegex, MASK_USER_INPUT_SYMBOL);
  const inferredFormatPatternWith2Digits = utils.formatByString(utils.date(staticDateWith2DigitTokens), format).replace(acceptRegex, "_");
  const isMaskValid = inferredFormatPatternWith2Digits === inferredFormatPatternWith1Digits && mask === inferredFormatPatternWith2Digits;
  if (!isMaskValid && utils.lib !== "luxon" && true) {
    if (format.includes("MMM")) {
      console.warn([`Mask does not support literals such as 'MMM'.`, `Either use numbers with fix length or disable mask feature with 'disableMaskedInput' prop`, `Falling down to uncontrolled no-mask input.`].join("\n"));
    } else if (inferredFormatPatternWith2Digits && inferredFormatPatternWith2Digits !== inferredFormatPatternWith1Digits) {
      console.warn([`Mask does not support numbers with variable length such as 'M'.`, `Either use numbers with fix length or disable mask feature with 'disableMaskedInput' prop`, `Falling down to uncontrolled no-mask input.`].join("\n"));
    } else if (mask) {
      console.warn([`The mask "${mask}" you passed is not valid for the format used ${format}.`, `Falling down to uncontrolled no-mask input.`].join("\n"));
    }
  }
  return isMaskValid;
}
var maskedDateFormatter = (mask, acceptRegexp) => (value) => {
  let outputCharIndex = 0;
  return value.split("").map((char, inputCharIndex) => {
    acceptRegexp.lastIndex = 0;
    if (outputCharIndex > mask.length - 1) {
      return "";
    }
    const maskChar = mask[outputCharIndex];
    const nextMaskChar = mask[outputCharIndex + 1];
    const acceptedChar = acceptRegexp.test(char) ? char : "";
    const formattedChar = maskChar === MASK_USER_INPUT_SYMBOL ? acceptedChar : maskChar + acceptedChar;
    outputCharIndex += formattedChar.length;
    const isLastCharacter = inputCharIndex === value.length - 1;
    if (isLastCharacter && nextMaskChar && nextMaskChar !== MASK_USER_INPUT_SYMBOL) {
      return formattedChar ? formattedChar + nextMaskChar : "";
    }
    return formattedChar;
  }).join("");
};

// node_modules/@mui/x-date-pickers/internals/hooks/useMaskedInput.js
var useMaskedInput = ({
  acceptRegex = /[\d]/gi,
  disabled,
  disableMaskedInput,
  ignoreInvalidInputs,
  inputFormat,
  inputProps,
  label,
  mask,
  onChange,
  rawValue,
  readOnly,
  rifmFormatter,
  TextFieldProps,
  validationError
}) => {
  const utils = useUtils();
  const formatHelperText = utils.getFormatHelperText(inputFormat);
  const {
    shouldUseMaskedInput,
    maskToUse
  } = React28.useMemo(() => {
    if (disableMaskedInput) {
      return {
        shouldUseMaskedInput: false,
        maskToUse: ""
      };
    }
    const computedMaskToUse = getMaskFromCurrentFormat(mask, inputFormat, acceptRegex, utils);
    return {
      shouldUseMaskedInput: checkMaskIsValidForCurrentFormat(computedMaskToUse, inputFormat, acceptRegex, utils),
      maskToUse: computedMaskToUse
    };
  }, [acceptRegex, disableMaskedInput, inputFormat, mask, utils]);
  const formatter = React28.useMemo(() => shouldUseMaskedInput && maskToUse ? maskedDateFormatter(maskToUse, acceptRegex) : (st) => st, [acceptRegex, maskToUse, shouldUseMaskedInput]);
  const parsedValue = rawValue === null ? null : utils.date(rawValue);
  const [innerInputValue, setInnerInputValue] = React28.useState(parsedValue);
  const [innerDisplayedInputValue, setInnerDisplayedInputValue] = React28.useState(getDisplayDate(utils, rawValue, inputFormat));
  const prevRawValue = React28.useRef();
  const prevLocale = React28.useRef(utils.locale);
  const prevInputFormat = React28.useRef(inputFormat);
  React28.useEffect(() => {
    const rawValueHasChanged = rawValue !== prevRawValue.current;
    const localeHasChanged = utils.locale !== prevLocale.current;
    const inputFormatHasChanged = inputFormat !== prevInputFormat.current;
    prevRawValue.current = rawValue;
    prevLocale.current = utils.locale;
    prevInputFormat.current = inputFormat;
    if (!rawValueHasChanged && !localeHasChanged && !inputFormatHasChanged) {
      return;
    }
    const newParsedValue = rawValue === null ? null : utils.date(rawValue);
    const isAcceptedValue = rawValue === null || utils.isValid(newParsedValue);
    let innerEqualsParsed = innerInputValue === null && newParsedValue === null;
    if (innerInputValue !== null && newParsedValue !== null) {
      const areEqual = utils.isEqual(innerInputValue, newParsedValue);
      if (areEqual) {
        innerEqualsParsed = true;
      } else {
        const diff = Math.abs(utils.getDiff(innerInputValue, newParsedValue));
        innerEqualsParsed = diff === 0 ? areEqual : diff < 1e3;
      }
    }
    if (!localeHasChanged && !inputFormatHasChanged && (!isAcceptedValue || innerEqualsParsed)) {
      return;
    }
    const newDisplayDate = getDisplayDate(utils, rawValue, inputFormat);
    setInnerInputValue(newParsedValue);
    setInnerDisplayedInputValue(newDisplayDate);
  }, [utils, rawValue, inputFormat, innerInputValue]);
  const handleChange = (text) => {
    const finalString = text === "" || text === mask ? "" : text;
    setInnerDisplayedInputValue(finalString);
    const date = finalString === null ? null : utils.parse(finalString, inputFormat);
    if (ignoreInvalidInputs && !utils.isValid(date)) {
      return;
    }
    setInnerInputValue(date);
    onChange(date, finalString || void 0);
  };
  const rifmProps = useRifm({
    value: innerDisplayedInputValue,
    onChange: handleChange,
    format: rifmFormatter || formatter
  });
  const inputStateArgs = shouldUseMaskedInput ? rifmProps : {
    value: innerDisplayedInputValue,
    onChange: (event) => {
      handleChange(event.currentTarget.value);
    }
  };
  return _extends({
    label,
    disabled,
    error: validationError,
    inputProps: _extends({}, inputStateArgs, {
      disabled,
      placeholder: formatHelperText,
      readOnly,
      type: shouldUseMaskedInput ? "tel" : "text"
    }, inputProps)
  }, TextFieldProps);
};

// node_modules/@mui/x-date-pickers/internals/components/KeyboardDateInput.js
var import_jsx_runtime30 = __toESM(require_jsx_runtime());
var _excluded12 = ["className", "components", "disableOpenPicker", "getOpenDialogAriaText", "InputAdornmentProps", "InputProps", "inputRef", "openPicker", "OpenPickerButtonProps", "renderInput"];
var KeyboardDateInput = React29.forwardRef(function KeyboardDateInput2(props, ref) {
  const {
    className,
    components = {},
    disableOpenPicker,
    getOpenDialogAriaText: getOpenDialogAriaTextProp,
    InputAdornmentProps,
    InputProps,
    inputRef,
    openPicker,
    OpenPickerButtonProps,
    renderInput
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded12);
  const localeText = useLocaleText();
  const getOpenDialogAriaText = getOpenDialogAriaTextProp != null ? getOpenDialogAriaTextProp : localeText.openDatePickerDialogue;
  const utils = useUtils();
  const textFieldProps = useMaskedInput(other);
  const adornmentPosition = (InputAdornmentProps == null ? void 0 : InputAdornmentProps.position) || "end";
  const OpenPickerIcon = components.OpenPickerIcon || Calendar;
  return renderInput(_extends({
    ref,
    inputRef,
    className
  }, textFieldProps, {
    InputProps: _extends({}, InputProps, {
      [`${adornmentPosition}Adornment`]: disableOpenPicker ? void 0 : (0, import_jsx_runtime30.jsx)(InputAdornment_default, _extends({
        position: adornmentPosition
      }, InputAdornmentProps, {
        children: (0, import_jsx_runtime30.jsx)(IconButton_default, _extends({
          edge: adornmentPosition,
          disabled: other.disabled || other.readOnly,
          "aria-label": getOpenDialogAriaText(other.rawValue, utils)
        }, OpenPickerButtonProps, {
          onClick: openPicker,
          children: (0, import_jsx_runtime30.jsx)(OpenPickerIcon, {})
        }))
      }))
    })
  }));
});

// node_modules/@mui/x-date-pickers/internals/components/CalendarOrClockPicker/CalendarOrClockPicker.js
init_objectWithoutPropertiesLoose();
init_extends();
var React32 = __toESM(require_react());

// node_modules/@mui/x-date-pickers/internals/hooks/useIsLandscape.js
var React30 = __toESM(require_react());
init_esm();
function getOrientation() {
  if (typeof window === "undefined") {
    return "portrait";
  }
  if (window.screen && window.screen.orientation && window.screen.orientation.angle) {
    return Math.abs(window.screen.orientation.angle) === 90 ? "landscape" : "portrait";
  }
  if (window.orientation) {
    return Math.abs(Number(window.orientation)) === 90 ? "landscape" : "portrait";
  }
  return "portrait";
}
var useIsLandscape = (views, customOrientation) => {
  const [orientation, setOrientation] = React30.useState(getOrientation);
  useEnhancedEffect_default(() => {
    const eventHandler = () => {
      setOrientation(getOrientation());
    };
    window.addEventListener("orientationchange", eventHandler);
    return () => {
      window.removeEventListener("orientationchange", eventHandler);
    };
  }, []);
  if (arrayIncludes(views, ["hours", "minutes", "seconds"])) {
    return false;
  }
  const orientationToUse = customOrientation || orientation;
  return orientationToUse === "landscape";
};

// node_modules/@mui/x-date-pickers/internals/components/CalendarOrClockPicker/useFocusManagement.js
var React31 = __toESM(require_react());
var useFocusManagement = ({
  autoFocus,
  openView
}) => {
  const [focusedView, setFocusedView] = React31.useState(autoFocus ? openView : null);
  const setFocusedViewCallback = React31.useCallback((view) => (newHasFocus) => {
    if (newHasFocus) {
      setFocusedView(view);
    } else {
      setFocusedView((prevFocusedView) => view === prevFocusedView ? null : prevFocusedView);
    }
  }, []);
  return {
    focusedView,
    setFocusedView: setFocusedViewCallback
  };
};

// node_modules/@mui/x-date-pickers/internals/components/CalendarOrClockPicker/calendarOrClockPickerClasses.js
function getCalendarOrClockPickerUtilityClass(slot) {
  return generateUtilityClass("MuiCalendarOrClockPicker", slot);
}
var calendarOrClockPickerClasses = generateUtilityClasses("MuiCalendarOrClockPicker", ["root", "mobileKeyboardInputView"]);

// node_modules/@mui/x-date-pickers/internals/components/CalendarOrClockPicker/CalendarOrClockPicker.js
var import_jsx_runtime31 = __toESM(require_jsx_runtime());
var import_jsx_runtime32 = __toESM(require_jsx_runtime());
var _excluded13 = ["autoFocus", "className", "parsedValue", "DateInputProps", "isMobileKeyboardViewOpen", "onDateChange", "onViewChange", "openTo", "orientation", "showToolbar", "toggleMobileKeyboardView", "ToolbarComponent", "toolbarFormat", "toolbarPlaceholder", "toolbarTitle", "views", "dateRangeIcon", "timeIcon", "hideTabs", "classes"];
var useUtilityClasses17 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"],
    mobileKeyboardInputView: ["mobileKeyboardInputView"]
  };
  return composeClasses(slots, getCalendarOrClockPickerUtilityClass, classes);
};
var MobileKeyboardInputView = styled_default("div", {
  name: "MuiCalendarOrClockPicker",
  slot: "MobileKeyboardInputView",
  overridesResolver: (_, styles) => styles.mobileKeyboardInputView
})({
  padding: "16px 24px"
});
var PickerRoot = styled_default("div", {
  name: "MuiCalendarOrClockPicker",
  slot: "Root",
  overridesResolver: (_, styles) => styles.root
})(({
  ownerState
}) => _extends({
  display: "flex",
  flexDirection: "column"
}, ownerState.isLandscape && {
  flexDirection: "row"
}));
var MobileKeyboardTextFieldProps = {
  fullWidth: true
};
var isDatePickerView = (view) => view === "year" || view === "month" || view === "day";
var isTimePickerView = (view) => view === "hours" || view === "minutes" || view === "seconds";
var warnedOnceNotValidOpenTo = false;
function CalendarOrClockPicker(inProps) {
  var _other$components, _other$componentsProp;
  const props = useThemeProps({
    props: inProps,
    name: "MuiCalendarOrClockPicker"
  });
  const {
    autoFocus,
    parsedValue,
    DateInputProps,
    isMobileKeyboardViewOpen,
    onDateChange,
    onViewChange,
    openTo,
    orientation,
    showToolbar,
    toggleMobileKeyboardView,
    ToolbarComponent = () => null,
    toolbarFormat,
    toolbarPlaceholder,
    toolbarTitle,
    views,
    dateRangeIcon,
    timeIcon,
    hideTabs
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded13);
  const TabsComponent = (_other$components = other.components) == null ? void 0 : _other$components.Tabs;
  const isLandscape = useIsLandscape(views, orientation);
  const wrapperVariant = React32.useContext(WrapperVariantContext);
  const classes = useUtilityClasses17(props);
  const toShowToolbar = showToolbar != null ? showToolbar : wrapperVariant !== "desktop";
  const showTabs = !hideTabs && typeof window !== "undefined" && window.innerHeight > 667;
  const handleDateChange = React32.useCallback((newDate, selectionState) => {
    onDateChange(newDate, wrapperVariant, selectionState);
  }, [onDateChange, wrapperVariant]);
  const handleViewChange = React32.useCallback((newView) => {
    if (isMobileKeyboardViewOpen) {
      toggleMobileKeyboardView();
    }
    if (onViewChange) {
      onViewChange(newView);
    }
  }, [isMobileKeyboardViewOpen, onViewChange, toggleMobileKeyboardView]);
  if (true) {
    if (!warnedOnceNotValidOpenTo && !views.includes(openTo)) {
      console.warn(`MUI: \`openTo="${openTo}"\` is not a valid prop.`, `It must be an element of \`views=["${views.join('", "')}"]\`.`);
      warnedOnceNotValidOpenTo = true;
    }
  }
  const {
    openView,
    setOpenView,
    handleChangeAndOpenNext
  } = useViews({
    view: void 0,
    views,
    openTo,
    onChange: handleDateChange,
    onViewChange: handleViewChange
  });
  const {
    focusedView,
    setFocusedView
  } = useFocusManagement({
    autoFocus,
    openView
  });
  return (0, import_jsx_runtime32.jsxs)(PickerRoot, {
    ownerState: {
      isLandscape
    },
    className: classes.root,
    children: [toShowToolbar && (0, import_jsx_runtime31.jsx)(ToolbarComponent, _extends({}, other, {
      views,
      isLandscape,
      parsedValue,
      onChange: handleDateChange,
      setOpenView,
      openView,
      toolbarTitle,
      toolbarFormat,
      toolbarPlaceholder,
      isMobileKeyboardViewOpen,
      toggleMobileKeyboardView
    })), showTabs && !!TabsComponent && (0, import_jsx_runtime31.jsx)(TabsComponent, _extends({
      dateRangeIcon,
      timeIcon,
      view: openView,
      onChange: setOpenView
    }, (_other$componentsProp = other.componentsProps) == null ? void 0 : _other$componentsProp.tabs)), (0, import_jsx_runtime31.jsx)(PickerViewRoot, {
      children: isMobileKeyboardViewOpen ? (0, import_jsx_runtime31.jsx)(MobileKeyboardInputView, {
        className: classes.mobileKeyboardInputView,
        children: (0, import_jsx_runtime31.jsx)(KeyboardDateInput, _extends({}, DateInputProps, {
          ignoreInvalidInputs: true,
          disableOpenPicker: true,
          TextFieldProps: MobileKeyboardTextFieldProps
        }))
      }) : (0, import_jsx_runtime32.jsxs)(React32.Fragment, {
        children: [isDatePickerView(openView) && (0, import_jsx_runtime31.jsx)(CalendarPicker, _extends({
          autoFocus,
          date: parsedValue,
          onViewChange: setOpenView,
          onChange: handleChangeAndOpenNext,
          view: openView,
          views: views.filter(isDatePickerView),
          focusedView,
          onFocusedViewChange: setFocusedView
        }, other)), isTimePickerView(openView) && (0, import_jsx_runtime31.jsx)(ClockPicker, _extends({}, other, {
          autoFocus,
          date: parsedValue,
          view: openView,
          views: views.filter(isTimePickerView),
          onChange: handleChangeAndOpenNext,
          onViewChange: setOpenView,
          showViewSwitcher: wrapperVariant === "desktop"
        }))]
      })
    })]
  });
}

// node_modules/@mui/x-date-pickers/internals/hooks/usePickerState.js
init_extends();
var React34 = __toESM(require_react());

// node_modules/@mui/x-date-pickers/internals/hooks/useOpenState.js
var React33 = __toESM(require_react());
var useOpenState = ({
  open,
  onOpen,
  onClose
}) => {
  const isControllingOpenProp = React33.useRef(typeof open === "boolean").current;
  const [openState, setIsOpenState] = React33.useState(false);
  React33.useEffect(() => {
    if (isControllingOpenProp) {
      if (typeof open !== "boolean") {
        throw new Error("You must not mix controlling and uncontrolled mode for `open` prop");
      }
      setIsOpenState(open);
    }
  }, [isControllingOpenProp, open]);
  const setIsOpen = React33.useCallback((newIsOpen) => {
    if (!isControllingOpenProp) {
      setIsOpenState(newIsOpen);
    }
    if (newIsOpen && onOpen) {
      onOpen();
    }
    if (!newIsOpen && onClose) {
      onClose();
    }
  }, [isControllingOpenProp, onOpen, onClose]);
  return {
    isOpen: openState,
    setIsOpen
  };
};

// node_modules/@mui/x-date-pickers/internals/hooks/usePickerState.js
var usePickerState = (props, valueManager) => {
  const {
    onAccept,
    onChange,
    value,
    closeOnSelect
  } = props;
  const utils = useUtils();
  const {
    isOpen,
    setIsOpen
  } = useOpenState(props);
  const parsedDateValue = React34.useMemo(() => valueManager.parseInput(utils, value), [valueManager, utils, value]);
  const [lastValidDateValue, setLastValidDateValue] = React34.useState(parsedDateValue);
  const [dateState, setDateState] = React34.useState(() => ({
    committed: parsedDateValue,
    draft: parsedDateValue,
    resetFallback: parsedDateValue
  }));
  const setDate = React34.useCallback((params) => {
    setDateState((prev) => {
      switch (params.action) {
        case "setAll":
        case "acceptAndClose": {
          return {
            draft: params.value,
            committed: params.value,
            resetFallback: params.value
          };
        }
        case "setCommitted": {
          return _extends({}, prev, {
            draft: params.value,
            committed: params.value
          });
        }
        case "setDraft": {
          return _extends({}, prev, {
            draft: params.value
          });
        }
        default: {
          return prev;
        }
      }
    });
    if (params.forceOnChangeCall || !params.skipOnChangeCall && !valueManager.areValuesEqual(utils, dateState.committed, params.value)) {
      onChange(params.value);
    }
    if (params.action === "acceptAndClose") {
      setIsOpen(false);
      if (onAccept && !valueManager.areValuesEqual(utils, dateState.resetFallback, params.value)) {
        onAccept(params.value);
      }
    }
  }, [onAccept, onChange, setIsOpen, dateState, utils, valueManager]);
  React34.useEffect(() => {
    if (utils.isValid(parsedDateValue)) {
      setLastValidDateValue(parsedDateValue);
    }
  }, [utils, parsedDateValue]);
  React34.useEffect(() => {
    if (isOpen) {
      setDate({
        action: "setAll",
        value: parsedDateValue,
        skipOnChangeCall: true
      });
    }
  }, [isOpen]);
  if (!valueManager.areValuesEqual(utils, dateState.committed, parsedDateValue)) {
    setDate({
      action: "setCommitted",
      value: parsedDateValue,
      skipOnChangeCall: true
    });
  }
  const wrapperProps = React34.useMemo(() => ({
    open: isOpen,
    onClear: () => {
      setDate({
        value: valueManager.emptyValue,
        action: "acceptAndClose",
        // force `onChange` in cases like input (value) === `Invalid date`
        forceOnChangeCall: !valueManager.areValuesEqual(utils, value, valueManager.emptyValue)
      });
    },
    onAccept: () => {
      setDate({
        value: dateState.draft,
        action: "acceptAndClose",
        // force `onChange` in cases like input (value) === `Invalid date`
        forceOnChangeCall: !valueManager.areValuesEqual(utils, value, parsedDateValue)
      });
    },
    onDismiss: () => {
      setDate({
        value: dateState.committed,
        action: "acceptAndClose"
      });
    },
    onCancel: () => {
      setDate({
        value: dateState.resetFallback,
        action: "acceptAndClose"
      });
    },
    onSetToday: () => {
      setDate({
        value: valueManager.getTodayValue(utils),
        action: "acceptAndClose"
      });
    }
  }), [setDate, isOpen, utils, dateState, valueManager, value, parsedDateValue]);
  const [isMobileKeyboardViewOpen, setMobileKeyboardViewOpen] = React34.useState(false);
  const pickerProps = React34.useMemo(() => ({
    parsedValue: dateState.draft,
    isMobileKeyboardViewOpen,
    toggleMobileKeyboardView: () => setMobileKeyboardViewOpen(!isMobileKeyboardViewOpen),
    onDateChange: (newDate, wrapperVariant, selectionState = "partial") => {
      switch (selectionState) {
        case "shallow": {
          return setDate({
            action: "setDraft",
            value: newDate,
            skipOnChangeCall: true
          });
        }
        case "partial": {
          return setDate({
            action: "setDraft",
            value: newDate
          });
        }
        case "finish": {
          if (closeOnSelect != null ? closeOnSelect : wrapperVariant === "desktop") {
            return setDate({
              value: newDate,
              action: "acceptAndClose"
            });
          }
          return setDate({
            value: newDate,
            action: "setCommitted"
          });
        }
        default: {
          throw new Error("MUI: Invalid selectionState passed to `onDateChange`");
        }
      }
    }
  }), [setDate, isMobileKeyboardViewOpen, dateState.draft, closeOnSelect]);
  const handleInputChange = React34.useCallback((newParsedValue, keyboardInputValue) => {
    const cleanParsedValue = valueManager.valueReducer ? valueManager.valueReducer(utils, lastValidDateValue, newParsedValue) : newParsedValue;
    onChange(cleanParsedValue, keyboardInputValue);
  }, [onChange, valueManager, lastValidDateValue, utils]);
  const inputProps = React34.useMemo(() => ({
    onChange: handleInputChange,
    open: isOpen,
    rawValue: value,
    openPicker: () => setIsOpen(true)
  }), [handleInputChange, isOpen, value, setIsOpen]);
  const pickerState = {
    pickerProps,
    inputProps,
    wrapperProps
  };
  React34.useDebugValue(pickerState, () => ({
    MuiPickerState: {
      dateState,
      other: pickerState
    }
  }));
  return pickerState;
};

// node_modules/@mui/x-date-pickers/internals/components/wrappers/MobileWrapper.js
init_extends();
init_objectWithoutPropertiesLoose();
var React36 = __toESM(require_react());

// node_modules/@mui/x-date-pickers/internals/components/PickersModalDialog.js
init_extends();
var React35 = __toESM(require_react());
var import_jsx_runtime33 = __toESM(require_jsx_runtime());
var import_jsx_runtime34 = __toESM(require_jsx_runtime());
var PickersModalDialogRoot = styled_default(Dialog_default)({
  [`& .${dialogClasses_default.container}`]: {
    outline: 0
  },
  [`& .${dialogClasses_default.paper}`]: {
    outline: 0,
    minWidth: DIALOG_WIDTH
  }
});
var PickersModalDialogContent = styled_default(DialogContent_default)({
  "&:first-of-type": {
    padding: 0
  }
});
var PickersModalDialog = (props) => {
  var _components$ActionBar;
  const {
    children,
    DialogProps = {},
    onAccept,
    onClear,
    onDismiss,
    onCancel,
    onSetToday,
    open,
    components,
    componentsProps
  } = props;
  const ActionBar = (_components$ActionBar = components == null ? void 0 : components.ActionBar) != null ? _components$ActionBar : PickersActionBar;
  return (0, import_jsx_runtime34.jsxs)(PickersModalDialogRoot, _extends({
    open,
    onClose: onDismiss
  }, DialogProps, {
    children: [(0, import_jsx_runtime33.jsx)(PickersModalDialogContent, {
      children
    }), (0, import_jsx_runtime33.jsx)(ActionBar, _extends({
      onAccept,
      onClear,
      onCancel,
      onSetToday,
      actions: ["cancel", "accept"]
    }, componentsProps == null ? void 0 : componentsProps.actionBar))]
  }));
};

// node_modules/@mui/x-date-pickers/internals/components/wrappers/MobileWrapper.js
var import_jsx_runtime35 = __toESM(require_jsx_runtime());
var import_jsx_runtime36 = __toESM(require_jsx_runtime());
var _excluded14 = ["children", "DateInputProps", "DialogProps", "onAccept", "onClear", "onDismiss", "onCancel", "onSetToday", "open", "PureDateInputComponent", "components", "componentsProps"];
function MobileWrapper(props) {
  const {
    children,
    DateInputProps,
    DialogProps,
    onAccept,
    onClear,
    onDismiss,
    onCancel,
    onSetToday,
    open,
    PureDateInputComponent,
    components,
    componentsProps
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded14);
  return (0, import_jsx_runtime36.jsxs)(WrapperVariantContext.Provider, {
    value: "mobile",
    children: [(0, import_jsx_runtime35.jsx)(PureDateInputComponent, _extends({
      components
    }, other, DateInputProps)), (0, import_jsx_runtime35.jsx)(PickersModalDialog, {
      DialogProps,
      onAccept,
      onClear,
      onDismiss,
      onCancel,
      onSetToday,
      open,
      components,
      componentsProps,
      children
    })]
  });
}

// node_modules/@mui/x-date-pickers/internals/components/PureDateInput.js
init_extends();
var React37 = __toESM(require_react());
init_utils();
var PureDateInput = React37.forwardRef(function PureDateInput2(props, ref) {
  const {
    disabled,
    getOpenDialogAriaText: getOpenDialogAriaTextProp,
    inputFormat,
    InputProps,
    inputRef,
    label,
    openPicker: onOpen,
    rawValue,
    renderInput,
    TextFieldProps = {},
    validationError,
    className
  } = props;
  const localeText = useLocaleText();
  const getOpenDialogAriaText = getOpenDialogAriaTextProp != null ? getOpenDialogAriaTextProp : localeText.openDatePickerDialogue;
  const utils = useUtils();
  const PureDateInputProps = React37.useMemo(() => _extends({}, InputProps, {
    readOnly: true
  }), [InputProps]);
  const inputValue = getDisplayDate(utils, rawValue, inputFormat);
  const handleOnClick = useEventCallback_default((event) => {
    event.stopPropagation();
    onOpen();
  });
  return renderInput(_extends({
    label,
    disabled,
    ref,
    inputRef,
    error: validationError,
    InputProps: PureDateInputProps,
    className
  }, !props.readOnly && !props.disabled && {
    onClick: handleOnClick
  }, {
    inputProps: _extends({
      disabled,
      readOnly: true,
      "aria-readonly": true,
      "aria-label": getOpenDialogAriaText(rawValue, utils),
      value: inputValue
    }, !props.readOnly && {
      onClick: handleOnClick
    }, {
      onKeyDown: onSpaceOrEnter(onOpen)
    })
  }, TextFieldProps));
});

// node_modules/@mui/x-date-pickers/internals/components/pickersToolbarClasses.js
function getPickersToolbarUtilityClass(slot) {
  return generateUtilityClass("MuiPickersToolbar", slot);
}
var pickersToolbarClasses = generateUtilityClasses("MuiPickersToolbar", ["root", "content", "penIconButton", "penIconButtonLandscape"]);

// node_modules/@mui/x-date-pickers/internals/components/PickersToolbar.js
init_extends();
var React38 = __toESM(require_react());
var import_jsx_runtime37 = __toESM(require_jsx_runtime());
var import_jsx_runtime38 = __toESM(require_jsx_runtime());
var useUtilityClasses18 = (ownerState) => {
  const {
    classes,
    isLandscape
  } = ownerState;
  const slots = {
    root: ["root"],
    content: ["content"],
    penIconButton: ["penIconButton", isLandscape && "penIconButtonLandscape"]
  };
  return composeClasses(slots, getPickersToolbarUtilityClass, classes);
};
var PickersToolbarRoot = styled_default("div", {
  name: "MuiPickersToolbar",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})(({
  theme,
  ownerState
}) => _extends({
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  justifyContent: "space-between",
  padding: theme.spacing(2, 3)
}, ownerState.isLandscape && {
  height: "auto",
  maxWidth: 160,
  padding: 16,
  justifyContent: "flex-start",
  flexWrap: "wrap"
}));
var PickersToolbarContent = styled_default(Grid_default, {
  name: "MuiPickersToolbar",
  slot: "Content",
  overridesResolver: (props, styles) => styles.content
})(({
  ownerState
}) => _extends({
  flex: 1
}, !ownerState.isLandscape && {
  alignItems: "center"
}));
var PickersToolbarPenIconButton = styled_default(IconButton_default, {
  name: "MuiPickersToolbar",
  slot: "PenIconButton",
  overridesResolver: (props, styles) => [{
    [`&.${pickersToolbarClasses.penIconButtonLandscape}`]: styles.penIconButtonLandscape
  }, styles.penIconButton]
})({});
var getViewTypeIcon = (viewType) => viewType === "clock" ? (0, import_jsx_runtime37.jsx)(Clock, {
  color: "inherit"
}) : (0, import_jsx_runtime37.jsx)(Calendar, {
  color: "inherit"
});
var PickersToolbar = React38.forwardRef(function PickersToolbar2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiPickersToolbar"
  });
  const {
    children,
    className,
    getMobileKeyboardInputViewButtonText,
    isLandscape,
    isMobileKeyboardViewOpen,
    landscapeDirection = "column",
    toggleMobileKeyboardView,
    toolbarTitle,
    viewType = "calendar"
  } = props;
  const ownerState = props;
  const localeText = useLocaleText();
  const classes = useUtilityClasses18(ownerState);
  return (0, import_jsx_runtime38.jsxs)(PickersToolbarRoot, {
    ref,
    className: clsx_m_default(classes.root, className),
    ownerState,
    children: [(0, import_jsx_runtime37.jsx)(Typography_default, {
      color: "text.secondary",
      variant: "overline",
      children: toolbarTitle
    }), (0, import_jsx_runtime38.jsxs)(PickersToolbarContent, {
      container: true,
      justifyContent: "space-between",
      className: classes.content,
      ownerState,
      direction: isLandscape ? landscapeDirection : "row",
      alignItems: isLandscape ? "flex-start" : "flex-end",
      children: [children, (0, import_jsx_runtime37.jsx)(PickersToolbarPenIconButton, {
        onClick: toggleMobileKeyboardView,
        className: classes.penIconButton,
        ownerState,
        color: "inherit",
        "aria-label": getMobileKeyboardInputViewButtonText ? getMobileKeyboardInputViewButtonText(isMobileKeyboardViewOpen, viewType) : localeText.inputModeToggleButtonAriaLabel(isMobileKeyboardViewOpen, viewType),
        children: isMobileKeyboardViewOpen ? getViewTypeIcon(viewType) : (0, import_jsx_runtime37.jsx)(Pen, {
          color: "inherit"
        })
      })]
    })]
  });
});

export {
  clsx_m_default,
  arrayIncludes,
  pickersMonthClasses,
  useUtils,
  useDefaultDates,
  useLocaleText,
  getMonthPickerUtilityClass,
  monthPickerClasses,
  parsePickerInputValue,
  parseNonNullablePickerDate,
  MonthPicker,
  useValidation,
  validateDate,
  useDateValidation,
  pickersFadeTransitionGroupClasses,
  DAY_SIZE,
  DAY_MARGIN,
  DIALOG_WIDTH,
  getPickersDayUtilityClass,
  pickersDayClasses,
  PickersDay2 as PickersDay,
  pickersSlideTransitionClasses,
  dayPickerClasses,
  Clock,
  DateRange,
  Time,
  createIsAfterIgnoreDatePart,
  useMeridiemMode,
  pickersCalendarHeaderClasses,
  WrapperVariantContext,
  pickersYearClasses,
  getYearPickerUtilityClass,
  yearPickerClasses,
  YearPicker,
  getCalendarPickerUtilityClass,
  calendarPickerClasses,
  CalendarPicker,
  clockPointerClasses,
  clockClasses,
  clockNumberClasses,
  getClockPickerUtilityClass,
  clockPickerClasses,
  ClockPicker,
  getPickersToolbarUtilityClass,
  pickersToolbarClasses,
  PickersToolbar,
  PickersActionBar,
  DesktopWrapper,
  KeyboardDateInput,
  CalendarOrClockPicker,
  usePickerState,
  MobileWrapper,
  PureDateInput
};
//# sourceMappingURL=chunk-2VGF66OV.js.map
