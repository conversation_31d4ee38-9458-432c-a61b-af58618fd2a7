import { useState, useEffect, useRef, useCallback } from 'react';
import { createLocationService } from '../services/locationService';
import { useAuth } from '../context/AuthContext';

/**
 * Custom hook for real-time location tracking
 * Provides easy integration with React components
 */
export const useLocationTracking = (options = {}) => {
  const { user } = useAuth();
  const [isTracking, setIsTracking] = useState(false);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [locationHistory, setLocationHistory] = useState([]);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState(null);
  const [permissionStatus, setPermissionStatus] = useState('prompt');
  
  const locationServiceRef = useRef(null);
  const statsIntervalRef = useRef(null);

  // Initialize location service
  useEffect(() => {
    if (user?.uid) {
      locationServiceRef.current = createLocationService(user.uid, user.role || 'courier');
      
      // Check geolocation permission
      checkPermissionStatus();
      
      // Set up stats monitoring
      statsIntervalRef.current = setInterval(() => {
        if (locationServiceRef.current) {
          const trackingStats = locationServiceRef.current.getTrackingStats();
          setStats(trackingStats);
          setIsTracking(trackingStats.isTracking);
        }
      }, 5000);
    }

    return () => {
      if (statsIntervalRef.current) {
        clearInterval(statsIntervalRef.current);
      }
    };
  }, [user]);

  // Check geolocation permission status
  const checkPermissionStatus = async () => {
    try {
      if ('permissions' in navigator) {
        const permission = await navigator.permissions.query({ name: 'geolocation' });
        setPermissionStatus(permission.state);
        
        // Listen for permission changes
        permission.addEventListener('change', () => {
          setPermissionStatus(permission.state);
        });
      }
    } catch (err) {
      console.warn('Could not check geolocation permission:', err);
    }
  };

  // Start location tracking
  const startTracking = useCallback(async (trackingOptions = {}) => {
    if (!locationServiceRef.current) {
      setError(new Error('Location service not initialized'));
      return false;
    }

    try {
      setError(null);
      const success = await locationServiceRef.current.startTracking({
        ...options,
        ...trackingOptions
      });
      
      if (success) {
        setIsTracking(true);
        
        // Subscribe to location updates
        locationServiceRef.current.subscribeToUserLocation(user.uid, (locationData) => {
          setCurrentLocation(locationData.location);
        });
        
        return true;
      } else {
        setError(new Error('Failed to start location tracking'));
        return false;
      }
    } catch (err) {
      setError(err);
      return false;
    }
  }, [user?.uid, options]);

  // Stop location tracking
  const stopTracking = useCallback(async () => {
    if (!locationServiceRef.current) return;

    try {
      await locationServiceRef.current.stopTracking();
      setIsTracking(false);
      setCurrentLocation(null);
      setError(null);
    } catch (err) {
      setError(err);
    }
  }, []);

  // Get current location once
  const getCurrentLocation = useCallback(async () => {
    if (!locationServiceRef.current) {
      throw new Error('Location service not initialized');
    }

    try {
      setError(null);
      const location = await locationServiceRef.current.getCurrentLocation();
      setCurrentLocation(location);
      return location;
    } catch (err) {
      setError(err);
      throw err;
    }
  }, []);

  // Get location history
  const getLocationHistory = useCallback(async (limit = 50) => {
    if (!locationServiceRef.current || !user?.uid) {
      return [];
    }

    try {
      const history = await locationServiceRef.current.getLocationHistory(user.uid, limit);
      setLocationHistory(history);
      return history;
    } catch (err) {
      setError(err);
      return [];
    }
  }, [user?.uid]);

  // Request geolocation permission
  const requestPermission = useCallback(async () => {
    try {
      const location = await getCurrentLocation();
      setPermissionStatus('granted');
      return location;
    } catch (err) {
      if (err.message.includes('denied')) {
        setPermissionStatus('denied');
      }
      throw err;
    }
  }, [getCurrentLocation]);

  // Subscribe to another user's location
  const subscribeToUserLocation = useCallback((userId, callback) => {
    if (!locationServiceRef.current) {
      return () => {};
    }

    return locationServiceRef.current.subscribeToUserLocation(userId, callback);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (locationServiceRef.current && isTracking) {
        locationServiceRef.current.stopTracking();
      }
    };
  }, [isTracking]);

  return {
    // State
    isTracking,
    currentLocation,
    locationHistory,
    error,
    stats,
    permissionStatus,
    
    // Actions
    startTracking,
    stopTracking,
    getCurrentLocation,
    getLocationHistory,
    requestPermission,
    subscribeToUserLocation,
    
    // Utilities
    isSupported: 'geolocation' in navigator,
    isOnline: navigator.onLine
  };
};

/**
 * Hook for tracking multiple users' locations
 */
export const useMultiUserTracking = (userIds = []) => {
  const [userLocations, setUserLocations] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const locationServiceRef = useRef(null);
  const unsubscribeCallbacks = useRef([]);

  useEffect(() => {
    if (userIds.length === 0) {
      setLoading(false);
      return;
    }

    // Create location service instance
    locationServiceRef.current = createLocationService('admin', 'admin');
    
    // Subscribe to each user's location
    const subscriptions = userIds.map(userId => {
      return locationServiceRef.current.subscribeToUserLocation(userId, (locationData) => {
        setUserLocations(prev => ({
          ...prev,
          [userId]: locationData
        }));
      });
    });

    unsubscribeCallbacks.current = subscriptions;
    setLoading(false);

    return () => {
      // Cleanup subscriptions
      unsubscribeCallbacks.current.forEach(unsubscribe => unsubscribe());
      unsubscribeCallbacks.current = [];
    };
  }, [userIds]);

  const getUserLocation = useCallback((userId) => {
    return userLocations[userId] || null;
  }, [userLocations]);

  const getAllLocations = useCallback(() => {
    return userLocations;
  }, [userLocations]);

  const getActiveUsers = useCallback(() => {
    return Object.entries(userLocations)
      .filter(([, data]) => data.isOnline && data.trackingActive)
      .map(([userId]) => userId);
  }, [userLocations]);

  return {
    userLocations,
    loading,
    error,
    getUserLocation,
    getAllLocations,
    getActiveUsers,
    totalUsers: userIds.length,
    activeUsers: getActiveUsers().length
  };
};

/**
 * Hook for location-based features (geofencing, proximity, etc.)
 */
export const useLocationFeatures = () => {
  const calculateDistance = useCallback((point1, point2) => {
    const R = 6371000; // Earth's radius in meters
    const dLat = (point2.lat - point1.lat) * Math.PI / 180;
    const dLng = (point2.lng - point1.lng) * Math.PI / 180;
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }, []);

  const isWithinRadius = useCallback((center, point, radius) => {
    const distance = calculateDistance(center, point);
    return distance <= radius;
  }, [calculateDistance]);

  const findNearestPoint = useCallback((origin, points) => {
    if (!points || points.length === 0) return null;

    let nearest = points[0];
    let minDistance = calculateDistance(origin, points[0]);

    for (let i = 1; i < points.length; i++) {
      const distance = calculateDistance(origin, points[i]);
      if (distance < minDistance) {
        minDistance = distance;
        nearest = points[i];
      }
    }

    return { point: nearest, distance: minDistance };
  }, [calculateDistance]);

  const formatDistance = useCallback((meters) => {
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    } else {
      return `${(meters / 1000).toFixed(1)}km`;
    }
  }, []);

  const getBearing = useCallback((point1, point2) => {
    const dLng = (point2.lng - point1.lng) * Math.PI / 180;
    const lat1 = point1.lat * Math.PI / 180;
    const lat2 = point2.lat * Math.PI / 180;
    
    const y = Math.sin(dLng) * Math.cos(lat2);
    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng);
    
    const bearing = Math.atan2(y, x) * 180 / Math.PI;
    return (bearing + 360) % 360;
  }, []);

  return {
    calculateDistance,
    isWithinRadius,
    findNearestPoint,
    formatDistance,
    getBearing
  };
};
