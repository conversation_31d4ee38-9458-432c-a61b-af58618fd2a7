{"version": 3, "sources": ["../../@googlemaps/js-api-loader/node_modules/tslib/tslib.es6.js", "../../@googlemaps/js-api-loader/node_modules/fast-deep-equal/index.js", "../../@googlemaps/js-api-loader/src/index.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "/**\n * Copyright 2019 Google LLC. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at.\n *\n *      Http://www.apache.org/licenses/LICENSE-2.0.\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport isEqual from \"fast-deep-equal\";\n\nexport const DEFAULT_ID = \"__googleMapsScriptId\";\n\n// https://developers.google.com/maps/documentation/javascript/libraries#libraries-for-dynamic-library-import\nexport type Library =\n  | \"core\"\n  | \"maps\"\n  | \"places\"\n  | \"geocoding\"\n  | \"routes\"\n  | \"marker\"\n  | \"geometry\"\n  | \"elevation\"\n  | \"streetView\"\n  | \"journeySharing\"\n  | \"drawing\"\n  | \"visualization\";\n\nexport type Libraries = Library[];\n\n/**\n * The Google Maps JavaScript API\n * [documentation](https://developers.google.com/maps/documentation/javascript/tutorial)\n * is the authoritative source for [[LoaderOptions]].\n/**\n * Loader options\n */\nexport interface LoaderOptions {\n  /**\n   * See https://developers.google.com/maps/documentation/javascript/get-api-key.\n   */\n  apiKey: string;\n  /**\n   * @deprecated See https://developers.google.com/maps/premium/overview.\n   */\n  channel?: string;\n  /**\n   * @deprecated See https://developers.google.com/maps/premium/overview, use `apiKey` instead.\n   */\n  client?: string;\n  /**\n   * In your application you can specify release channels or version numbers:\n   *\n   * The weekly version is specified with `version=weekly`. This version is\n   * updated once per week, and is the most current.\n   *\n   * ```\n   * const loader = Loader({apiKey, version: 'weekly'});\n   * ```\n   *\n   * The quarterly version is specified with `version=quarterly`. This version\n   * is updated once per quarter, and is the most predictable.\n   *\n   * ```\n   * const loader = Loader({apiKey, version: 'quarterly'});\n   * ```\n   *\n   * The version number is specified with `version=n.nn`. You can choose\n   * `version=3.40`, `version=3.39`, or `version=3.38`. Version numbers are\n   * updated once per quarter.\n   *\n   * ```\n   * const loader = Loader({apiKey, version: '3.40'});\n   * ```\n   *\n   * If you do not explicitly specify a version, you will receive the\n   * weekly version by default.\n   */\n  version?: string;\n  /**\n   * The id of the script tag. Before adding a new script, the Loader will check for an existing one.\n   */\n  id?: string;\n  /**\n   * When loading the Maps JavaScript API via the URL you may optionally load\n   * additional libraries through use of the libraries URL parameter. Libraries\n   * are modules of code that provide additional functionality to the main Maps\n   * JavaScript API but are not loaded unless you specifically request them.\n   *\n   * ```\n   * const loader = Loader({\n   *  apiKey,\n   *  libraries: ['drawing', 'geometry', 'places', 'visualization'],\n   * });\n   * ```\n   *\n   * Set the [list of libraries](https://developers.google.com/maps/documentation/javascript/libraries) for more options.\n   */\n  libraries?: Libraries;\n  /**\n   * By default, the Maps JavaScript API uses the user's preferred language\n   * setting as specified in the browser, when displaying textual information\n   * such as the names for controls, copyright notices, driving directions and\n   * labels on maps. In most cases, it's preferable to respect the browser\n   * setting. However, if you want the Maps JavaScript API to ignore the\n   * browser's language setting, you can force it to display information in a\n   * particular language when loading the Maps JavaScript API code.\n   *\n   * For example, the following example localizes the language to Japan:\n   *\n   * ```\n   * const loader = Loader({apiKey, language: 'ja', region: 'JP'});\n   * ```\n   *\n   * See the [list of supported\n   * languages](https://developers.google.com/maps/faq#languagesupport). Note\n   * that new languages are added often, so this list may not be exhaustive.\n   *\n   */\n  language?: string;\n  /**\n   * When you load the Maps JavaScript API from maps.googleapis.com it applies a\n   * default bias for application behavior towards the United States. If you\n   * want to alter your application to serve different map tiles or bias the\n   * application (such as biasing geocoding results towards the region), you can\n   * override this default behavior by adding a region parameter when loading\n   * the Maps JavaScript API code.\n   *\n   * The region parameter accepts Unicode region subtag identifiers which\n   * (generally) have a one-to-one mapping to country code Top-Level Domains\n   * (ccTLDs). Most Unicode region identifiers are identical to ISO 3166-1\n   * codes, with some notable exceptions. For example, Great Britain's ccTLD is\n   * \"uk\" (corresponding to the domain .co.uk) while its region identifier is\n   * \"GB.\"\n   *\n   * For example, the following example localizes the map to the United Kingdom:\n   *\n   * ```\n   * const loader = Loader({apiKey, region: 'GB'});\n   * ```\n   */\n  region?: string;\n  /**\n   * @deprecated Passing `mapIds` is no longer required in the script tag.\n   */\n  mapIds?: string[];\n  /**\n   * Use a custom url and path to load the Google Maps API script.\n   */\n  url?: string;\n  /**\n   * Use a cryptographic nonce attribute.\n   */\n  nonce?: string;\n  /**\n   * The number of script load retries.\n   */\n  retries?: number;\n  /**\n   * Maps JS customers can configure HTTP Referrer Restrictions in the Cloud\n   * Console to limit which URLs are allowed to use a particular API Key. By\n   * default, these restrictions can be configured to allow only certain paths\n   * to use an API Key. If any URL on the same domain or origin may use the API\n   * Key, you can set `auth_referrer_policy=origin` to limit the amount of data\n   * sent when authorizing requests from the Maps JavaScript API. This is\n   * available starting in version 3.46. When this parameter is specified and\n   * HTTP Referrer Restrictions are enabled on Cloud Console, Maps JavaScript\n   * API will only be able to load if there is an HTTP Referrer Restriction that\n   * matches the current website's domain without a path specified.\n   */\n  authReferrerPolicy?: \"origin\";\n}\n\n/**\n * The status of the [[Loader]].\n */\nexport enum LoaderStatus {\n  INITIALIZED,\n  LOADING,\n  SUCCESS,\n  FAILURE,\n}\n\n/**\n * [[Loader]] makes it easier to add Google Maps JavaScript API to your application\n * dynamically using\n * [Promises](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise).\n * It works by dynamically creating and appending a script node to the the\n * document head and wrapping the callback function so as to return a promise.\n *\n * ```\n * const loader = new Loader({\n *   apiKey: \"\",\n *   version: \"weekly\",\n *   libraries: [\"places\"]\n * });\n *\n * loader.load().then((google) => {\n *   const map = new google.maps.Map(...)\n * })\n * ```\n */\nexport class Loader {\n  private static instance: Loader;\n  /**\n   * See [[LoaderOptions.version]]\n   */\n  public readonly version: string;\n  /**\n   * See [[LoaderOptions.apiKey]]\n   */\n  public readonly apiKey: string;\n  /**\n   * See [[LoaderOptions.channel]]\n   */\n  public readonly channel: string;\n  /**\n   * See [[LoaderOptions.client]]\n   */\n  public readonly client: string;\n  /**\n   * See [[LoaderOptions.id]]\n   */\n  public readonly id: string;\n  /**\n   * See [[LoaderOptions.libraries]]\n   */\n  public readonly libraries: Libraries;\n  /**\n   * See [[LoaderOptions.language]]\n   */\n  public readonly language: string;\n\n  /**\n   * See [[LoaderOptions.region]]\n   */\n  public readonly region: string;\n\n  /**\n   * See [[LoaderOptions.mapIds]]\n   */\n  public readonly mapIds: string[];\n\n  /**\n   * See [[LoaderOptions.nonce]]\n   */\n  public readonly nonce: string | null;\n\n  /**\n   * See [[LoaderOptions.retries]]\n   */\n  public readonly retries: number;\n\n  /**\n   * See [[LoaderOptions.url]]\n   */\n  public readonly url: string;\n  /**\n   * See [[LoaderOptions.authReferrerPolicy]]\n   */\n  public readonly authReferrerPolicy: \"origin\";\n\n  private callbacks: ((e: ErrorEvent) => void)[] = [];\n  private done = false;\n  private loading = false;\n  private onerrorEvent: ErrorEvent;\n  private errors: ErrorEvent[] = [];\n\n  /**\n   * Creates an instance of Loader using [[LoaderOptions]]. No defaults are set\n   * using this library, instead the defaults are set by the Google Maps\n   * JavaScript API server.\n   *\n   * ```\n   * const loader = Loader({apiKey, version: 'weekly', libraries: ['places']});\n   * ```\n   */\n  constructor({\n    apiKey,\n    authReferrerPolicy,\n    channel,\n    client,\n    id = DEFAULT_ID,\n    language,\n    libraries = [],\n    mapIds,\n    nonce,\n    region,\n    retries = 3,\n    url = \"https://maps.googleapis.com/maps/api/js\",\n    version,\n  }: LoaderOptions) {\n    this.apiKey = apiKey;\n    this.authReferrerPolicy = authReferrerPolicy;\n    this.channel = channel;\n    this.client = client;\n    this.id = id || DEFAULT_ID; // Do not allow empty string\n    this.language = language;\n    this.libraries = libraries;\n    this.mapIds = mapIds;\n    this.nonce = nonce;\n    this.region = region;\n    this.retries = retries;\n    this.url = url;\n    this.version = version;\n\n    if (Loader.instance) {\n      if (!isEqual(this.options, Loader.instance.options)) {\n        throw new Error(\n          `Loader must not be called again with different options. ${JSON.stringify(\n            this.options\n          )} !== ${JSON.stringify(Loader.instance.options)}`\n        );\n      }\n\n      return Loader.instance;\n    }\n\n    Loader.instance = this;\n  }\n\n  public get options(): LoaderOptions {\n    return {\n      version: this.version,\n      apiKey: this.apiKey,\n      channel: this.channel,\n      client: this.client,\n      id: this.id,\n      libraries: this.libraries,\n      language: this.language,\n      region: this.region,\n      mapIds: this.mapIds,\n      nonce: this.nonce,\n      url: this.url,\n      authReferrerPolicy: this.authReferrerPolicy,\n    };\n  }\n\n  public get status(): LoaderStatus {\n    if (this.errors.length) {\n      return LoaderStatus.FAILURE;\n    }\n    if (this.done) {\n      return LoaderStatus.SUCCESS;\n    }\n    if (this.loading) {\n      return LoaderStatus.LOADING;\n    }\n    return LoaderStatus.INITIALIZED;\n  }\n\n  private get failed(): boolean {\n    return this.done && !this.loading && this.errors.length >= this.retries + 1;\n  }\n\n  /**\n   * CreateUrl returns the Google Maps JavaScript API script url given the [[LoaderOptions]].\n   *\n   * @ignore\n   * @deprecated\n   */\n  public createUrl(): string {\n    let url = this.url;\n\n    url += `?callback=__googleMapsCallback&loading=async`;\n\n    if (this.apiKey) {\n      url += `&key=${this.apiKey}`;\n    }\n\n    if (this.channel) {\n      url += `&channel=${this.channel}`;\n    }\n\n    if (this.client) {\n      url += `&client=${this.client}`;\n    }\n\n    if (this.libraries.length > 0) {\n      url += `&libraries=${this.libraries.join(\",\")}`;\n    }\n\n    if (this.language) {\n      url += `&language=${this.language}`;\n    }\n\n    if (this.region) {\n      url += `&region=${this.region}`;\n    }\n\n    if (this.version) {\n      url += `&v=${this.version}`;\n    }\n\n    if (this.mapIds) {\n      url += `&map_ids=${this.mapIds.join(\",\")}`;\n    }\n\n    if (this.authReferrerPolicy) {\n      url += `&auth_referrer_policy=${this.authReferrerPolicy}`;\n    }\n\n    return url;\n  }\n\n  public deleteScript(): void {\n    const script = document.getElementById(this.id);\n    if (script) {\n      script.remove();\n    }\n  }\n\n  /**\n   * Load the Google Maps JavaScript API script and return a Promise.\n   * @deprecated, use importLibrary() instead.\n   */\n  public load(): Promise<typeof google> {\n    return this.loadPromise();\n  }\n\n  /**\n   * Load the Google Maps JavaScript API script and return a Promise.\n   *\n   * @ignore\n   * @deprecated, use importLibrary() instead.\n   */\n  public loadPromise(): Promise<typeof google> {\n    return new Promise((resolve, reject) => {\n      this.loadCallback((err: ErrorEvent) => {\n        if (!err) {\n          resolve(window.google);\n        } else {\n          reject(err.error);\n        }\n      });\n    });\n  }\n\n  /**\n   * See https://developers.google.com/maps/documentation/javascript/reference/top-level#google.maps.importLibrary\n   */\n  public importLibrary(name: \"core\"): Promise<google.maps.CoreLibrary>;\n  public importLibrary(name: \"maps\"): Promise<google.maps.MapsLibrary>;\n  public importLibrary(name: \"places\"): Promise<google.maps.PlacesLibrary>;\n  public importLibrary(\n    name: \"geocoding\"\n  ): Promise<google.maps.GeocodingLibrary>;\n  public importLibrary(name: \"routes\"): Promise<google.maps.RoutesLibrary>;\n  public importLibrary(name: \"marker\"): Promise<google.maps.MarkerLibrary>;\n  public importLibrary(name: \"geometry\"): Promise<google.maps.GeometryLibrary>;\n  public importLibrary(\n    name: \"elevation\"\n  ): Promise<google.maps.ElevationLibrary>;\n  public importLibrary(\n    name: \"streetView\"\n  ): Promise<google.maps.StreetViewLibrary>;\n  public importLibrary(\n    name: \"journeySharing\"\n  ): Promise<google.maps.JourneySharingLibrary>;\n  public importLibrary(name: \"drawing\"): Promise<google.maps.DrawingLibrary>;\n  public importLibrary(\n    name: \"visualization\"\n  ): Promise<google.maps.VisualizationLibrary>;\n  public importLibrary(name: Library): Promise<unknown>;\n  public importLibrary(name: Library): Promise<unknown> {\n    this.execute();\n    return google.maps.importLibrary(name);\n  }\n\n  /**\n   * Load the Google Maps JavaScript API script with a callback.\n   * @deprecated, use importLibrary() instead.\n   */\n  public loadCallback(fn: (e: ErrorEvent) => void): void {\n    this.callbacks.push(fn);\n    this.execute();\n  }\n\n  /**\n   * Set the script on document.\n   */\n  private setScript(): void {\n    if (document.getElementById(this.id)) {\n      // TODO wrap onerror callback for cases where the script was loaded elsewhere\n      this.callback();\n      return;\n    }\n\n    const params = {\n      key: this.apiKey,\n      channel: this.channel,\n      client: this.client,\n      libraries: this.libraries.length && this.libraries,\n      v: this.version,\n      mapIds: this.mapIds,\n      language: this.language,\n      region: this.region,\n      authReferrerPolicy: this.authReferrerPolicy,\n    };\n    // keep the URL minimal:\n    Object.keys(params).forEach(\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      (key) => !(params as any)[key] && delete (params as any)[key]\n    );\n\n    if (!window?.google?.maps?.importLibrary) {\n      // tweaked copy of https://developers.google.com/maps/documentation/javascript/load-maps-js-api#dynamic-library-import\n      // which also sets the base url, the id, and the nonce\n      /* eslint-disable */\n      ((g) => {\n        // @ts-ignore\n        let h,\n          a,\n          k,\n          p = \"The Google Maps JavaScript API\",\n          c = \"google\",\n          l = \"importLibrary\",\n          q = \"__ib__\",\n          m = document,\n          b = window;\n        // @ts-ignore\n        b = b[c] || (b[c] = {});\n        // @ts-ignore\n        const d = b.maps || (b.maps = {}),\n          r = new Set(),\n          e = new URLSearchParams(),\n          u = () =>\n            // @ts-ignore\n            h || (h = new Promise(async (f, n) => {\n              await (a = m.createElement(\"script\"));\n              a.id = this.id;\n              e.set(\"libraries\", [...r] + \"\");\n              // @ts-ignore\n              for (k in g) e.set(k.replace(/[A-Z]/g, (t) => \"_\" + t[0].toLowerCase()), g[k]);\n              e.set(\"callback\", c + \".maps.\" + q);\n              a.src = this.url + `?` + e;\n              d[q] = f;\n              a.onerror = () => (h = n(Error(p + \" could not load.\")));\n              // @ts-ignore\n              a.nonce = this.nonce || m.querySelector(\"script[nonce]\")?.nonce || \"\";\n              m.head.append(a);\n            }));\n        // @ts-ignore\n        d[l] ? console.warn(p + \" only loads once. Ignoring:\", g) : (d[l] = (f, ...n) => r.add(f) && u().then(() => d[l](f, ...n)));\n      })(params);\n      /* eslint-enable */\n    }\n\n    // While most libraries populate the global namespace when loaded via bootstrap params,\n    // this is not the case for \"marker\" when used with the inline bootstrap loader\n    // (and maybe others in the future). So ensure there is an importLibrary for each:\n    const libraryPromises = this.libraries.map((library) =>\n      this.importLibrary(library)\n    );\n    // ensure at least one library, to kick off loading...\n    if (!libraryPromises.length) {\n      libraryPromises.push(this.importLibrary(\"core\"));\n    }\n    Promise.all(libraryPromises).then(\n      () => this.callback(),\n      (error) => {\n        const event = new ErrorEvent(\"error\", { error }); // for backwards compat\n        this.loadErrorCallback(event);\n      }\n    );\n  }\n\n  /**\n   * Reset the loader state.\n   */\n  private reset(): void {\n    this.deleteScript();\n    this.done = false;\n    this.loading = false;\n    this.errors = [];\n    this.onerrorEvent = null;\n  }\n\n  private resetIfRetryingFailed(): void {\n    if (this.failed) {\n      this.reset();\n    }\n  }\n\n  private loadErrorCallback(e: ErrorEvent): void {\n    this.errors.push(e);\n\n    if (this.errors.length <= this.retries) {\n      const delay = this.errors.length * 2 ** this.errors.length;\n\n      console.error(\n        `Failed to load Google Maps script, retrying in ${delay} ms.`\n      );\n\n      setTimeout(() => {\n        this.deleteScript();\n        this.setScript();\n      }, delay);\n    } else {\n      this.onerrorEvent = e;\n      this.callback();\n    }\n  }\n\n  private callback(): void {\n    this.done = true;\n    this.loading = false;\n\n    this.callbacks.forEach((cb) => {\n      cb(this.onerrorEvent);\n    });\n\n    this.callbacks = [];\n  }\n\n  private execute(): void {\n    this.resetIfRetryingFailed();\n\n    if (this.loading) {\n      // do nothing but wait\n      return;\n    }\n\n    if (this.done) {\n      this.callback();\n    } else {\n      // short circuit and warn if google.maps is already loaded\n      if (window.google && window.google.maps && window.google.maps.version) {\n        console.warn(\n          \"Google Maps already loaded outside @googlemaps/js-api-loader. \" +\n            \"This may result in undesirable behavior as options and script parameters may not match.\"\n        );\n        this.callback();\n        return;\n      }\n\n      this.loading = true;\n      this.setScript();\n    }\n  }\n}\n"], "mappings": ";AAkHO,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AACzD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;IAAE,CAAE;EAAE;AAC1G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;MAAE,SAAU,GAAG;AAAE,eAAO,CAAC;MAAE;IAAE;AACzF,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;MAAE,SAAU,GAAG;AAAE,eAAO,CAAC;MAAE;IAAE;AAC5F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;IAAE;AAC5G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAA,CAAE,GAAG,KAAI,CAAE;EAC5E,CAAK;AACL;;;;ACpHA,IAAA,gBAAiB,SAAS,MAAM,GAAG,GAAG;AACpC,MAAI,MAAM,EAAG,QAAO;AAEpB,MAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,QAAI,EAAE,gBAAgB,EAAE,YAAa,QAAO;AAE5C,QAAI,QAAQ,GAAG;AACf,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,eAAS,EAAE;AACX,UAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,WAAK,IAAI,QAAQ,QAAQ;AACvB,YAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AACjC,aAAO;IACb;AAII,QAAI,EAAE,gBAAgB,OAAQ,QAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAC5E,QAAI,EAAE,YAAY,OAAO,UAAU,QAAS,QAAO,EAAE,QAAO,MAAO,EAAE,QAAO;AAC5E,QAAI,EAAE,aAAa,OAAO,UAAU,SAAU,QAAO,EAAE,SAAQ,MAAO,EAAE,SAAQ;AAEhF,WAAO,OAAO,KAAK,CAAC;AACpB,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE,OAAQ,QAAO;AAE7C,SAAK,IAAI,QAAQ,QAAQ;AACvB,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,EAAG,QAAO;AAEhE,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,UAAI,MAAM,KAAK,CAAC;AAEhB,UAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,EAAG,QAAO;IACzC;AAEI,WAAO;EACX;AAGE,SAAO,MAAI,KAAK,MAAI;AACtB;;AC3BO,IAAM,aAAa;IAqKd;CAAZ,SAAYA,eAAY;AACtB,EAAAA,cAAAA,cAAA,aAAA,IAAA,CAAA,IAAA;AACA,EAAAA,cAAAA,cAAA,SAAA,IAAA,CAAA,IAAA;AACA,EAAAA,cAAAA,cAAA,SAAA,IAAA,CAAA,IAAA;AACA,EAAAA,cAAAA,cAAA,SAAA,IAAA,CAAA,IAAA;AACF,GALY,iBAAA,eAKX,CAAA,EAAA;IAqBY,eAAA,QAAM;;;;;;;;;;EA2EjB,YAAY,EACV,QACA,oBACA,SACA,QACA,KAAK,YACL,UACA,YAAY,CAAA,GACZ,QACA,OACA,QACA,UAAU,GACV,MAAM,2CACN,QAAO,GACO;AA7BR,SAAS,YAAgC,CAAA;AACzC,SAAI,OAAG;AACP,SAAO,UAAG;AAEV,SAAM,SAAiB,CAAA;AA0B7B,SAAK,SAAS;AACd,SAAK,qBAAqB;AAC1B,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,KAAK,MAAM;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,MAAM;AACX,SAAK,UAAU;AAEf,QAAI,QAAO,UAAU;AACnB,UAAI,CAAC,QAAQ,KAAK,SAAS,QAAO,SAAS,OAAO,GAAG;AACnD,cAAM,IAAI,MACR,2DAA2D,KAAK,UAC9D,KAAK,OAAO,CACb,QAAQ,KAAK,UAAU,QAAO,SAAS,OAAO,CAAC,EAAE;;AAItD,aAAO,QAAO;;AAGhB,YAAO,WAAW;;EAGpB,IAAW,UAAO;AAChB,WAAO;MACL,SAAS,KAAK;MACd,QAAQ,KAAK;MACb,SAAS,KAAK;MACd,QAAQ,KAAK;MACb,IAAI,KAAK;MACT,WAAW,KAAK;MAChB,UAAU,KAAK;MACf,QAAQ,KAAK;MACb,QAAQ,KAAK;MACb,OAAO,KAAK;MACZ,KAAK,KAAK;MACV,oBAAoB,KAAK;;;EAI7B,IAAW,SAAM;AACf,QAAI,KAAK,OAAO,QAAQ;AACtB,aAAO,aAAa;;AAEtB,QAAI,KAAK,MAAM;AACb,aAAO,aAAa;;AAEtB,QAAI,KAAK,SAAS;AAChB,aAAO,aAAa;;AAEtB,WAAO,aAAa;;EAGtB,IAAY,SAAM;AAChB,WAAO,KAAK,QAAQ,CAAC,KAAK,WAAW,KAAK,OAAO,UAAU,KAAK,UAAU;;;;;;;;EASrE,YAAS;AACd,QAAI,MAAM,KAAK;AAEf,WAAO;AAEP,QAAI,KAAK,QAAQ;AACf,aAAO,QAAQ,KAAK,MAAM;;AAG5B,QAAI,KAAK,SAAS;AAChB,aAAO,YAAY,KAAK,OAAO;;AAGjC,QAAI,KAAK,QAAQ;AACf,aAAO,WAAW,KAAK,MAAM;;AAG/B,QAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,aAAO,cAAc,KAAK,UAAU,KAAK,GAAG,CAAC;;AAG/C,QAAI,KAAK,UAAU;AACjB,aAAO,aAAa,KAAK,QAAQ;;AAGnC,QAAI,KAAK,QAAQ;AACf,aAAO,WAAW,KAAK,MAAM;;AAG/B,QAAI,KAAK,SAAS;AAChB,aAAO,MAAM,KAAK,OAAO;;AAG3B,QAAI,KAAK,QAAQ;AACf,aAAO,YAAY,KAAK,OAAO,KAAK,GAAG,CAAC;;AAG1C,QAAI,KAAK,oBAAoB;AAC3B,aAAO,yBAAyB,KAAK,kBAAkB;;AAGzD,WAAO;;EAGF,eAAY;AACjB,UAAM,SAAS,SAAS,eAAe,KAAK,EAAE;AAC9C,QAAI,QAAQ;AACV,aAAO,OAAM;;;;;;;EAQV,OAAI;AACT,WAAO,KAAK,YAAW;;;;;;;;EASlB,cAAW;AAChB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,WAAK,aAAa,CAAC,QAAmB;AACpC,YAAI,CAAC,KAAK;AACR,kBAAQ,OAAO,MAAM;eAChB;AACL,iBAAO,IAAI,KAAK;;MAEpB,CAAC;IACH,CAAC;;EA6BI,cAAc,MAAa;AAChC,SAAK,QAAO;AACZ,WAAO,OAAO,KAAK,cAAc,IAAI;;;;;;EAOhC,aAAa,IAA2B;AAC7C,SAAK,UAAU,KAAK,EAAE;AACtB,SAAK,QAAO;;;;;EAMN,YAAS;;AACf,QAAI,SAAS,eAAe,KAAK,EAAE,GAAG;AAEpC,WAAK,SAAQ;AACb;;AAGF,UAAM,SAAS;MACb,KAAK,KAAK;MACV,SAAS,KAAK;MACd,QAAQ,KAAK;MACb,WAAW,KAAK,UAAU,UAAU,KAAK;MACzC,GAAG,KAAK;MACR,QAAQ,KAAK;MACb,UAAU,KAAK;MACf,QAAQ,KAAK;MACb,oBAAoB,KAAK;;AAG3B,WAAO,KAAK,MAAM,EAAE;;MAElB,CAAC,QAAQ,CAAE,OAAe,GAAG,KAAK,OAAQ,OAAe,GAAG;IAAC;AAG/D,QAAI,GAAC,MAAA,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,gBAAe;AAIxC,OAAC,CAAC,MAAK;AAEL,YAAI,GACF,GACA,GACA,IAAI,kCACJ,IAAI,UACJ,IAAI,iBACJ,IAAI,UACJ,IAAI,UACJ,IAAI;AAEN,YAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAA;AAEpB,cAAM,IAAI,EAAE,SAAS,EAAE,OAAO,CAAA,IAC5B,IAAI,oBAAI,IAAG,GACX,IAAI,IAAI,gBAAe,GACvB,IAAI;;UAEF,MAAM,IAAI,IAAI,QAAQ,CAAO,GAAG,MAAK,UAAA,MAAA,QAAA,QAAA,aAAA;;AACnC,kBAAO,IAAI,EAAE,cAAc,QAAQ;AACnC,cAAE,KAAK,KAAK;AACZ,cAAE,IAAI,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE;AAE9B,iBAAK,KAAK;AAAG,gBAAE,IAAI,EAAE,QAAQ,UAAU,CAAC,MAAM,MAAM,EAAE,CAAC,EAAE,YAAW,CAAE,GAAG,EAAE,CAAC,CAAC;AAC7E,cAAE,IAAI,YAAY,IAAI,WAAW,CAAC;AAClC,cAAE,MAAM,KAAK,MAAM,MAAM;AACzB,cAAE,CAAC,IAAI;AACP,cAAE,UAAU,MAAO,IAAI,EAAE,MAAM,IAAI,kBAAkB,CAAC;AAEtD,cAAE,QAAQ,KAAK,WAASC,MAAA,EAAE,cAAc,eAAe,OAAC,QAAAA,QAAA,SAAA,SAAAA,IAAE,UAAS;AACnE,cAAE,KAAK,OAAO,CAAC;WAChB,CAAA;;AAEL,UAAE,CAAC,IAAI,QAAQ,KAAK,IAAI,+BAA+B,CAAC,IAAK,EAAE,CAAC,IAAI,CAAC,MAAM,MAAM,EAAE,IAAI,CAAC,KAAK,EAAC,EAAG,KAAK,MAAM,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;MAC3H,GAAG,MAAM;;AAOX,UAAM,kBAAkB,KAAK,UAAU,IAAI,CAAC,YAC1C,KAAK,cAAc,OAAO,CAAC;AAG7B,QAAI,CAAC,gBAAgB,QAAQ;AAC3B,sBAAgB,KAAK,KAAK,cAAc,MAAM,CAAC;;AAEjD,YAAQ,IAAI,eAAe,EAAE,KAC3B,MAAM,KAAK,SAAQ,GACnB,CAAC,UAAS;AACR,YAAM,QAAQ,IAAI,WAAW,SAAS,EAAE,MAAK,CAAE;AAC/C,WAAK,kBAAkB,KAAK;IAC9B,CAAC;;;;;EAOG,QAAK;AACX,SAAK,aAAY;AACjB,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,SAAS,CAAA;AACd,SAAK,eAAe;;EAGd,wBAAqB;AAC3B,QAAI,KAAK,QAAQ;AACf,WAAK,MAAK;;;EAIN,kBAAkB,GAAa;AACrC,SAAK,OAAO,KAAK,CAAC;AAElB,QAAI,KAAK,OAAO,UAAU,KAAK,SAAS;AACtC,YAAM,QAAQ,KAAK,OAAO,SAAS,KAAA,IAAA,GAAK,KAAK,OAAO,MAAM;AAE1D,cAAQ,MACN,kDAAkD,KAAK,MAAM;AAG/D,iBAAW,MAAK;AACd,aAAK,aAAY;AACjB,aAAK,UAAS;SACb,KAAK;WACH;AACL,WAAK,eAAe;AACpB,WAAK,SAAQ;;;EAIT,WAAQ;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AAEf,SAAK,UAAU,QAAQ,CAAC,OAAM;AAC5B,SAAG,KAAK,YAAY;IACtB,CAAC;AAED,SAAK,YAAY,CAAA;;EAGX,UAAO;AACb,SAAK,sBAAqB;AAE1B,QAAI,KAAK,SAAS;AAEhB;;AAGF,QAAI,KAAK,MAAM;AACb,WAAK,SAAQ;WACR;AAEL,UAAI,OAAO,UAAU,OAAO,OAAO,QAAQ,OAAO,OAAO,KAAK,SAAS;AACrE,gBAAQ,KACN,uJAC2F;AAE7F,aAAK,SAAQ;AACb;;AAGF,WAAK,UAAU;AACf,WAAK,UAAS;;;AAGnB;", "names": ["LoaderStatus", "_a"]}