{"name": "courier-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.11.0", "@googlemaps/js-api-loader": "^1.16.8", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "@mui/x-data-grid": "^6.19.2", "@mui/x-date-pickers": "^5.0.19", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@react-firebase/firestore": "^0.5.5", "@react-google-maps/api": "^2.20.6", "@tanstack/react-query": "^5.74.7", "chart.js": "^4.4.1", "class-variance-authority": "^0.7.1", "date-fns": "^2.29.3", "drizzle-kit": "^0.31.0", "file-saver": "^2.0.5", "firebase": "^10.14.1", "framer-motion": "^10.18.0", "fuse.js": "^7.1.0", "google-map-react": "^2.2.1", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.1", "lucide-react": "^0.503.0", "moment": "^2.30.1", "notistack": "^3.0.2", "papaparse": "^5.5.3", "pdf-lib": "^1.17.1", "prop-types": "^15.8.1", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-barcode": "^1.6.1", "react-day-picker": "^9.6.7", "react-dom": "^18.2.0", "react-error-boundary": "^5.0.0", "react-firebase-hooks": "^5.1.1", "react-hotkeys-hook": "^5.0.1", "react-i18next": "^15.4.1", "react-qr-code": "^2.0.15", "react-router-dom": "^6.30.0", "react-signature-canvas": "^1.1.0-alpha.2", "react-to-print": "^3.1.0", "react-toastify": "^9.1.3", "react-virtuoso": "^4.12.6", "recharts": "^2.10.4", "tailwind-variants": "^1.0.0", "wouter": "^3.7.0", "xlsx": "^0.18.5", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.3", "tailwindcss": "^3.3.5", "typescript": "^5.8.3", "vite": "^5.0.12"}}