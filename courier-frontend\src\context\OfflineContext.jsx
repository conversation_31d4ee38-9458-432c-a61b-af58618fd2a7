import React, { createContext, useContext, useState, useEffect } from "react";

const OfflineContext = createContext();

export const OfflineProvider = ({ children }) => {
  // Track online status
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  
  // Track pending operations that need to be synced when back online
  const [pendingOperations, setPendingOperations] = useState(() => {
    try {
      const savedOperations = localStorage.getItem('pendingOperations');
      return savedOperations ? JSON.parse(savedOperations) : [];
    } catch (err) {
      console.error('Error loading pending operations from localStorage:', err);
      return [];
    }
  });
  
  // Track last sync time
  const [lastSyncTime, setLastSyncTime] = useState(() => {
    try {
      const savedTime = localStorage.getItem('lastSyncTime');
      return savedTime ? new Date(savedTime) : null;
    } catch (err) {
      console.error('Error loading last sync time from localStorage:', err);
      return null;
    }
  });

  // Handle online/offline status changes
  useEffect(() => {
    const handleOnline = () => {
      console.log('App is online');
      setIsOnline(true);
      
      // Attempt to process pending operations when back online
      if (pendingOperations.length > 0) {
        console.log(`Found ${pendingOperations.length} pending operations to process`);
      }
    };
    
    const handleOffline = () => {
      console.log('App is offline');
      setIsOnline(false);
    };
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [pendingOperations.length]);
  
  // Save pending operations to localStorage whenever they change
  useEffect(() => {
    try {
      localStorage.setItem('pendingOperations', JSON.stringify(pendingOperations));
    } catch (err) {
      console.error('Error saving pending operations to localStorage:', err);
    }
  }, [pendingOperations]);
  
  // Save last sync time to localStorage whenever it changes
  useEffect(() => {
    if (lastSyncTime) {
      try {
        localStorage.setItem('lastSyncTime', lastSyncTime.toISOString());
      } catch (err) {
        console.error('Error saving last sync time to localStorage:', err);
      }
    }
  }, [lastSyncTime]);
  
  // Add a new operation to the pending queue
  const addPendingOperation = (operation) => {
    setPendingOperations(prev => [...prev, {
      ...operation,
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString()
    }]);
  };
  
  // Remove an operation from the pending queue
  const removePendingOperation = (operationId) => {
    setPendingOperations(prev => prev.filter(op => op.id !== operationId));
  };
  
  // Clear all pending operations
  const clearPendingOperations = () => {
    setPendingOperations([]);
  };
  
  // Update the last sync time
  const updateLastSyncTime = () => {
    const now = new Date();
    setLastSyncTime(now);
    return now;
  };
  
  return (
    <OfflineContext.Provider value={{
      isOnline,
      pendingOperations,
      lastSyncTime,
      addPendingOperation,
      removePendingOperation,
      clearPendingOperations,
      updateLastSyncTime
    }}>
      {children}
    </OfflineContext.Provider>
  );
};

export const useOffline = () => useContext(OfflineContext);

export default OfflineContext;
