/**
 * Enhanced SMS Service for the Courier Management System
 * Provides a unified interface for sending SMS and OTP messages
 * using 2Factor.in API
 */

import { doc, updateDoc, serverTimestamp, getDoc } from 'firebase/firestore';
import { db } from '../firebase';
import { validateIndianPhoneNumber } from './phoneUtils';

// 2Factor API key - In production, this should be stored in environment variables
const TWO_FACTOR_API_KEY = "c824e20b-2b69-11f0-8b17-0200cd936042";

// Rate limiting configuration
const RATE_LIMIT = {
  SMS_INTERVAL_SECONDS: 60, // Minimum time between SMS (1 minute)
  OTP_INTERVAL_SECONDS: 30,  // Minimum time between OTPs (30 seconds)
  DAILY_LIMIT: 50,           // Maximum SMS per day per user
};

// Store last sent timestamps for rate limiting
const lastSentTimestamps = {
  sms: new Map(),  // Map of phone numbers to timestamps
  otp: new Map(),  // Map of phone numbers to timestamps
  dailyCounts: new Map(), // Map of phone numbers to daily counts
  lastResetDate: new Date().toDateString() // Track when we last reset daily counts
};

/**
 * Reset daily counts if it's a new day
 */
const checkAndResetDailyCounts = () => {
  const today = new Date().toDateString();
  if (lastSentTimestamps.lastResetDate !== today) {
    lastSentTimestamps.dailyCounts.clear();
    lastSentTimestamps.lastResetDate = today;
  }
};

/**
 * Check if we can send an SMS to a phone number based on rate limits
 * @param {string} phoneNumber - The phone number
 * @param {string} type - The type of message ('sms' or 'otp')
 * @returns {Object} - Whether we can send and time remaining if not
 */
const canSendMessage = (phoneNumber, type = 'sms') => {
  checkAndResetDailyCounts();
  
  const now = Date.now();
  const timestamps = type === 'otp' ? lastSentTimestamps.otp : lastSentTimestamps.sms;
  const interval = type === 'otp' ? RATE_LIMIT.OTP_INTERVAL_SECONDS : RATE_LIMIT.SMS_INTERVAL_SECONDS;
  
  // Check daily limit
  const dailyCount = lastSentTimestamps.dailyCounts.get(phoneNumber) || 0;
  if (dailyCount >= RATE_LIMIT.DAILY_LIMIT) {
    return {
      canSend: false,
      timeRemaining: null,
      reason: 'Daily limit exceeded. Please try again tomorrow.'
    };
  }
  
  // Check time interval
  const lastSent = timestamps.get(phoneNumber) || 0;
  const timeSinceLastSent = (now - lastSent) / 1000; // in seconds
  
  if (timeSinceLastSent < interval) {
    return {
      canSend: false,
      timeRemaining: Math.ceil(interval - timeSinceLastSent),
      reason: `Please wait ${Math.ceil(interval - timeSinceLastSent)} seconds before sending another message.`
    };
  }
  
  return { canSend: true, timeRemaining: 0, reason: null };
};

/**
 * Update rate limiting after sending a message
 * @param {string} phoneNumber - The phone number
 * @param {string} type - The type of message ('sms' or 'otp')
 */
const updateRateLimiting = (phoneNumber, type = 'sms') => {
  const now = Date.now();
  const timestamps = type === 'otp' ? lastSentTimestamps.otp : lastSentTimestamps.sms;
  timestamps.set(phoneNumber, now);
  
  // Update daily count
  const dailyCount = lastSentTimestamps.dailyCounts.get(phoneNumber) || 0;
  lastSentTimestamps.dailyCounts.set(phoneNumber, dailyCount + 1);
};

/**
 * Generate a random 6-digit OTP
 * @returns {string} 6-digit OTP
 */
export const generateOtp = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

/**
 * Send an SMS using 2Factor.in API
 * @param {string} phoneNumber - The phone number to send the SMS to
 * @param {string} message - The message to send
 * @returns {Promise<Object>} - Result of the SMS sending operation
 */
export const sendSMS = async (phoneNumber, message) => {
  try {
    // Validate phone number
    const { isValid, formattedNumber, error } = validateIndianPhoneNumber(phoneNumber);
    if (!isValid) {
      return {
        success: false,
        error: `Invalid phone number: ${error}`,
        formattedPhone: null
      };
    }
    
    // Check rate limiting
    const rateLimit = canSendMessage(formattedNumber, 'sms');
    if (!rateLimit.canSend) {
      return {
        success: false,
        error: rateLimit.reason,
        formattedPhone: formattedNumber,
        timeRemaining: rateLimit.timeRemaining
      };
    }
    
    // Format phone number (remove +91 if present)
    const apiNumber = formattedNumber.replace(/^\+91/, '');
    
    // Create the API URL for sending SMS
    const apiUrl = `https://2factor.in/API/V1/${TWO_FACTOR_API_KEY}/SMS/${apiNumber}/AUTOGEN/${encodeURIComponent(message)}`;
    
    console.log(`Sending SMS to ${formattedNumber}: ${message.substring(0, 30)}...`);
    
    // Send the SMS
    const response = await fetch(apiUrl);
    const data = await response.json();
    
    // Update rate limiting
    updateRateLimiting(formattedNumber, 'sms');
    
    if (data.Status === 'Success') {
      console.log('SMS sent successfully');
      return {
        success: true,
        error: null,
        messageId: data.Details,
        formattedPhone: formattedNumber
      };
    } else {
      console.error('Failed to send SMS:', data.Details);
      return {
        success: false,
        error: data.Details || 'Failed to send SMS',
        formattedPhone: formattedNumber
      };
    }
  } catch (error) {
    console.error('Error sending SMS:', error);
    return {
      success: false,
      error: error.message || 'Failed to send SMS',
      formattedPhone: phoneNumber
    };
  }
};

/**
 * Send OTP via 2Factor.in API
 * @param {string} phoneNumber - Phone number to send OTP to
 * @param {string} method - Method to send OTP ('sms' or 'call')
 * @returns {Promise<Object>} Response from 2Factor API
 */
export const sendOTP = async (phoneNumber, method = 'sms') => {
  try {
    // Validate phone number
    const { isValid, formattedNumber, error } = validateIndianPhoneNumber(phoneNumber);
    if (!isValid) {
      return {
        success: false,
        error: `Invalid phone number: ${error}`,
        formattedPhone: null
      };
    }
    
    // Check rate limiting
    const rateLimit = canSendMessage(formattedNumber, 'otp');
    if (!rateLimit.canSend) {
      return {
        success: false,
        error: rateLimit.reason,
        formattedPhone: formattedNumber,
        timeRemaining: rateLimit.timeRemaining
      };
    }
    
    // Generate a 6-digit OTP
    const otp = generateOtp();
    
    // Format phone number (remove +91 if present)
    const apiNumber = formattedNumber.replace(/^\+91/, '');
    
    // Create the API URL based on the method
    let apiUrl;
    if (method.toLowerCase() === 'call') {
      // Voice call OTP
      apiUrl = `https://2factor.in/API/V1/${TWO_FACTOR_API_KEY}/VOICE/${apiNumber}/${otp}`;
      console.log(`Sending OTP ${otp} to ${formattedNumber} via 2Factor Voice Call API`);
    } else {
      // SMS OTP (default)
      apiUrl = `https://2factor.in/API/V1/${TWO_FACTOR_API_KEY}/SMS/${apiNumber}/${otp}/OTPSEND`;
      console.log(`Sending OTP ${otp} to ${formattedNumber} via 2Factor SMS API`);
    }
    
    // Make the API request
    const response = await fetch(apiUrl);
    const data = await response.json();
    
    // Update rate limiting
    updateRateLimiting(formattedNumber, 'otp');
    
    if (data.Status === 'Success') {
      console.log(`2Factor OTP sent successfully via ${method}`);
      return {
        success: true,
        error: null,
        sessionId: data.Details,
        formattedPhone: formattedNumber,
        method: method,
        otp: otp // Include OTP in development, remove in production
      };
    } else {
      console.error(`Failed to send 2Factor OTP via ${method}:`, data.Details);
      return {
        success: false,
        error: data.Details || 'Failed to send OTP',
        formattedPhone: formattedNumber,
        method: method
      };
    }
  } catch (error) {
    console.error(`Error sending OTP via ${method}:`, error);
    return {
      success: false,
      error: error.message || 'Failed to send OTP',
      formattedPhone: phoneNumber,
      method: method
    };
  }
};
