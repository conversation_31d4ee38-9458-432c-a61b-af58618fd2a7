import React from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import DoneIcon from '@mui/icons-material/Done';
import DoneAllIcon from '@mui/icons-material/DoneAll';

/**
 * Component to display read receipts for chat messages
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.read - Whether the message has been read
 * @param {boolean} props.delivered - Whether the message has been delivered
 * @param {string} props.color - Color of the receipt icons
 * @param {Object} props.sx - Additional styles
 */
const ReadReceipt = ({ read, delivered = true, color = 'primary', sx = {} }) => {
  const tooltipTitle = read 
    ? 'Read' 
    : delivered 
      ? 'Delivered' 
      : 'Sent';

  return (
    <Tooltip title={tooltipTitle} arrow>
      <Box 
        component="span" 
        sx={{ 
          display: 'inline-flex',
          alignItems: 'center',
          ...sx 
        }}
      >
        {read ? (
          <DoneAllIcon 
            sx={{ 
              fontSize: 'inherit', 
              color: `${color}.main`,
              transform: 'scale(0.9)'
            }} 
          />
        ) : (
          <DoneIcon 
            sx={{ 
              fontSize: 'inherit',
              color: 'text.secondary',
              opacity: 0.7,
              transform: 'scale(0.9)'
            }} 
          />
        )}
      </Box>
    </Tooltip>
  );
};

export default ReadReceipt;
