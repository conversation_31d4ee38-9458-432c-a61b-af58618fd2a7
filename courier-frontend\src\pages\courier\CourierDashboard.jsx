import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  CircularProgress,
  Alert,
  Chip,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  LocalShipping as ShippingIcon,
  CheckCircle as CompletedIcon,
  PendingActions as PendingIcon,
  DirectionsBike as BikeIcon,
  AccessTime as TimeIcon,
  AttachMoney as MoneyIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from '../../firebase';
import { useAuth } from '../../context/AuthContext';
import { format } from 'date-fns';
import CourierLayout from '../../components/layout/CourierLayout';
import LocationTrackingPanel from '../../components/courier/LocationTrackingPanel';
import LocationIntegration from '../../components/location/LocationIntegration';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Line,
  ComposedChart
} from 'recharts';

const CourierDashboard = () => {
  const { user } = useAuth();
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [stats, setStats] = useState({
    pendingDeliveries: 0,
    completedDeliveries: 0,
    totalEarnings: 0,
    todayDeliveries: 0
  });
  const [recentDeliveries, setRecentDeliveries] = useState([]);
  const [performanceData, setPerformanceData] = useState([]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user) return;

      setLoading(true);
      setError('');

      try {
        // Fetch courier's deliveries
        const deliveriesRef = collection(db, 'orders');

        // Try different field names for courier assignment
        const pendingQuery1 = query(
          deliveriesRef,
          where('assignedCourierId', '==', user.uid),
          where('status', 'in', ['pending', 'processing', 'in_transit'])
        );

        const pendingQuery2 = query(
          deliveriesRef,
          where('courierId', '==', user.uid),
          where('status', 'in', ['pending', 'processing', 'in_transit'])
        );

        const completedQuery1 = query(
          deliveriesRef,
          where('assignedCourierId', '==', user.uid),
          where('status', '==', 'delivered')
        );

        const completedQuery2 = query(
          deliveriesRef,
          where('courierId', '==', user.uid),
          where('status', '==', 'delivered')
        );

        // Get pending deliveries
        let pendingDeliveries = [];
        try {
          const pendingSnapshot1 = await getDocs(pendingQuery1);
          pendingDeliveries = pendingSnapshot1.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
        } catch (err) {
          console.error('Error fetching pending deliveries with assignedCourierId:', err);
        }

        // If no results, try alternative field
        if (pendingDeliveries.length === 0) {
          try {
            const pendingSnapshot2 = await getDocs(pendingQuery2);
            pendingDeliveries = pendingSnapshot2.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));
          } catch (err) {
            console.error('Error fetching pending deliveries with courierId:', err);
          }
        }

        // Get completed deliveries
        let completedDeliveries = [];
        try {
          const completedSnapshot1 = await getDocs(completedQuery1);
          completedDeliveries = completedSnapshot1.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
        } catch (err) {
          console.error('Error fetching completed deliveries with assignedCourierId:', err);
        }

        // If no results, try alternative field
        if (completedDeliveries.length === 0) {
          try {
            const completedSnapshot2 = await getDocs(completedQuery2);
            completedDeliveries = completedSnapshot2.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));
          } catch (err) {
            console.error('Error fetching completed deliveries with courierId:', err);
          }
        }

        // Get recent deliveries
        const recentQuery = query(
          deliveriesRef,
          where('assignedCourierId', '==', user.uid),
          orderBy('createdAt', 'desc'),
          limit(5)
        );

        let recentDeliveriesData = [];
        try {
          const recentSnapshot = await getDocs(recentQuery);
          recentDeliveriesData = recentSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            createdAt: doc.data().createdAt?.toDate() || new Date()
          }));
        } catch (err) {
          console.error('Error fetching recent deliveries:', err);

          // Try alternative field
          try {
            const recentQuery2 = query(
              deliveriesRef,
              where('courierId', '==', user.uid),
              orderBy('createdAt', 'desc'),
              limit(5)
            );

            const recentSnapshot2 = await getDocs(recentQuery2);
            recentDeliveriesData = recentSnapshot2.docs.map(doc => ({
              id: doc.id,
              ...doc.data(),
              createdAt: doc.data().createdAt?.toDate() || new Date()
            }));
          } catch (err2) {
            console.error('Error fetching recent deliveries with alternative field:', err2);
          }
        }

        // Calculate total earnings
        const totalEarnings = completedDeliveries.reduce((sum, delivery) => {
          return sum + (delivery.courierPayment || 0);
        }, 0);

        // Calculate today's deliveries
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const todayDeliveries = completedDeliveries.filter(delivery => {
          const deliveryDate = delivery.deliveredAt?.toDate() || new Date(0);
          deliveryDate.setHours(0, 0, 0, 0);
          return deliveryDate.getTime() === today.getTime();
        }).length;

        // Generate performance data for custom chart
        const last7Days = Array.from({ length: 7 }, (_, i) => {
          const date = new Date();
          date.setDate(date.getDate() - i);
          return date;
        }).reverse();

        // Prepare data for our custom chart
        const chartData = last7Days.map(date => {
          const startOfDay = new Date(date);
          startOfDay.setHours(0, 0, 0, 0);

          const endOfDay = new Date(date);
          endOfDay.setHours(23, 59, 59, 999);

          const deliveries = completedDeliveries.filter(delivery => {
            const deliveryDate = delivery.deliveredAt?.toDate() || new Date(0);
            return deliveryDate >= startOfDay && deliveryDate <= endOfDay;
          });

          const count = deliveries.length;
          const earnings = deliveries.reduce((sum, delivery) => sum + (delivery.courierPayment || 0), 0);

          return {
            date: format(date, 'MMM dd'),
            count,
            earnings
          };
        });

        // Update state
        setStats({
          pendingDeliveries: pendingDeliveries.length,
          completedDeliveries: completedDeliveries.length,
          totalEarnings,
          todayDeliveries
        });

        setRecentDeliveries(recentDeliveriesData);
        setPerformanceData(chartData);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again.');
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user, theme.palette.primary.main, theme.palette.success.main]);

  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  return (
    <CourierLayout>
      <LocationIntegration
        autoStart={true}
        showNotifications={true}
        onLocationUpdate={(location) => {
          console.log('Courier location updated:', location);
        }}
        onError={(error) => {
          console.error('Location tracking error:', error);
        }}
      >
        <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 3 } }}>
          <Typography variant="h4" gutterBottom sx={{
            fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' },
            mb: { xs: 2, sm: 3 }
          }}>
            Courier Dashboard
          </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 5 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={{ xs: 2, sm: 3 }}>
            {/* Stats Cards */}
            <Grid item xs={6} sm={6} md={3}>
              <Card sx={{
                height: '100%',
                boxShadow: theme.palette.mode === 'dark'
                  ? '0 4px 8px rgba(0,0,0,0.4)'
                  : '0 2px 8px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.palette.mode === 'dark'
                    ? '0 6px 12px rgba(0,0,0,0.5)'
                    : '0 4px 12px rgba(0,0,0,0.15)',
                }
              }}>
                <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
                  <Typography color="textSecondary" gutterBottom sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                    Pending Deliveries
                  </Typography>
                  <Typography variant="h4" component="div" sx={{ fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' } }}>
                    {stats.pendingDeliveries}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    <PendingIcon color="warning" fontSize="small" sx={{ mr: 0.5 }} />
                    <Typography variant="body2" color="textSecondary" sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                      Awaiting delivery
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={6} sm={6} md={3}>
              <Card sx={{
                height: '100%',
                boxShadow: theme.palette.mode === 'dark'
                  ? '0 4px 8px rgba(0,0,0,0.4)'
                  : '0 2px 8px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.palette.mode === 'dark'
                    ? '0 6px 12px rgba(0,0,0,0.5)'
                    : '0 4px 12px rgba(0,0,0,0.15)',
                }
              }}>
                <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
                  <Typography color="textSecondary" gutterBottom sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                    Completed Deliveries
                  </Typography>
                  <Typography variant="h4" component="div" sx={{ fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' } }}>
                    {stats.completedDeliveries}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    <CompletedIcon color="success" fontSize="small" sx={{ mr: 0.5 }} />
                    <Typography variant="body2" color="textSecondary" sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                      Total completed
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={6} sm={6} md={3}>
              <Card sx={{
                height: '100%',
                boxShadow: theme.palette.mode === 'dark'
                  ? '0 4px 8px rgba(0,0,0,0.4)'
                  : '0 2px 8px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.palette.mode === 'dark'
                    ? '0 6px 12px rgba(0,0,0,0.5)'
                    : '0 4px 12px rgba(0,0,0,0.15)',
                }
              }}>
                <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
                  <Typography color="textSecondary" gutterBottom sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                    Today's Deliveries
                  </Typography>
                  <Typography variant="h4" component="div" sx={{ fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' } }}>
                    {stats.todayDeliveries}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    <BikeIcon color="info" fontSize="small" sx={{ mr: 0.5 }} />
                    <Typography variant="body2" color="textSecondary" sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                      Completed today
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={6} sm={6} md={3}>
              <Card sx={{
                height: '100%',
                boxShadow: theme.palette.mode === 'dark'
                  ? '0 4px 8px rgba(0,0,0,0.4)'
                  : '0 2px 8px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.palette.mode === 'dark'
                    ? '0 6px 12px rgba(0,0,0,0.5)'
                    : '0 4px 12px rgba(0,0,0,0.15)',
                }
              }}>
                <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
                  <Typography color="textSecondary" gutterBottom sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                    Total Earnings
                  </Typography>
                  <Typography variant="h4" component="div" sx={{ fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' } }}>
                    ₹{stats.totalEarnings.toFixed(2)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    <MoneyIcon color="success" fontSize="small" sx={{ mr: 0.5 }} />
                    <Typography variant="body2" color="textSecondary" sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                      Lifetime earnings
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Performance Chart */}
            <Grid item xs={12} md={8}>
              <Paper sx={{ p: { xs: 1.5, sm: 2, md: 3 } }}>
                <Typography variant="h6" gutterBottom>
                  Performance Overview
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Box sx={{
                  height: { xs: 250, sm: 300, md: 350 },
                  mt: 2,
                  width: '100%',
                  overflow: 'hidden'
                }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart
                      data={performanceData}
                      margin={{
                        top: 20,
                        right: isMobile ? 10 : 30,
                        left: isMobile ? 5 : 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="date"
                        tick={{ fontSize: isMobile ? 10 : 12 }}
                        tickFormatter={isMobile ? (value) => value.split(' ')[0] : undefined}
                      />
                      <YAxis
                        yAxisId="left"
                        orientation="left"
                        label={isMobile ? null : { value: 'Deliveries', angle: -90, position: 'insideLeft' }}
                        tick={{ fontSize: isMobile ? 10 : 12 }}
                      />
                      <YAxis
                        yAxisId="right"
                        orientation="right"
                        label={isMobile ? null : { value: 'Earnings (₹)', angle: 90, position: 'insideRight' }}
                        tick={{ fontSize: isMobile ? 10 : 12 }}
                      />
                      <Tooltip
                        formatter={(value, name) => {
                          if (name === 'earnings') return [`₹${value.toFixed(2)}`, 'Earnings'];
                          return [value, 'Deliveries'];
                        }}
                      />
                      <Legend wrapperStyle={{ fontSize: isMobile ? 10 : 12 }} />
                      <Bar yAxisId="left" dataKey="count" name="Deliveries" fill={theme.palette.primary.main} barSize={isMobile ? 15 : 20} />
                      <Line yAxisId="right" type="monotone" dataKey="earnings" name="Earnings (₹)" stroke={theme.palette.success.main} />
                    </ComposedChart>
                  </ResponsiveContainer>
                </Box>
              </Paper>
            </Grid>

            {/* Location Tracking Panel */}
            <Grid item xs={12} md={4}>
              <LocationTrackingPanel />
            </Grid>

            {/* Recent Deliveries */}
            <Grid item xs={12} md={8}>
              <Paper sx={{ p: { xs: 1.5, sm: 2, md: 3 } }}>
                <Typography variant="h6" gutterBottom>
                  Recent Deliveries
                </Typography>
                <Divider sx={{ mb: 2 }} />
                {recentDeliveries.length > 0 ? (
                  <List sx={{
                    maxHeight: { xs: 300, sm: 350, md: 400 },
                    overflow: 'auto',
                    '&::-webkit-scrollbar': {
                      width: '4px',
                    },
                    '&::-webkit-scrollbar-track': {
                      background: 'transparent',
                    },
                    '&::-webkit-scrollbar-thumb': {
                      background: theme.palette.divider,
                      borderRadius: '4px',
                    },
                  }}>
                    {recentDeliveries.map((delivery) => (
                      <ListItem
                        key={delivery.id}
                        component={Link}
                        to={`/courier/deliveries/${delivery.id}`}
                        sx={{
                          textDecoration: 'none',
                          color: 'inherit',
                          py: { xs: 1, sm: 1.5 },
                          px: { xs: 1, sm: 2 },
                          mb: 0.5,
                          '&:hover': {
                            bgcolor: 'action.hover',
                            borderRadius: 1
                          }
                        }}
                      >
                        <ListItemIcon sx={{ minWidth: { xs: 36, sm: 40 } }}>
                          <ShippingIcon
                            color={delivery.status === 'delivered' ? 'success' : 'primary'}
                            fontSize={isMobile ? 'small' : 'medium'}
                          />
                        </ListItemIcon>
                        <ListItemText
                          primary={`Order #${delivery.orderNumber || delivery.id.slice(0, 8)}`}
                          secondary={
                            <>
                              <Typography variant="body2" component="span" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                                {delivery.customerName || 'Customer'}
                              </Typography>
                              <Typography component="div" variant="body2" sx={{ mt: 0.5 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                  <TimeIcon fontSize="small" />
                                  <Typography variant="caption">
                                    {delivery.createdAt && delivery.createdAt instanceof Date && !isNaN(delivery.createdAt)
                                      ? format(delivery.createdAt, 'MMM dd, yyyy')
                                      : 'N/A'}
                                  </Typography>
                                </Box>
                              </Typography>
                            </>
                          }
                          primaryTypographyProps={{
                            fontWeight: 500,
                            fontSize: { xs: '0.875rem', sm: '1rem' }
                          }}
                          secondaryTypographyProps={{ component: 'div' }}
                        />
                        <Chip
                          size="small"
                          label={delivery.status}
                          color={
                            delivery.status === 'delivered' ? 'success' :
                            delivery.status === 'in_transit' ? 'primary' :
                            'warning'
                          }
                          sx={{
                            height: { xs: 20, sm: 24 },
                            fontSize: { xs: '0.625rem', sm: '0.75rem' }
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 3 }}>
                    <Typography color="textSecondary">
                      No recent deliveries found
                    </Typography>
                  </Box>
                )}
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                  <Button
                    component={Link}
                    to="/courier/deliveries"
                    variant="outlined"
                    endIcon={<ShippingIcon />}
                    size={isMobile ? "small" : "medium"}
                    sx={{ borderRadius: 2 }}
                  >
                    View All Deliveries
                  </Button>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        )}
        </Container>
      </LocationIntegration>
    </CourierLayout>
  );
};

export default CourierDashboard;
