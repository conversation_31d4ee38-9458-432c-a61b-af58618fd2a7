// Enhanced Order Details with Print Functionality
import React, { useState, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  Divider,
  IconButton,
  Tooltip,
  Chip
} from '@mui/material';
import {
  Print,
  Download,
  Share,
  LocationOn,
  Person,
  Package,
  Payment,
  Schedule,
  LocalShipping
} from '@mui/icons-material';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../../firebase';
import { toast } from 'react-toastify';

const OrderDetails = () => {
  const [trackingNumber, setTrackingNumber] = useState('');
  const [orderDetails, setOrderDetails] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const printRef = useRef();

  // Print functionality
  const handlePrint = () => {
    if (!orderDetails) {
      toast.error('No order details to print');
      return;
    }

    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();

    toast.success('Print dialog opened');
  };

  // Generate print content
  const generatePrintContent = () => {
    const currentDate = new Date().toLocaleString();

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Order Bill - ${orderDetails.trackingId || orderDetails.orderNumber}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
            .header { text-align: center; border-bottom: 2px solid #1976d2; padding-bottom: 20px; margin-bottom: 30px; }
            .company-name { font-size: 24px; font-weight: bold; color: #1976d2; margin-bottom: 5px; }
            .company-tagline { font-size: 14px; color: #666; }
            .bill-title { font-size: 20px; font-weight: bold; margin: 20px 0; }
            .section { margin-bottom: 25px; }
            .section-title { font-size: 16px; font-weight: bold; color: #1976d2; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
            .detail-row { display: flex; justify-content: space-between; margin-bottom: 8px; }
            .detail-label { font-weight: bold; color: #555; }
            .detail-value { color: #333; }
            .cost-summary { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-top: 20px; }
            .total-amount { font-size: 18px; font-weight: bold; color: #1976d2; }
            .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }
            .status-chip { display: inline-block; padding: 4px 12px; border-radius: 16px; font-size: 12px; font-weight: bold; }
            .status-pending { background-color: #fff3cd; color: #856404; }
            .status-processing { background-color: #cce5ff; color: #004085; }
            .status-delivered { background-color: #d4edda; color: #155724; }
            .status-cancelled { background-color: #f8d7da; color: #721c24; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="company-name">🚚 FastCourier Pro</div>
            <div class="company-tagline">Fast, Reliable, Secure Delivery Services</div>
          </div>

          <div class="bill-title">📋 ORDER BILL & RECEIPT</div>

          <div class="section">
            <div class="section-title">📦 Order Information</div>
            <div class="detail-row">
              <span class="detail-label">Order Number:</span>
              <span class="detail-value">${orderDetails.orderNumber || orderDetails.trackingId || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Tracking ID:</span>
              <span class="detail-value">${orderDetails.trackingId || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Order Date:</span>
              <span class="detail-value">${orderDetails.createdAt || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Status:</span>
              <span class="detail-value">
                <span class="status-chip status-${orderDetails.status}">
                  ${orderDetails.status?.toUpperCase() || 'PENDING'}
                </span>
              </span>
            </div>
          </div>

          <div class="section">
            <div class="section-title">👤 Customer Information</div>
            <div class="detail-row">
              <span class="detail-label">Name:</span>
              <span class="detail-value">${orderDetails.customerName || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Phone:</span>
              <span class="detail-value">${orderDetails.customerPhone || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Email:</span>
              <span class="detail-value">${orderDetails.customerEmail || 'N/A'}</span>
            </div>
          </div>

          <div class="section">
            <div class="section-title">🛍️ Product Details</div>
            <div class="detail-row">
              <span class="detail-label">Product Name:</span>
              <span class="detail-value">${orderDetails.productName || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Quantity:</span>
              <span class="detail-value">${orderDetails.quantity || 1}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Unit Price:</span>
              <span class="detail-value">₹${orderDetails.unitPrice || orderDetails.amount || '0.00'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Package Weight:</span>
              <span class="detail-value">${orderDetails.packageWeight || 'N/A'} kg</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Package Type:</span>
              <span class="detail-value">${orderDetails.packageType || 'Standard'}</span>
            </div>
          </div>

          <div class="section">
            <div class="section-title">📍 Delivery Information</div>
            <div class="detail-row">
              <span class="detail-label">Pickup Address:</span>
              <span class="detail-value">${orderDetails.pickupAddress || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Delivery Address:</span>
              <span class="detail-value">${orderDetails.deliveryAddress || 'N/A'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Priority:</span>
              <span class="detail-value">${orderDetails.priority?.toUpperCase() || 'STANDARD'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Estimated Distance:</span>
              <span class="detail-value">${orderDetails.estimatedDistance || 'N/A'} km</span>
            </div>
          </div>

          <div class="cost-summary">
            <div class="section-title">💰 Cost Breakdown</div>
            <div class="detail-row">
              <span class="detail-label">Product Value:</span>
              <span class="detail-value">₹${orderDetails.totalAmount || orderDetails.amount || '0.00'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Delivery Charges:</span>
              <span class="detail-value">₹${orderDetails.estimatedCost || '0.00'}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Payment Method:</span>
              <span class="detail-value">${orderDetails.paymentMethod?.toUpperCase() || 'CASH'}</span>
            </div>
            <hr style="margin: 15px 0;">
            <div class="detail-row total-amount">
              <span class="detail-label">TOTAL AMOUNT:</span>
              <span class="detail-value">₹${orderDetails.finalAmount || orderDetails.amount || '0.00'}</span>
            </div>
          </div>

          <div class="footer">
            <p>Thank you for choosing FastCourier Pro!</p>
            <p>For support, contact <NAME_EMAIL> | +91-1234567890</p>
            <p>Generated on: ${currentDate}</p>
          </div>
        </body>
      </html>
    `;
  };

  // Share functionality
  const handleShare = async () => {
    if (!orderDetails) return;

    const shareData = {
      title: `Order ${orderDetails.trackingId || orderDetails.orderNumber}`,
      text: `Track your order: ${orderDetails.trackingId || orderDetails.orderNumber}`,
      url: window.location.href
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
        toast.success('Order details shared successfully');
      } catch (error) {
        console.error('Error sharing:', error);
        copyToClipboard();
      }
    } else {
      copyToClipboard();
    }
  };

  const copyToClipboard = () => {
    const trackingInfo = `Order: ${orderDetails.trackingId || orderDetails.orderNumber}\nStatus: ${orderDetails.status}\nTrack at: ${window.location.href}`;
    navigator.clipboard.writeText(trackingInfo).then(() => {
      toast.success('Order details copied to clipboard');
    }).catch(() => {
      toast.error('Failed to copy to clipboard');
    });
  };

  const handleTrack = async (e) => {
    e.preventDefault();
    if (!trackingNumber) return;

    setLoading(true);
    setError('');
    setOrderDetails(null);

    try {
      const ordersRef = collection(db, 'orders');

      // Try to find by trackingNumber first
      let q = query(ordersRef, where('trackingNumber', '==', trackingNumber));
      let querySnapshot = await getDocs(q);

      // If not found, try to find by orderNumber (which might be numeric)
      if (querySnapshot.empty) {
        // Try to parse as number if it's numeric
        const numericTracking = !isNaN(trackingNumber) ? parseInt(trackingNumber) : trackingNumber;
        q = query(ordersRef, where('orderNumber', '==', numericTracking));
        querySnapshot = await getDocs(q);
      }

      // If still not found, try one more field
      if (querySnapshot.empty) {
        q = query(ordersRef, where('trackingId', '==', trackingNumber));
        querySnapshot = await getDocs(q);
      }

      if (querySnapshot.empty) {
        setError('No order found with this tracking number');
        return;
      }

      const orderData = {
        id: querySnapshot.docs[0].id,
        ...querySnapshot.docs[0].data(),
        createdAt: querySnapshot.docs[0].data().createdAt?.toDate().toLocaleString() || 'N/A'
      };

      setOrderDetails(orderData);
    } catch (error) {
      console.error('Error tracking order:', error);
      setError('Failed to track order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getSteps = () => {
    const steps = [
      { label: 'Order Created', description: 'Order has been placed' },
      { label: 'Processing', description: 'Order is being processed' },
      { label: 'In Transit', description: 'Order is out for delivery' },
      { label: 'Delivered', description: 'Order has been delivered' }
    ];

    const statusIndex = {
      pending: 0,
      processing: 1,
      'in-transit': 2,
      'in_transit': 2,
      'intransit': 2,
      'shipped': 2,
      'shipping': 2,
      'on_the_way': 2,
      'out_for_delivery': 2,
      delivered: 3,
      cancelled: -1
    };

    return {
      steps,
      activeStep: orderDetails ? statusIndex[orderDetails.status] : -1
    };
  };

  const { steps, activeStep } = getSteps();

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h5" gutterBottom>
          Track Order
        </Typography>

        <form onSubmit={handleTrack}>
          <Box sx={{ display: 'flex', gap: 2, mb: 4 }}>
            <TextField
              fullWidth
              label="Tracking Number"
              value={trackingNumber}
              onChange={(e) => setTrackingNumber(e.target.value)}
              placeholder="Enter tracking number"
            />
            <Button
              variant="contained"
              type="submit"
              disabled={loading || !trackingNumber}
              sx={{ minWidth: 120 }}
            >
              {loading ? <CircularProgress size={24} /> : 'Track'}
            </Button>
          </Box>
        </form>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {orderDetails && (
          <Box>
            {/* Header with Action Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">
                📦 Order Details
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Tooltip title="Print Bill">
                  <IconButton onClick={handlePrint} color="primary">
                    <Print />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Share Order">
                  <IconButton onClick={handleShare} color="primary">
                    <Share />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>

            {/* Enhanced Order Information Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {/* Order Info Card */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Package color="primary" />
                      Order Information
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Order Number:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {orderDetails.orderNumber || orderDetails.trackingId || 'N/A'}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Tracking ID:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {orderDetails.trackingId || 'N/A'}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Created:</Typography>
                      <Typography variant="body2">{orderDetails.createdAt}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Status:</Typography>
                      <Chip
                        label={orderDetails.status?.toUpperCase() || 'PENDING'}
                        color={
                          orderDetails.status === 'delivered' ? 'success' :
                          orderDetails.status === 'processing' ? 'primary' :
                          orderDetails.status === 'cancelled' ? 'error' : 'default'
                        }
                        size="small"
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Customer Info Card */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Person color="primary" />
                      Customer Information
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Name:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {orderDetails.customerName || 'N/A'}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Phone:</Typography>
                      <Typography variant="body2">{orderDetails.customerPhone || 'N/A'}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Email:</Typography>
                      <Typography variant="body2">{orderDetails.customerEmail || 'N/A'}</Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Product Details Card */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LocalShipping color="primary" />
                      Product Details
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Product:</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {orderDetails.productName || 'N/A'}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Quantity:</Typography>
                      <Typography variant="body2">{orderDetails.quantity || 1}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Weight:</Typography>
                      <Typography variant="body2">{orderDetails.packageWeight || 'N/A'} kg</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Type:</Typography>
                      <Typography variant="body2">{orderDetails.packageType || 'Standard'}</Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Payment Info Card */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Payment color="primary" />
                      Payment Information
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Product Value:</Typography>
                      <Typography variant="body2">₹{orderDetails.totalAmount || orderDetails.amount || '0.00'}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Delivery Charges:</Typography>
                      <Typography variant="body2">₹{orderDetails.estimatedCost || '0.00'}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Payment Method:</Typography>
                      <Typography variant="body2">{orderDetails.paymentMethod?.toUpperCase() || 'CASH'}</Typography>
                    </Box>
                    <Divider sx={{ my: 1 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body1" fontWeight="bold" color="primary">Total Amount:</Typography>
                      <Typography variant="body1" fontWeight="bold" color="primary">
                        ₹{orderDetails.finalAmount || orderDetails.amount || '0.00'}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Delivery Information Card */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LocationOn color="primary" />
                      Delivery Information
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>Pickup Address:</Typography>
                        <Typography variant="body2" sx={{ mb: 2 }}>
                          {orderDetails.pickupAddress || 'N/A'}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>Delivery Address:</Typography>
                        <Typography variant="body2" sx={{ mb: 2 }}>
                          {orderDetails.deliveryAddress || 'N/A'}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="body2" color="text.secondary">Priority:</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {orderDetails.priority?.toUpperCase() || 'STANDARD'}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="body2" color="text.secondary">Distance:</Typography>
                        <Typography variant="body2">{orderDetails.estimatedDistance || 'N/A'} km</Typography>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="body2" color="text.secondary">Estimated Delivery:</Typography>
                        <Typography variant="body2">
                          {orderDetails.priority === 'urgent' ? '2-4 hours' :
                           orderDetails.priority === 'express' ? 'Same day' : '1-2 days'}
                        </Typography>
                      </Grid>
                    </Grid>
                    {orderDetails.deliveryInstructions && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2" color="text.secondary">Special Instructions:</Typography>
                        <Typography variant="body2">{orderDetails.deliveryInstructions}</Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            <Stepper activeStep={activeStep} orientation="vertical">
              {steps.map((step, index) => (
                <Step key={step.label}>
                  <StepLabel>
                    {step.label}
                  </StepLabel>
                  <StepContent>
                    <Typography>{step.description}</Typography>
                    {index === activeStep && orderDetails.status !== 'cancelled' && (
                      <Typography variant="caption" color="text.secondary">
                        Current Status
                      </Typography>
                    )}
                  </StepContent>
                </Step>
              ))}
            </Stepper>

            {orderDetails.status === 'cancelled' && (
              <Alert severity="error" sx={{ mt: 2 }}>
                This order has been cancelled
              </Alert>
            )}
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default OrderDetails;
