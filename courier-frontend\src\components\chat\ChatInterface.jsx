import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  IconButton,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  CircularProgress,
  Badge,
  Tooltip,
  alpha,
  Chip
} from '@mui/material';
import {
  Send,
  AttachFile,
  InsertEmoticon,
  MoreVert,
  Search,
  ArrowBack,
  Person,
  PersonAdd,
  Refresh,
  Delete,
  NotificationsOff,
  Image,
  Close
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { useAuth } from '../../context/AuthContext';
import { motion } from 'framer-motion';
import {
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  addDoc,
  serverTimestamp,
  onSnapshot,
  doc,
  updateDoc,
  getDoc,
  setDoc
} from 'firebase/firestore';
import { db } from '../../firebase';
import { formatDistanceToNow } from 'date-fns';
import UserStatus from '../common/UserStatus';
import ReadReceipt from './ReadReceipt';

const ChatInterface = ({ recipientId, recipientName, recipientRole, onClose }) => {
  const { user } = useAuth();
  const theme = useTheme();
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState('');
  const [contacts, setContacts] = useState([]);
  const [selectedContact, setSelectedContact] = useState(null);
  const [showContacts, setShowContacts] = useState(!recipientId);
  const messagesEndRef = useRef(null);
  const unsubscribeRef = useRef(null);

  // Scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Load contacts based on user role
  useEffect(() => {
    const loadContacts = async () => {
      if (!user) return;

      try {
        let contactsQuery;

        if (user.role === 'admin') {
          // Admin sees all couriers
          contactsQuery = query(
            collection(db, 'users'),
            where('role', '==', 'courier')
          );
        } else {
          // Couriers see admins
          contactsQuery = query(
            collection(db, 'users'),
            where('role', '==', 'admin')
          );
        }

        const snapshot = await getDocs(contactsQuery);

        if (snapshot.empty) {
          // Try alternative collection if no results
          const altQuery = user.role === 'admin'
            ? query(collection(db, 'couriers'))
            : query(collection(db, 'admins'));

          const altSnapshot = await getDocs(altQuery);

          if (!altSnapshot.empty) {
            const contactsList = altSnapshot.docs.map(doc => ({
              id: doc.id,
              name: doc.data().name || doc.data().displayName || doc.data().email || `User ${doc.id.substring(0, 5)}`,
              ...doc.data()
            }));
            setContacts(contactsList);
          } else {
            setContacts([]);
          }
        } else {
          const contactsList = snapshot.docs.map(doc => ({
            id: doc.id,
            name: doc.data().name || doc.data().displayName || doc.data().email || `User ${doc.id.substring(0, 5)}`,
            ...doc.data()
          }));
          setContacts(contactsList);
        }
      } catch (err) {
        console.error('Error loading contacts:', err);
        setError('Failed to load contacts');
      }
    };

    loadContacts();
  }, [user]);

  // Set initial recipient if provided
  useEffect(() => {
    if (recipientId && recipientName) {
      setSelectedContact({
        id: recipientId,
        name: recipientName,
        role: recipientRole || (user?.role === 'admin' ? 'courier' : 'admin')
      });
      setShowContacts(false);
    }
  }, [recipientId, recipientName, recipientRole, user]);

  // Load messages when a contact is selected
  useEffect(() => {
    if (!user || !selectedContact) return;

    setLoading(true);
    setError('');

    // Clean up previous listener
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
    }

    // Create chat ID (combination of both user IDs, alphabetically sorted)
    const chatId = [user.uid, selectedContact.id].sort().join('_');

    // Set up real-time listener for messages
    try {
      const messagesQuery = query(
        collection(db, 'chats', chatId, 'messages'),
        orderBy('timestamp', 'asc'),
        limit(100)
      );

      unsubscribeRef.current = onSnapshot(
        messagesQuery,
        (snapshot) => {
          const messagesList = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            timestamp: doc.data().timestamp?.toDate() || new Date()
          }));

          setMessages(messagesList);
          setLoading(false);
        },
        (err) => {
          console.error('Error in messages listener:', err);
          setError('Failed to load messages');
          setLoading(false);
        }
      );
    } catch (err) {
      console.error('Error setting up messages listener:', err);
      setError('Failed to load messages');
      setLoading(false);
    }

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [selectedContact, user]);

  // Send a new message
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !user || !selectedContact) return;

    setSending(true);

    try {
      // Create chat ID (combination of both user IDs, alphabetically sorted)
      const chatId = [user.uid, selectedContact.id].sort().join('_');

      // Check if chat document exists, create if not
      const chatDocRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatDocRef);

      if (!chatDoc.exists()) {
        // Use setDoc instead of updateDoc for new documents
        await setDoc(chatDocRef, {
          participants: [user.uid, selectedContact.id],
          createdAt: serverTimestamp(),
          lastMessage: newMessage,
          lastMessageTimestamp: serverTimestamp()
        });
      } else {
        // Update last message
        await updateDoc(chatDocRef, {
          lastMessage: newMessage,
          lastMessageTimestamp: serverTimestamp()
        });
      }

      // Add message to subcollection
      await addDoc(collection(db, 'chats', chatId, 'messages'), {
        senderId: user.uid,
        senderName: user.displayName || user.email || 'User',
        recipientId: selectedContact.id,
        text: newMessage,
        timestamp: serverTimestamp(),
        read: false
      });

      setNewMessage('');
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Failed to send message');
    } finally {
      setSending(false);
    }
  };

  // Handle contact selection
  const handleSelectContact = (contact) => {
    setSelectedContact(contact);
    setShowContacts(false);
  };

  // Format timestamp
  const formatMessageTime = (timestamp) => {
    if (!timestamp) return '';
    return formatDistanceToNow(timestamp, { addSuffix: true });
  };

  return (
    <Paper
      elevation={3}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        overflow: 'hidden',
        borderRadius: 2
      }}
    >
      {/* Chat header */}
      <Box
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: `1px solid ${theme.palette.divider}`,
          bgcolor: theme.palette.mode === 'dark'
            ? alpha(theme.palette.primary.dark, 0.2)
            : alpha(theme.palette.primary.light, 0.1)
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {showContacts ? (
            <Typography variant="h6">Contacts</Typography>
          ) : (
            <>
              <IconButton
                size="small"
                onClick={() => setShowContacts(true)}
                sx={{ mr: 1 }}
              >
                <ArrowBack />
              </IconButton>
              <UserStatus userId={selectedContact?.id} sx={{ mr: 1 }}>
                <Avatar
                  sx={{
                    bgcolor: selectedContact?.role === 'admin'
                      ? theme.palette.primary.main
                      : theme.palette.secondary.main
                  }}
                >
                  {selectedContact?.name?.charAt(0) || <Person />}
                </Avatar>
              </UserStatus>
              <Box sx={{ ml: 1 }}>
                <Typography variant="subtitle1" fontWeight="bold">
                  {selectedContact?.name || 'Select a contact'}
                </Typography>
                <UserStatus userId={selectedContact?.id} inline={true}>
                  {/* This will just show the status text */}
                </UserStatus>
              </Box>
            </>
          )}
        </Box>

        <Box>
          {onClose && (
            <IconButton onClick={onClose} size="small">
              <Close />
            </IconButton>
          )}
        </Box>
      </Box>

      {/* Contacts list or chat messages */}
      {showContacts ? (
        <List
          sx={{
            flex: 1,
            overflow: 'auto',
            p: 0,
            '&::-webkit-scrollbar': {
              width: '4px',
            },
            '&::-webkit-scrollbar-track': {
              background: 'transparent',
            },
            '&::-webkit-scrollbar-thumb': {
              background: theme.palette.divider,
              borderRadius: '4px',
            },
          }}
        >
          {contacts.length === 0 ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                p: 3
              }}
            >
              <Typography variant="body1" color="text.secondary" align="center">
                {loading ? 'Loading contacts...' : 'No contacts found'}
              </Typography>
              {!loading && (
                <Button
                  startIcon={<Refresh />}
                  variant="outlined"
                  sx={{ mt: 2 }}
                  onClick={() => window.location.reload()}
                >
                  Refresh
                </Button>
              )}
            </Box>
          ) : (
            contacts.map((contact) => (
              <React.Fragment key={contact.id}>
                <ListItem
                  button
                  onClick={() => handleSelectContact(contact)}
                  sx={{
                    py: 1.5,
                    '&:hover': {
                      bgcolor: theme.palette.action.hover
                    }
                  }}
                >
                  <UserStatus userId={contact.id} sx={{ mr: 1 }}>
                    <Avatar
                      sx={{
                        bgcolor: contact.role === 'admin'
                          ? theme.palette.primary.main
                          : theme.palette.secondary.main
                      }}
                    >
                      {contact.name?.charAt(0) || <Person />}
                    </Avatar>
                  </UserStatus>
                  <ListItemText
                    primary={contact.name}
                    secondary={contact.role === 'admin' ? 'Administrator' : 'Courier'}
                    primaryTypographyProps={{
                      fontWeight: 'medium'
                    }}
                    secondaryTypographyProps={{
                      component: 'div' // Change from default 'p' to 'div'
                    }}
                  />
                </ListItem>
                <Divider component="li" />
              </React.Fragment>
            ))
          )}
        </List>
      ) : (
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}
        >
          {/* Messages container */}
          <Box
            sx={{
              flex: 1,
              overflow: 'auto',
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              '&::-webkit-scrollbar': {
                width: '4px',
              },
              '&::-webkit-scrollbar-track': {
                background: 'transparent',
              },
              '&::-webkit-scrollbar-thumb': {
                background: theme.palette.divider,
                borderRadius: '4px',
              },
            }}
          >
            {loading ? (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%'
                }}
              >
                <CircularProgress size={24} />
                <Typography variant="body2" sx={{ ml: 2 }}>
                  Loading messages...
                </Typography>
              </Box>
            ) : messages.length === 0 ? (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%'
                }}
              >
                <Typography variant="body1" color="text.secondary">
                  No messages yet
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Send a message to start the conversation
                </Typography>
              </Box>
            ) : (
              messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: message.senderId === user?.uid ? 'flex-end' : 'flex-start',
                      mb: 2
                    }}
                  >
                    <Box
                      sx={{
                        maxWidth: '70%',
                        p: 2,
                        borderRadius: message.senderId === user?.uid
                          ? '16px 16px 4px 16px'
                          : '16px 16px 16px 4px',
                        bgcolor: message.senderId === user?.uid
                          ? theme.palette.primary.main
                          : theme.palette.mode === 'dark'
                            ? alpha(theme.palette.background.paper, 0.8)
                            : alpha(theme.palette.grey[100], 0.8),
                        color: message.senderId === user?.uid
                          ? theme.palette.primary.contrastText
                          : theme.palette.text.primary,
                        boxShadow: theme.shadows[1]
                      }}
                    >
                      <Typography variant="body1">{message.text}</Typography>
                      <Typography
                        variant="caption"
                        sx={{
                          display: 'flex',
                          mt: 0.5,
                          justifyContent: message.senderId === user?.uid ? 'flex-end' : 'flex-start',
                          alignItems: 'center',
                          opacity: 0.7
                        }}
                      >
                        {formatMessageTime(message.timestamp)}
                        {message.senderId === user?.uid && (
                          <Box component="span" sx={{ ml: 0.5, display: 'inline-flex', alignItems: 'center' }}>
                            <ReadReceipt read={message.read} color="info" />
                          </Box>
                        )}
                      </Typography>
                    </Box>
                  </Box>
                </motion.div>
              ))
            )}
            <div ref={messagesEndRef} />
          </Box>

          {/* Message input */}
          <Box
            sx={{
              p: 2,
              borderTop: `1px solid ${theme.palette.divider}`,
              bgcolor: theme.palette.background.paper
            }}
          >
            <Box
              component="form"
              onSubmit={(e) => {
                e.preventDefault();
                handleSendMessage();
              }}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}
            >
              <TextField
                fullWidth
                placeholder="Type a message..."
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                variant="outlined"
                size="small"
                disabled={sending}
                InputProps={{
                  sx: {
                    borderRadius: 4
                  }
                }}
              />
              <Button
                variant="contained"
                color="primary"
                disabled={!newMessage.trim() || sending}
                type="submit"
                sx={{
                  borderRadius: 4,
                  minWidth: 'auto',
                  width: 40,
                  height: 40,
                  p: 0
                }}
              >
                {sending ? <CircularProgress size={20} /> : <Send />}
              </Button>
            </Box>
          </Box>
        </Box>
      )}
    </Paper>
  );
};

export default ChatInterface;
