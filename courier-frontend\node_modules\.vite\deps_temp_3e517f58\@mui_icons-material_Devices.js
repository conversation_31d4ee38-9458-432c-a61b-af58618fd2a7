"use client";
import {
  require_createSvgIcon
} from "./chunk-NPTO7V2K.js";
import "./chunk-2LARW4OK.js";
import "./chunk-HRVW6ACS.js";
import "./chunk-3L5MCMBQ.js";
import "./chunk-FWU3CBNV.js";
import {
  require_interopRequireDefault
} from "./chunk-HYFG2SCB.js";
import "./chunk-KR3C65D2.js";
import "./chunk-R4ZBIWV7.js";
import "./chunk-PSO6HBME.js";
import {
  require_jsx_runtime
} from "./chunk-CRNJR6QK.js";
import "./chunk-ZMLY2J2T.js";
import {
  __commonJS
} from "./chunk-4B2QHNJT.js";

// node_modules/@mui/icons-material/Devices.js
var require_Devices = __commonJS({
  "node_modules/@mui/icons-material/Devices.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M4 6h18V4H4c-1.1 0-2 .9-2 2v11H0v3h14v-3H4zm19 2h-6c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h6c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1m-1 9h-4v-7h4z"
    }), "Devices");
  }
});
export default require_Devices();
//# sourceMappingURL=@mui_icons-material_Devices.js.map
