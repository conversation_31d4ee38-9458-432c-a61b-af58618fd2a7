import {
  useThemeProps
} from "./chunk-WPV7LSQT.js";
import {
  _extends,
  init_extends
} from "./chunk-R4ZBIWV7.js";
import {
  require_prop_types
} from "./chunk-PSO6HBME.js";
import {
  require_jsx_runtime
} from "./chunk-CRNJR6QK.js";
import {
  require_react
} from "./chunk-ZMLY2J2T.js";
import {
  __toESM
} from "./chunk-4B2QHNJT.js";

// node_modules/@mui/x-date-pickers/locales/utils/getPickersLocalization.js
init_extends();
var getPickersLocalization = (pickersTranslations) => {
  return {
    components: {
      MuiLocalizationProvider: {
        defaultProps: {
          localeText: _extends({}, pickersTranslations)
        }
      }
    }
  };
};

// node_modules/@mui/x-date-pickers/locales/nlNL.js
var nlNLPickers = {
  // Calendar navigation
  previousMonth: "Vorige maand",
  nextMonth: "Volgende maand",
  // View navigation
  openPreviousView: "open vorige view",
  openNextView: "open volgende view",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "jaarweergave is geopend, schakel over naar kalenderweergave" : "kalenderweergave is geopend, switch naar jaarweergave",
  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen: boolean, viewType: 'calendar' | 'clock') => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,
  // DateRange placeholders
  start: "Start",
  end: "Einde",
  // Action bar
  cancelButtonLabel: "Annuleren",
  clearButtonLabel: "Resetten",
  okButtonLabel: "OK",
  todayButtonLabel: "Vandaag",
  // Toolbar titles
  // datePickerDefaultToolbarTitle: 'Select date',
  // dateTimePickerDefaultToolbarTitle: 'Select date & time',
  // timePickerDefaultToolbarTitle: 'Select time',
  // dateRangePickerDefaultToolbarTitle: 'Select date range',
  // Clock labels
  clockLabelText: (view, time, adapter) => `Selecteer ${view}. ${time === null ? "Geen tijd geselecteerd" : `Geselecteerde tijd is ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} uren`,
  minutesClockNumberText: (minutes) => `${minutes} minuten`,
  secondsClockNumberText: (seconds) => `${seconds} seconden`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Kies datum, geselecteerde datum is ${utils.format(utils.date(rawValue), "fullDate")}` : "Kies datum",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Kies tijd, geselecteerde tijd is ${utils.format(utils.date(rawValue), "fullTime")}` : "Kies tijd",
  // Table labels
  timeTableLabel: "kies tijd",
  dateTableLabel: "kies datum"
};
var nlNL = getPickersLocalization(nlNLPickers);

// node_modules/@mui/x-date-pickers/locales/plPL.js
var plPLPickers = {
  // Calendar navigation
  previousMonth: "Poprzedni miesiąc",
  nextMonth: "Następny miesiąc",
  // View navigation
  openPreviousView: "otwórz poprzedni widok",
  openNextView: "otwórz następny widok",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "otwarty jest widok roku, przełącz na widok kalendarza" : "otwarty jest widok kalendarza, przełącz na widok roku",
  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen: boolean, viewType: 'calendar' | 'clock') => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,
  // DateRange placeholders
  start: "Początek",
  end: "Koniec",
  // Action bar
  cancelButtonLabel: "Anuluj",
  clearButtonLabel: "Wyczyść",
  okButtonLabel: "Zatwierdź",
  todayButtonLabel: "Dzisiaj",
  // Toolbar titles
  // datePickerDefaultToolbarTitle: 'Select date',
  // dateTimePickerDefaultToolbarTitle: 'Select date & time',
  // timePickerDefaultToolbarTitle: 'Select time',
  // dateRangePickerDefaultToolbarTitle: 'Select date range',
  // Clock labels
  clockLabelText: (view, time, adapter) => `Select ${view}. ${time === null ? "Nie wybrano czasu" : `Wybrany czas to ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} godzin`,
  minutesClockNumberText: (minutes) => `${minutes} minut`,
  secondsClockNumberText: (seconds) => `${seconds} sekund`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Wybierz datę, obecnie wybrana data to ${utils.format(utils.date(rawValue), "fullDate")}` : "Wybierz datę",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Wybierz czas, obecnie wybrany czas to ${utils.format(utils.date(rawValue), "fullTime")}` : "Wybierz czas",
  // Table labels
  timeTableLabel: "wybierz czas",
  dateTableLabel: "wybierz datę"
};
var plPL = getPickersLocalization(plPLPickers);

// node_modules/@mui/x-date-pickers/locales/ptBR.js
var ptBRPickers = {
  // Calendar navigation
  previousMonth: "Mês anterior",
  nextMonth: "Próximo mês",
  // View navigation
  openPreviousView: "Abrir próxima seleção",
  openNextView: "Abrir seleção anterior",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "Seleção de ano está aberta, alternando para seleção de calendário" : "Seleção de calendários está aberta, alternando para seleção de ano",
  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen: boolean, viewType: 'calendar' | 'clock') => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,
  // DateRange placeholders
  start: "Início",
  end: "Fim",
  // Action bar
  cancelButtonLabel: "Cancelar",
  clearButtonLabel: "Limpar",
  okButtonLabel: "OK",
  todayButtonLabel: "Hoje",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Selecione a data",
  dateTimePickerDefaultToolbarTitle: "Selecione data e hora",
  timePickerDefaultToolbarTitle: "Selecione a hora",
  dateRangePickerDefaultToolbarTitle: "Selecione o intervalo entre datas",
  // Clock labels
  clockLabelText: (view, time, adapter) => `Selecione ${view}. ${time === null ? "Hora não selecionada" : `Selecionado a hora ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} horas`,
  minutesClockNumberText: (minutes) => `${minutes} minutos`,
  secondsClockNumberText: (seconds) => `${seconds} segundos`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Escolha uma data, data selecionada ${utils.format(utils.date(rawValue), "fullDate")}` : "Escolha uma data",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Escolha uma hora, hora selecionada ${utils.format(utils.date(rawValue), "fullTime")}` : "Escolha uma hora",
  // Table labels
  timeTableLabel: "escolha uma hora",
  dateTableLabel: "escolha uma data"
};
var ptBR = getPickersLocalization(ptBRPickers);

// node_modules/@mui/x-date-pickers/locales/trTR.js
var trTRPickers = {
  // Calendar navigation
  previousMonth: "Önceki ay",
  nextMonth: "Sonraki ay",
  // View navigation
  openPreviousView: "sonraki görünüm",
  openNextView: "önceki görünüm",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "yıl görünümü açık, takvim görünümüne geç" : "takvim görünümü açık, yıl görünümüne geç",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `metin girişi görünümü açık, şuraya gidin: ${viewType} görünümü` : `${viewType} görünüm açık, metin girişi görünümüne gidin`,
  // DateRange placeholders
  start: "Başlangıç",
  end: "Bitiş",
  // Action bar
  cancelButtonLabel: "iptal",
  clearButtonLabel: "Temizle",
  okButtonLabel: "Tamam",
  todayButtonLabel: "Bugün",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Tarih Seç",
  dateTimePickerDefaultToolbarTitle: "Tarih & Saat seç",
  timePickerDefaultToolbarTitle: "Saat seç",
  dateRangePickerDefaultToolbarTitle: "Tarih aralığı seçin",
  // Clock labels
  clockLabelText: (view, time, adapter) => `${view} seç.  ${time === null ? "Zaman seçilmedi" : `Seçilen zaman: ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} saat`,
  minutesClockNumberText: (minutes) => `${minutes} dakika`,
  secondsClockNumberText: (seconds) => `${seconds} saniye`,
  // Open picker labels
  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Tarih seçin, seçilen tarih: ${utils.format(value, "fullDate")}` : "Tarih seç",
  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Saat seçin, seçilen saat: ${utils.format(value, "fullTime")}` : "Saat seç",
  // Table labels
  timeTableLabel: "saat seç",
  dateTableLabel: "tarih seç"
};
var trTR = getPickersLocalization(trTRPickers);

// node_modules/@mui/x-date-pickers/locales/deDE.js
var clockViews = {
  hours: "Stunden",
  minutes: "Minuten",
  seconds: "Sekunden"
};
var pickerViews = {
  calendar: "Kalenderansicht",
  clock: "Uhransicht"
};
var deDEPickers = {
  // Calendar navigation
  previousMonth: "Letzter Monat",
  nextMonth: "Nächster Monat",
  // View navigation
  openPreviousView: "Letzte Ansicht öffnen",
  openNextView: "Nächste Ansicht öffnen",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "Jahresansicht ist geöffnet, zur Kalenderansicht wechseln" : "Kalenderansicht ist geöffnet, zur Jahresansicht wechseln",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `Texteingabeansicht ist geöffnet, zur ${pickerViews[viewType]} wechseln` : `${pickerViews[viewType]} ist geöffnet, zur Texteingabeansicht wechseln`,
  // DateRange placeholders
  start: "Beginn",
  end: "Ende",
  // Action bar
  cancelButtonLabel: "Abbrechen",
  clearButtonLabel: "Löschen",
  okButtonLabel: "OK",
  todayButtonLabel: "Heute",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Datum auswählen",
  dateTimePickerDefaultToolbarTitle: "Datum & Uhrzeit auswählen",
  timePickerDefaultToolbarTitle: "Uhrzeit auswählen",
  dateRangePickerDefaultToolbarTitle: "Datumsbereich auswählen",
  // Clock labels
  clockLabelText: (view, time, adapter) => {
    var _clockViews$view;
    return `${(_clockViews$view = clockViews[view]) != null ? _clockViews$view : view} auswählen. ${time === null ? "Keine Uhrzeit ausgewählt" : `Gewählte Uhrzeit ist ${adapter.format(time, "fullTime")}`}`;
  },
  hoursClockNumberText: (hours) => `${hours} ${clockViews.hours}`,
  minutesClockNumberText: (minutes) => `${minutes} ${clockViews.minutes}`,
  secondsClockNumberText: (seconds) => `${seconds}  ${clockViews.seconds}`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Datum auswählen, gewähltes Datum ist ${utils.format(utils.date(rawValue), "fullDate")}` : "Datum auswählen",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Uhrzeit auswählen, gewählte Uhrzeit ist ${utils.format(utils.date(rawValue), "fullTime")}` : "Uhrzeit auswählen",
  // Table labels
  timeTableLabel: "Uhrzeit auswählen",
  dateTableLabel: "Datum auswählen"
};
var deDE = getPickersLocalization(deDEPickers);

// node_modules/@mui/x-date-pickers/locales/esES.js
var views = {
  hours: "las horas",
  minutes: "los minutos",
  seconds: "los segundos"
};
var esESPickers = {
  // Calendar navigation
  previousMonth: "Último mes",
  nextMonth: "Próximo mes",
  // View navigation
  openPreviousView: "abrir la última vista",
  openNextView: "abrir la siguiente vista",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "la vista del año está abierta, cambie a la vista de calendario" : "la vista de calendario está abierta, cambie a la vista del año",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `la vista de entrada de texto está abierta, ir a la vista ${viewType}` : `la vista ${viewType} está abierta, ir a la vista de entrada de texto`,
  // DateRange placeholders
  start: "Empezar",
  end: "Terminar",
  // Action bar
  cancelButtonLabel: "Cancelar",
  clearButtonLabel: "Limpiar",
  okButtonLabel: "OK",
  todayButtonLabel: "Hoy",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Seleccionar fecha",
  dateTimePickerDefaultToolbarTitle: "Seleccionar fecha & hora",
  timePickerDefaultToolbarTitle: "Seleccionar hora",
  dateRangePickerDefaultToolbarTitle: "Seleccionar rango de fecha",
  // Clock labels
  clockLabelText: (view, time, adapter) => `Seleccione ${views[view]}. ${time === null ? "Sin tiempo seleccionado" : `El tiempo seleccionado es ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} horas`,
  minutesClockNumberText: (minutes) => `${minutes} minutos`,
  secondsClockNumberText: (seconds) => `${seconds} segundos`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Elige la fecha, la fecha elegida es ${utils.format(utils.date(rawValue), "fullDate")}` : "Elige la fecha",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Elige la hora, la hora elegido es ${utils.format(utils.date(rawValue), "fullTime")}` : "Elige la hora",
  // Table labels
  timeTableLabel: "elige la fecha",
  dateTableLabel: "elige la hora"
};
var esES = getPickersLocalization(esESPickers);

// node_modules/@mui/x-date-pickers/locales/faIR.js
var faIRPickers = {
  // Calendar navigation
  previousMonth: "ماه گذشته",
  nextMonth: "ماه آینده",
  // View navigation
  openPreviousView: "نمای قبلی",
  openNextView: "نمای بعدی",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "نمای سال باز است، رفتن به نمای تقویم" : "نمای تقویم باز است، رفتن به نمای سال",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `نمای ورودی متن باز است، رفتن به نمای ${viewType}` : `نمای ${viewType} باز است، رفتن به نمای ورودی متن`,
  // DateRange placeholders
  start: "شروع",
  end: "پایان",
  // Action bar
  cancelButtonLabel: "لغو",
  clearButtonLabel: "پاک کردن",
  okButtonLabel: "اوکی",
  todayButtonLabel: "امروز",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "تاریخ را انتخاب کنید",
  dateTimePickerDefaultToolbarTitle: "تاریخ و ساعت را انتخاب کنید",
  timePickerDefaultToolbarTitle: "ساعت را انتخاب کنید",
  dateRangePickerDefaultToolbarTitle: "محدوده تاریخ را انتخاب کنید",
  // Clock labels
  clockLabelText: (view, time, adapter) => `Select ${view}. ${time === null ? "هیچ ساعتی انتخاب نشده است" : `ساعت انتخاب ${adapter.format(time, "fullTime")} می باشد`}`,
  hoursClockNumberText: (hours) => `${hours} ساعت ها`,
  minutesClockNumberText: (minutes) => `${minutes} دقیقه ها`,
  secondsClockNumberText: (seconds) => `${seconds} ثانیه ها`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `تاریخ را انتخاب کنید، تاریخ انتخاب شده ${utils.format(utils.date(rawValue), "fullDate")} می باشد` : "تاریخ را انتخاب کنید",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `ساعت را انتخاب کنید، ساعت انتخاب شده ${utils.format(utils.date(rawValue), "fullTime")} می باشد` : "ساعت را انتخاب کنید",
  // Table labels
  timeTableLabel: "انتخاب تاریخ",
  dateTableLabel: "انتخاب ساعت"
};
var faIR = getPickersLocalization(faIRPickers);

// node_modules/@mui/x-date-pickers/locales/fiFI.js
var views2 = {
  hours: "tunnit",
  minutes: "minuutit",
  seconds: "sekuntit"
};
var viewTranslation = {
  calendar: "kalenteri",
  clock: "kello"
};
var fiFIPickers = {
  // Calendar navigation
  previousMonth: "Edellinen kuukausi",
  nextMonth: "Seuraava kuukausi",
  // View navigation
  openPreviousView: "avaa edellinen kuukausi",
  openNextView: "avaa seuraava kuukausi",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "vuosinäkymä on auki, vaihda kalenterinäkymään" : "kalenterinäkymä on auki, vaihda vuosinäkymään",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `tekstikenttä on auki, mene ${viewTranslation[viewType]}näkymään` : `${viewTranslation[viewType]}näkymä on auki, mene tekstikenttään`,
  // DateRange placeholders
  start: "Alku",
  end: "Loppu",
  // Action bar
  cancelButtonLabel: "Peruuta",
  clearButtonLabel: "Tyhjennä",
  okButtonLabel: "OK",
  todayButtonLabel: "Tänään",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Valitse päivä",
  dateTimePickerDefaultToolbarTitle: "Valitse päivä ja aika",
  timePickerDefaultToolbarTitle: "Valitse aika",
  dateRangePickerDefaultToolbarTitle: "Valitse aikaväli",
  // Clock labels
  clockLabelText: (view, time, adapter) => `Valitse ${views2[view]}. ${time === null ? "Ei aikaa valittuna" : `Valittu aika on ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} tuntia`,
  minutesClockNumberText: (minutes) => `${minutes} minuuttia`,
  secondsClockNumberText: (seconds) => `${seconds} sekunttia`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Valitse päivä, valittu päivä on ${utils.format(utils.date(rawValue), "fullDate")}` : "Valitse päivä",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Valitse aika, valittu aika on ${utils.format(utils.date(rawValue), "fullTime")}` : "Valitse aika",
  // Table labels
  timeTableLabel: "valitse aika",
  dateTableLabel: "valitse päivä"
};
var fiFI = getPickersLocalization(fiFIPickers);

// node_modules/@mui/x-date-pickers/locales/csCZ.js
var timeViews = {
  hours: "Hodiny",
  minutes: "Minuty",
  seconds: "Sekundy"
};
var pickerViews2 = {
  calendar: "kalendáře",
  clock: "času"
};
var csCZPickers = {
  // Calendar navigation
  previousMonth: "Další měsíc",
  nextMonth: "Předchozí month",
  // View navigation
  openPreviousView: "otevřít předchozí zobrazení",
  openNextView: "otevřít další zobrazení",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "roční zobrazení otevřeno, přepněte do zobrazení kalendáře" : "zobrazení kalendáře otevřeno, přepněte do zobrazení roku",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `Zobrazení pro zadávání textu je otevřené, přepněte do zobrazení ${pickerViews2[viewType]}` : `Zobrazení ${pickerViews2[viewType]} je otevřené, přepněte do zobrazení textového pole`,
  // DateRange placeholders
  start: "Začátek",
  end: "Konec",
  // Action bar
  cancelButtonLabel: "Zrušit",
  clearButtonLabel: "Vymazat",
  okButtonLabel: "Potvrdit",
  todayButtonLabel: "Dnes",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Vyberte datum",
  dateTimePickerDefaultToolbarTitle: "Vyberte datum a čas",
  timePickerDefaultToolbarTitle: "Vyberte čas",
  dateRangePickerDefaultToolbarTitle: "Vyberete rozmezí dat",
  // Clock labels
  clockLabelText: (view, time, adapter) => {
    var _timeViews$view;
    return `${(_timeViews$view = timeViews[view]) != null ? _timeViews$view : view} vybrány. ${time === null ? "Není vybrán čas" : `Vybraný čas je ${adapter.format(time, "fullTime")}`}`;
  },
  hoursClockNumberText: (hours) => `${hours} hodin`,
  minutesClockNumberText: (minutes) => `${minutes} minut`,
  secondsClockNumberText: (seconds) => `${seconds} sekund`,
  // Open picker labels
  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Vybrané datum, vybrané datum je ${utils.format(value, "fullDate")}` : "Vyberte datum",
  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Vybrané čas, vybraný čas je ${utils.format(value, "fullTime")}` : "Vyberte čas",
  // Table labels
  timeTableLabel: "vyberte čas",
  dateTableLabel: "vyberte datum"
};
var csCZ = getPickersLocalization(csCZPickers);

// node_modules/@mui/x-date-pickers/locales/frFR.js
var views3 = {
  hours: "heures",
  minutes: "minutes",
  seconds: "secondes"
};
var viewTranslation2 = {
  calendar: "calendrier",
  clock: "horloge"
};
var frFRPickers = {
  // Calendar navigation
  previousMonth: "Mois précédent",
  nextMonth: "Mois suivant",
  // View navigation
  openPreviousView: "Ouvrir la vue précédente",
  openNextView: "Ouvrir la vue suivante",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "La vue année est ouverte, ouvrir la vue calendrier" : "La vue calendrier est ouverte, ouvrir la vue année",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `passer du champ text au ${viewTranslation2[viewType]}` : `passer du ${viewTranslation2[viewType]} au champ text`,
  // DateRange placeholders
  start: "Début",
  end: "Fin",
  // Action bar
  cancelButtonLabel: "Annuler",
  clearButtonLabel: "Vider",
  okButtonLabel: "OK",
  todayButtonLabel: "Aujourd'hui",
  // Toolbar titles
  // datePickerDefaultToolbarTitle: 'Select date',
  // dateTimePickerDefaultToolbarTitle: 'Select date & time',
  // timePickerDefaultToolbarTitle: 'Select time',
  // dateRangePickerDefaultToolbarTitle: 'Select date range',
  // Clock labels
  clockLabelText: (view, time, adapter) => `Choix des ${views3[view]}. ${time === null ? "Aucune heure choisie" : `L'heure choisie est ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} heures`,
  minutesClockNumberText: (minutes) => `${minutes} minutes`,
  secondsClockNumberText: (seconds) => `${seconds} secondes`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Choisir la date, la date sélectionnée est ${utils.format(utils.date(rawValue), "fullDate")}` : "Choisir la date",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Choisir l'heure, l'heure sélectionnée est ${utils.format(utils.date(rawValue), "fullTime")}` : "Choisir l'heure",
  // Table labels
  timeTableLabel: "choix de l'heure",
  dateTableLabel: "choix de la date"
};
var frFR = getPickersLocalization(frFRPickers);

// node_modules/@mui/x-date-pickers/locales/huHU.js
var timeViews2 = {
  hours: "Óra",
  minutes: "Perc",
  seconds: "Másodperc"
};
var pickerViews3 = {
  calendar: "naptár",
  clock: "óra"
};
var huHUPickers = {
  // Calendar navigation
  previousMonth: "Előző hónap",
  nextMonth: "Következő hónap",
  // View navigation
  openPreviousView: "Előző nézet megnyitása",
  openNextView: "Következő nézet megnyitása",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "az évválasztó már nyitva, váltson a naptárnézetre" : "a naptárnézet már nyitva, váltson az évválasztóra",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `szöveges beviteli nézet aktív, váltás ${pickerViews3[viewType]} nézetre` : `${pickerViews3[viewType]} beviteli nézet aktív, váltás szöveges beviteli nézetre`,
  // DateRange placeholders
  start: "Kezdő dátum",
  end: "Záró dátum",
  // Action bar
  cancelButtonLabel: "Mégse",
  clearButtonLabel: "Törlés",
  okButtonLabel: "OK",
  todayButtonLabel: "Ma",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Dátum kiválasztása",
  dateTimePickerDefaultToolbarTitle: "Dátum és idő kiválasztása",
  timePickerDefaultToolbarTitle: "Idő kiválasztása",
  dateRangePickerDefaultToolbarTitle: "Dátumhatárok kiválasztása",
  // Clock labels
  clockLabelText: (view, time, adapter) => {
    var _timeViews$view;
    return `${(_timeViews$view = timeViews2[view]) != null ? _timeViews$view : view} kiválasztása. ${time === null ? "Nincs kiválasztva idő" : `A kiválasztott idő ${adapter.format(time, "fullTime")}`}`;
  },
  hoursClockNumberText: (hours) => `${hours} ${timeViews2.hours.toLowerCase()}`,
  minutesClockNumberText: (minutes) => `${minutes} ${timeViews2.minutes.toLowerCase()}`,
  secondsClockNumberText: (seconds) => `${seconds}  ${timeViews2.seconds.toLowerCase()}`,
  // Open picker labels
  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Válasszon dátumot, a kiválasztott dátum: ${utils.format(value, "fullDate")}` : "Válasszon dátumot",
  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Válasszon időt, a kiválasztott idő: ${utils.format(value, "fullTime")}` : "Válasszon időt",
  // Table labels
  timeTableLabel: "válasszon időt",
  dateTableLabel: "válasszon dátumot"
};
var huHU = getPickersLocalization(huHUPickers);

// node_modules/@mui/x-date-pickers/locales/enUS.js
var enUSPickers = {
  // Calendar navigation
  previousMonth: "Previous month",
  nextMonth: "Next month",
  // View navigation
  openPreviousView: "open previous view",
  openNextView: "open next view",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "year view is open, switch to calendar view" : "calendar view is open, switch to year view",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,
  // DateRange placeholders
  start: "Start",
  end: "End",
  // Action bar
  cancelButtonLabel: "Cancel",
  clearButtonLabel: "Clear",
  okButtonLabel: "OK",
  todayButtonLabel: "Today",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Select date",
  dateTimePickerDefaultToolbarTitle: "Select date & time",
  timePickerDefaultToolbarTitle: "Select time",
  dateRangePickerDefaultToolbarTitle: "Select date range",
  // Clock labels
  clockLabelText: (view, time, adapter) => `Select ${view}. ${time === null ? "No time selected" : `Selected time is ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} hours`,
  minutesClockNumberText: (minutes) => `${minutes} minutes`,
  secondsClockNumberText: (seconds) => `${seconds} seconds`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Choose date, selected date is ${utils.format(utils.date(rawValue), "fullDate")}` : "Choose date",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Choose time, selected time is ${utils.format(utils.date(rawValue), "fullTime")}` : "Choose time",
  // Table labels
  timeTableLabel: "pick time",
  dateTableLabel: "pick date"
};
var DEFAULT_LOCALE = enUSPickers;
var enUS = getPickersLocalization(enUSPickers);

// node_modules/@mui/x-date-pickers/locales/nbNO.js
var nbNOPickers = {
  // Calendar navigation
  previousMonth: "Forrige måned",
  nextMonth: "Neste måned",
  // View navigation
  openPreviousView: "åpne forrige visning",
  openNextView: "åpne neste visning",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "årsvisning er åpen, bytt til kalendervisning" : "kalendervisning er åpen, bytt til årsvisning",
  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen: boolean, viewType: 'calendar' | 'clock') => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,
  // DateRange placeholders
  start: "Start",
  end: "Slutt",
  // Action bar
  cancelButtonLabel: "Avbryt",
  clearButtonLabel: "Fjern",
  okButtonLabel: "OK",
  todayButtonLabel: "I dag",
  // Toolbar titles
  // datePickerDefaultToolbarTitle: 'Select date',
  // dateTimePickerDefaultToolbarTitle: 'Select date & time',
  // timePickerDefaultToolbarTitle: 'Select time',
  // dateRangePickerDefaultToolbarTitle: 'Select date range',
  // Clock labels
  clockLabelText: (view, time, adapter) => `Velg ${view}. ${time === null ? "Ingen tid valgt" : `Valgt tid er ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} timer`,
  minutesClockNumberText: (minutes) => `${minutes} minutter`,
  secondsClockNumberText: (seconds) => `${seconds} sekunder`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Velg dato, valgt dato er ${utils.format(utils.date(rawValue), "fullDate")}` : "Velg dato",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Velg tid, valgt tid er ${utils.format(utils.date(rawValue), "fullTime")}` : "Velg tid",
  // Table labels
  timeTableLabel: "velg tid",
  dateTableLabel: "velg dato"
};
var nbNO = getPickersLocalization(nbNOPickers);

// node_modules/@mui/x-date-pickers/locales/svSE.js
var svSEPickers = {
  // Calendar navigation
  previousMonth: "Föregående månad",
  nextMonth: "Nästa månad",
  // View navigation
  openPreviousView: "öppna föregående vy",
  openNextView: "öppna nästa vy",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "årsvyn är öppen, byt till kalendervy" : "kalendervyn är öppen, byt till årsvy",
  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen: boolean, viewType: 'calendar' | 'clock') => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,
  // DateRange placeholders
  start: "Start",
  end: "Slut",
  // Action bar
  cancelButtonLabel: "Avbryt",
  clearButtonLabel: "Rensa",
  okButtonLabel: "OK",
  todayButtonLabel: "Idag",
  // Toolbar titles
  // datePickerDefaultToolbarTitle: 'Select date',
  // dateTimePickerDefaultToolbarTitle: 'Select date & time',
  // timePickerDefaultToolbarTitle: 'Select time',
  // dateRangePickerDefaultToolbarTitle: 'Select date range',
  // Clock labels
  clockLabelText: (view, time, adapter) => `Select ${view}. ${time === null ? "Ingen tid vald" : `Vald tid är ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} timmar`,
  minutesClockNumberText: (minutes) => `${minutes} minuter`,
  secondsClockNumberText: (seconds) => `${seconds} sekunder`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Välj datum, valt datum är ${utils.format(utils.date(rawValue), "fullDate")}` : "Välj datum",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Välj tid, vald tid är ${utils.format(utils.date(rawValue), "fullTime")}` : "Välj tid",
  // Table labels
  timeTableLabel: "välj tid",
  dateTableLabel: "välj datum"
};
var svSE = getPickersLocalization(svSEPickers);

// node_modules/@mui/x-date-pickers/locales/itIT.js
var views4 = {
  hours: "le ore",
  minutes: "i minuti",
  seconds: "i secondi"
};
var itITPickers = {
  // Calendar navigation
  previousMonth: "Mese precedente",
  nextMonth: "Mese successivo",
  // View navigation
  openPreviousView: "apri la vista precedente",
  openNextView: "apri la vista successiva",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "la vista dell'anno è aperta, passare alla vista del calendario" : "la vista dell'calendario è aperta, passare alla vista dell'anno",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `la vista del campo di testo è aperta, passare alla vista ${viewType}` : `la vista aperta è: ${viewType}, vai alla vista del campo di testo`,
  // DateRange placeholders
  start: "Inizio",
  end: "Fine",
  // Action bar
  cancelButtonLabel: "Cancellare",
  clearButtonLabel: "Sgomberare",
  okButtonLabel: "OK",
  todayButtonLabel: "Oggi",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Seleziona data",
  dateTimePickerDefaultToolbarTitle: "Seleziona data e orario",
  timePickerDefaultToolbarTitle: "Seleziona orario",
  dateRangePickerDefaultToolbarTitle: "Seleziona intervallo di date",
  // Clock labels
  clockLabelText: (view, time, adapter) => `Seleziona ${views4[view]}. ${time === null ? "Nessun orario selezionato" : `L'ora selezionata è ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} ore`,
  minutesClockNumberText: (minutes) => `${minutes} minuti`,
  secondsClockNumberText: (seconds) => `${seconds} secondi`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Scegli la data, la data selezionata è ${utils.format(utils.date(rawValue), "fullDate")}` : "Scegli la data",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Scegli l'ora, l'ora selezionata è ${utils.format(utils.date(rawValue), "fullTime")}` : "Scegli l'ora",
  // Table labels
  timeTableLabel: "scegli un'ora",
  dateTableLabel: "scegli una data"
};
var itIT = getPickersLocalization(itITPickers);

// node_modules/@mui/x-date-pickers/locales/zhCN.js
var views5 = {
  hours: "小时",
  minutes: "分钟",
  seconds: "秒"
};
var zhCNPickers = {
  // Calendar navigation
  previousMonth: "上个月",
  nextMonth: "下个月",
  // View navigation
  openPreviousView: "前一个视图",
  openNextView: "下一个视图",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "年视图已打开，切换为日历视图" : "日历视图已打开，切换为年视图",
  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen: boolean, viewType: 'calendar' | 'clock') => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,
  // DateRange placeholders
  start: "开始",
  end: "结束",
  // Action bar
  cancelButtonLabel: "取消",
  clearButtonLabel: "清除",
  okButtonLabel: "确认",
  todayButtonLabel: "今天",
  // Toolbar titles
  // datePickerDefaultToolbarTitle: 'Select date',
  // dateTimePickerDefaultToolbarTitle: 'Select date & time',
  // timePickerDefaultToolbarTitle: 'Select time',
  // dateRangePickerDefaultToolbarTitle: 'Select date range',
  // Clock labels
  clockLabelText: (view, time, adapter) => `Select ${views5[view]}. ${time === null ? "未选择时间" : `已选择${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours}小时`,
  minutesClockNumberText: (minutes) => `${minutes}分钟`,
  secondsClockNumberText: (seconds) => `${seconds}秒`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `选择日期，已选择${utils.format(utils.date(rawValue), "fullDate")}` : "选择日期",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `选择时间，已选择${utils.format(utils.date(rawValue), "fullTime")}` : "选择时间",
  // Table labels
  timeTableLabel: "选择时间",
  dateTableLabel: "选择日期"
};
var zhCN = getPickersLocalization(zhCNPickers);

// node_modules/@mui/x-date-pickers/locales/koKR.js
var views6 = {
  hours: "시간을",
  minutes: "분을",
  seconds: "초를"
};
var koKRPickers = {
  // Calendar navigation
  previousMonth: "이전 달",
  nextMonth: "다음 달",
  // View navigation
  openPreviousView: "이전 화면 보기",
  openNextView: "다음 화면 보기",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "연도 선택 화면에서 달력 화면으로 전환하기" : "달력 화면에서 연도 선택 화면으로 전환하기",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `텍스트 입력 화면에서 ${viewType} 화면으로 전환하기` : `${viewType} 화면에서 텍스트 입력 화면으로 전환하기`,
  // DateRange placeholders
  start: "시작",
  end: "종료",
  // Action bar
  cancelButtonLabel: "취소",
  clearButtonLabel: "초기화",
  okButtonLabel: "확인",
  todayButtonLabel: "오늘",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "날짜 선택하기",
  dateTimePickerDefaultToolbarTitle: "날짜 & 시간 선택하기",
  timePickerDefaultToolbarTitle: "시간 선택하기",
  dateRangePickerDefaultToolbarTitle: "날짜 범위 선택하기",
  // Clock labels
  clockLabelText: (view, time, adapter) => `${views6[view]} 선택하세요. ${time === null ? "시간을 선택하지 않았습니다." : `현재 선택된 시간은 ${adapter.format(time, "fullTime")}입니다.`}`,
  hoursClockNumberText: (hours) => `${hours}시간`,
  minutesClockNumberText: (minutes) => `${minutes}분`,
  secondsClockNumberText: (seconds) => `${seconds}초`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `날짜를 선택하세요. 현재 선택된 날짜는 ${utils.format(utils.date(rawValue), "fullDate")}입니다.` : "날짜를 선택하세요",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `시간을 선택하세요. 현재 선택된 시간은 ${utils.format(utils.date(rawValue), "fullTime")}입니다.` : "시간을 선택하세요",
  // Table labels
  timeTableLabel: "선택한 시간",
  dateTableLabel: "선택한 날짜"
};
var koKR = getPickersLocalization(koKRPickers);

// node_modules/@mui/x-date-pickers/locales/isIS.js
var isISPickers = {
  // Calendar navigation
  previousMonth: "Fyrri mánuður",
  nextMonth: "Næsti mánuður",
  // View navigation
  openPreviousView: "opna fyrri skoðun",
  openNextView: "opna næstu skoðun",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "ársskoðun er opin, skipta yfir í dagatalsskoðun" : "dagatalsskoðun er opin, skipta yfir í ársskoðun",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => {
    const viewTypeTranslated = viewType === "calendar" ? "dagatals" : "klukku";
    return isKeyboardInputOpen ? `textainnsláttur er opinn, fara í ${viewTypeTranslated}skoðun` : `${viewTypeTranslated}skoðun er opin, opna fyrir textainnslátt`;
  },
  // DateRange placeholders
  start: "Upphaf",
  end: "Endir",
  // Action bar
  cancelButtonLabel: "Hætta við",
  clearButtonLabel: "Hreinsa",
  okButtonLabel: "OK",
  todayButtonLabel: "Í dag",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Velja dagsetningu",
  dateTimePickerDefaultToolbarTitle: "Velja dagsetningu og tíma",
  timePickerDefaultToolbarTitle: "Velja tíma",
  dateRangePickerDefaultToolbarTitle: "Velja tímabil",
  // Clock labels
  clockLabelText: (view, time, adapter) => `Select ${view}. ${time === null ? "Enginn tími valinn" : `Valinn tími er ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} klukkustundir`,
  minutesClockNumberText: (minutes) => `${minutes} mínútur`,
  secondsClockNumberText: (seconds) => `${seconds} sekúndur`,
  // Open picker labels
  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Velja dagsetningu, valin dagsetning er ${utils.format(utils.date(rawValue), "fullDate")}` : "Velja dagsetningu",
  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Velja tíma, valinn tími er ${utils.format(utils.date(rawValue), "fullTime")}` : "Velja tíma",
  // Table labels
  timeTableLabel: "velja tíma",
  dateTableLabel: "velja dagsetningu"
};
var isIS = getPickersLocalization(isISPickers);

// node_modules/@mui/x-date-pickers/locales/jaJP.js
var clockViews2 = {
  hours: "時間",
  minutes: "分",
  seconds: "秒"
};
var pickerViews4 = {
  calendar: "カレンダー表示",
  clock: "時計表示"
};
var jaJPPickers = {
  // Calendar navigation
  previousMonth: "先月",
  nextMonth: "来月",
  // View navigation
  openPreviousView: "前の表示を開く",
  openNextView: "次の表示を開く",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "年選択表示からカレンダー表示に切り替える" : "カレンダー表示から年選択表示に切り替える",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `テキスト入力表示から${pickerViews4[viewType]}に切り替える` : `${pickerViews4[viewType]}からテキスト入力表示に切り替える`,
  // DateRange placeholders
  start: "開始",
  end: "終了",
  // Action bar
  cancelButtonLabel: "キャンセル",
  clearButtonLabel: "クリア",
  okButtonLabel: "確定",
  todayButtonLabel: "今日",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "日付を選択",
  dateTimePickerDefaultToolbarTitle: "日時を選択",
  timePickerDefaultToolbarTitle: "時間を選択",
  dateRangePickerDefaultToolbarTitle: "日付の範囲を選択",
  // Clock labels
  clockLabelText: (view, time, adapter) => {
    var _clockViews$view;
    return `${(_clockViews$view = clockViews2[view]) != null ? _clockViews$view : view}を選択してください ${time === null ? "時間が選択されていません" : `選択した時間は ${adapter.format(time, "fullTime")} です`}`;
  },
  hoursClockNumberText: (hours) => `${hours} ${clockViews2.hours}`,
  minutesClockNumberText: (minutes) => `${minutes} ${clockViews2.minutes}`,
  secondsClockNumberText: (seconds) => `${seconds} ${clockViews2.seconds}`,
  // Open picker labels
  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `日付を選択してください。選択した日付は ${utils.format(value, "fullDate")} です` : "日付を選択してください",
  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `時間を選択してください。選択した時間は ${utils.format(value, "fullTime")} です` : "時間を選択してください",
  // Table labels
  timeTableLabel: "時間を選択",
  dateTableLabel: "日付を選択"
};
var jaJP = getPickersLocalization(jaJPPickers);

// node_modules/@mui/x-date-pickers/locales/ukUA.js
var ukUAPickers = {
  // Calendar navigation
  previousMonth: "Попередній місяць",
  nextMonth: "Наступний місяць",
  // View navigation
  openPreviousView: "відкрити попередній вигляд",
  openNextView: "відкрити наступний вигляд",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "річний вигляд відкрито, перейти до календарного вигляду" : "календарний вигляд відкрито, перейти до річного вигляду",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `текстове поле відкрите, перейти до  ${viewType} вигляду` : `${viewType} вигляд наразі відкрито, перейти до текстового поля`,
  // DateRange placeholders
  start: "Початок",
  end: "Кінець",
  // Action bar
  cancelButtonLabel: "Відміна",
  clearButtonLabel: "Очистити",
  okButtonLabel: "OK",
  todayButtonLabel: "Сьогодні",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Вибрати дату",
  dateTimePickerDefaultToolbarTitle: "Вибрати дату і час",
  timePickerDefaultToolbarTitle: "Вибрати час",
  dateRangePickerDefaultToolbarTitle: "Вибрати календарний період",
  // Clock labels
  clockLabelText: (view, time, adapter) => `Select ${view}. ${time === null ? "Час не вибраний" : `Вибрано час ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} годин`,
  minutesClockNumberText: (minutes) => `${minutes} хвилин`,
  secondsClockNumberText: (seconds) => `${seconds} секунд`,
  // Open picker labels
  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Оберіть дату, обрана дата  ${utils.format(value, "fullDate")}` : "Оберіть дату",
  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Оберіть час, обраний час  ${utils.format(value, "fullTime")}` : "Оберіть час",
  // Table labels
  timeTableLabel: "оберіть час",
  dateTableLabel: "оберіть дату"
};
var ukUA = getPickersLocalization(ukUAPickers);

// node_modules/@mui/x-date-pickers/locales/urPK.js
var urPKPickers = {
  // Calendar navigation
  previousMonth: "پچھلا مہینہ",
  nextMonth: "اگلا مہینہ",
  // View navigation
  openPreviousView: "پچھلا ویو کھولیں",
  openNextView: "اگلا ویو کھولیں",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "سال والا ویو کھلا ہے۔ کیلنڈر والا ویو کھولیں" : "کیلنڈر والا ویو کھلا ہے۔ سال والا ویو کھولیں",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `،ٹیکسٹ ویو کھلا ہے ${viewType} ویو کھولیں` : `${viewType} ویو کھلا ہے، ٹیکسٹ ویو کھولیں`,
  // DateRange placeholders
  start: "شروع",
  end: "ختم",
  // Action bar
  cancelButtonLabel: "کینسل",
  clearButtonLabel: "کلئیر",
  okButtonLabel: "اوکے",
  todayButtonLabel: "آج",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "تاریخ منتخب کریں",
  dateTimePickerDefaultToolbarTitle: "تاریخ اور وقت منتخب کریں",
  timePickerDefaultToolbarTitle: "وقت منتخب کریں",
  dateRangePickerDefaultToolbarTitle: "تاریخوں کی رینج منتخب کریں",
  // Clock labels
  clockLabelText: (view, time, adapter) => `${view} منتخب کریں ${time === null ? "کوئی وقت منتخب نہیں" : `منتخب وقت ہے ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} گھنٹے`,
  minutesClockNumberText: (minutes) => `${minutes} منٹ`,
  secondsClockNumberText: (seconds) => `${seconds} سیکنڈ`,
  // Open picker labels
  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `تاریخ منتخب کریں، منتخب شدہ تاریخ ہے ${utils.format(value, "fullDate")}` : "تاریخ منتخب کریں",
  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `وقت منتخب کریں، منتخب شدہ وقت ہے ${utils.format(value, "fullTime")}` : "وقت منتخب کریں",
  // Table labels
  timeTableLabel: "وقت منتخب کریں",
  dateTableLabel: "تاریخ منتخب کریں"
};
var urPK = getPickersLocalization(urPKPickers);

// node_modules/@mui/x-date-pickers/locales/beBY.js
var views7 = {
  // maps TimeView to its translation
  hours: "гадзіны",
  minutes: "хвіліны",
  seconds: "секунды",
  // maps PickersToolbar["viewType"] to its translation
  calendar: "календара",
  clock: "часу"
};
var beBYPickers = {
  // Calendar navigation
  previousMonth: "Папярэдні месяц",
  nextMonth: "Наступны месяц",
  // View navigation
  openPreviousView: "адкрыць папярэдні выгляд",
  openNextView: "адкрыць наступны выгляд",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "гадавы выгляд адкрыты, перайсці да каляндарнага выгляду" : "каляндарны выгляд адкрыты, перайсці да гадавога выгляду",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `тэкставае поле адкрыта, перайсці да выгляду ${views7[viewType]}` : `Выгляд ${views7[viewType]} зараз адкрыты, перайсці да тэкставага поля`,
  // DateRange placeholders
  start: "Пачатак",
  end: "Канец",
  // Action bar
  cancelButtonLabel: "Адмена",
  clearButtonLabel: "Ачысціць",
  okButtonLabel: "OK",
  todayButtonLabel: "Сёння",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Абраць дату",
  dateTimePickerDefaultToolbarTitle: "Абраць дату і час",
  timePickerDefaultToolbarTitle: "Абраць час",
  dateRangePickerDefaultToolbarTitle: "Абраць каляндарны перыяд",
  // Clock labels
  clockLabelText: (view, time, adapter) => `Абярыце ${views7[view]}. ${time === null ? "Час не абраны" : `Абраны час ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} гадзін`,
  minutesClockNumberText: (minutes) => `${minutes} хвілін`,
  secondsClockNumberText: (seconds) => `${seconds} секунд`,
  // Open picker labels
  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Абраць дату, абрана дата  ${utils.format(value, "fullDate")}` : "Абраць дату",
  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Абраць час, абрыны час  ${utils.format(value, "fullTime")}` : "Абраць час",
  // Table labels
  timeTableLabel: "абраць час",
  dateTableLabel: "абраць дату"
};
var beBY = getPickersLocalization(beBYPickers);

// node_modules/@mui/x-date-pickers/locales/ruRU.js
var timeViews3 = {
  hours: "часы",
  minutes: "минуты",
  seconds: "секунды"
};
var viewTypes = {
  calendar: "календарный",
  clock: "часовой"
};
var ruRUPickers = {
  // Calendar navigation
  previousMonth: "Предыдущий месяц",
  nextMonth: "Следующий месяц",
  // View navigation
  openPreviousView: "открыть предыдущий вид",
  openNextView: "открыть следующий вид",
  calendarViewSwitchingButtonAriaLabel: (view) => view === "year" ? "открыт годовой вид, переключить на календарный вид" : "открыт календарный вид, переключить на годовой вид",
  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `Открыт текстовый вид, перейти на ${viewTypes[viewType]} вид` : `Открыт ${viewTypes[viewType]} вид, перейти на текстовый вид`,
  // DateRange placeholders
  start: "Начало",
  end: "Конец",
  // Action bar
  cancelButtonLabel: "Отмена",
  clearButtonLabel: "Очистить",
  okButtonLabel: "Ок",
  todayButtonLabel: "Сегодня",
  // Toolbar titles
  datePickerDefaultToolbarTitle: "Выбрать дату",
  dateTimePickerDefaultToolbarTitle: "Выбрать дату и время",
  timePickerDefaultToolbarTitle: "Выбрать время",
  dateRangePickerDefaultToolbarTitle: "Выбрать период",
  // Clock labels
  clockLabelText: (view, time, adapter) => `Выбрать ${timeViews3[view]}. ${time === null ? "Время не выбрано" : `Выбрано время ${adapter.format(time, "fullTime")}`}`,
  hoursClockNumberText: (hours) => `${hours} часов`,
  minutesClockNumberText: (minutes) => `${minutes} минут`,
  secondsClockNumberText: (seconds) => `${seconds} секунд`,
  // Open picker labels
  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Выберите дату, выбрана дата ${utils.format(value, "fullDate")}` : "Выберите дату",
  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Выберите время, выбрано время ${utils.format(value, "fullTime")}` : "Выберите время",
  // Table labels
  timeTableLabel: "выбрать время",
  dateTableLabel: "выбрать дату"
};
var ruRU = getPickersLocalization(ruRUPickers);

// node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js
init_extends();
var React = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var import_jsx_runtime = __toESM(require_jsx_runtime());
var MuiPickersAdapterContext = React.createContext(null);
if (true) {
  MuiPickersAdapterContext.displayName = "MuiPickersAdapterContext";
}
var warnedOnce = false;
function LocalizationProvider(inProps) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiLocalizationProvider"
  });
  const {
    children,
    dateAdapter: Utils,
    dateFormats,
    dateLibInstance,
    locale,
    adapterLocale,
    localeText
  } = props;
  if (true) {
    if (!warnedOnce && locale !== void 0) {
      warnedOnce = true;
      console.warn("LocalizationProvider's prop `locale` is deprecated and replaced by `adapterLocale`");
    }
  }
  const utils = React.useMemo(() => new Utils({
    locale: adapterLocale != null ? adapterLocale : locale,
    formats: dateFormats,
    instance: dateLibInstance
  }), [Utils, locale, adapterLocale, dateFormats, dateLibInstance]);
  const defaultDates = React.useMemo(() => {
    return {
      minDate: utils.date("1900-01-01T00:00:00.000"),
      maxDate: utils.date("2099-12-31T00:00:00.000")
    };
  }, [utils]);
  const contextValue = React.useMemo(() => {
    return {
      utils,
      defaultDates,
      localeText: _extends({}, DEFAULT_LOCALE, localeText != null ? localeText : {})
    };
  }, [defaultDates, utils, localeText]);
  return (0, import_jsx_runtime.jsx)(MuiPickersAdapterContext.Provider, {
    value: contextValue,
    children
  });
}
true ? LocalizationProvider.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "yarn proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Locale for the date library you are using
   */
  adapterLocale: import_prop_types.default.oneOfType([import_prop_types.default.object, import_prop_types.default.string]),
  children: import_prop_types.default.node,
  /**
   * DateIO adapter class function
   */
  dateAdapter: import_prop_types.default.func.isRequired,
  /**
   * Formats that are used for any child pickers
   */
  dateFormats: import_prop_types.default.shape({
    dayOfMonth: import_prop_types.default.string,
    fullDate: import_prop_types.default.string,
    fullDateTime: import_prop_types.default.string,
    fullDateTime12h: import_prop_types.default.string,
    fullDateTime24h: import_prop_types.default.string,
    fullDateWithWeekday: import_prop_types.default.string,
    fullTime: import_prop_types.default.string,
    fullTime12h: import_prop_types.default.string,
    fullTime24h: import_prop_types.default.string,
    hours12h: import_prop_types.default.string,
    hours24h: import_prop_types.default.string,
    keyboardDate: import_prop_types.default.string,
    keyboardDateTime: import_prop_types.default.string,
    keyboardDateTime12h: import_prop_types.default.string,
    keyboardDateTime24h: import_prop_types.default.string,
    minutes: import_prop_types.default.string,
    month: import_prop_types.default.string,
    monthAndDate: import_prop_types.default.string,
    monthAndYear: import_prop_types.default.string,
    monthShort: import_prop_types.default.string,
    normalDate: import_prop_types.default.string,
    normalDateWithWeekday: import_prop_types.default.string,
    seconds: import_prop_types.default.string,
    shortDate: import_prop_types.default.string,
    weekday: import_prop_types.default.string,
    weekdayShort: import_prop_types.default.string,
    year: import_prop_types.default.string
  }),
  /**
   * Date library instance you are using, if it has some global overrides
   * ```jsx
   * dateLibInstance={momentTimeZone}
   * ```
   */
  dateLibInstance: import_prop_types.default.any,
  /**
   * Locale for the date library you are using
   * @deprecated Use `adapterLocale` instead
   */
  locale: import_prop_types.default.oneOfType([import_prop_types.default.object, import_prop_types.default.string]),
  /**
   * Locale for components texts
   */
  localeText: import_prop_types.default.object
} : void 0;

export {
  nlNL,
  plPL,
  ptBR,
  trTR,
  deDE,
  esES,
  faIR,
  fiFI,
  csCZ,
  frFR,
  huHU,
  DEFAULT_LOCALE,
  enUS,
  nbNO,
  svSE,
  itIT,
  zhCN,
  koKR,
  isIS,
  jaJP,
  ukUA,
  urPK,
  beBY,
  ruRU,
  MuiPickersAdapterContext,
  LocalizationProvider
};
//# sourceMappingURL=chunk-BLIVCLMD.js.map
